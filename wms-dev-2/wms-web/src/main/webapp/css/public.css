body {
    margin: 10px;
    background: #f2f2f2;
}
.white-bg {
    background-color: #fff;
}
.help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 0px;
    color: #737373;
    font-size:10px;
}

.layuimini-container {
    border: 1px solid #f2f2f2;
    border-radius: 8px;
    /*background-color: #ffffff*/
}

.layuimini-main {
    padding: 10px 10px;
    background-color: #ffffff;
}

.layuimini-main .layui-form-item{
    margin: 0px !important;
}
.layuimini-main .layui-form-item .layui-inline{
    margin-top: 5px !important;
}
 .layuimini-main .layui-form-item .layui-inline .layui-form-label{
     padding: 5px 5px 5px 0px; !important;
     width:100px;
     text-align:left;
 }
.layuimini-main .layui-form-item .layui-inline .layui-input{
    height: 32px !important;
    border-radius: 5px;
}
.layuimini-main .layui-form-item .layui-inline .layui-btn{
    height: 30px !important;
    line-height: 28px !important;
}
.layuimini-container .layui-table-view{
    background-color: #ffffff
}

/**必填红点 */
.layuimini-form > .layui-form-item > .required:after {
    content: '*';
    color: red;
    position: absolute;
    margin-left: 4px;
    font-weight: bold;
    line-height: 1.8em;
    top: 6px;
    right: 5px;
}

.layuimini-form > .layui-form-item > .layui-form-label {
    width: 120px !important;
}

.layuimini-form > .layui-form-item > .layui-input-block {
    margin-left: 150px !important;
}

.layuimini-form > .layui-form-item > .layui-input-block > tip {
    display: inline-block;
    margin-top: 10px;
    line-height: 10px;
    font-size: 10px;
    color: #a29c9c;
}

/**搜索框*/
.layuimini-container .table-search-fieldset {
    margin: 0;
    border: 1px solid #e6e6e6;
    padding: 10px 20px 5px 20px;
    color: #6b6b6b;
}
input.layui-disabled{
    background-color: #F6F6F6;
}

/**自定义滚动条样式 */
::-webkit-scrollbar {
    width: 15px;
    height: 15px
}

::-webkit-scrollbar-track {
    background-color: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: #9c9da0;
}
.layui-block{
    margin: 15px;
}
.layui-form-label.required:before { /* 这种写法也可以*/
    content: ' *';
    color: red;
}

.layui-btn-col1 {
    background-color: #66cc99;
}
.layui-btn-xs{
    height: 26px !important;
    line-height: 26px !important;
}

.layui-icon-save:before{
    content:"\f0c7"
}