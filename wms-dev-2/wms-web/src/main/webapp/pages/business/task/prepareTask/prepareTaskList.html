<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">拉布单：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_taskId" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">对应铺床：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_taskId" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">执行状态：</label>
                    <div class="layui-input-inline">
                        <select name="eq_state" id="state"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">计划日期：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="eq_planTime" id="planTime" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="start">
            <i class="layui-icon layui-icon-play"></i>开始备货
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="stop">
            <i class="layui-icon layui-icon-pause"></i>停止备货
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="complete">
            <i class="layui-icon layui-icon-ok-circle"></i>完成备货
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="updateDetail">
            <i class="layui-icon layui-icon-refresh"></i>更新备货明细
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script>
    layui.use(['form', 'table','miniTab'], function () {
        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab;

        //日期
        layui.laydate.render({
            elem: '#planTime',
            weekStart:1
        });

        var tableId = "dataTable";
        var urlPath = "task/prepareTask/";

        var dictTable = createTable({
            elem: tableId,
            url: urlPath +'list',
            toolbar: "tableBar",
            where: {
                searchParams: form.val('inputForm')
            },
            cols: [[
                {type:'radio', fixed: 'left'},
                {field: 'billNo', width: 100, title: '拉布单号'},
                {field: 'latheNo', width: 150, title: '对应铺床', sort: true},
                {field: 'area_dictText', width: 150, title: '对应区域'},
                {field: 'clothCount', width: 120, title: '布匹总数'},
                {field: 'completedCount', width: 120, title: '完成数量'},
                {field: 'contractNo', width: 150, title: '合约号'},
                {field: 'planTime', width: 120, title: '计划日期'},
                {field: 'state_dictText', width: 100, title: '执行状态'},
                {field: 'msg', width: 100, title: '提示信息'},
                {field: 'agvCompleted_dictText', width: 100, title: 'AGV预搬运'},
                {field: 'planSort', width: 100, title: '执行序号'},
                {field: 'priority', width: 100, title: '优先级'},
                {field: 'allow_dictText', width: 100, title: '允许备货'},
                {field: 'publishTime', width: 100, title: '发布时间'},
                {field: 'createTime', width: 100, title: '读取时间'},
                {field: 'startTime', width: 120, title: '执行时间'},
                {field: 'updateTime', width: 180, title: '更新时间'},
                {title: '操作', width: 160, minWidth: 160, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload(tableId, {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
            switch(obj.event){
                //开始备货
                case 'start':
                    var data = checkStatus.data;  //获取选中行数据
                    if(data == null || data.length ==0){
                        $.modal.alertWarning("请选择一条记录");
                        return;
                    }
                    $.operate.postJson({
                        url: urlPath +'start',
                        data: JSON.stringify({"id": data[0].id}),
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload(tableId);
                            }
                        }
                    });
                    break;

                //停止备货
                case 'stop':
                    var data = checkStatus.data;  //获取选中行数据
                    if(data == null || data.length ==0){
                        $.modal.alertWarning("请选择一条记录");
                        return;
                    }
                    $.operate.postJson({
                        url: urlPath +'stop',
                        data: JSON.stringify({"id": data[0].id}),
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload(tableId);
                            }
                        }
                    });
                    break;

                //完成备货
                case 'complete':
                    var data = checkStatus.data;  //获取选中行数据
                    if(data == null || data.length ==0){
                        $.modal.alertWarning("请选择一条记录");
                        return;
                    }
                    $.operate.postJson({
                        url: urlPath +'complete',
                        data: JSON.stringify({"id": data[0].id}),
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload(tableId);
                            }
                        }
                    });
                    break;

                //更新备货明细
                case 'updateDetail':
                    var data = checkStatus.data;  //获取选中行数据
                    if(data == null || data.length ==0){
                        $.modal.alertWarning("请选择一条记录");
                        return;
                    }
                    $.operate.postJson({
                        url: urlPath +'update',
                        data: JSON.stringify({"id": data[0].id}),
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload(tableId);
                            }
                        }
                    });
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑',
                        url: "prepareTaskInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    $.operate.postJson({
                        url: urlPath +'delete',
                        data: JSON.stringify(postData),
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload(tableId);
                            }
                            layer.close(index);
                        }
                    });
                    break;
                case 'delete':
                    layer.confirm('真的删除行么', function (index) {
                        $.operate.postJson({
                            url: urlPath +'delete',
                            data: data.id,
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }
        });
        $.operate.initEnumDropDown("PrepareStatus", "state");
    });
</script>
</body>
</html>