<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .layui-form .layui-form-label{
            width:110px;
            padding: 9px 5px;
        }
        .layui-form .layui-input-block{
            margin-left: 130px;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-form-item">
            <label class="layui-form-label required">目的位置：</label>
            <div class="layui-input-block">
                <select id="toSite" name="toSite" lay-verify="required"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">优先级：</label>
            <div class="layui-input-block">
                <input type="text" name="priority" lay-filter="priority" autocomplete="off" class="layui-input" value="20">
            </div>
        </div>
    </form>

    <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>

    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createBy" lay-filter="createBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createTime" lay-filter="createTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateBy" lay-filter="updateBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新时间：</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateTime" lay-filter="updateTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="button" value="保存" class="layui-btn" id="btnSave" />
            <input type="button" value="关闭" class="layui-btn layui-btn-primary" id="btnClose" />
        </div>
    </div>
</div>


<script>
    layui.use(['form', 'table'], function () {
        var tableId = "dataTable";
        var urlPath = "task/taskBox/";

        var dataTable = createTable({
            elem: tableId,
            url: 'storage/stock/list',
            where: {
                searchParams: layui.form.val('inputForm')
            },
            cols: [[
                {type:'checkbox', fixed: 'left'},
                {field: 'code', width: 100, title: '货位编码'},
                {field: 'location', width: 80, title: '货位', sort: true},
                {field: 'level', width: 110, title: '层位'},
                {field: 'boxType_dictText', width: 110, title: '容器类型', sort: true},
                {field: 'boxNo', width: 110, title: '容器号', sort: true},
                {field: 'boxEmpty_dictText', width: 110, title: '空实状态', sort: true},
                {field: 'area', width: 120, title: '区域'},
                {field: 'state_dictText', width: 100, title: '容器状态'},
                {field: 'bookNo', width: 140, title: '单编号', sort: true}
            ]]
        });
    });
    $(document).ready(function(){
        initPage();
        $("#btnSave").click(function () {
            saveFun();
        });
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
    });

    function initPage(){
        $.operate.initDictDropDown("wms_point,point_name,point_no,description='O'", "toSite");
        $.operate.initDictRadio("box_empty_status", "block_boxEmpty", "boxEmpty");

        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: "task/taskBox/queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }

    function saveFun(){
        var postData = layui.form.val('inputForm');
        var id = $.common.getURLParameter("id");

        var url = "task/taskBox/addbooktask";
        if($.common.isNotEmpty(id)){
            url = "task/taskBox/editempty";
            postData.id = id;
        }

        var boxNoList = [];
        var selDetails = layui.table.checkStatus("dataTable");
        if(selDetails.data.length ==0){
            $.modal.alert("请选择数据");
            return;
        }
        for(var i = 0 ; i < selDetails.data.length; i++){
            var boxList={
                "boxNo":selDetails.data[i].boxNo
            }
            boxNoList.push(boxList);
        }
        postData.boxNoList = boxNoList;
        // var toSite=$('#toSite').text();
        // var priority=$('#priority').text();
        // var res={
        //     "toSite":toSite,
        //     "priority":priority,
        //     "boxNoList":boxNoList
        // }
        $.operate.postJson({
            url: url,
            data: JSON.stringify(postData),
            isShowMsg:true,
            callback: function(result){
                if(result.code==200){
                    $.modal.closeDrawer(result);
                }
            }
        });
    }
</script>
</body>
</html>