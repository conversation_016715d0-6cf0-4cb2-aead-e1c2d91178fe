<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.11.0/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <input type="hidden" name="eq_taskStatus" value="0">
            <input type="hidden" name="eq_boxType" value="1">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">任务号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_taskOrder" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">容器号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_boxNo" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label ">目的位置：</label>
                    <div class="layui-input-block">
                        <input type="text" name="lk_toSite" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dictTable" lay-filter="dictTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script>
    layui.use(['form', 'table','miniTab'], function () {
        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab;

        var tableId = "dictTable";
        var urlPath = "task/taskBox/";

        var dictTable = createTable({
            elem: tableId,
            url: urlPath+'rootList',
            toolbar: "tableBar",
            where: {
                searchParams: form.val('inputForm')
            },
            cols: [[
                {type: "checkbox", width: 50},
                {field: 'taskOrder', width: 100, title: '任务号', sort: true},
                {field: 'boxNo', width: 100, title: '容器号'},
                {field: 'boxType', width: 100, title: '容器类型'},
                {field: 'fromSite', width: 100, title: '起始位置'},
                {field: 'toSite', width: 150, title: '目的位置', sort: true},
                {field: 'taskType', width: 150, title: '任务类型',templet: function (d) {
                        //  任务类型：0采购入库，1调拨入库，2生产退料回库，3领料出库，4调拨出库，5采购退货出库，6指定出库
                        if (d.taskType == 0) {
                            return '采购入库';
                        } else if (d.taskType == 1) {
                            return '调拨入库';
                        } else if (d.taskType == 2) {
                            return '生产退料回库';
                        } else if (d.taskType == 3) {
                            return '领料出库';
                        } else if (d.taskType == 4) {
                            return '调拨出库';
                        }else if (d.taskType == 7) {
                            return '指定入库';
                        }
                    }
                },
                {field: 'priority', width: 120, title: '优先级', sort: true},
                {field: 'taskStatus', width: 120, title: '任务状态', sort: true,templet: function (d) {
                       //任务状态：0-创建，1.任务已下发，2.执行搬运，3.输送线运输， 4.完成
                        if(d.taskStatus == 0){
                            return '创建';
                        }else if(d.taskStatus == 1){
                            return '任务已下发';
                        }else if(d.taskStatus == 2){
                            return '执行搬运';
                        }else if(d.taskStatus == 3){
                            return '输送线运输';
                        }else if(d.taskStatus == 4){
                            return '完成';
                        }
                    },
                },
                {field: 'createBy', width: 120, title: '操作员', sort: true},
                {field: 'createTime', width: 180, title: '创建时间'},
                {field: 'updateTime', width: 180, title: '更新时间'},
                {title: '操作', width: 230, minWidth: 230, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('dictTable', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            switch(obj.event){
                case 'add':
                    $.modal.openDrawer({
                        title: '新增',
                        url: "emptyBoxInboundInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'refresh':
                    table.reload(tableId);
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑',
                        url: "emptyBoxInboundInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'delete':
                    layer.confirm('确定要删除该空箱入库任务吗？', function (index) {
                        $.operate.postJson({
                            url: urlPath +'delete',
                            data: data.id,
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }
        });
        $.operate.initDictDropDown("wms_point,point_name,point_no,description='O'", "toSite");
    });
</script>
</body>
</html>
