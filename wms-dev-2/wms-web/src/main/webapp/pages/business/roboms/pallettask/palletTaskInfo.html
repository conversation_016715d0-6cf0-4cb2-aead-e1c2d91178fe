<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .layui-form .layui-form-label{
            width:110px;
            padding: 9px 5px;
        }
        .layui-form .layui-input-block{
            margin-left: 130px;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">搬运任务起始位置：</label>
                    <div class="layui-input-block">
                        <input type="text" name="fromSite" lay-filter="fromSite" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">搬运任务目的位置：</label>
                    <div class="layui-input-block">
                        <input type="text" name="toSite" lay-filter="toSite" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">托盘上坯布容器号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="containerNo" lay-filter="containerNo" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">拉布单号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="labrador" lay-filter="labrador" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">布匹号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="content" lay-filter="content" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

        </div>




        <div class="layui-form-item">
            <label class="layui-form-label required">任务类型：</label>
            <div class="layui-input-block">
                <select name="forkTaskType" id="forkTaskType"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">任务状态：</label>
            <div class="layui-input-block">
                <select name="forkTaskStatus" id="forkTaskStatus"></select>
            </div>
        </div>


    </form>

    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 130px;">
            <button type="button" class="layui-btn" id="btnSave" ><i class="fa fa-save"></i>保存</button>
            <button type="button" class="layui-btn layui-btn-primary" id="btnClose" ><i class="layui-icon layui-icon-close"></i>关闭</button>
        </div>
    </div>
</div>

<script>
    var urlPath = "roboms/pallettask/";



    $(document).ready(function(){
        initPage();
        $("#btnSave").click(function () {
            saveFun();
        });
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
    });

    function initPage(){



        $.operate.initDictDropDown("fork_task_type", "forkTaskStatus");
        $.operate.initDictDropDown("fork_task_status", "forkTaskType");



        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: urlPath + "queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }

    function saveFun(){
        var postData = layui.form.val('inputForm');
        var id = $.common.getURLParameter("id");

        var url = urlPath + "add";
        if($.common.isNotEmpty(id)){
            url = urlPath + "edit";
            postData.id = id;
        }

        $.operate.postJson({
            url: url,
            data: JSON.stringify(postData),
            isShowMsg:true,
            callback: function(result){
                if(result.code==200){
                    $.modal.closeDrawer(result);
                }
            }
        });
    }
</script>
</body>
</html>