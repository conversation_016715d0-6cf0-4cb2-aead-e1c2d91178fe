<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">起始位置：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_fromSite" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">目的位置：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_toSite" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">拉布单号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_labrador" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">布匹号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_content" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">搬运任务类型：</label>
                    <div class="layui-input-inline">
                        <select name="eq_forkTaskType" id="forkTaskType"></select>

                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">搬运任务状态：</label>
                    <div class="layui-input-inline">
                        <select name="eq_forkTaskStatus" id="forkTaskStatus"></select>
                    </div>
                </div>


                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>手动新增任务
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="complete">
            <i class="layui-icon layui-icon-ok-circle"></i>手工完成
        </button>

        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon layui-icon-refresh-3"></i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script>
    layui.use(['form', 'table','miniTab','laydate'], function () {


        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab,
            laydate = layui.laydate;



        var tableId = "dataTable";
        var urlPath = "roboms/pallettask/";

        var dictTable = createTable({
            elem: tableId,
            url: urlPath +'list',
            toolbar: "tableBar",
            where: {
                searchParams: form.val('inputForm')
            },
            cols: [[
                {type:'radio', fixed: 'left'},
                {type:'numbers'},
                {field: 'fromSite', width: 100, title: '起始位置'},
                {field: 'toSite', width: 150, title: '目的位置'},
                {field: 'containerNo', width: 150, title: '托盘号'},
                {field: 'labrador', width: 150, title: '拉布单号'},
                {field: 'content', width: 150, title: '布匹号'},
                {field: 'forkTaskType_dictText', width: 300, title: '搬运任务类型'},
                {field: 'forkTaskStatus_dictText', width: 150, title: '搬运任务状态'},



                {title: '操作', width: 160, minWidth: 160, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });





        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload(tableId, {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
            switch(obj.event){
                //增加
                case 'add':
                    $.modal.openDrawer({
                        title: '新增',
                        width: '800px',
                        url: "palletTaskInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;

                //手动完成
                case 'complete':
                    var data = checkStatus.data;  //获取选中行数据
                    if(data == null || data.length ==0){
                        $.modal.alertWarning("请选择一条记录");
                        return;
                    }

                    $.modal.confirm("是否手动完成选中作业??",function () {
                        $.operate.postJson({
                            url: urlPath +'complete',
                            data: JSON.stringify({"id": data[0].id}),
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                            }
                        });
                    });

                    break;







                //刷新
                case 'refresh':
                    table.reload(tableId);
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑',
                        width: '800px',
                        url: "palletTaskInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'delete':
                    layer.confirm('真的删除行么', function (index) {
                        $.operate.postJson({
                            url: urlPath +'delete',
                            data: data.id,
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }a
        });

        $.operate.initDictDropDown("fork_task_type", "forkTaskType");
        $.operate.initDictDropDown("fork_task_status", "forkTaskStatus");

    });
</script>
</body>
</html>