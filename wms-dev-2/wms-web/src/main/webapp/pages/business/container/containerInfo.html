<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>容器信息</title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../css/font-awesome.min.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .tree-box {
            width: 100%;
            border: 1px solid #ddd;
            overflow: auto;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-form-item">
            <label class="layui-form-label required">容器编码：</label>
            <div class="layui-input-block">
                <input type="text" name="containerNo" lay-filter="value" lay-verify="required" autocomplete="off" placeholder="请输入编码" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">容器类型：</label>
            <div class="layui-input-block" id="containerType">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">箱型:</label>
            <div class="layui-input-block">
                <select type="text" name="boxContainerType" id="boxContainerType" autocomplete="off" class="layui-input"></select>
            </div>

        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">状态：</label>
            <div class="layui-input-block" id="status">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">创建人:</label>
            <div class="layui-input-block">
                <input type="text" name="createBy" lay-filter="createBy" autocomplete="off" disabled class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">创建时间:</label>
            <div class="layui-input-block">
                <input type="text" name="createTime" lay-filter="createTime" autocomplete="off" disabled class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">更新人:</label>
            <div class="layui-input-block">
                <input type="text" name="updateBy" lay-filter="updateBy" autocomplete="off" disabled class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">更新时间：</label>
            <div class="layui-input-block">
                <input type="text" name="updateTime" lay-filter="updateTime" autocomplete="off" disabled class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <input type="button" value="保存" class="layui-btn" id="btnSave" lay-submit/>
                <input type="button" value="关闭" class="layui-btn layui-btn-primary" id="btnClose" />
            </div>
        </div>
    </form>
</div>

<script>
    layui.use(['form','tree'], function () {
        var form = layui.form;
        var tree = layui.tree;
        $.operate.initDictRadio("container_type","containerType","containerType");
        $.operate.initDictDropDown("box_type","boxContainerType");
        $.operate.initDictRadio("container_status","status","status");
        form.on('submit',function (data){
            var id = $.common.getURLParameter("id");
            var url = "container/add";
            if($.common.isNotEmpty(id)){
                url = "container/edit";
                data.field.id = id;
            }
            //提交字段跟后端字段映射转换
            data.field.boxNo = data.field.containerNo;
            data.field.boxType = data.field.containerType;
            data.field.boxContainerType = data.field.boxContainerType;
            data.field.boxEmpty = data.field.status;
            $.operate.postJson({
                url: url,
                data: JSON.stringify(data.field),
                isShowMsg:true,
                callback: function(result){
                    if(result.code==200){
                        $.modal.closeDrawer(result);
                    }
                }
            });
        });
    });


    $(document).ready(function(){
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
        initPage();
    });
    function initPage(){
        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: "container/queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        // 回显数据
                        var formData = result.data;
                        layui.form.val('inputForm', {
                            'containerNo': formData.boxNo,
                            'containerType': formData.boxType,
                            'boxContainerType': formData.boxContainerType,
                            'status': formData.boxEmpty,
                            'createBy': formData.createBy,
                            'createTime': formData.createTime,
                            'updateBy': formData.updateBy,
                            'updateTime': formData.updateTime
                        });
                       //设置容器编码不可编辑，并置灰
                        $("input[name='containerNo']").attr("disabled",true);
                        $("input[name='containerNo']").addClass("layui-disabled");
                        layui.form.render();
                    }
                }
            });
        }
    }
</script>
</body>
</html>