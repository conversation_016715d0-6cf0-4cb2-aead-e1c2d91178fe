<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">松布架号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_dollyNo" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">站点编码：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_siteCode" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">搬运方向：</label>
                    <div class="layui-input-inline">
                        <select name="eq_priority" id="priority" class=""></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">搜索布号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_content" autocomplete="off" class="layui-input">
                    </div>
                </div>
<!--                <div class="layui-inline">-->
<!--                    <label class="layui-form-label">调度类型：</label>-->
<!--                    <div class="layui-input-inline">-->
<!--                        <select name="eq_dispatchType" id="dispatchType" class=""></select>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="layui-inline">
                    <label class="layui-form-label">完成日期：</label>
                    <div class="layui-input-inline" style="width: 190px;">
                        <input type="text" name="ge_endTime" placeholder="请选择开始时间" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline" style="width: 190px;">
                        <input type="text" name="le_endTime" placeholder="请选择结束时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon layui-icon-refresh-3"></i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="detail">详情</a>
</script>

<script>
    layui.use(['form', 'table','miniTab'], function () {
        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab;
        $.operate.initDictDropDown("bound_site", "priority");
        $.operate.initDictDropDown("wms_task_agv_type,text,value", "dispatchType");

        var tableId = "dataTable";
        var urlPath = "history/agvDollyHistory/";

        var dictTable = createTable({
            elem: tableId,
            url: urlPath +'list',
            toolbar: "tableBar",
            where: {
                searchParams: form.val('inputForm')
            },
            cols: [[
                {type:'numbers'},
                {field: 'siteCode_dictText', width: 100, title: '站点编码', sort: true},
                {field: 'dollyNo', width: 150, title: '松布架号', sort: true},
                {field: 'dollyLayers_dictText', width: 150, title: '松布架层数'},
                {field: 'robot_dictText', width: 150, title: '智能设备', sort: true},
                {field: 'level', width: 80, title: '层位'},
                {field: 'height_dictText', width: 80, title: '层高度'},
                {field: 'locked_dictText', width: 80, title: '锁定'},
                {field: 'operatetag_dictText', width: 120, title: '操作标记'},
                {field: 'disable_dictText', width: 100, title: '是否搬运'},
                {field: 'toLatheNo', width: 100, title: '铺床'},
                {field: 'clothNo', width: 100, title: '布匹条码'},
                {field: 'billno', width: 100, title: '拉布单号'},
                {field: 'ispallet_dictText', width: 100, title: '是否有板'},
                {field: 'palletType_dictText', width: 100, title: '装载类型'},
                {field: 'palletHeight_dictText', width: 100, title: '板高度'},
                {field: 'createTime', width: 180, title: '创建时间'},
                {field: 'endTime', width: 180, title: '完成时间'},
                {title: '操作', width: 100, minWidth: 100, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload(tableId, {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
            switch(obj.event){
                //刷新
                case 'refresh':
                    table.reload(tableId);
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'detail':
                    $.modal.openDrawer({
                        title: '详情',
                        width: '800px',
                        url: "taskBoxHistoryInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                        }
                    });
                    break;
            }
        });
    });
</script>
</body>
</html>