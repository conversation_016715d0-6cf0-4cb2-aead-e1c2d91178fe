<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .layui-form .layui-form-label{
            width:110px;
            padding: 9px 5px;
        }
        .layui-form .layui-input-block{
            margin-left: 130px;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">站点编码：</label>
                    <div class="layui-input-block">
                        <select id="siteCode" name="siteCode"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">松布架号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="dollyNo" lay-filter="dollyNo" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">松布架层数：</label>
                    <div class="layui-input-block">
                        <select name="dollyLayers" id="dollyLayers"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">松布架类型：</label>
                    <div class="layui-input-block">
                        <select name="dollyType" id="dollyType"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">区域：</label>
                    <div class="layui-input-block">
                        <select name="area" id="area"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">智能设备：</label>
                    <div class="layui-input-block">
                        <select name="robot" id="robot"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">货位：</label>
                    <div class="layui-input-block">
                        <input type="text" name="location" lay-filter="location" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">层位：</label>
                    <div class="layui-input-block">
                        <input type="text" name="level" lay-filter="level" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">行位：</label>
                    <div class="layui-input-block">
                        <input type="text" name="rowNum" lay-filter="rowNum" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">列位：</label>
                    <div class="layui-input-block">
                        <input type="text" name="columnNumber" lay-filter="columnNumber" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">层高度：</label>
                    <div class="layui-input-block" id="heightBolck">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">锁定：</label>
                    <div class="layui-input-block" id="lockedBlock">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">禁止搬运：</label>
                    <div class="layui-input-block" id="disableBlock">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">操作标记：</label>
                    <div class="layui-input-block" id="operatetagBlock">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">货位状态：</label>
                    <div class="layui-input-block">
                        <select name="state" id="state"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">布匹条码：</label>
                    <div class="layui-input-block">
                        <input type="text" name="clothNo" lay-filter="clothNo" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">是否有板：</label>
                    <div class="layui-input-block" id="ispalletBlock">

                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">装载类型：</label>
                    <div class="layui-input-block">
                        <select name="palletType" id="palletType"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">板高度：</label>
                    <div class="layui-input-block">
                        <select name="palletHeight" id="palletHeight"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">拉布单号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="billno" lay-filter="billno" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">对应铺床：</label>
                    <div class="layui-input-block">
                        <input type="text" name="toLatheNo" lay-filter="toLatheNo" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">计划日期：</label>
                    <div class="layui-input-block">
                        <input type="text" id="planDate" name="planDate" lay-filter="planDate" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createBy" lay-filter="createBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createTime" lay-filter="createTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateBy" lay-filter="updateBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新时间：</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateTime" lay-filter="updateTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

    </form>

    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 130px;">
            <button type="button" class="layui-btn" id="btnSave" ><i class="fa fa-save"></i>保存</button>
            <button type="button" class="layui-btn layui-btn-primary" id="btnClose" ><i class="layui-icon layui-icon-close"></i>关闭</button>
        </div>
    </div>
</div>


<script>
    var urlPath = "dispatch/agvDollyHistory/";

    $(document).ready(function(){
        initPage();
        $("#btnSave").click(function () {
            saveFun();
        });
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
    });

    function initPage(){
        $.operate.initDictRadio("location_height", "heightBolck", "height");
        $.operate.initDictRadio("locked_status", "lockedBlock", "locked");
        $.operate.initDictRadio("yn", "disableBlock", "disable");
        $.operate.initDictRadio("yn", "operatetagBlock", "operatetag");
        $.operate.initDictRadio("yn", "ispalletBlock", "ispallet");

        $.operate.initDictDropDown("wms_vritual_site,site_code,site_code", "siteCode");
        $.operate.initDictDropDown("dolly_layers_type", "dollyLayers");
        $.operate.initDictDropDown("dolly_type", "dollyType");
        $.operate.initDictDropDown("wms_base_area,text,code", "area");
        $.operate.initDictDropDown("wms_commncation_device,deviceName,deviceNo", "robot");
        $.operate.initDictDropDown("location_status", "state");
        $.operate.initDictDropDown("pallet_type", "palletType");
        $.operate.initDictDropDown("pallet_height", "palletHeight");

        //日期
        layui.laydate.render({
            elem: '#planDate'
        });


        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: urlPath + "queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }

</script>
</body>
</html>