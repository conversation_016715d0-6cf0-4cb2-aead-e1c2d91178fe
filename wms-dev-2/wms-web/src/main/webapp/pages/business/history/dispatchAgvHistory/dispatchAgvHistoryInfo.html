<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .layui-form .layui-form-label{
            width:110px;
            padding: 9px 5px;
        }
        .layui-form .layui-input-block{
            margin-left: 130px;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">任务号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="taskId" lay-filter="taskId" lay-verify="required" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">任务类型：</label>
                    <div class="layui-input-block">
                        <select name="type" id="type" disabled class="layui-disabled"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">源位置：</label>
                    <div class="layui-input-block">
                        <select name="fromSite" id="fromSite" disabled class="layui-disabled"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">目的位置：</label>
                    <div class="layui-input-block">
                        <select name="toSite" id="toSite" disabled class="layui-disabled"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">源区域：</label>
                    <div class="layui-input-block">
                        <select name="fromArea" id="fromArea" disabled class="layui-disabled"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">目的区域：</label>
                    <div class="layui-input-block">
                        <select name="toArea" id="toArea" disabled class="layui-disabled"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">调度执行对象：</label>
                    <div class="layui-input-block">
                        <input type="text" name="sysid" lay-filter="sysid" lay-verify="required" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">任务状态：</label>
                    <div class="layui-input-block">
                        <select name="state" id="state" ></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">优先级：</label>
                    <div class="layui-input-block">
                        <input type="text" name="priority" lay-filter="priority" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">松布架号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="dollyNo" lay-filter="dollyNo" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">调度明细：</label>
                    <div class="layui-input-block">
                        <input type="text" name="content" lay-filter="content" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">松布架层数：</label>
                    <div class="layui-input-block">
                        <select name="dollyLayers" id="dollyLayers" disabled class="layui-disabled"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">余布标记：</label>
                    <div class="layui-input-block">
                        <input type="text" name="scrapFlag" lay-filter="scrapFlag" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">铺床号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="latheno" lay-filter="latheno" lay-verify="required" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">拉布单号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="billno" lay-filter="billno" lay-verify="required" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">布匹编码：</label>
                    <div class="layui-input-block">
                        <input type="text" name="clothNo" lay-filter="clothNo" lay-verify="required" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">异常代码：</label>
                    <div class="layui-input-block">
                        <input type="text" name="batch" lay-filter="batch" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">异常描述：</label>
                    <div class="layui-input-block">
                        <input type="text" name="errmsg" lay-filter="errmsg" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createBy" lay-filter="createBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createTime" lay-filter="createTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateBy" lay-filter="updateBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新时间：</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateTime" lay-filter="updateTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 130px;">
            <button type="button" class="layui-btn layui-btn-primary" id="btnClose" ><i class="layui-icon layui-icon-close"></i>关闭</button>
        </div>
    </div>
</div>

<script>
    var urlPath = "history/dispatchAgv/";

    $(document).ready(function(){
        initPage();
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
    });

    function initPage(){
        $.operate.initEnumDropDown("DispatchType", "type");
        $.operate.initDictDropDown("wms_point,point_name,point_no", "fromSite,toSite");
        $.operate.initDictDropDown("wms_base_area,text,code", "fromArea,toArea");
        $.operate.initEnumDropDown("DispatchStatus", "state");
        $.operate.initDictDropDown("dolly_layers_type", "dollyLayers");


        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: urlPath + "queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }

</script>
</body>
</html>