<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>包裹信息</title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .tree-box {
            width: 100%;
            border: 1px solid #ddd;
            overflow: auto;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-form-item">
            <label class="layui-form-label required">包裹编码：</label>
            <div class="layui-input-block">
                <input type="text" name="packageInfoCode" lay-verify="required" autocomplete="off" placeholder="请输入包裹编码" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">包裹名称：</label>
            <div class="layui-input-block">
                <input type="text" name="cargoName" lay-verify="required" autocomplete="off" placeholder="请输入包裹名称" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">总件数（包裹数）：</label>
            <div class="layui-input-block">
                <input type="text" name="totalNumber" lay-verify="required" autocomplete="off" placeholder="请输入总件数（包裹数）" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label required">总重量:</label>
            <div class="layui-input-block">
                <input type="text" name="totalWeight" lay-verify="required" autocomplete="off"  placeholder="请输入总重量" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">总体积：</label>
            <div class="layui-input-block">
                <input type="text" name="totalVolume" lay-verify="required" autocomplete="off" placeholder="请输入总体积" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">包装:</label>
            <div class="layui-input-block">
                <input type="text" name="packageService" autocomplete="off"  placeholder="纸、纤、木箱、木架、托膜、托木（大客户模式下运输方式为零担时必填）" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label required">送货方式:</label>
            <div class="layui-input-block">
                <select name="deliveryType" id="deliveryType" lay-verify="required"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <input type="button" value="保存" class="layui-btn" lay-submit/>
                <input type="button" value="关闭" class="layui-btn layui-btn-primary" id="btnClose" />
            </div>
        </div>
    </form>
</div>

<script>
    layui.use(['form','tree'], function () {
        $.operate.initDictDropDown("dop_delivery_type", "deliveryType");
        var form = layui.form;
        var tree = layui.tree;
        form.on('submit',function (data){
            var id = $.common.getURLParameter("id");
            var url = "dop/packageInfo/add";
            if($.common.isNotEmpty(id)){
                url = "dop/packageInfo/edit";
                data.field.id = id;
            }
            $.operate.postJson({
                url: url,
                data: JSON.stringify(data.field),
                isShowMsg:true,
                callback: function(result){
                    if(result.code==200){
                        $.modal.closeDrawer(result);
                    }
                }
            });
        });
    });


    $(document).ready(function(){
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
        initPage();
    });

    function initPage(){
        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: "dop/packageInfo/queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }
</script>
</body>
</html>