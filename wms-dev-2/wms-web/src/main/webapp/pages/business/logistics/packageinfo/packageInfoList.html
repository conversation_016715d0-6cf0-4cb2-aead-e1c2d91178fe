<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
            <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
            <link rel="stylesheet" href="../../../../css/public.css" media="all">
            <script src="../../../../js/base-config.js" charset="utf-8"></script>
            <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
            <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
            <script src="../../../../js/common-util.js" charset="utf-8"></script>
        </head>
<body>

<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">包裹名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_cargoName" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">送货方式：</label>
                    <div class="layui-input-inline">
                        <select type="text" name="eq_deliveryType" autocomplete="off" class="layui-input"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>

    <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
</div>

<script type="text/html" id="currentTableBar">
    <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除</a>
</script>

<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>
<!-- 操作列 -->
<script type="text/html" id="auth-state">
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><i class="layui-icon">&#xe642;</i>编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete"><i class="layui-icon">&#xe640;</i>删除</a>
</script>

<script>
    layui.use(['form', 'table'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        var tableObj = table.render({
            elem: '#currentTableId',
            url: ctx +'dop/packageInfo/list',
            method:'post',
            contentType:'application/json',
            toolbar: '#toolbarDemo',
            parseData:function(result){
                return {
                    "code": (result.code ==200 ? '0': result.code),
                    "data": result.data,
                    "count": result.total
                }
            },
            cols: [[
                {type: "radio",disabled:false},
                {field: 'packageInfoCode', width: 150, title: '包裹编码', sort: true},
                {field: 'cargoName', width: 150, title: '包裹名称', sort: true},
                {field: 'totalNumber', width: 120, title: '包裹总数量'},
                {field: 'totalWeight', width: 160, title: '包裹总重量'},
                {field: 'totalVolume', width: 100, title: '包裹总体积'},
                {field: 'packageService', width: 100, title: '包装'},
                {field: 'deliveryType_dictText', width: 120, title: '送货方式'},
                {templet: '#auth-state', width:150, minWidth: 150, align: 'center', title: '操作'}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true
        });
        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            switch(obj.event) {
                case 'add':
                    $.modal.openDrawer({
                        title: '新增出库类型',
                        url: "packageInfoInfo.html",
                        layerCallBack: function (returnVal) {
                            table.reload('currentTableId');
                        }
                    });
                    break;
                case 'refresh':
                    table.reload('currentTableId');
                    break;
            }
        });
        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                $.modal.openDrawer({
                    title: '新增出库类型',
                    url: "packageInfoInfo.html?id="+data.id,
                    layerCallBack:function(returnVal){
                        table.reload('currentTableId');
                    }
                });
                return false;
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除行么', function (index) {
                    $.operate.postJson({
                        url: "dop/packageInfo/delete",
                        data: data.id,
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload('currentTableId');
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });

    });
</script>
</body>
</html>