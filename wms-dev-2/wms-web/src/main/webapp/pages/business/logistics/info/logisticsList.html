<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
            <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
            <link rel="stylesheet" href="../../../../css/public.css" media="all">
            <script src="../../../../js/base-config.js" charset="utf-8"></script>
            <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
            <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
            <script src="../../../../js/common-util.js" charset="utf-8"></script>
        </head>
<body>

<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">公司编码：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_companyCode " autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">客户编码：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_customerCode" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">渠道单号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_logisticID" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">订单提交时间：</label>
                    <div class="layui-input-inline" style="width: 190px;">
                        <input type="text" name="ge_gmtCommit" placeholder="请选择开始时间" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline" style="width: 190px;">
                        <input type="text" name="le_gmtCommit" placeholder="请选择结束时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>

    <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
</div>

<script type="text/html" id="currentTableBar">
    <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除</a>
</script>

<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="create">
            <i class="layui-icon">&#xe9aa</i>下单服务
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="update">
            <i class="layui-icon">&#xe9aa</i>修改订单
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="read">
            <i class="layui-icon">&#xe9aa</i>查询订单
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="delete">
            <i class="layui-icon">&#xe9aa</i>撤销订单
        </button>
    </div>
</script>
<!-- 操作列 -->
<script type="text/html" id="auth-state">
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><i class="layui-icon">&#xe642;</i>编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete"><i class="layui-icon">&#xe640;</i>删除</a>
</script>

<script>
    layui.use(['form', 'table'], function () {
        layui.laydate.render({
            elem: 'input[name="ge_gmtCommit"],input[name="le_gmtCommit"]'
            ,type: 'datetime'
        });
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        var tableObj = table.render({
            elem: '#currentTableId',
            url: ctx +'dop/order/list',
            method:'post',
            contentType:'application/json',
            toolbar: '#toolbarDemo',
            parseData:function(result){
                return {
                    "code": (result.code ==200 ? '0': result.code),
                    "data": result.data,
                    "count": result.total
                }
            },
            cols: [[
                {type: "radio",disabled:false},
                {field: 'custOrderNo', width: 150, title: '商户订单号', sort: true},
                {field: 'logisticCompanyId', width: 150, title: '物流公司ID', sort: true},
                {field: 'logisticId', width: 120, title: '渠道单号'},
                {field: 'mailNo', width: 160, title: '运单号'},
                {field: 'needTraceInfo_dictText', width: 160, title: '是否订阅轨迹'},
                {field: 'companyCode', width: 160, title: '公司编码'},
                {field: 'orderType_dictText', width: 160, title: '下单模式'},
                {field: 'transportType_dictText', width: 160, title: '运输方式/产品类型'},
                {field: 'customerCode', width: 160, title: '客户编码/月结账号'},
                {field: 'senderCode_dictText', width: 100, title: '发货人'},
                {field: 'receiverCode_dictText', width: 100, title: '收货人'},
                {field: 'payType_dictText', width: 100, title: '支付方式'},
                {field: 'smsNotify_dictText', width: 100, title: '短信通知'},
                {field: 'sendStartTime', width: 100, title: '上门接货开始时间'},
                {field: 'sendEndTime', width: 100, title: '上门接货结束时间'},
                {field: 'originalWaybillNumber', width: 100, title: '原运单号'},
                {field: 'isOut_dictText', width: 100, title: '是否外发'},
                {field: 'passwordSigning_dictText', width: 100, title: '是否口令签收'},
                {field: 'isdispatched_dictText', width: 100, title: '是否可派送'},
                {field: 'ispresaleorder_dictText', width: 100, title: '是否预售单'},
                {field: 'isPickupSelf_dictText', width: 100, title: '是否合伙人自提'},
                {field: 'gmtCommit', width: 160, title: '订单提交时间'},
                {field: 'status_dictText', width: 100, title: '订单状态'},
                {field: 'remark', width: 160, title: '备注'},
                {templet: '#auth-state', width:150, minWidth: 150, align: 'center', title: '操作'}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true
        });
        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            switch(obj.event){
                case 'add':
                    $.modal.openDrawer({
                        title: '新增出库类型',
                        url: "logisticsInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload('currentTableId');
                        }
                    });
                    break;
                case 'refresh':
                    table.reload('currentTableId');
                    break;
                case 'create':
                    layer.confirm('确定下单吗?', function (index) {
                        let data=table.checkStatus(obj.config.id).data;
                        $.operate.postJson({
                            url: "dop/order/create",
                            data: JSON.stringify(data[0]),
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload('currentTableId');
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
                case 'read':
                    let data=table.checkStatus(obj.config.id).data;
                    $.operate.postJson({
                        url: "dop/order/read",
                        data: JSON.stringify(data[0]),
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload('currentTableId');
                            }
                            layer.close(index);
                        }
                    });
            }
        });
        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                $.modal.openDrawer({
                    title: '创建订单',
                    url: "logisticsInfo.html?id="+data.id,
                    layerCallBack:function(returnVal){
                        table.reload('currentTableId');
                    }
                });
                return false;
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除行么', function (index) {
                    $.operate.postJson({
                        url: "dop/order/delete",
                        data: data.id,
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload('currentTableId');
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });

    });
</script>
</body>
</html>