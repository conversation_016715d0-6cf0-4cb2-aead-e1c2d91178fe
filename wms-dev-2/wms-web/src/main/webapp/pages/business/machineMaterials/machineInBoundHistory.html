<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">机物料编码：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_materialCode" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">机物料名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_materialName" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">入库单号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_inStoreNumber" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit
                            lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset><i
                            class="layui-icon layui-icon-refresh-1"></i>重置
                    </button>
                </div>
            </div>
        </form>
    </div>

    <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
</div>

<script type="text/html" id="currentTableBar">
    <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除</a>
</script>

<!--<script type="text/html" id="toolbarDemo">-->
<!--    <div class="layui-btn-container">-->
<!--        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal" lay-event="InBound">-->
<!--            <i class="layui-icon"></i>入库-->
<!--        </button>-->
<!--        &lt;!&ndash;    <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal" lay-event="VirtualInBound">&ndash;&gt;-->
<!--        &lt;!&ndash;      <i class="layui-icon"></i>虚拟入库&ndash;&gt;-->
<!--        &lt;!&ndash;    </button>&ndash;&gt;-->
<!--    </div>-->
<!--</script>-->
<!-- 操作列 -->
<script type="text/html" id="auth-state">
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><i class="layui-icon">&#xe642;</i>编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete"><i class="layui-icon">&#xe640;</i>删除</a>
</script>

<script>
    layui.use(['form', 'table'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        $.operate.initDictDropDown("material_type", "materialType")

        var tableObj = table.render({
            elem: '#currentTableId',
            url: ctx + 'MachineMaterialInboundList/historyList',
            method: 'post',
            contentType: 'application/json',
            toolbar: '#toolbarDemo',
            parseData: function (result) {
                return {
                    "code": (result.code == 200 ? '0' : result.code),
                    "data": result.data,
                    "count": result.total
                }
            },
            cols: [[
                {type: "checkbox", width: 50},
                {field: 'inStoreNumber', width: 200, title: '入库单号'},
                {field: 'materialName', width: 150, title: '机物料名称'},
                {field: 'materialCode', width: 150, title: '机物料编码'},
                {field: 'boxType', width: 100, title: '优先箱型'},
                {field: 'recQuantity', width: 100, title: '来料数量'},
                {field: 'inQuantity', width: 100, title: '入库数量'},
                {field: 'brand', width: 100, title: '品牌'},
                {field: 'assetModel', width: 100, title: '规格'},
                {field: 'createTime', width: 150, title: '创建时间'},
                {field: 'assetClass', width: 150, title: '类别'},
                {field: 'status', width: 150, title: '状态',templet: function(d){
                        if(d.status == '0' || d.status ==""){
                            return '待入库';
                        }else if(d.status == '1'){
                            return '已预装';
                        }else if(d.status == '2'){
                            return '已入库';
                        }
                    }},
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true
        });


        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });


        /**
         * toolbar监听事件
         */
        // table.on('toolbar(currentTableFilter)', function (obj) {
        //     switch (obj.event) {
        //         case 'InBound':
        //             // InBound();
        //             break;
        //         case 'VirtualInBound':
        //             // VirtualInBound();
        //             break;
        //     }
        // });

    });
</script>
</body>
</html></title>
</head>
<body>

</body>
</html>