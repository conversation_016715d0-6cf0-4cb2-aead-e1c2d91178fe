<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>拣货</title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.11.0/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <script src="../../../js/auxiliaryPrePacking.js" charset="utf-8"></script>
    <style>
        body {
            padding: 6px;
        }
        h4 {
            font-size: 14px;
            font-weight: bold;
            padding-left: 5px;
            border-left: 3px solid #1E9FFF;
        }
        .layui-table-view + h4, /* 调整间距 */
        .info-display-item + h4 {
            margin-top: 25px;
        }
        /* 新信息显示样式 */
        .info-display-item {
            display: inline-block; /* 水平显示项目 */
            margin-right: 30px;
        }
        .info-display-item .info-label {
            margin-right: 5px;
            color: #666;
        }

        /* 确保静态表格有布局 */
        .static-table-container table {
            width: 100%;
            margin-top: 10px; /* 工具栏下方间距 */
        }

        /* --- 料箱部分特定样式 --- */
        /* 自身属性容器保留内边距 */
        #boxInfoDisplay {
            padding: 10px 0;
        }
        /* 信息项Grid布局 (应用到包裹层) */
        .box-info-grid-items { /* 新的包裹层类 */
            display: grid;
            grid-template-columns: repeat(2, auto); /* 响应式列 */
            gap: 10px 15px; /* 行间距 列间距 */
            align-items: center; /* 垂直居中 */
            margin-bottom: 15px; /* 在Grid下方增加间距 */
            justify-content: center;
        }
        /* Grid 内标签样式 (针对包裹层) */
        .box-info-grid-items .info-label {
            width: 80px; /* 统一标签宽度 */
            text-align: right;
            white-space: nowrap;
        }
        /* Grid 内值样式 (确保对齐) (针对包裹层) */
        .box-info-grid-items .info-value {
            vertical-align: middle;
        }
        /* 款式属性信息展示Grid布局 (保持不变) */
        #boxMaterialInfoDisplay {
            display: grid;
            grid-template-columns: repeat(2, auto);
            gap: 10px 25px;
            padding: 15px 0; /* 垂直内边距 */
            align-items: center; /* 在每个网格单元中垂直居中项目 */
            justify-content: center; /* 恢复居中对齐以匹配托盘 */
        }

        #boxMaterialToolbar .info-display-item {
            line-height: 32px; /* 与托盘信息项行高一致 */
        }
        /* 按钮工具栏样式 (保持不变) */
        #boxInfoToolbarButtons {
            text-align: center; /* 按钮栏内容居中 */
            padding: 5px 0;
        }

        /* 按钮工具栏内按钮间距 (保持不变) */
        #boxInfoToolbarButtons .info-display-item,
        #boxMaterialToolbarButtons .info-display-item {
            display: inline-block;

        }


        /* === 优化后的托盘信息展示布局（使用精确的 4 列网格） === */
        #palletInfoDisplay {
            display: grid;
            grid-template-columns: repeat(5, auto); /* 精确的四列等宽 */
            gap: 10px 15px; /* 行间距和缩小的列间距 */
            padding: 10px 0; /* 保留现有内边距 */
            justify-content: center; /* 水平居中网格轨道 */
        }
        /* 移除了 .key-value-row 相关样式 */

        #palletInfoDisplay .info-display-item { /* 网格内项目的样式 */
            /* display: inline-block; 如果存在则移除，网格负责放置 */
            /* vertical-align: middle; 如果存在则移除 */
            /* margin-right: 25px; 如果存在则移除 */
            /* min-width: 280px; 如果存在则移除 */
            line-height: 28px; /* 确保一致的行高 */
            /* 项目本身占据一个网格单元 */
        }
        /* 移除了 :last-child margin 相关规则 */

        #palletInfoDisplay .info-label {
            display: inline-block; /* 保留 inline-block */
            width: 80px; /* 为适应较窄列宽而缩小的标签宽度 */
            text-align: right;
            margin-right: 5px; /* 恢复标签和值之间的间距 */
            color: #666;
            /* 确保标签不会不必要地换行 */
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        /* 值 span/button 会自动跟在 inline-block 的标签后面 */

        /* === 优化后的按钮工具栏布局 (保持不变) === */
        #palletInfoToolbar {

            text-align: center; /* 居中按钮 */
            padding: 5px 0; /* 添加一些内边距 */
        }
        #palletInfoToolbar .info-display-item {
            margin-right: 10px; /* 按钮之间的间距 */
            display: inline-block; /* 保持按钮内联 */
        }
        #palletInfoToolbar .info-display-item:last-child {
            margin-right: 0; /* 移除最后一个按钮的右边距 */
        }
        /* === 托盘物料信息显示样式 === */
        #palletMaterialInfoDisplay {
            display: grid;
            grid-template-columns: repeat(2, auto); /* 创建一个两列布局 */
            gap: 10px 25px; /* 行间距和列间距 */
            padding: 15px 0; /* 垂直内边距 */
            align-items: center; /* 在每个网格单元中垂直居中项目 */
            justify-content: center; /* 将网格内容对齐到开始位置 */
        }

        #palletMaterialInfoDisplay .info-display-item {
            /* 网格处理布局，边距可能不需要 */
            line-height: 32px; /* 确保垂直对齐一致性 */
            /* 已移除 margin-right，因为 grid gap 会处理间距 */
        }

        #palletMaterialInfoDisplay .info-label {
            display: inline-block;
            width: 90px; /* 标签稍宽 */
            text-align: right;
            margin-right: 8px; /* 标签和值/选择框之间的间距 */
            color: #666;
            white-space: nowrap;
        }

        #palletMaterialInfoDisplay .info-value {
            vertical-align: middle; /* 将 span 值与标签文本对齐 */
            text-align: right; /* 添加右对齐 */
        }

        #palletMaterialInfoDisplay .layui-select {
            display: inline-block; /* 使 select 表现的像其他内联元素 */
            width: auto;         /* 允许 select 根据内容或容器调整大小 */
            min-width: 180px;    /* 设置最小宽度以获得更好的外观 */
            vertical-align: middle; /* 将 select 与标签文本对齐 */
        }
        /* 可选：如果需要，为 select 下拉容器设置样式 */
        #palletMaterialInfoDisplay .layui-form-select {
            display: inline-block;
            vertical-align: middle;
            width: auto;
            min-width: 180px; /* 匹配 select 的最小宽度 */
        }

        #palletMaterialInfoDisplay .layui-form-select dl {
            min-width: 180px; /* 确保下拉列表宽度匹配 */
        }
    </style>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">

        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">

            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-collapse">
                        <div class="layui-colla-item" id="boxInfoColla">
                            <h4 class="layui-colla-title">料箱拣货明细</h4>
                            <div class="layui-colla-content layui-show">
                                <div id="boxInfoDisplay" class="layui-form layui-form-pane">
                                    <div class="box-info-grid-items">
                                        <div class="info-display-item">
                                            <span class="info-label">箱号:</span>
                                            <span class="info-value" id="boxNoDisplay"></span>
                                        </div>
                                        <div class="info-display-item">
                                            <span class="info-label">容器类型:</span>
                                            <span class="info-value" id="boxTypeDisplay"></span>
                                        </div>
                                    </div>
                                    <div id="boxInfoToolbarButtons">
                                        <!--                                        <div class="info-display-item">-->
                                        <!--                                            <button class="layui-btn layui-btn-sm" id="btnBoxRefresh" type="button"><i class="layui-icon layui-icon-refresh"></i>刷新</button>-->
                                        <!--                                        </div>-->
                                        <div class="info-display-item">
                                            <button class="layui-btn layui-btn-sm" id="btnBoxBind" type="button">解绑入库</button>
                                        </div>
<!--                                        <div class="info-display-item">-->
<!--                                            <button class="layui-btn layui-btn-sm" id="btnBoxInbound" type="button">入库</button>-->
<!--                                        </div>-->
                                    </div>
                                    <div class="static-table-container">
                                        <table class="layui-table" id="boxInfoTableId" lay-filter="boxInfoTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-colla-item" id="boxMaterialInfoColla">
                            <h4 class="layui-colla-title">料架拣货明细</h4>
                            <div class="layui-colla-content layui-show">
                                <div id="boxMaterialInfoDisplay" class="layui-form layui-form-pane" style="text-align: right;">

                                    <div class="info-display-item">
                                        <span class="info-label">料架号:</span>
                                        <span class="info-value" id="MaterialInfoTrayNo"></span>
                                    </div>

                                    <div class="info-display-item">
                                        <!--                                        <button class="layui-btn layui-btn-primary" id="btnBoxMaterialPO">-->
                                        <!--                                            <span>料架容量</span>-->
                                        <!--                                            <i class="layui-icon layui-icon-down layui-font-12"></i>-->
                                        <!--                                        </button>-->
                                        <select id="MaterialInfoTrayCapacity"> name="MaterialInfoTrayCapacity" lay-filter="MaterialInfoTrayCapacity">
                                            <option value="0">请选择料架容量</option>
                                            <option value="25">25%</option>
                                            <option value="50">50%</option>
                                            <option value="75">75%</option>
                                            <option value="100">100%</option>
                                        </select>
                                    </div>

                                </div>
                                <div id="boxMaterialToolbar">
                                    <div class="layui-btn-container" style="margin-top: 10px; text-align: center;">

                                        <div class="info-display-item">
                                            <button class="layui-btn layui-btn-sm" id="btnBoxMaterialBind" type="button">解绑入库</button>
                                        </div>
<!--                                        <div class="info-display-item">-->
<!--                                            <button class="layui-btn layui-btn-sm" id="btnBoxMaterialInbound" type="button">入库</button>-->
<!--                                        </div>-->
                                    </div>
                                </div>
                                <!-- 静态 HTML 表格 -->
                                <div class="static-table-container">
                                    <table class="layui-table" id="boxMaterialTableId" lay-filter="boxMaterialTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

    </div>
</div>
<script>
    layui.use(['layer', 'form', 'table'], function() {
        var layer = layui.layer
            ,form = layui.form
            ,table = layui.table;

    });

    // 对外暴露初始化方法，供父页面调用
    function initData(preboxData) {
        if (preboxData && preboxData.length > 0) {
            handlePreboxData(preboxData);
        } else {
            queryPrePackingLineInfo();
        }
    }

    layui.use(['form', 'table'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        // 初始化料箱信息表格
        var boxInfoTable = table.render({
            height: 'full-200',
            elem: '#boxInfoTableId',
            page: false,
            toolbar: '#boxInfoToolbar',
            defaultToolbar: ['filter'],
            url:ctx + 'outDetailRecord/boxDetailsRecord',
            method: 'post',
            contentType: 'application/json',
            dataType : "json",

            cols: [[
                {field: 'gridCode', title: '格号', width: 80},
                {field: 'outStoreNumber', title: '出库单号', width: 200},
                {field: 'materialCode', title: '物料编码', width: 200},
                {field: 'materialName', title: '物料名称', width: 150},
                {field: 'specification', title: '物料规格', width: 100},
                {field: 'brand', title: '品牌', width: 100},
                // {field: 'originalOuantity', title: '库存数量', width: 100},
                // {field: 'pendingQuantity', title: '待出库数量', width: 120},
                {field: 'inQuantity', title: '出库数量', width: 120,edit: 'textarea'}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 15,
            text: {
                none: '暂无相关数据'
            },parseData: function (result) {
                debugger;

                if (result.success && result.data)
                {
                    $('#boxNoDisplay').text(result.data.boxCode)
                    $('#boxTypeDisplay').text(result.data.boxType);
                    return {
                        "code": (result.code == 200 ? '0' : result.code),
                        "data": result.data.outDetailsDtoList,
                        "count": result.data.total
                    }
                }
                else{
                    // document.getElementById("boxInfoColla").style.display = "none";
                    return {
                        "code": (result.code == 200? '0' : result.code),
                        "data": [],
                        "count": 0
                    }
                }
                ;
            }
        });

        //初始化托盘表格
        var boxAttributeTable = table.render({
            elem: '#boxMaterialToolbar',
            url:ctx + 'outDetailRecord/trayDetailsRecord',
            method: 'post',
            page: false,
            contentType: 'application/json',
            dataType : "json",
            height: 'full-200',
            toolbar: '#boxAttributeToolbar',
            defaultToolbar: ['filter'],
            parseData: function(res){ // res 即为原始返回的数据
                debugger;
                if(res.code==200)
                {
                    // $('#MaterialInfoTrayNo').text(res.data.boxCode);
                    // // document.getElementById('MaterialInfoTrayCapacity').value=25;
                    // layui.use('form', function(){
                    //     var form = layui.form;
                    //     // 假设你想设置值为"2"的选项为默认选中项
                    //     form.val('MaterialInfoTrayCapacity', '25'); // 注意这里的'city'要与select的name属性对应
                    // });
                    // if(res.data.trayCapacity>0) {
                    //     // $('#MaterialInfoTrayCapacity').value(res.data.trayCapacity);
                    //     document.getElementById('MaterialInfoTrayCapacity').value=res.data.trayCapacity;
                    //     // $("#MaterialInfoTrayCapacity").attr('Value',res.data.trayCapacity);
                    //     // $(".MaterialInfoTrayCapacity").find("option[text='res.data.trayCapacity']").attr("MaterialInfoTrayCapacity",true);
                    // }
                    if (res.success && res.data) {
                        $('#MaterialInfoTrayNo').text(res.data.boxCode);
                        return {
                            "code": (res.code == 200 ? '0' : result.code),
                            "data": res.data.outDetailsDtoList,
                            "count": res.data.total
                        }}else{
                        return {
                            "code": (res.code == 200? '0' : res.code),
                            "data": [],
                            "count": 0
                        }
                    };
                }else {
                    return {
                        "code": res.code == 200 ? 0 : res.code,
                        "msg": res.message || "数据格式错误或无数据",
                        "count": 0,
                        "data": []
                    };
                }

            },

            cols: [[
                {field: 'outStoreNumber', title: '出库单号', width: 120},
                {field:'materialCode', title: '物料编码', width: 200},
                {field:'materialName', title: '物料名称', width: 150},
                {field:'specification', title: '物料规格', width: 100},
                {field: 'brand', title: '品牌', width: 100},
                // {field:'originalOuantity', title: '库存数量', width: 100},
                // {field: 'pendingQuantity', title: '待入库数量', width: 120},
                {field: 'inQuantity', title: '出库数量', width: 120,edit: 'textarea'}
            ]],

        })



        // 绑定刷新按钮事件
        // $('#btnBoxRefresh').on('click', function() {
        //     boxInfoTable.reload();
        // });

        // 每5秒刷新一次页面
        setInterval(function(){
            var boxNo=$('#boxNoDisplay').text();
            if(boxNo==null || boxNo=="") {
                // alert("测试");
                boxInfoTable.reload();
            }
            var trayNo=$('#MaterialInfoTrayNo').text();
            if(trayNo==null || trayNo==""){
                boxAttributeTable.reload();
            }
            // window.location.reload(1); // 刷新当前页面
        }, 5000); // 每5000毫秒执行一次

        // 料箱拣货解绑入库按钮事件
        $('#btnBoxBind').on('click', function() {
            // 实现绑定逻辑
            debugger;
            var data = layui.table.cache['boxInfoTableId']; // 'testTable' 是你的表格ID
            var boxNo=$('#boxNoDisplay').text();
            var boxType=$('#boxTypeDisplay').text();
            // 将选中的数据映射为后端需要的 DTO 格式
            var preboxData = data.map(function(item) {
                return {
                    id:item.id,
                    gridNo:item.gridCode,
                    materialCode:item.materialCode,
                    materialName:item.materialName,
                    outStoreNumber: item.outStoreNumber,
                    materialName: item.materialName,
                    materialCode: item.materialCode,
                    inQuantity: item.inQuantity,
                    objectId:item.objectId,
                    assetModel: item.specification
                };
            });

            var res={
                "boxNo":boxNo,
                "containerType":boxType,
                "machineMaterialOutBoundRecordListDTOList":preboxData
            }

            var preboxDataJson = JSON.stringify(res); // 映射后的数据
            //加载Loading
            layer.msg('加载中...', {icon: 16});
            $.ajax({
                url: ctx + 'boxItem/OutBoundDetails',
                type: 'post',
                contentType: 'application/json', // 添加 contentType
                data: preboxDataJson, // 发送映射后的 JSON 字符串
                success: function(res) {
                    // 处理成功响应，例如根据后端的返回决定是否打开弹窗
                    if (res.code == 200) { // 假设后端成功返回 code 200
                        // openPrePacking();
                        // layer.msg(res.msg);
                        boxInfoTable.reload();
                        $('#boxNoDisplay').text("")
                    } else {
                        layer.msg(res.msg || '拣货解绑请求失败');
                    }
                },
                error: function(xhr, status, error) {
                    // 处理错误情况
                    layer.msg('拣货解绑请求失败: ' + error);
                    console.error("Prebox request failed:", status, error);
                }
            });
        });



        // 料架拣货解绑入库按钮事件
        $('#btnBoxMaterialBind').on('click', function() {
            // 料架实现绑定逻辑
            debugger;
            var data = layui.table.cache['boxMaterialToolbar']; // 'testTable' 是你的表格ID
            var trayNo=$('#MaterialInfoTrayNo').text();
            var volume=$('#MaterialInfoTrayCapacity').val();


            // 将选中的数据映射为后端需要的 DTO 格式
            var preboxData = data.map(function(item) {
                return {
                    id:item.id,
                    outStoreNumber:item.outStoreNumber,
                    objectId: item.objectId,
                    materialCode:item.materialCode,
                    materialName:item.materialName,
                    inStoreNumber: item.inStoreNumber,
                    materialName: item.materialName,
                    materialCode: item.materialCode,
                    actualQuantity: item.actualQuantity,
                    assetModel: item.specification
                };
            });

            var res={
                "boxNo":trayNo,
                "volume":volume,
                "machineMaterialOutBoundRecordListDTOList":preboxData
            }

            var preboxDataJson = JSON.stringify(res); // 映射后的数据
            //加载Loading
            layer.msg('加载中...', {icon: 16});
            $.ajax({
                url: ctx + 'boxItem/OutBoundDetails',
                type: 'post',
                contentType: 'application/json', // 添加 contentType
                data: preboxDataJson, // 发送映射后的 JSON 字符串
                success: function(res) {
                    // 处理成功响应，例如根据后端的返回决定是否打开弹窗
                    if (res.code == 200) { // 假设后端成功返回 code 200
                        // openPrePacking();
                        // layer.msg(res.msg);
                        boxAttributeTable.reload();
                        $('#MaterialInfoTrayNo').text("");
                    } else {
                        layer.msg(res.msg || '请求失败');
                    }
                },
                error: function(xhr, status, error) {
                    // 处理错误情况
                    layer.msg('请求接口失败: ' + error);
                    console.error("Prebox request failed:", status, error);
                }
            });
        });


    });

</script>
</body>
</html>