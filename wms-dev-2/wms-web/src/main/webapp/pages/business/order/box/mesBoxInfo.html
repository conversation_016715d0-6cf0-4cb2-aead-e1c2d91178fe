<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>工单信息</title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .tree-box {
            width: 100%;
            border: 1px solid #ddd;
            overflow: auto;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-form-item">
            <label class="layui-form-label required">料箱编号：</label>
            <div class="layui-input-block">
                <input type="text" name="boxNo" lay-filter="boxNo" lay-verify="required" autocomplete="off" placeholder="请输入料箱编号" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">空箱类型：</label>
            <div class="layui-input-block" id="boxEmptyBlock" lay-filter="boxEmptyBlockFilter">
            </div>
        </div>
        <div class="layui-form-item boxType">
            <label class="layui-form-label required">物料类型：</label>
            <div class="layui-input-block">
                <select name="boxType" id="boxType"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">料箱高度：</label>
            <div class="layui-input-block" id="boxHeightBlock">
            </div>
        </div>
        <div class="layui-form-item boxVolumeBlock">
            <label class="layui-form-label required">容量：</label>
            <div class="layui-input-block" id="boxVolumeBlock">
            </div>
        </div>
        <div class="layui-form-item bookNo">
            <label class="layui-form-label required">单编号：</label>
            <div class="layui-input-block">
                <input type="text" name="bookNo" id="bookNo" lay-filter="bookNo" autocomplete="off" placeholder="请输入单编号多个用|分隔" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">执行状态：</label>
            <div class="layui-input-block">
                <select name="status" id="status"></select>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">创建人:</label>
            <div class="layui-input-block">
                <input type="text" name="createBy" lay-filter="createBy" autocomplete="off" disabled class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">创建时间:</label>
            <div class="layui-input-block">
                <input type="text" name="createTime" lay-filter="createTime" autocomplete="off" disabled class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">更新人:</label>
            <div class="layui-input-block">
                <input type="text" name="updateBy" lay-filter="updateBy" autocomplete="off" disabled class="layui-input layui-disabled">
            </div>
        </div>
    </form>
    <div class="layui-form-item">
        <label class="layui-form-label">更新时间：</label>
        <div class="layui-input-block">
            <input type="text" name="updateTime" lay-filter="updateTime" autocomplete="off" disabled class="layui-input layui-disabled">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <input type="button" value="保存" class="layui-btn" id="btnSave" />
            <input type="button" value="关闭" class="layui-btn layui-btn-primary" id="btnClose" />
        </div>
    </div>
</div>

<script>
    layui.use(['form','tree'], function () {
        var form = layui.form;
        var tree = layui.tree;
        form.on('radio(isEmpty)', function (data){
            var value=data.elem.value;
            if (value!=2 || value==-1){
                $("#boxType").val(0).attr("disabled",true);
                $(".boxType").hide()
                $("[input]").val(0).attr("disabled",true);
                $(".boxVolumeBlock").hide()
                $("#bookNo").attr("disabled",true);
                $(".bookNo").hide()
            }else{
                $(".boxType").show()
                $(".boxVolumeBlock").show()
                $(".bookNo").show()
            }
        });
    });


    $(document).ready(function(){
        $("#btnSave").click(function () {
            saveFun();
        });
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
        initPage();
    });

    function saveFun(){
        var postData = layui.form.val('inputForm');
        var id = $.common.getURLParameter("id");

        var url = "mes/box/add";
        if($.common.isNotEmpty(id)){
            url = "mes/box/edit";
            postData.id = id;
        }

        $.operate.postJson({
            url: url,
            data: JSON.stringify(postData),
            isShowMsg:true,
            callback: function(result){
                if(result.code==200){
                    $.modal.closeDrawer(result);
                }
            }
        });
    }
    function initPage(){
        $.operate.initDictDropDown("box_type", "boxType");
        $.operate.initDictDropDown("mes_order_status", "status");
        $.operate.initDictRadio("box_empty_status", "boxEmptyBlock", "isEmpty");
        $.operate.initDictRadio("box_height", "boxHeightBlock", "boxHeight");
        $.operate.initDictRadio("box_volume", "boxVolumeBlock", "boxVolume");
        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: "mes/box/queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }
</script>
</body>
</html>