<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">单编号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_bookNo" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">计划日期：</label>
                    <div class="layui-input-inline" style="width: 190px;">
                        <input type="text" name="ge_planTime" placeholder="请选择开始时间" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline" style="width: 190px;">
                        <input type="text" name="le_planTime" placeholder="请选择结束时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">配套状态：</label>
                    <div class="layui-input-inline">
                        <select name="eq_fullStatus" id="fullStatus"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>

    <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
</div>
<script type="text/html" id="currentTableBar">
    <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除</a>
</script>

<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
<!--        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">-->
<!--            <i class="layui-icon">&#xe654;</i>入库-->
<!--        </button>-->
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon">&#xe9aa;</i>刷新
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="out">
            <i class="layui-icon">&#xe691;</i>出库
        </button>
    </div>
</script>
<!-- 操作列 -->
<script type="text/html" id="auth-state">
    <a class="layui-btn layui-btn-xs layui-icon-eye" lay-event="info"><i class="layui-icon">&#xe748;</i>明细</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><i class="layui-icon">&#xe642;</i>编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete"><i class="layui-icon">&#xe640;</i>删除</a>
</script>

<script>
    layui.use(['form', 'table'], function () {
        layui.laydate.render({
            elem: 'input[name="ge_planTime"],input[name="le_planTime"]'
            ,type: 'datetime'
        });
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;
        $.operate.initDictDropDown("order_full_status", "fullStatus");
        var tableObj = table.render({
            elem: '#currentTableId',
            url: ctx +'mes/order/list',
            method:'post',
            contentType:'application/json',
            toolbar: '#toolbarDemo',
            parseData:function(result){
                return {
                    "code": (result.code ==200 ? '0': result.code),
                    "data": result.data,
                    "count": result.total
                }
            },
            cols: [[
                {type: "radio", width: 50},
                {field: 'bookNo', width: 150, title: '单编号', sort: true},
                {field: 'status_dictText', width: 100, title: '执行状态'},
                {field: 'fullStatus_dictText',width: 100,title: '配套状态'},
                {field: 'description', width: 250, title: '描述', sort: true},
                {field: 'planTime', width: 150, title: '计划日期', sort: true},
                {field: 'createBy', width: 120, title: '创建人'},
                {field: 'createTime', width: 160, title: '创建时间'},
                {field: 'updateBy', width: 120, title: '更新人'},
                {field: 'updateTime', width: 160, title: '更新时间'},
                {templet: '#auth-state', width:215, minWidth: 150, align: 'center', title: '操作',fixed: 'right'}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            switch(obj.event){
                case 'add':
                    $.modal.openDrawer({
                        title: '入库',
                        url: "mesOrderInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload('currentTableId');
                        }
                    });
                    break;
                case 'refresh':
                    table.reload('currentTableId');
                    break;
                case 'out':
                    var checkStatus = table.checkStatus(obj.config.id);
                    var data=checkStatus.data;
                    if (data.length==0){
                        alert("请选择后再出库！");
                        return;
                    }

                    var bookNos=[];
                    for (var i=0;i<data.length;i++){
                        if (data[i].fullStatus != 1) {
                            layer.alert('还未配套完成！');
                            return;
                        }
                        bookNos.push(data[i].bookNo);
                    }
                    console.log(bookNos);
                    var bookNoJson = JSON.stringify(bookNos);
                    openSelect(bookNoJson);
            }
        });

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if  (obj.event === 'info'){
                $.modal.openDrawer({
                    title: '明细',
                    url: "boxListInfo.html?bookNo="+data.bookNo,
                    layerCallBack:function(returnVal){
                        table.reload('currentTableId');
                    }
                });
                return false;
            } else if (obj.event === 'edit') {
                $.modal.openDrawer({
                    title: '新增作业区域',
                    url: "mesOrderInfo.html?id="+data.id,
                    layerCallBack:function(returnVal){
                        table.reload('currentTableId');
                    }
                });
                return false;
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除行么', function (index) {
                    $.operate.postJson({
                        url: "mes/order/delete",
                        data: data.id,
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload('currentTableId');
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });
        function openSelect (bookNoJson){
            $.operate.postJson({
                url: "mes/order/pointSelect?type=order",
                isShowMsg:false,
                callback: function(result){
                    let data=result.data;
                    let option='';
                    for (let i = 0; i < data.length; i++) {
                        option=option+"<option value="+data[i].pointNo+">"+data[i].pointName+"</option>"
                    }
                    let content='<div class="layui-form-item">' +
                        '<label class="layui-form-label">出库工位</label>' +
                        '<div class="layui-input-block">' +
                        '<select name="range" id="range" lay-filter="range">' +
                        option +
                        '</select>' +
                        '</div>' +
                        '</div>';
                    layer.open({
                        title: '选择出库工位'
                        , btn: ['确定']
                        , content: content,
                        yes: function (index) {
                            let value=$("#range").val();
                            $.operate.postJson({
                                url: "mes/order/out?siteNo="+value,
                                data: bookNoJson,
                                isShowMsg:true,
                                callback: function(result){
                                    if(result.code==200){
                                        table.reload('currentTableId');
                                    }
                                }
                            });
                            layer.close(index);
                        }
                    })
                }
            });
        }
    });
</script>
</body>
</html>