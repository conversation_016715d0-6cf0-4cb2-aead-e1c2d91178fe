<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>辅料出库详情</title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.11.0/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <style>
        body { padding: 12px; font-size: 14px; }
        .page-title { 
            font-size: 18px; 
            font-weight: bold; 
            text-align: center; 
            margin-bottom: 20px; 
            color: #009688;
        }
        .section-title { 
            font-size: 15px; 
            font-weight: bold; 
            padding: 8px; 
            background-color: #f2f2f2; 
            border-left: 4px solid #1E9FFF;
            margin: 15px 0;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-title .title-text {
            flex: 1;
        }
        .section-title .collapse-icon {
            margin-right: 10px;
            transition: transform 0.3s;
        }
        .section-title.collapsed .collapse-icon {
            transform: rotate(-90deg);
        }
        .container-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            padding: 10px 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
            margin-bottom: 15px;
            transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
            max-height: 500px;
            overflow: hidden;
            opacity: 1;
        }
        .container-info.collapsed {
            max-height: 0;
            opacity: 0;
            margin-bottom: 0;
            padding: 0 15px;
        }
        .control-panel {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
            transition: max-height 0.3s ease-out, opacity 0.3s ease-out, margin 0.3s ease-out;
            max-height: 50px;
            overflow: hidden;
            opacity: 1;
        }
        .control-panel.collapsed {
            max-height: 0;
            opacity: 0;
            margin: 0;
        }
        .info-item { display: flex; align-items: center; }
        .info-label { 
            min-width: 90px; 
            text-align: right; 
            color: #666; 
            margin-right: 8px;
            white-space: nowrap;
        }
        .info-value { font-weight: 500; }
        .table-container {
            margin: 10px 0 25px 0;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            transition: max-height 0.3s ease-out, opacity 0.3s ease-out, margin 0.3s ease-out;
            max-height: 600px;
            overflow: hidden;
            opacity: 1;
        }
        .table-container.collapsed {
            max-height: 0;
            opacity: 0;
            margin: 0;
        }
        .capacity-select {
            min-width: 120px;
        }
        .content-section {
            margin-bottom: 30px;
        }
        .material-type-section {
            display: none;
        }
        .material-type-section.active {
            display: block;
        }
        .section-content {
            transition: all 0.3s ease-out;
        }
    </style>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <!-- 自身属性部分 -->
        <div id="selfPropertySection" class="material-type-section">
            <!-- 料箱信息区 -->
            <div class="content-section">
                <div class="section-title" data-target="selfBoxContent">
                    <span class="title-text">自身属性料箱出库拣货</span>
                    <i class="layui-icon layui-icon-down collapse-icon"></i>
                </div>
                <div id="selfBoxContent" class="section-content">
                    <div class="container-info">
                        <div class="info-item">
                            <span class="info-label">箱号:</span>
                            <span class="info-value" id="selfBoxNo">加载中...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">容器类型:</span>
                            <span class="info-value" id="selfBoxType">加载中...</span>
                        </div>
                    </div>
                    
                    <div class="control-panel">
                        <button class="layui-btn layui-btn-normal" id="btnSelfBoxRefresh">刷新</button>
                        <button class="layui-btn" id="btnSelfBoxBind">解绑入库</button>
                    </div>
                    
                    <div class="table-container">
                        <table class="layui-table" id="selfBoxTable" lay-filter="selfBoxTableFilter"></table>
                    </div>
                </div>
            </div>
            
            <!-- 料架信息区 -->
            <div class="content-section">
                <div class="section-title" data-target="selfPalletContent">
                    <span class="title-text">自身属性料架出库拣货</span>
                    <i class="layui-icon layui-icon-down collapse-icon"></i>
                </div>
                <div id="selfPalletContent" class="section-content">
                    <div class="container-info">
                        <div class="info-item">
                            <span class="info-label">料架号:</span>
                            <span class="info-value" id="selfPalletNo">加载中...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合约号:</span>
                            <span class="info-value" id="selfPalletContractNo">加载中...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">托盘容量:</span>
                            <select class="layui-select capacity-select" id="selfPalletCapacity" name="selfPalletCapacity" lay-filter="selfPalletCapacity">
                                <option value="25">25%</option>
                                <option value="50">50%</option>
                                <option value="75">75%</option>
                                <option value="100">100%</option>
                                <option value="0">0</option>
                            </select>
                        </div>
                        <div class="info-item">
                            <span class="info-label">待出库数量:</span>
                            <span class="info-value" id="selfPalletOutboundQty">加载中...</span>
                        </div>
                    </div>
                    
                    <div class="control-panel">
                        <button class="layui-btn" id="btnSelfPalletRefresh">刷新</button>
                        <button class="layui-btn" id="btnSelfPalletUnbind">解绑入库</button>
                        <button class="layui-btn" id="btnSelfPalletInboundSplit">拆零入库</button>
                    </div>
                    
                    <div class="table-container">
                        <table class="layui-table" id="selfPalletTable" lay-filter="selfPalletTableFilter"></table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 款式属性部分 -->
        <div id="stylePropertySection" class="material-type-section">
            <!-- 料箱信息区 -->
            <div class="content-section">
                <div class="section-title" data-target="styleBoxContent">
                    <span class="title-text">款式属性料箱出库拣货</span>
                    <i class="layui-icon layui-icon-down collapse-icon"></i>
                </div>
                <div id="styleBoxContent" class="section-content">
                    <div class="container-info">
                        <div class="info-item">
                            <span class="info-label">箱号:</span>
                            <span class="info-value" id="styleBoxNo">加载中...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">容器类型:</span>
                            <span class="info-value" id="styleBoxType">加载中...</span>
                        </div>
                    </div>
                    
                    <div class="control-panel">
                        <button class="layui-btn" id="btnStyleBoxRefresh">刷新</button>
                        <button class="layui-btn" id="btnStyleBoxBind">解绑入库</button>
                    </div>
                    
                    <div class="table-container">
                        <table class="layui-table" id="styleBoxTable" lay-filter="styleBoxTableFilter"></table>
                    </div>
                </div>
            </div>
            
            <!-- 料架信息区 -->
            <div class="content-section">
                <div class="section-title" data-target="stylePalletContent">
                    <span class="title-text">款式属性料架出库拣货</span>
                    <i class="layui-icon layui-icon-down collapse-icon"></i>
                </div>
                <div id="stylePalletContent" class="section-content">
                    <div class="container-info">
                        <div class="info-item">
                            <span class="info-label">料架号:</span>
                            <span class="info-value" id="stylePalletNo">加载中...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">合约号:</span>
                            <span class="info-value" id="stylePalletContractNo">加载中...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">托盘容量:</span>
                            <select class="layui-select capacity-select" id="stylePalletCapacity" name="stylePalletCapacity" lay-filter="stylePalletCapacity">
                                <option value="25">25%</option>
                                <option value="50">50%</option>
                                <option value="75">75%</option>
                                <option value="100">100%</option>
                                <option value="0">0</option>
                            </select>
                        </div>
                        <div class="info-item">
                            <span class="info-label">待出库数量:</span>
                            <span class="info-value" id="stylePalletOutboundQty">加载中...</span>
                        </div>
                    </div>
                    
                    <div class="control-panel">
                        <button class="layui-btn" id="btnStylePalletRefresh">刷新</button>
                        <button class="layui-btn" id="btnStylePalletUnbind">解绑入库</button>
                        <button class="layui-btn" id="btnStylePalletInboundSplit">拆零入库</button>
                    </div>
                    
                    <div class="table-container">
                        <table class="layui-table" id="stylePalletTable" lay-filter="stylePalletTableFilter"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 获取参数,需要根据materialType来判断是哪个页面
var answer_id = "";
var urlParams = new URLSearchParams(window.location.search);
var materialType = parseInt(urlParams.get('materialType') || '1');
if(materialType == 0){
    answer_id=JSON.parse(parent.bin_answer_id);
}else if(materialType == 1){
    answer_id=JSON.parse(parent.pallet_answer_id);
}

var apiUrl = '/auxiliary/outbound/confirmStock?materialType=' + materialType;

// 当前处理的数组索引
var currentBoxIndex = 0;
var currentRackIndex = 0;

// 存储从confirmStock接口返回的任务号
var taskOrderList = [];

// 根据materialType显示相应的区域
function showSectionByMaterialType() {
    if (materialType === 0) {
        document.getElementById('selfPropertySection').classList.add('active');
        document.getElementById('stylePropertySection').classList.remove('active');
    } else if (materialType === 1) {
        document.getElementById('selfPropertySection').classList.remove('active');
        document.getElementById('stylePropertySection').classList.add('active');
    }
}

// 折叠/展开内容区域
function toggleSection(targetId) {
    const contentEl = document.getElementById(targetId);
    if (!contentEl) return;
    
    const containerInfo = contentEl.querySelector('.container-info');
    const controlPanel = contentEl.querySelector('.control-panel');
    const tableContainer = contentEl.querySelector('.table-container');
    const sectionTitle = document.querySelector(`.section-title[data-target="${targetId}"]`);
    
    const isCollapsed = sectionTitle.classList.contains('collapsed');
    
    // 切换折叠状态
    sectionTitle.classList.toggle('collapsed');
    
    // 更新内容区域的折叠状态
    [containerInfo, controlPanel, tableContainer].forEach(el => {
        if (el) el.classList.toggle('collapsed', !isCollapsed);
    });
}

/**
 * 初始化表格
 * @param {Object} table - Layui表格对象
 */
function initializeTables(table) {
    // 创建表格配置
    const tableConfigs = {
        selfBox: {
            elem: '#selfBoxTable',
            page: false,
            limits: [10, 15, 20, 50, 100],
            limit: 15,
            cols: [[
                {field: 'id', title: 'ID', width: 60, hide: true}, // 隐藏但保留ID字段
                {field: 'gridId', title: '格号', width: 80},
                {field: 'contractNo', title: '合约号', width: 120},
                {field: 'itemNo', title: '款号', width: 120},
                {field: 'materialCode', title: '物料编码', width: 120},
                {field: 'materialName', title: '物料名称', width: 150},
                {field: 'materialModel', title: '物料规格', width: 100},
                {field: 'materialColor', title: '物料颜色', width: 100},
                {field: 'stockQuantity', title: '库存数量', width: 100},
                {field: 'inQuantity', title: '待入库数量', width: 100},
                {field: 'actualInboundQty', title: '实际出库数量', width: 120, edit: 'number'}
            ]]
        },
        selfPallet: {
            elem: '#selfPalletTable',
            contentType: 'application/json',
            page: true,
            limits: [10, 15, 20, 50, 100],
            limit: 15,
            cols: [[
                {type: "checkbox", width: 50, fixed: "left"},
                {field: 'id', title: 'ID', width: 60, hide: true}, // 隐藏但保留ID字段
                {field: 'contractNo', title: '合约号', width: 120},
                {field: 'itemNo', title: '款号', width: 120},
                {field: 'materialCode', title: '物料编码', width: 120},
                {field: 'materialName', title: '物料名称', width: 150},
                {field: 'materialModel', title: '物料规格', width: 100},
                {field: 'materialColor', title: '物料颜色', width: 100},
                {field: 'inQuantity', title: '库存数量', width: 100},
                {field: 'actualInboundQty', title: '实际出库数量', width: 120, edit:'number'}
            ]]
        },
        styleBox: {
            elem: '#styleBoxTable',
            page: true,
            limits: [10, 15, 20, 50, 100],
            limit: 15,
            cols: [[
                {field: 'id', title: 'ID', width: 60, hide: true}, // 隐藏但保留ID字段
                {field: 'gridId', title: '格号', width: 80},
                {field: 'poNo', title: 'PO号', width: 120},
                {field: 'contractNo', title: '合约号', width: 120},
                {field: 'itemNo', title: '款号', width: 120},
                {field: 'materialCode', title: '物料编码', width: 120},
                {field: 'materialName', title: '物料名称', width: 150},
                {field: 'materialColor', title: '款式颜色', width: 100},
                {field: 'materialModel', title: '物料规格', width: 100},
                {field: 'inQuantity', title: '库存数量', width: 100},
                {field: 'actualInboundQty', title: '实际出库数量', width: 120, edit:'number'}
            ]]
        },
        stylePallet: {
            elem: '#stylePalletTable',
            contentType: 'application/json',
            page: true,
            limits: [10, 15, 20, 50, 100],
            limit: 15,
            cols: [[
                {type: "checkbox", width: 50, fixed: "left"},
                {field: 'id', title: 'ID', width: 60, hide: true}, // 隐藏但保留ID字段
                {field: 'poNo', title: 'PO号', width: 120},
                {field: 'contractNo', title: '合约号', width: 120},
                {field: 'itemNo', title: '款号', width: 120},
                {field: 'materialCode', title: '物料编码', width: 120},
                {field: 'materialName', title: '物料名称', width: 150},
                {field: 'materialColor', title: '物料颜色', width: 100},
                {field: 'materialModel', title: '物料规格', width: 100},
                {field: 'inQuantity', title: '库存数量', width: 100},
                {field: 'actualInboundQty', title: '实际出库数量', width: 120, edit:'number'}
            ]]
        }
    };

    // 根据materialType初始化相应表格
    if (materialType === 0) {
        // table.render($.extend({}, tableConfigs.selfBox, {
        //     done: function(res){
        //         layui.table.on('edit(selfBoxTableFilter)', function(obj){
        //             validateEditValue(obj);
        //         });
        //     }
        // }));
        
        // table.render($.extend({}, tableConfigs.selfPallet, {
        //     done: function(res){
        //         layui.table.on('edit(selfPalletTableFilter)', function(obj){
        //             validateEditValue(obj);
        //         });
        //     }
        // }));
        table.render(tableConfigs.selfBox);
        table.render(tableConfigs.selfPallet);
        // 加载自身属性数据
        loadData(table);
    } else if (materialType ===1) {
        table.render(tableConfigs.styleBox);
        table.render(tableConfigs.stylePallet);
        // 加载款式属性数据
        loadData(table);
    }
}

/**
 * 获取当前用户信息
 * @returns {string} 当前操作人员
 */
function getCurrentUser() {
    // 尝试从多个可能的来源获取用户信息
    if (typeof parent !== 'undefined' && parent.layui && parent.layui.data && parent.layui.data('layuimini-settings')) {
        var userInfo = parent.layui.data('layuimini-settings').userInfo;
        if (userInfo && userInfo.realName) {
            return userInfo.realName;
        }
        if (userInfo && userInfo.username) {
            return userInfo.username;
        }
    }
    
    // 从sessionStorage或localStorage获取
    try {
        var userData = sessionStorage.getItem('userInfo') || localStorage.getItem('userInfo');
        if (userData) {
            var user = JSON.parse(userData);
            return user.realName || user.username || '系统用户';
        }
    } catch (e) {
        console.warn('无法从存储中获取用户信息:', e);
    }
    
    // 默认返回值
    return '系统用户';
}

/**
 * 更新容器信息显示
 * @param {Object} data - 容器数据
 * @param {string} containerType - 容器类型 ('box' 或 'pallet')
 * @param {string} propertyType - 属性类型 ('self' 或 'style')
 */
function updateContainerInfo(data, containerType, propertyType) {
    if (!data) return;
    
    const prefix = propertyType + containerType.charAt(0).toUpperCase() + containerType.slice(1);
    
    if (containerType === 'box') {
        document.getElementById(prefix + 'No').textContent = data.boxNo || data.boxNo || '--';
        document.getElementById(prefix + 'Type').textContent = data.boxType || data.boxType || '--';
    } else if (containerType === 'pallet') {
        document.getElementById(prefix + 'No').textContent = data.boxNo || data.boxNo || '--';
        document.getElementById(prefix + 'ContractNo').textContent = data.contractNo || '--';
        document.getElementById(prefix + 'OutboundQty').textContent = data.outQuantityList || '--';
    }
}

/**
 * 从响应数据中提取表格数据
 * @param {Object} res - API响应数据
 * @param {string} containerType - 容器类型 ('box' 或 'pallet')
 * @returns {Array} 表格数据数组
 */
function validateEditValue(obj) {
    var value = obj.value;
    if(isNaN(value) || value < 0) {
        layer.msg('请输入有效的正数数值');
        obj.update({ actualInboundQty: obj.data.stockQuantity });
    }
    if(Number(value) > Number(obj.data.stockQuantity)) {
        layer.msg('出库数量不能超过库存数量');
        obj.update({ actualInboundQty: obj.data.stockQuantity });
    }
}

function extractTableData(res, containerType) {
    if (!res || !res.data) return [];
    
    let items = [];
    let currentIndex = containerType === 'box' ? currentBoxIndex : currentRackIndex;
    
    if (containerType === 'box' && res.data.boxOutItemDtoList && res.data.boxOutItemDtoList.length > 0) {
        // 确保索引不超出数组范围
        if (currentIndex >= res.data.boxOutItemDtoList.length) {
            currentIndex = 0;
            if (containerType === 'box') currentBoxIndex = 0;
        }
        items = res.data.boxOutItemDtoList[currentIndex].boxItemList || [];
    } else if (containerType === 'pallet' && res.data.rackOutItemDtoList && res.data.rackOutItemDtoList.length > 0) {
        // 确保索引不超出数组范围
        if (currentIndex >= res.data.rackOutItemDtoList.length) {
            currentIndex = 0;
            if (containerType === 'pallet') currentRackIndex = 0;
        }
        items = res.data.rackOutItemDtoList[currentIndex].boxItemList || [];
    }
    
    return items;
}

/**
 * 加载数据并更新表格
 * @param {Object} table - Layui表格对象
 */
 function loadData(table) {
    // 先调用confirmStock接口确认库存和创建任务
    layer.msg('确认库存中...', {icon: 16});
    
    // 构建符合StockActionRequestDto结构的请求对象
    var requestData = {
        detailId: answer_id || [],  // 出库单ID列表
        materialType: materialType,    // 物料类型
        actionType: 'CONFIRM_STOCK',   // 操作类型
        operator: getCurrentUser(),    // 操作人
        remark: '确认库存操作'         // 备注
    };
    
    $.ajax({
        url: ctx + apiUrl,
        method: 'post',
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        success: function(res) {
            if (!res.success||res.code!=200) {
                var index = parent.layer.getFrameIndex(window.name); 
                layer.alert(res.message || '出库确认失败',function(){
                    parent.layer.close(index); // 再执行关闭
                });
                // 先得到当前 iframe 层的索引
                return false;
            }
            
            // 提取任务号信息
            extractTaskOrders(res.data);
            
            // 处理confirmStock返回的数据并更新界面
          //  processConfirmStockData(res.data, table);
            
            // 只有confirmStock成功后才继续查询详细的任务数据
            layer.msg('加载任务详情中...', {icon: 16});
            queryOutTaskData(table);
        },
        error: function(xhr, status, error) {
            console.error('confirmStock API请求失败:', error);
            layer.msg('确认库存失败: ' + (error || '未知错误'));
            // confirmStock失败时不调用queryOutTaskData
        }
    });
}

/**
 * 从confirmStock返回的数据中提取任务号
 * @param {Object} data - confirmStock返回的数据
 */
function extractTaskOrders(data) {
    taskOrderList = []; // 清空之前的任务号
    
    if (!data) return;
    
    // 从boxOutItemDtoList提取任务号
    if (data.boxOutItemDtoList && Array.isArray(data.boxOutItemDtoList)) {
        data.boxOutItemDtoList.forEach(function(item) {
            if (item.taskOrder) {
                taskOrderList.push(item.taskOrder);
            }
        });
    }
    
    // 从rackOutItemDtoList提取任务号
    if (data.rackOutItemDtoList && Array.isArray(data.rackOutItemDtoList)) {
        data.rackOutItemDtoList.forEach(function(item) {
            if (item.taskOrder) {
                taskOrderList.push(item.taskOrder);
            }
        });
    }
    
    console.log('提取到的任务号:', taskOrderList);
}

/**
 * 处理confirmStock返回的数据
 * @param {Object} data - confirmStock返回的数据
 * @param {Object} table - Layui表格对象
 */
function processConfirmStockData(data, table) {
    if (!data) {
        console.warn('confirmStock返回数据为空');
        return;
    }
    
    // 保存confirmStock原始数据到全局变量
    window.confirmStockRawData = data;
    
    // 根据materialType确定属性类型
    const propertyType = materialType === 0 ? 'self' : 'style';
    
    // 处理料箱数据
    if (data.boxOutItemDtoList && data.boxOutItemDtoList.length > 0) {
        // 确保索引不超出范围
        if (currentBoxIndex >= data.boxOutItemDtoList.length) {
            currentBoxIndex = 0;
        }
        
        const currentBoxData = data.boxOutItemDtoList[currentBoxIndex];
        updateContainerDisplay(currentBoxData, 'box', propertyType);
        
        // 更新表格数据
        if (table && currentBoxData.boxItemList) {
            table.reload(`${propertyType}BoxTable`, {
                data: currentBoxData.boxItemList
            });
        }
    }
    
    // 处理托盘数据
    if (data.rackOutItemDtoList && data.rackOutItemDtoList.length > 0) {
        // 确保索引不超出范围
        if (currentRackIndex >= data.rackOutItemDtoList.length) {
            currentRackIndex = 0;
        }
        
        const currentPalletData = data.rackOutItemDtoList[currentRackIndex];
        updateContainerDisplay(currentPalletData, 'pallet', propertyType);
        
        // 更新表格数据
        if (table && currentPalletData.boxItemList) {
            table.reload(`${propertyType}PalletTable`, {
                data: currentPalletData.boxItemList
            });
        }
    }
    
    console.log('confirmStock数据处理完成');
}

/**
 * 处理confirmStock返回的数据
 * @param {Object} data - confirmStock返回的数据
 * @param {Object} table - Layui表格对象
 */
function processConfirmStockData(data, table) {
    if (!data) {
        console.warn('confirmStock返回数据为空');
        return;
    }
    
    // 保存confirmStock原始数据到全局变量
    window.confirmStockRawData = data;
    
    // 根据materialType确定属性类型
    const propertyType = materialType === 0 ? 'self' : 'style';
    
    // 处理料箱数据
    if (data.boxOutItemDtoList && data.boxOutItemDtoList.length > 0) {
        // 确保索引不超出范围
        if (currentBoxIndex >= data.boxOutItemDtoList.length) {
            currentBoxIndex = 0;
        }
        
        const currentBoxData = data.boxOutItemDtoList[currentBoxIndex];
        updateContainerDisplay(currentBoxData, 'box', propertyType);
        
        // 更新表格数据
        if (table && currentBoxData.boxItemList) {
            table.reload(`${propertyType}BoxTable`, {
                data: currentBoxData.boxItemList
            });
        }
    }
    
    // 处理托盘数据
    if (data.rackOutItemDtoList && data.rackOutItemDtoList.length > 0) {
        // 确保索引不超出范围
        if (currentRackIndex >= data.rackOutItemDtoList.length) {
            currentRackIndex = 0;
        }
        
        const currentPalletData = data.rackOutItemDtoList[currentRackIndex];
        updateContainerDisplay(currentPalletData, 'pallet', propertyType);
        
        // 更新表格数据
        if (table && currentPalletData.boxItemList) {
            table.reload(`${propertyType}PalletTable`, {
                data: currentPalletData.boxItemList
            });
        }
    }
    
    console.log('confirmStock数据处理完成');
}

/**
 * 查询出库任务数据
 * @param {Object} table - Layui表格对象
 */
function queryOutTaskData(table) {
    // 构建查询参数
    var searchParams = {};
    
    // 如果有任务号，则加入查询参数
    if (taskOrderList.length > 0) {
        searchParams.taskOrder = taskOrderList[0];
    }
    // 如果没有任务号，可以根据其他条件查询（比如物料类型等）
    else {
        // 可以添加其他查询条件，比如物料属性
        searchParams.materialType = materialType;
    }
    
    $.ajax({
        url: ctx + '/auxiliary/outbound/queryOutTask',
        method: 'post',
        contentType: 'application/json',
        data: JSON.stringify({
            searchParams: searchParams,
            page: 1,
            limit: 100
        }),
        success: function(res) {
            if (!res.success) {
                layer.msg(res.message || '查询任务失败');
                return;
            }
            
            // 处理queryOutTask返回的数据
            processQueryOutTaskData(res.data, table);
        },
        error: function(xhr, status, error) {
            console.error('queryOutTask API请求失败:', error);
            layer.msg('查询任务失败: ' + (error || '未知错误'));
        }
    });
}

/**
 * 处理queryOutTask返回的数据
 * @param {Object} data - queryOutTask返回的数据
 * @param {Object} table - Layui表格对象
 */
function processQueryOutTaskData(data, table) {
    // 保存原始数据到全局变量，用于获取boundId
    window.queryOutTaskRawData = data;
    
    if (!data || !data.boxProperty) {
        // 清空显示
        updateContainerDisplay(null, 'box');
        updateContainerDisplay(null, 'pallet');
        if (table) {
            reloadTables(table, [], []);
        }
        return;
    }
    
    // 根据materialType确定属性类型
    const propertyType = materialType === 0 ? 'self' : 'style';
    
    // 分离料箱和托盘数据
    const boxData = data.boxProperty.filter(item => item.containerType === 1 || item.boxType === 1);
    const palletData = data.boxProperty.filter(item => item.containerType === 2 || item.boxType === 2);
    
    // 处理料箱数据
    if (boxData.length > 0) {
        // 确保索引不超出范围
        if (currentBoxIndex >= boxData.length) {
            currentBoxIndex = 0;
        }
        
        const currentBoxData = boxData[currentBoxIndex];
        updateContainerDisplay(currentBoxData, 'box', propertyType);
        
        // 保存当前料箱属性数据到全局变量，用于获取boundId
        window.currentBoxPropertyData = currentBoxData;
        
        // 重载料箱表格
        if (table) {
            table.reload(`${propertyType}BoxTable`, {
                data: currentBoxData.boxItemList || []
            });
        }
    } else {
        updateContainerDisplay(null, 'box', propertyType);
        // 清空料箱属性数据
        window.currentBoxPropertyData = null;
        if (table) {
            table.reload(`${propertyType}BoxTable`, {
                data: []
            });
        }
    }
    
    // 处理托盘数据
    if (palletData.length > 0) {
        // 确保索引不超出范围
        if (currentRackIndex >= palletData.length) {
            currentRackIndex = 0;
        }
        
        const currentPalletData = palletData[currentRackIndex];
        updateContainerDisplay(currentPalletData, 'pallet', propertyType);
        
        // 保存当前托盘属性数据到全局变量，用于获取boundId
        window.currentPalletPropertyData = currentPalletData;
        
        // 重载托盘表格
        if (table) {
            table.reload(`${propertyType}PalletTable`, {
                data: currentPalletData.boxItemList || []
            });
        }
    } else {
        updateContainerDisplay(null, 'pallet', propertyType);
        // 清空托盘属性数据
        window.currentPalletPropertyData = null;
        if (table) {
            table.reload(`${propertyType}PalletTable`, {
                data: []
            });
        }
    }
    
    // 如果有任务状态信息，可以显示统计信息
    if (data.taskStatusSummary) {
        console.log('任务状态统计:', data.taskStatusSummary);
    }
    
    if (data.statusMessage) {
        console.log('状态消息:', data.statusMessage);
    }
}

/**
 * 更新容器信息显示
 * @param {Object} containerData - 容器数据
 * @param {string} containerType - 容器类型 ('box' 或 'pallet')
 * @param {string} propertyType - 属性类型 ('self' 或 'style')
 */
function updateContainerDisplay(containerData, containerType, propertyType) {
    if (!containerData) {
        // 清空显示
        if (containerType === 'box') {
            document.getElementById(`${propertyType}BoxNo`).textContent = '--';
            document.getElementById(`${propertyType}BoxType`).textContent = '--';
        } else if (containerType === 'pallet') {
            document.getElementById(`${propertyType}PalletNo`).textContent = '--';
            document.getElementById(`${propertyType}PalletContractNo`).textContent = '--';
            document.getElementById(`${propertyType}PalletOutboundQty`).textContent = '--';
        }
        return;
    }
    
    if (containerType === 'box') {
        document.getElementById(`${propertyType}BoxNo`).textContent = containerData.boxNo || '--';
        document.getElementById(`${propertyType}BoxType`).textContent = getContainerTypeDesc(containerData.containerType) || '--';
    } else if (containerType === 'pallet') {
        document.getElementById(`${propertyType}PalletNo`).textContent = containerData.boxNo || '--';
        
        // 从料箱内物料获取合约号
        const contractNo = containerData.boxItemList && containerData.boxItemList.length > 0 
            ? containerData.boxItemList[0].contractNo 
            : '--';
        document.getElementById(`${propertyType}PalletContractNo`).textContent = contractNo;
        
        // 计算总的出库数量
        const totalOutQuantity = containerData.boxItemList 
            ? containerData.boxItemList.reduce((sum, item) => sum + (item.inQuantity || 0), 0)
            : 0;
        document.getElementById(`${propertyType}PalletOutboundQty`).textContent = totalOutQuantity;
    }
}

/**
 * 获取容器类型描述
 * @param {number} containerType - 容器类型
 * @returns {string} 容器类型描述
 */
function getContainerTypeDesc(containerType) {
    switch(containerType) {
        case 1: return '料箱';
        case 2: return '托盘';
        default: return '未知';
    }
}

/**
 * 重载表格（兼容方法）
 * @param {Object} table - Layui表格对象
 * @param {Array} boxItems - 料箱数据
 * @param {Array} palletItems - 托盘数据
 */
function reloadTables(table, boxItems, palletItems) {
    const propertyType = materialType === 0 ? 'self' : 'style';
    
    if (table) {
        table.reload(`${propertyType}BoxTable`, {
            data: boxItems || []
        });
        
        table.reload(`${propertyType}PalletTable`, {
            data: palletItems || []
        });
    }
}

/**
 * 绑定页面按钮事件
 * @param {Object} $ - jQuery对象
 * @param {Object} table - Layui表格对象
 */
 function bindButtonEvents($, table) {
    const propertyType = materialType === 0 ? 'self' : 'style';
    const capitalizedType = propertyType.charAt(0).toUpperCase() + propertyType.slice(1);
    
    // 料箱解绑入库按钮事件
    $(`#btn${capitalizedType}BoxBind`).on('click', function() {
        handleUnbindOutbound();
    });
    
    
    // 料架解绑入库功能
    $(`#btn${capitalizedType}PalletUnbind`).on('click', function() {
        layer.msg('解绑入库功能开发中...');
        // TODO: 实现托盘解绑入库逻辑
    });
    
    //托盘拆零入库功能
    $(`#btn${capitalizedType}PalletInboundSplit`).on('click', function() {
        layer.msg('拆零入库功能开发中...');
        // TODO: 实现拆零入库逻辑
    });

    //料箱刷新功能 - 重新获取最新的任务状态
    $(`#btn${capitalizedType}BoxRefresh`).on('click', function() {
        refreshTaskData(table);
    });
    
    //托盘刷新功能 - 重新获取最新的任务状态
    $(`#btn${capitalizedType}PalletRefresh`).on('click', function() {
        refreshTaskData(table);
    });


}

/**
 * 刷新任务数据 - 重新查询任务状态
 * @param {Object} table - Layui表格对象
 */
function refreshTaskData(table) {
    layer.msg('刷新中...', {icon: 16});
    
    // 直接查询最新的任务状态
    queryOutTaskData(table);
}

/**
 * 处理解绑入库操作
 */
function handleUnbindOutbound() {
    // 检查是否有出库单ID
    if (!answer_id || answer_id.length === 0) {
        layer.msg('未找到出库单信息，无法执行解绑操作');
        return;
    }
    
    // 验证解绑前的数据准备情况
    if (!validateBeforeUnbind()) {
        return;
    }
    
    // 确认对话框
    layer.confirm('确定要解绑并确认入库吗？解绑后将完成剩余物料入库流程。', {
        icon: 3,
        title: '确认解绑'
    }, function(index) {
        // 关闭确认框
        layer.close(index);
        
        // 显示加载提示
        var loadingIndex = layer.msg('正在解绑入库中...', {
            icon: 16,
            time: 0 // 不自动关闭
        });
        
        // 收集完整的出库数据
        var outboundData = collectActualOutQuantities();
        
        // 从当前显示的数据中获取出库单ID
        var outBoundIds = collectOutBoundIds();
        
        // 构建RemainInboundDto对象
        var remainInboundRequest = {
            outBoundId: outBoundIds,
            detailQuantities: outboundData.detailQuantities,
            inboundType: 1, // 1-正常入库
            remark: '前端解绑操作触发的剩余物料出库回库处理',
            operator: getCurrentUser()
        };
        
        // 调用解绑接口
        $.ajax({
            url: ctx + '/auxiliary/outbound/remainingInbound',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(remainInboundRequest),
            success: function(res) {
                // 关闭加载提示
                layer.close(loadingIndex);
                
                if (res.success) {
                    layer.msg(res.message || '解绑成功，入库已确认', {
                        icon: 1,
                        time: 2000
                    }, function() {
                        // 解绑成功后，查询是否还有其他料箱需要处理
                        checkRemainingTasks();
                    });
                } else {
                    layer.msg(res.message || '解绑失败', {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                // 关闭加载提示
                layer.close(loadingIndex);
                
                console.error('解绑接口调用失败:', error);
                var errorMsg = '解绑操作失败';
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        var errorResponse = JSON.parse(xhr.responseText);
                        errorMsg = errorResponse.message || errorMsg;
                    } catch (e) {
                        errorMsg = '解绑操作失败: ' + (error || '未知错误');
                    }
                }
                
                layer.msg(errorMsg, {icon: 2});
            }
        });
    });
}

/**
 * 收集实际出库数量信息
 * @returns {Object} 包含明细数量映射的对象
 */
function collectActualOutQuantities() {
    var detailQuantities = {};
    var propertyType = materialType === 0 ? 'self' : 'style';
    
    try {
        // 从料箱表格收集数据
        var boxTableData = layui.table.cache[propertyType + 'BoxTable'] || [];
        boxTableData.forEach(function(item) {
            // 明细数量映射 - 包括实际出库数量为0的记录
            if (item.id) {
                var actualQty = item.actualInboundQty ? parseInt(item.actualInboundQty) : 0;
                detailQuantities[item.id] = actualQty;
            }
        });
        
        // 从托盘表格收集数据
        var palletTableData = layui.table.cache[propertyType + 'PalletTable'] || [];
        palletTableData.forEach(function(item) {
            // 明细数量映射 - 包括实际出库数量为0的记录
            if (item.id) {
                var actualQty = item.actualInboundQty ? parseInt(item.actualInboundQty) : 0;
                detailQuantities[item.id] = actualQty;
            }
        });
        
        console.log('收集到的明细出库数量信息:', {
            detailQuantities: detailQuantities
        });
        
    } catch (e) {
        console.warn('收集实际出库数量时出现错误:', e);
        return {
            detailQuantities: {}
        };
    }
    
    return {
        detailQuantities: detailQuantities
    };
}

/**
 * 生成物料唯一键
 * @param {Object} item - 物料项数据
 * @returns {string} 物料唯一键 格式: 物料编码_合约号_款号_型号_颜色
 */
function generateMaterialKey(item) {
    var materialCode = item.materialCode || '';
    var contractNo = item.contractNo || '';
    var itemNo = item.itemNo || '';
    var materialModel = item.materialModel || '';
    var materialColor = item.materialColor || '';
    return materialCode + '_' + contractNo + '_' + itemNo + '_' + materialModel + '_' + materialColor;
}

/**
 * 收集当前显示数据中的出库单ID
 * @returns {Array} 出库单ID列表
 */
function collectOutBoundIds() {
    var boundIds = [];
    
    try {
        // 从全局的容器属性数据中获取boundId
        // 这些数据在processQueryOutTaskData中处理并存储
        if (window.currentBoxPropertyData && window.currentBoxPropertyData.boundId) {
            boundIds.push(window.currentBoxPropertyData.boundId);
        }
        if (window.currentPalletPropertyData && window.currentPalletPropertyData.boundId) {
            boundIds.push(window.currentPalletPropertyData.boundId);
        }
        
        // 如果容器数据中没有boundId，尝试从原始数据中获取
        if (boundIds.length === 0 && window.queryOutTaskRawData && window.queryOutTaskRawData.boxProperty) {
            window.queryOutTaskRawData.boxProperty.forEach(function(item) {
                if (item.boundId) {
                    boundIds.push(item.boundId);
                }
            });
        }
        
        // 去重
        boundIds = [...new Set(boundIds)];
        
        console.log('收集到的出库单ID:', boundIds);
        
    } catch (e) {
        console.warn('收集出库单ID时出现错误:', e);
        // 如果出现错误，使用原来的answer_id作为fallback
        return answer_id || [];
    }
    
    return boundIds.length > 0 ? boundIds : (answer_id || []);
}

/**
 * 验证解绑前的数据准备情况
 * @returns {boolean} 是否可以进行解绑操作
 */
function validateBeforeUnbind() {
    var propertyType = materialType === 0 ? 'self' : 'style';
    var boxTableData = layui.table.cache[propertyType + 'BoxTable'] || [];
    var palletTableData = layui.table.cache[propertyType + 'PalletTable'] || [];
    
    var hasActualQuantity = false;
    var errorMessages = [];
    
    // 验证料箱数据
    boxTableData.forEach(function(item, index) {
        if (item.actualInboundQty) {
            var actualQty = item.actualInboundQty;
            var stockQty = parseInt(item.stockQuantity || item.inQuantity || 0);
            
            // 验证数量格式
            if (isNaN(actualQty) || actualQty === '' || actualQty === null) {
                errorMessages.push(`料箱第${index + 1}行：实际出库数量格式不正确`);
            } else {
                actualQty = parseInt(actualQty);
                
                // 验证是否为负数
                if (actualQty < 0) {
                    errorMessages.push(`料箱第${index + 1}行：实际出库数量不能为负数`);
                }
                // 验证是否超过库存
                else if (actualQty > stockQty) {
                    errorMessages.push(`料箱第${index + 1}行：实际出库数量(${actualQty})不能超过库存数量(${stockQty})`);
                }
                // 有效的出库数量
                else if (actualQty > 0) {
                    hasActualQuantity = true;
                }
            }
        }
    });
    
    // 验证托盘数据
    palletTableData.forEach(function(item, index) {
        if (item.actualInboundQty) {
            var actualQty = item.actualInboundQty;
            var stockQty = parseInt(item.stockQuantity || item.inQuantity || 0);
            
            // 验证数量格式
            if (isNaN(actualQty) || actualQty === '' || actualQty === null) {
                errorMessages.push(`托盘第${index + 1}行：实际出库数量格式不正确`);
            } else {
                actualQty = parseInt(actualQty);
                
                // 验证是否为负数
                if (actualQty < 0) {
                    errorMessages.push(`托盘第${index + 1}行：实际出库数量不能为负数`);
                }
                // 验证是否超过库存
                else if (actualQty > stockQty) {
                    errorMessages.push(`托盘第${index + 1}行：实际出库数量(${actualQty})不能超过库存数量(${stockQty})`);
                }
                // 有效的出库数量
                else if (actualQty > 0) {
                    hasActualQuantity = true;
                }
            }
        }
    });
    
    // 如果有验证错误，显示错误信息
    if (errorMessages.length > 0) {
        layer.msg('数据验证失败：<br>' + errorMessages.join('<br>'), {
            icon: 2,
            time: 8000,
            area: ['500px', 'auto']
        });
        return false;
    }
    
    // 检查是否有有效的出库数量
    if (!hasActualQuantity) {
        layer.msg('请填写有效的实际出库数量后再进行解绑操作', {icon: 0});
        return false;
    }
    
    return true;
}

// 主初始化函数
layui.use(['form', 'table'], function () {
    var $ = layui.jquery,
        form = layui.form,
        table = layui.table;
    
    // 初始显示相应区域
    showSectionByMaterialType();
    
    // 绑定折叠/展开事件
    $('.section-title').on('click', function() {
        var targetId = $(this).data('target');
        toggleSection(targetId);
    });
    
    // 初始化表格和数据
    initializeTables(table);
    
    // 绑定按钮事件
    bindButtonEvents($, table);
    
    // 监听表单选择器变化
    form.on('select(selfPalletCapacity)', function(data) {
        console.log('自身属性托盘容量变更为: ' + data.value);
        // 这里可以添加容量变更的处理逻辑
    });
    
    form.on('select(stylePalletCapacity)', function(data) {
        console.log('款式属性托盘容量变更为: ' + data.value);
        // 这里可以添加容量变更的处理逻辑
    });
});

/**
 * 检查是否还有其他料箱需要处理
 * 如果有，则刷新当前页面数据继续处理
 * 如果没有，则关闭弹窗并刷新父页面
 */
function checkRemainingTasks() {
    // 构建查询参数
    var searchParams = {};
    
    // 如果有任务号，则加入查询参数
    if (taskOrderList.length > 0) {
        searchParams.taskOrder = taskOrderList[0];
    } else {
        // 可以添加其他查询条件，比如物料属性
        searchParams.materialType = materialType;
    }
    
    $.ajax({
        url: ctx + '/auxiliary/outbound/queryOutTask',
        method: 'post',
        contentType: 'application/json',
        data: JSON.stringify({
            searchParams: searchParams,
            page: 1,
            limit: 100
        }),
        success: function(res) {
            if (!res.success) {
                layer.msg(res.message || '查询任务失败');
                return;
            }
            
            // 检查是否还有待处理的料箱
            var hasRemainingTasks = false;
            if (res.data && res.data.boxProperty && res.data.boxProperty.length > 0) {
                // 检查是否有未处理完的料箱
                res.data.boxProperty.forEach(function(container) {
                    if (container.boxItemList && container.boxItemList.length > 0) {
                        // 检查容器中是否还有物料需要处理
                        var hasUnprocessedItems = container.boxItemList.some(function(item) {
                            return item.inQuantity > 0; // 如果还有入库数量，说明需要处理
                        });
                        if (hasUnprocessedItems) {
                            hasRemainingTasks = true;
                        }
                    }
                });
            }
            
            if (hasRemainingTasks) {
                // 还有其他料箱需要处理，刷新当前页面数据
                layui.use(['table'], function() {
                    var table = layui.table;
                    processQueryOutTaskData(res.data, table);
                });
                layer.msg('当前料箱处理完成，请继续处理剩余料箱', {icon: 1});
            } else {
                // 没有其他料箱需要处理，关闭弹窗并刷新父页面
                layer.msg('所有料箱处理完成', {
                    icon: 1,
                    time: 1500
                }, function() {
                    var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                    parent.layer.close(index); //再执行关闭
                    // 刷新父页面的表格
                    if(parent.aotuReload) {
                       parent.aotuReload();
                    } else if (parent.layui && parent.layui.table) {
                        // 尝试一个通用的刷新父页面表格的方法
                         try{
                            parent.layui.table.reload('auxiliaryOutboundTableId',{page:{curr:1}});
                         }catch(e){
                            console.warn("尝试刷新父页面表格失败",e);
                         }
                    }
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('查询剩余任务失败:', error);
            layer.msg('查询剩余任务失败，请手动刷新页面检查');
        }
    });
}

</script>
</body>
</html>