<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.11.0/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>

</head>
<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <div class="layui-form-item">
            <form class="layui-form" action="" lay-filter="palletInputForm">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">合约号：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="contractNo" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">出库单号：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="outStoreNumber" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit
                                lay-filter="pallet-search-btn"><i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary" lay-reset><i
                                class="layui-icon layui-icon-refresh-1"></i>重置
                        </button>
                    </div>
                </div>
            </form>
            </div>
        </div>
            <table class="layui-table" id="palletTableId" lay-filter="palletTableFilter"></table>
        
       
    </div>
        <script type="text/html" id="palletToolbarDemo">
            <div class="layui-btn-container">
              <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal" lay-event="auxiliaryOutbound">
                <i class="layui-icon"></i>出库
              </button>
              <!-- <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal" lay-event="virtualOutbound">
                <i class="layui-icon"></i>虚拟出库
              </button> -->
            </div>
          </script>
        
        <script>
            var pallet_answer_id;//全局变量
            
            // 页面卸载时清理
            $(window).on('beforeunload', function() {
                if (window.layui && window.layui.table) {
                    // 清理表格实例
                    try {
                        window.layui.table.cache = window.layui.table.cache || {};
                        delete window.layui.table.cache['palletTableId'];
                    } catch(e) {
                        console.warn('清理表格缓存失败:', e);
                    }
                }
            });
            
            layui.use(['form', 'table'], function () {
                var $ = layui.jquery,
                    form = layui.form,
                    table = layui.table;
        
                var tableObj = table.render({
                    elem: '#palletTableId',
                    url: ctx + '/auxiliary/outbound/query',
                    method: 'post',
                    contentType: 'application/json',
                    dataType : "json",
                    loading: true, // 显示加载状态
                    request: {
                        pageName: 'page', // 页码的参数名称，默认：page
                        limitName: 'size' // 每页数据量的参数名，默认：limit
                    },
                    toolbar: '#palletToolbarDemo',
                    defaultToolbar: ['filter'],
                    cols: [[
                        {type: "checkbox", width: 50, fixed: "left"},
                        {field: 'id', width: 80, title: 'ID', sort: true, hide: true},
                        {field: 'outStoreNumber', width: 150, title: '出库单号', sort: true},
                        {field: 'contractNo', width: 150, title: '合约号', sort: true},
                        {field: 'outType', width: 120, title: '出库类型', sort: true, templet: function(d){
                            if(d.outType == '0'){
                                return '领料出库';
                            }else if(d.outType == '2'){
                                return '调拨';
                            }else if(d.outType == '1'){
                                return '采购退货';
                            }else{
                                return '';
                            }
                        }},
                        {field: 'itemNo', width: 100, title: '款号', sort: true},
                        {field: 'poNumber', width: 100, title: 'PO号', sort: true},
                        {field: 'materialCode', width: 150, title: '物料编码', sort: true},
                        {field: 'materialName', width: 200, title: '物料名称'},
                        {field: 'materialModel', width: 100, title: '物料规格'},
                        {field: 'materialColor', width: 100, title: '物料颜色'},
                        {field: 'quantity', width: 100, title: '出库数量', sort: true},
                        {field: 'materialUnit', width: 80, title: '单位'},
                        {field: 'materialType', width: 100, title: '物料属性', sort: true, templet: function(d){
                            if(d.materialType == '0'){
                                return '自身属性';
                            }else if(d.materialType == '1'){
                                return '款式属性';
                            }else{
                                return '';
                            }
                        }},
                        {field: 'createBy', width: 120, title: '创建人', hide: true},
                        {field: 'createTime', width: 160, title: '创建时间', hide: true},
                        {field: 'updateBy', width: 120, title: '更新人', hide: true},
                        {field: 'updateTime', width: 160, title: '更新时间', hide: true}
                    ]],
                    limits: [10, 15, 20, 25, 50, 100],
                    limit: 10,
                    page: true,
                    where: {
    searchParams: Object.assign({}, form.val('palletInputForm'), {materialType: 1})
},
                    text: {
            none: '暂无相关数据'
        }, parseData: function (result) {
            // 检查响应是否成功
            if (result.success && result.data) {
                return {
                    "code": 0, // layui表格成功状态码
                    "data": result.data.records || [],
                    "count": result.data.total || 0
                };
            } else {
                // 请求失败或数据格式错误
                return {
                    "code": result.code || 500,
                    "msg": result.msg || "数据加载失败",
                    "data": [],
                    "count": 0
                };
            }
        },
        
                });
                
                // 监听表格数据加载完成
                table.on('done(palletTableFilter)', function(res){
                    console.log('托盘表格数据加载完成:', res);
                    if (res.code !== 0) {
                        layer.msg('数据加载失败: ' + (res.msg || '未知错误'));
                    }
                });
        
                // 监听搜索操作
                form.on('submit(pallet-search-btn)', function (data) {
                    //执行搜索重载
                    table.reload('palletTableId', {
                        page: {
                            curr: 1
                        },
                        where: {
                            searchParams: Object.assign({}, data.field, {materialType:1})
                        }
                    }, 'data');
        
                    return false;
                });
        
                /**
                 * toolbar监听事件
                 */
                table.on('toolbar(palletTableFilter)', function (obj) {
                    if (obj.event === 'refresh') {
                        table.reload('palletTableId');
                    }
                    if (obj.event === 'auxiliaryOutbound') {
                        auxiliaryOutbound();
                    }
                    if (obj.event === 'virtualOutbound') {
                        virtualOutbound();
                    }
                });
        
             
            });
        
               // 辅料出库按钮点击事件
               function auxiliaryOutbound(){
        
                   //获取勾选数据，判断是否有勾选数据
                   var checkStatus = layui.table.checkStatus('palletTableId');
                   var checkData = checkStatus.data;
                   if(checkData.length == 0){
                    layer.msg('请选择要出库的辅料');
                    return;
                   }
        
                   // 将选中的数据映射为后端需要的 DTO 格式
                   var preboxData = [];
                   checkData.forEach(function(item) {
                    if (preboxData.indexOf(item.detailId) === -1) {
                        preboxData.push(item.detailId);
                    }
                   });
        
                   //将勾选的数据转换为json字符串，并调用后端预装箱接口
                   // var checkDataJson = JSON.stringify(checkData); // 旧代码
                   var preboxDataJson = JSON.stringify(preboxData); // 映射后的数据
                   pallet_answer_id = preboxDataJson;
                   //加载Loading
                   layer.msg('加载中...', {icon: 16});
                   $.ajax({
                    url: ctx + '/auxiliary/outbound/verifyStock',
                    type: 'post',
                    contentType: 'application/json', // 添加 contentType
                    data: preboxDataJson, // 发送映射后的 JSON 字符串
                    success: function(res) {
                         // 处理成功响应，例如根据后端的返回决定是否打开弹窗
                         if (res.code == 200) { // 假设后端成功返回 code 200
                           if (res.data.insufficientStockList.length > 0) {
                            var needManualConfirm = res.data.needManualConfirm;
                            var insufficientStockList = res.data.insufficientStockList;
                               // 构建库存信息HTML
                               var insufficientStockHtml = '';
                            insufficientStockList.forEach(function(item) {
                              insufficientStockHtml += '<div class="stock-item">' + item.message + '</div>';
                            });
                            
                            // 创建优化的弹窗内容
                            var dialogHtml = `
                              <div class="stock-alert">
                                <div class="alert-title">
                                  <i class="layui-icon layui-icon-tips"></i>
                                  <span>库存不足提示</span>
                                </div>
                                
                                <div class="stock-info">
                                  ${insufficientStockHtml}
                                </div>
                                
                                ${needManualConfirm ? 
                                  `<div class="hint-text">
                                    库存数量不足，您可以选择继续出库或取消操作。
                                  </div>
                                  
                                  <div class="alert-footer">
                                    <button type="button" class="layui-btn layui-btn-normal" id="insufficient-confirm-btn">继续出库</button>
                                    <button type="button" class="layui-btn layui-btn-primary" id="insufficient-cancel-btn">取消</button>
                                  </div>` : ''
                                }
                              </div>
                            `;
                            // 添加自定义样式
                            var customStyle = `
                              <style>
                                .stock-alert {
                                  padding: 30px;
                                }
                                .alert-title {
                                  font-size: 16px;
                                  font-weight: 500;
                                  color: #333;
                                  margin-bottom: 20px;
                                  display: flex;
                                  align-items: center;
                                }
                                .alert-title i {
                                  font-size: 22px;
                                  color: #FF5722;
                                  margin-right: 10px;
                                }
                                .stock-info {
                                  padding: 15px 20px;
                                  background-color: #f8f8f8;
                                  border-left: 5px solid #FFB800;
                                  margin-bottom: 30px;
                                  line-height: 1.8;
                                  color: #666;
                                  border-radius: 0 2px 2px 0;
                                }
                                .stock-item {
                                  margin-bottom: 8px;
                                }
                                .stock-item:last-child {
                                  margin-bottom: 0;
                                }
                                .hint-text {
                                  color: #333;
                                  font-size: 15px;
                                  margin-bottom: 20px;
                                }
                                .alert-footer {
                                  text-align: right;
                                }
                              </style>
                            `;
                            layer.open({
                              type: 1,
                              area: ['500px', 'auto'],
                              title: '出库信息提示',
                              shade: 0.6,
                              shadeClose: false,
                              maxmin: true,
                              anim: 0,
                              content: customStyle + dialogHtml,
                              success: function(layero, index) {
                                if (needManualConfirm) {
                                  // 按钮事件绑定
                                  $(layero).find('#insufficient-confirm-btn').on('click', function() {
                                    layer.close(index);
                                    openAuxiliaryOut();
                                  });
                                  $(layero).find('#insufficient-cancel-btn').on('click', function() {
                                    layer.close(index);
                                  });
                                }
                              }
                            });
                          }
                           else {
                            if (res.data.availableStockList.length > 0) {
                                openAuxiliaryOut();
                            }else{
                                layer.msg('未找到物料库存信息');
                            }
                           }
                        } else {
                            layer.msg(res.msg || '出库预处理请求失败');
                         }
                    },
                    error: function(xhr, status, error) {
                        // 处理错误情况
                        layer.msg('请求出库预处理接口失败: ' + error);
                        console.error("Outbound prebox request failed:", status, error);
                    }
                   });
        
                };
               function openAuxiliaryOut(){ // 打开辅料出库预处理页面
                layer.open({
                                type: 2, // 类型改为 2，表示是 iframe 层
                                area: ['100%', '100%'], // 调整弹出层宽高，使用百分比可以自适应
                                title: '辅料出库处理', // 设置一个更合适的标题
                                shade: 0.6,
                                shadeClose: false, // iframe 层建议设置为 false，避免误触关闭
                                maxmin: true,
                                anim: 0,
                                contentType: 'application/json',
                                dataType : "json",
                                
                                // content 直接设置为目标页面的 URL
                                content: 'auxiliaryOutboundDetail.html?materialType=1', // 出库预处理页面
                                // 可选：可以添加 success 回调，在 iframe 加载完毕后执行操作
                                success: function(layero, index){
                                     console.log('辅料出库处理页面加载成功');
                                   
                                }
                            });
               }
        </script>
</body>
</html>