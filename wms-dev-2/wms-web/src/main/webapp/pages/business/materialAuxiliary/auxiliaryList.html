<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.11.0/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">物料编码：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_materialCode" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">物料名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_materialName" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">是否入智能仓：</label>
                    <div class="layui-input-inline">
                        <select type="text" name="eq_isStore" id="isStore" autocomplete="off"
                                class="layui-input">
                            <option value="">请选择</option>
                            <option value="1">是</option>
                            <option value="0">否</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">物料属性：</label>
                        <div class="layui-input-inline">
                            <select type="text" name="eq_materialType" id="materialType" autocomplete="off"
                                    class="layui-input">
                                <option value="">请选择</option>
                                <option value="0">自身属性</option>
                                <option value="1">款式属性</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit
                                lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary" lay-reset><i
                                class="layui-icon layui-icon-refresh-1"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
</div>


<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batchCapacity">
            <i class="layui-icon">&#xe614;</i>批量设置容积
        </button>
        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batchType">
            <i class="layui-icon">&#xe614;</i>批量设置物料属性
        </button>
        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>
<!-- 操作列 -->
<script type="text/html" id="auth-state">
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><i class="layui-icon">&#xe642;</i>编辑</a>
</script>

<script>
    layui.use(['form', 'table'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        var tableObj = table.render({
            elem: '#currentTableId',
            url: ctx + '/auxiliary/info/page',
            method: 'post',
            contentType: 'application/json',
            toolbar: '#toolbarDemo',
            parseData: function (result) {
                return {
                    "code": (result.code == 200 ? '0' : result.code),
                    "data": result.data,
                    "count": result.total
                }
            },
            cols: [[
                {type: "checkbox", width: 50},
                {field:"stockBatchId", title:"入库批次号", width: 150, hide: true},
                {field:"stockInNo", width: 150, title: '入库单单号', hide: true},
                {field:"stockInId", width: 150, title: '入库单id',hide: true},
                {field: 'materialName', width: 150, title: '物料名称', sort: true},
                {field: 'materialCode', width: 150, title: '物料编码', sort: true},
                {field: 'category', width: 120, title: '分类', sort: true},
                {field: 'unit', width: 100, title: '单位', sort: true},
                {field: 'capacity', width: 120, title: '容积(箱容量)', sort: true},
                {field: 'priorityContainerType', width: 150, title: '优先料箱类型', sort: true},
                {field: 'materialType', width: 150, title: '物料属性', sort: true,templet: function(d){
                    if(d.materialType === 0){
                        return '自身属性';
                    }else if(d.materialType === 1){
                        return '款式属性';
                    }else{
                        return '';
                    }
                }},
                {field: 'isStore', width: 120, title: '是否入智能仓', templet: function(d){
                    return d.isStore === 1 ? '是' : '否';
                }},
                {field: 'remark', width: 200, title: '备注'},
                {field: 'createBy', width: 120, title: '创建人',hide: true},
                {field: 'createTime', width: 160, title: '创建时间',hide: true},
                {field: 'updateBy', width: 120, title: '更新人',hide: true},
                {field: 'updateTime', width: 160, title: '更新时间',hide: true},
                 {templet: '#auth-state', width: 150, minWidth: 150, align: 'center', title: '操作'}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            var checkStatus = table.checkStatus('currentTableId');
            var data = checkStatus.data;
            
            switch (obj.event) {
                case 'batchCapacity':
                    if(data.length === 0){
                        layer.msg('请选择至少一条数据', {icon: 2});
                        return;
                    }
                    layer.prompt({
                        formType: 0,
                        title: '请输入容积值',
                        area: ['330px', '150px']
                    }, function(value, index, elem){
                        // 验证输入是否为数字
                        if(!/^\d+(\.\d+)?$/.test(value)){
                            layer.msg('请输入有效的容积数值', {icon: 2});
                            return;
                        }
                        
                        var ids = [];
                        for(var i=0; i<data.length; i++){
                            ids.push(data[i].id);
                        }
                        
                        $.operate.postJson({
                            url: '/auxiliary/info/batchUpdateCapacity',
                            data: JSON.stringify({
                                ids: ids,
                                capacity: value
                            }),
                            isShowMsg: true,
                            callback: function(result){
                                if(result.code == 200){
                                    table.reload('currentTableId');
                                    layer.close(index);
                                }
                            }
                        });
                    });
                    break;
                case 'batchType':
                    if(data.length === 0){
                        layer.msg('请选择至少一条数据', {icon: 2});
                        return;
                    }
                    layer.open({
                        type: 1,
                        title: '批量设置物料属性',
                        area: ['370px', '316px'],
                        content: '<div style="padding: 30px 20px 10px 20px">' +
                                '<form class="layui-form" action="" id="batchTypeForm">' +
                                '<div class="layui-form-item">' +
                                '<label class="layui-form-label">物料属性</label>' +
                                '<div class="layui-input-block">' +
                                '<select name="materialType" lay-verify="required">' +
                                '<option value="-1">--请选择--</option>' +
                                '<option value="0">自身属性</option>' +
                                '<option value="1">款式属性</option>' +
                                '</select>' +
                                '</div></div>' +
                                '</form></div>',
                        btn: ['确定', '取消'],
                        yes: function(index, layero){
                            var materialType = $('#batchTypeForm').find('select[name="materialType"]').val();
                            if(materialType == -1){
                                layer.msg("请选择物料属性", {icon: 2});
                                return;
                            }
                            
                            var ids = [];
                            for(var i=0; i<data.length; i++){
                                ids.push(data[i].id);
                            }
                            
                            $.operate.postJson({
                                url: '/auxiliary/info/batchUpdateType',
                                data: JSON.stringify({
                                    ids: ids,
                                    materialType: materialType
                                }),
                                isShowMsg: true,
                                callback: function(result){
                                    if(result.code == 200){
                                        table.reload('currentTableId');
                                        layer.closeAll();
                                    }
                                }
                            });
                        },
                        success: function(layero, index){
                            form.render();
                        }
                    });
                    break;
                case 'refresh':
                    table.reload('currentTableId');
                    break;
            }
        });

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                layer.open({
                    type: 1,
                    title: '编辑辅料信息',
                    area: ['400px', '300px'],
                    content: '<div style="padding: 20px;">' +
                            '<form class="layui-form" action="" lay-filter="editForm">' +
                            '<div class="layui-form-item">' +
                            '<label class="layui-form-label">容积(箱容量)</label>' +
                            '<div class="layui-input-block">' +
                            '<input type="text" name="capacity" required lay-verify="required|number" placeholder="请输入容积" autocomplete="off" class="layui-input" value="' + (data.capacity || 0) + '">' +
                            '</div></div>' +
                            '<div class="layui-form-item">' +
                            '<label class="layui-form-label">物料属性</label>' +
                            '<div class="layui-input-block">' +
                            '<select name="materialType" lay-verify="required">' +
                                +'<option value="-1">--请选择--</option>' +
                            '<option value="0" ' + (data.materialType == 0 ? 'selected' : '') + '>自身属性</option>' +
                            '<option value="1" ' + (data.materialType == 1 ? 'selected' : '') + '>款式属性</option>' +
                            '</select>' +
                            '</div></div>' +
                            '<div class="layui-form-item">' +
                            '<div class="layui-input-block">' +
                            '<button class="layui-btn" lay-submit lay-filter="editSubmit">保存</button>' +
                            '<button type="reset" class="layui-btn layui-btn-primary">重置</button>' +
                            '</div></div>' +
                            '</form></div>',
                    success: function(){
                        form.render();
                        form.on('submit(editSubmit)', function(formData){
                            // 验证输入是否为数字
                            if(!/^\d+(\.\d+)?$/.test(formData.field.capacity)){
                                layer.msg('容积必须是有效的数值', {icon: 2});
                                return false;
                            }
                            
                            if(formData.field.materialType == -1){
                                layer.msg('请选择物料属性', {icon: 2});
                                return false;
                            }
                            
                            $.operate.postJson({
                                url: '/auxiliary/info/update',
                                data: JSON.stringify({
                                    id: data.id,
                                    capacity: formData.field.capacity,
                                    materialType: formData.field.materialType
                                }),
                                isShowMsg: true,
                                callback: function(result){
                                    if(result.code == 200){
                                        table.reload('currentTableId');
                                        layer.closeAll();
                                    }
                                }
                            });
                            return false;
                        });
                    }
                });
                return false;
            }
        });
    });
</script>
</body>
</html>