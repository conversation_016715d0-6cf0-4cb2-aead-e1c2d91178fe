<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>预装箱</title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.11.0/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <script src="../../../js/auxiliaryPrePacking.js" charset="utf-8"></script>
    <style>
        body {
             padding: 6px;
        }
        h4 {
            font-size: 14px;
            font-weight: bold;
            padding-left: 5px;
            border-left: 3px solid #1E9FFF;
        }
        .layui-table-view + h4, /* 调整间距 */
        .info-display-item + h4 {
             margin-top: 25px;
        }
        /* 新信息显示样式 */
        .info-display-item {
            display: inline-block; /* 水平显示项目 */
            margin-right: 30px;
        }
        .info-display-item .info-label {
            margin-right: 5px;
            color: #666;
        }

        /* 确保静态表格有布局 */
        .static-table-container table {
            width: 100%;
            margin-top: 10px; /* 工具栏下方间距 */
        }

        /* --- 料箱部分特定样式 --- */
        /* 自身属性容器保留内边距 */
        #auxPrePack_boxInfoDisplay {
            padding: 10px 0;
        }
        /* 信息项Grid布局 (应用到包裹层) */
        .box-info-grid-items { /* 新的包裹层类 */
            display: grid;
            grid-template-columns: repeat(2, auto); /* 响应式列 */
            gap: 10px 15px; /* 行间距 列间距 */
            align-items: center; /* 垂直居中 */
            margin-bottom: 15px; /* 在Grid下方增加间距 */
            justify-content: center;
        }
        /* Grid 内标签样式 (针对包裹层) */
        .box-info-grid-items .info-label {
            width: 80px; /* 统一标签宽度 */
            text-align: right;
            white-space: nowrap;
        }
         /* Grid 内值样式 (确保对齐) (针对包裹层) */
        .box-info-grid-items .info-value {
            vertical-align: middle;
        }
        /* 款式属性信息展示Grid布局 (保持不变) */
        #auxPrePack_boxMaterialInfoDisplay {
            display: grid;
            grid-template-columns: repeat(2, auto);
            gap: 10px 25px;
            padding: 15px 0; /* 垂直内边距 */
            align-items: center; /* 在每个网格单元中垂直居中项目 */
            justify-content: center; /* 恢复居中对齐以匹配托盘 */
        }
      
        #auxPrePack_boxMaterialToolbar .info-display-item {
            line-height: 32px; /* 与托盘信息项行高一致 */
        }
        /* 按钮工具栏样式 (保持不变) */
        #auxPrePack_boxInfoToolbarButtons {
            text-align: center; /* 按钮栏内容居中 */
            padding: 5px 0;
        }

        /* 按钮工具栏内按钮间距 (保持不变) */
        #auxPrePack_boxInfoToolbarButtons .info-display-item,
        #auxPrePack_boxMaterialToolbar .info-display-item {
            display: inline-block;
            
        }
    

        /* === 优化后的托盘信息展示布局（使用精确的 4 列网格） === */
        #auxPrePack_palletInfoDisplay {
            display: grid;
            grid-template-columns: repeat(3, auto); /* 精确的四列等宽 */
            gap: 10px 15px; /* 行间距和缩小的列间距 */
            padding: 10px 0; /* 保留现有内边距 */
            justify-content: center; /* 水平居中网格轨道 */
        }
        /* 移除了 .key-value-row 相关样式 */

        #auxPrePack_palletInfoDisplay .info-display-item { /* 网格内项目的样式 */
           /* display: inline-block; 如果存在则移除，网格负责放置 */
           /* vertical-align: middle; 如果存在则移除 */
           /* margin-right: 25px; 如果存在则移除 */
           /* min-width: 280px; 如果存在则移除 */
           line-height: 28px; /* 确保一致的行高 */
           /* 项目本身占据一个网格单元 */
        }
         /* 移除了 :last-child margin 相关规则 */

        #auxPrePack_palletInfoDisplay .info-label {
             display: inline-block; /* 保留 inline-block */
             width: 80px; /* 为适应较窄列宽而缩小的标签宽度 */
             text-align: right;
             margin-right: 5px; /* 恢复标签和值之间的间距 */
             color: #666;
             /* 确保标签不会不必要地换行 */
             white-space: nowrap;
             text-overflow: ellipsis;
        }
        /* 值 span/button 会自动跟在 inline-block 的标签后面 */

        /* === 优化后的按钮工具栏布局 (保持不变) === */
        #auxPrePack_palletInfoToolbar {
           
            text-align: center; /* 居中按钮 */
            padding: 5px 0; /* 添加一些内边距 */
        }
         #auxPrePack_palletInfoToolbar .info-display-item {
            margin-right: 10px; /* 按钮之间的间距 */
            display: inline-block; /* 保持按钮内联 */
        }
        #auxPrePack_palletInfoToolbar .info-display-item:last-child {
            margin-right: 0; /* 移除最后一个按钮的右边距 */
        }
        /* === 托盘物料信息显示样式 === */
        #auxPrePack_palletMaterialInfoDisplay {
            display: grid;
            grid-template-columns: repeat(3, auto); /* 创建一个两列布局 */
            gap: 10px 25px; /* 行间距和列间距 */
            padding: 15px 0; /* 垂直内边距 */
            align-items: center; /* 在每个网格单元中垂直居中项目 */
            justify-content: center; /* 将网格内容对齐到开始位置 */
        }

        #auxPrePack_palletMaterialInfoDisplay .info-display-item {
           /* 网格处理布局，边距可能不需要 */
           line-height: 32px; /* 确保垂直对齐一致性 */
           /* 已移除 margin-right，因为 grid gap 会处理间距 */
        }

        #auxPrePack_palletMaterialInfoDisplay .info-label {
             display: inline-block;
             width: 90px; /* 标签稍宽 */
             text-align: right;
             margin-right: 8px; /* 标签和值/选择框之间的间距 */
             color: #666;
             white-space: nowrap;
        }

        #auxPrePack_palletMaterialInfoDisplay .info-value {
             vertical-align: middle; /* 将 span 值与标签文本对齐 */
             text-align: right; /* 添加右对齐 */
        }

        #auxPrePack_palletMaterialInfoDisplay .layui-select {
             display: inline-block; /* 使 select 表现的像其他内联元素 */
             width: auto;         /* 允许 select 根据内容或容器调整大小 */
             min-width: 180px;    /* 设置最小宽度以获得更好的外观 */
             vertical-align: middle; /* 将 select 与标签文本对齐 */
        }
        /* 可选：如果需要，为 select 下拉容器设置样式 */
        #auxPrePack_palletMaterialInfoDisplay .layui-form-select {
            display: inline-block;
            vertical-align: middle;
            width: auto;
            min-width: 180px; /* 匹配 select 的最小宽度 */
        }

        #auxPrePack_palletMaterialInfoDisplay .layui-form-select dl {
            min-width: 180px; /* 确保下拉列表宽度匹配 */
        }
    </style>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">

        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            <ul class="layui-tab-title">
                <li class="layui-this">料箱</li>
                <li>托盘</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-collapse">
                        <div class="layui-colla-item" id="auxPrePack_boxInfoColla">
                            <h4 class="layui-colla-title">自身属性</h4>
                            <div class="layui-colla-content layui-show">
                                <div id="auxPrePack_boxInfoDisplay" class="layui-form layui-form-pane">
                                     <div class="box-info-grid-items">
                                         <div class="info-display-item">
                                             <span class="info-label">箱号:</span>
                                             <span class="info-value" id="auxPrePack_boxNoDisplay">--</span>
                                         </div>
                                         <div class="info-display-item">
                                              <span class="info-label">容器类型:</span>
                                              <span class="info-value" id="auxPrePack_boxTypeDisplay">--</span>
                                         </div>
                                     </div>
                                     <div id="auxPrePack_boxInfoToolbarButtons">
                                         <div class="info-display-item">
                                             <button class="layui-btn layui-btn-sm" id="auxPrePack_btnBoxRefresh" type="button"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
                                         </div>
                                         <div class="info-display-item">
                                             <button class="layui-btn layui-btn-sm" id="auxPrePack_btnBoxBind" type="button">绑定入库</button>
                                         </div>
                                         <!-- <div class="info-display-item">
                                             <button class="layui-btn layui-btn-sm" id="auxPrePack_btnBoxInbound" type="button">入库</button>
                                         </div> -->
                                     </div>
                                     <div class="static-table-container">
                                        <table class="layui-table" id="auxPrePack_boxInfoTableId" lay-filter="auxPrePack_boxInfoTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-colla-item" id="auxPrePack_boxMaterialInfoColla">
                            <h4 class="layui-colla-title">款式属性</h4>
                            <div class="layui-colla-content layui-show">
                                <div id="auxPrePack_boxMaterialInfoDisplay" class="layui-form layui-form-pane" style="text-align: right;">
                                 
                                    <div class="info-display-item">
                                        <span class="info-label">箱号:</span>
                                        <span class="info-value" id="auxPrePack_boxMaterialInfoBoxNo">--</span>
                                    </div>
                                    <div class="info-display-item">
                                         <span class="info-label">容器类型:</span>
                                         <span class="info-value" id="auxPrePack_boxMaterialInfoContainerType">--</span>
                                    </div>
                                    <div class="info-display-item">
                                        <span class="info-label">待入库数量:</span>
                                        <span class="info-value" id="auxPrePack_boxMaterialInfoPendingQty">--</span>
                                    </div>
                                  
                               </div>
                                <div id="auxPrePack_boxMaterialToolbar">
                                    <div class="layui-btn-container" style="margin-top: 10px; text-align: center;">
                                        <div class="info-display-item">
                                            <span class="info-label">PO号:</span>
                                            <select class="layui-select" id="auxPrePack_boxMaterialPoSelect" lay-filter="boxMaterialPoSelect" style="min-width: 200px;">
                                                <option value="">请选择PO号</option>
                                            </select>
                                        </div>
                                        <div class="info-display-item">
                                            <button class="layui-btn layui-btn-sm" id="auxPrePack_btnBoxMaterialRefresh" type="button"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
                                        </div>
                                        <div class="info-display-item">
                                            <button class="layui-btn layui-btn-sm" id="auxPrePack_btnBoxMaterialBind" type="button">绑定入库</button>
                                        </div>
                                        <!-- <div class="info-display-item">
                                            <button class="layui-btn layui-btn-sm" id="auxPrePack_btnBoxMaterialInbound" type="button">入库</button>
                                        </div> -->
                                    </div>
                                </div>
                                <!-- 静态 HTML 表格 -->
                                <div class="static-table-container">
                                    <table class="layui-table" id="auxPrePack_boxMaterialTableId" lay-filter="auxPrePack_boxMaterialTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-collapse">
                        <div class="layui-colla-item" id="auxPrePack_palletInfoColla">
                            <h4 class="layui-colla-title">自身属性</h4>
                            <div class="layui-colla-content layui-show">
                                 <div id="auxPrePack_palletInfoDisplay" class="layui-form layui-form-pane" style="padding: 10px 0;">
                                     <!-- 项目是网格容器的直接子元素 -->
                                     <div class="info-display-item">
                                         <span class="info-label">托盘号:</span>
                                         <span class="info-value" id="auxPrePack_palletNoDisplay">--</span>
                                     </div>
                                     <div class="info-display-item">
                                          <span class="info-label">装料架款号:</span>
                                          <span class="info-value" id="auxPrePack_palletStyleNoDisplay">--</span>
                                     </div>
                                     <div class="info-display-item">
                                         <span class="info-label">合约号:</span>
                                         <span class="info-value" id="auxPrePack_palletContractNoDisplay">--</span>
                                     </div>
                                     <div class="info-display-item">
                                         <span class="info-label">容器类型:</span>
                                         <span class="info-value" id="auxPrePack_palletTypeDisplay">--</span>
                                     </div>
                                     <div class="info-display-item">
                                         <span class="info-label">托盘容量:</span>
                                         <select  id="auxPrePack_palletCapacityDisplay" lay-filter="palletCapacityDisplay" style="min-width: 200px;">
                                            <option value="">请选择托盘容量</option>
                                            <option value="20%">20%</option>
                                            <option value="50%">50%</option>
                                            <option value="75%">75%</option>
                                            <option value="100%">100%</option>
                                        </select>
                                     </div>
                                     </div>
                                </div>
                                 <div id="auxPrePack_palletInfoToolbar">
                                     <div class="info-display-item">
                                         <button class="layui-btn layui-btn-sm" id="auxPrePack_btnPalletRefresh" type="button"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
                                     </div>
                                     <div class="info-display-item">
                                         <button class="layui-btn layui-btn-sm" id="auxPrePack_btnPalletBindStatus" type="button">绑定入库</button>
                                     </div>
                                      <!-- <div class="info-display-item">
                                         <button class="layui-btn layui-btn-sm" id="auxPrePack_btnPalletInboundStatus" type="button">入库</button>
                                      </div> -->
                                  </div>
                                <!-- 新的托盘信息详情表格 -->
                                <div class="static-table-container">
                                    <table class="layui-table" id="auxPrePack_palletInfoTableId"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-colla-item" id="auxPrePack_palletMateriaInfoColla">
                             <h4 class="layui-colla-title">款式属性</h4>
                             <div class="layui-colla-content layui-show">
                                <div id="auxPrePack_palletMaterialInfoDisplay" class="layui-form layui-form-pane">
                                 
                                    <div class="info-display-item">
                                        <span class="info-label">托盘号:</span>
                                        <span class="info-value" id="auxPrePack_palletMaterialNoDisplay">--</span>
                                    </div>
                                    <div class="info-display-item">
                                         <span class="info-label">装料架款号:</span>
                                         <span class="info-value" id="auxPrePack_palletMaterialStyleNoDisplay">--</span>
                                    </div>
                                    <div class="info-display-item">
                                        <span class="info-label">合约号:</span>
                                        <span class="info-value" id="auxPrePack_palletMaterialContractNoDisplay">--</span>
                                    </div>
                                    <div class="info-display-item">
                                        <span class="info-label">待入库数量:</span>
                                        <span class="info-value" id="auxPrePack_palletMaterialPendingQty">--</span>
                                    </div>
                                    <div class="info-display-item">
                                          <span class="info-label">容器类型:</span>
                                        <span class="info-value" id="auxPrePack_palletMaterialTypeDisplay">--</span> 
                                    </div>
                                    <div class="info-display-item">
                                        <span class="info-label">托盘容量:</span>
                                        <select id="auxPrePack_palletMaterialCapacitySelect" lay-filter="palletMaterialCapacitySelect" style="min-width: 200px;">
                                            <option value="">请选择托盘容量</option>
                                            <option value="20%">20%</option>
                                            <option value="50%">50%</option>
                                            <option value="75%">75%</option>
                                            <option value="100%">100%</option>
                                        </select>
                                    </div>
                                  
                               </div>
                                <div id="auxPrePack_palletMaterialToolbar">
                                    <div class="layui-btn-container" style="margin-top: 10px; text-align: center;">
                                        <div class="info-display-item">
                                            <span class="info-label">PO号:</span>
                                            <select class="layui-select" id="auxPrePack_palletMaterialPoSelect" lay-filter="palletMaterialPoSelect" style="min-width: 200px;">
                                                <option value="">请选择PO号</option>
                                            </select>
                                        </div>
                                       
                                        <div class="info-display-item">
                                            <button class="layui-btn layui-btn-sm" id="auxPrePack_btnPalletMaterialRefresh" type="button"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
                                        </div>
                                        <div class="info-display-item">
                                            <button class="layui-btn layui-btn-sm" id="auxPrePack_btnPalletMaterialBind" type="button">绑定入库</button>
                                        </div>
                                        <!-- <div class="info-display-item">
                                            <button class="layui-btn layui-btn-sm" id="auxPrePack_btnPalletMaterialInbound" type="button">入库</button>
                                        </div> -->
                                    </div>
                                </div>
                              
                              
                                 <!-- 静态 HTML 表格 -->
                                 <div class="static-table-container">
                                    <table class="layui-table" id="auxPrePack_palletMaterialTableId" lay-filter="auxPrePack_palletMaterialTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<script>
    layui.use(['layer', 'form', 'table'], function() {
        var layer = layui.layer
        ,form = layui.form
        ,table = layui.table;
        
       });
  
    // 对外暴露初始化方法，供父页面调用
    function initData(preboxData) {
        if (preboxData && preboxData.length > 0) {
            handlePreboxData(preboxData);
        } else {
            queryPrePackingLineInfo();
        }
    }


</script>
</body>
</html>