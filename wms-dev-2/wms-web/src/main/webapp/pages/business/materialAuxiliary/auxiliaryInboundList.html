<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>辅物料入库</title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.11.0/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.11.0/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>

</head>
<body>
<div class="layuimini-container">
<div class="layuimini-main">
    <div class="layui-form-item">
   <!-- <h3 style="text-align: center; margin: 10px 5px;" class="layui-elem-quote">辅料入库单信息</h3> -->
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">合约号：</label>
                <div class="layui-input-inline">
                    <input type="text" name="lk_contractNo" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">入库单号：</label>
                <div class="layui-input-inline">
                    <input type="text" name="lk_inStoreNumber" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit
                        lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索
                </button>
                <button type="reset" class="layui-btn layui-btn-primary" lay-reset><i
                        class="layui-icon layui-icon-refresh-1"></i>重置
                </button>
            </div>
        </div>
    </form>
    </div>
</div>
    <table class="layui-table" id="currentTableId" lay-filter="currentTableFilter"></table>

</div>
<style>
.layuimini-container {
    height: 100%;
}
/* 合并单元格样式 */
.layui-table tbody tr td {
    position: relative;
}
.layui-table tbody tr td[rowspan] {
    position: relative;
}
.layui-table[lay-data="{}"] .layui-table-cell {
    height: auto;
    white-space: normal;
    word-break: break-all;
}
</style>
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
      <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal" lay-event="auxiliaryInbound">
        <i class="layui-icon"></i>入库
      </button>
      <!-- <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal" lay-event="virtualInbound">
        <i class="layui-icon"></i>虚拟入库
      </button> -->
    </div>
  </script>

<script>
    // 合并单元格函数
    function merge(field, colspan, target) {
        // 合并行
        if (target == 'rowspan') {
            var firstTr = $('table.layui-table tbody tr:first');
            var firstTd = firstTr.find('td[data-field="' + field + '"]');
            var firstTdText = firstTd.text();
            var rowspan = 1;
            firstTr.nextAll().each(function() {
                var currentText = $(this).find('td[data-field="' + field + '"]').text();
                if (currentText == firstTdText) {
                    rowspan++;
                    $(this).find('td[data-field="' + field + '"]').hide();
                } else {
                    firstTd.attr('rowspan', rowspan);
                    firstTd = $(this).find('td[data-field="' + field + '"]');
                    firstTdText = currentText;
                    rowspan = 1;
                }
                firstTd.attr('rowspan', rowspan);
            });
        }
        // 合并列
        else if (target == 'colspan') {
            // 列合并逻辑（如果需要）
        }
        // 合并多列
        else if (target == 'fixed') {
            var columsTitle = $('table.layui-table thead tr').find('th');
            var colIndex = 0;
            for (var i = 0; i < columsTitle.length; i++) {
                if (field.indexOf($(columsTitle[i]).attr('data-field')) >= 0) {
                    colIndex = i;
                    break;
                }
            }
            var firstTr = $('table.layui-table tbody tr:first');
            var firstTd = firstTr.find('td').eq(colIndex);
            var firstTdText = firstTd.text();
            var rowspan = 1;
            firstTr.nextAll().each(function() {
                var currentText = $(this).find('td').eq(colIndex).text();
                if (currentText == firstTdText) {
                    rowspan++;
                    $(this).find('td').eq(colIndex).hide();
                } else {
                    firstTd.attr('rowspan', rowspan);
                    firstTd = $(this).find('td').eq(colIndex);
                    firstTdText = currentText;
                    rowspan = 1;
                }
                firstTd.attr('rowspan', rowspan);
            });
        }
    }
    
    layui.use(['form', 'table'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        var tableObj = table.render({
            elem: '#currentTableId',
            url: ctx + '/auxiliary/inbound/query',
            method: 'post',
            contentType: 'application/json',
            defaultToolbar: ['filter'],
            toolbar: '#toolbarDemo',
            parseData: function (result) {
                if (result.code != 200) {
                    return {
                        "code": result.code,
                        "msg": result.msg,
                        "count": 0,
                        "data": []
                    };
                }
                // 按照入库单号排序，确保相同入库单号的行是相邻的
                if (result.data && result.data.length > 0) {
                    result.data.sort((a, b) => {
                        if (!a.inStoreNumber && !b.inStoreNumber) return 0;
                        if (!a.inStoreNumber) return 1;
                        if (!b.inStoreNumber) return -1;
                        if (a.inStoreNumber < b.inStoreNumber) return -1;
                        if (a.inStoreNumber > b.inStoreNumber) return 1;
                        return 0;
                    });
                }

                return {
                    "code": (result.code == 200 ? '0' : result.code),
                    "data": result.data,
                    "count": result.total
                }
            },
            cols: [[
                {type: "checkbox", width: 50, fixed: "left"},
                {field: 'id', width: 80, title: 'ID', sort: true, hide: true},
                {field: 'inStoreNumber', width: 150, title: '入库单号', sort: true},
                {field: 'contractNo', width: 150, title: '合约号', sort: true},
                {field: 'itemNo', width: 100, title: '款号', sort: true},
                {field: 'materialCode', width: 150, title: '物料编码', sort: true},
                {field: 'materialName', width: 200, title: '物料名称'},
                {field: 'materialModel', width: 100, title: '物料规格'},
                {field: 'materialColor', width: 100, title: '物料颜色'},
                {field: 'quantity', width: 100, title: '来料数量', sort: true},
                {field: 'materialUnit', width: 80, title: '单位'},
                {field: 'materialType', width: 100, title: '物料属性', sort: true, templet: function(d){
                    if(d.materialType == 0){
                        return '自身属性';
                    }else if(d.materialType == 1){
                        return '款式属性';
                    }else{
                        return '';
                    }
                }},
                {field: 'isStore', width: 100, title: '是否入智能仓', sort: true, templet: function(d){
                    if(d.isStore == 1){
                        return '是';
                    }else{
                        return '否';
                    }
                }},
                //状态
                {field: 'status', width: 100, title: '状态', sort: true, templet: function(d){
                    if(d.status == 0){
                        return '待处理';
                    }else if(d.status == 1){
                        return '待入库';
                    }else if(d.status == 2){
                        return '已入库';
                    }else{
                        return '';
                    }
                }},
                {field: 'createBy', width: 120, title: '创建人', hide: true},
                {field: 'createTime', width: 160, title: '创建时间', hide: true},
                {field: 'updateBy', width: 120, title: '更新人', hide: true},
                {field: 'updateTime', width: 160, title: '更新时间', hide: true}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true,
            where: {
                    searchParams:form.val('inputForm')
                    },
            done: function(res, curr, count) {
                // 合并入库单号单元格
                merge('inStoreNumber', 1, 'rowspan');
            },
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            // 执行搜索重载，并重置到第一页
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: form.val('inputForm')
                }
            }, 'data');
            return false;
        });

        // 监听排序操作
        table.on('sort(currentTableFilter)', function (obj) {
            table.reload('currentTableId', {
                initSort: obj,
                page: { curr: 1 },  // 排序后回到第一页
                where: {
                    searchParams: form.val('inputForm')
                }
            }, 'data');
            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            if (obj.event === 'refresh') {
                table.reload('currentTableId');
            }
            if (obj.event === 'auxiliaryInbound') {
                auxiliaryInbound();
            }
            if (obj.event === 'virtualInbound') {
                virtualInbound();
            }
        });

     
    });

       // 辅料入库按钮点击事件
       function auxiliaryInbound(){

           //获取勾选数据，判断是否有勾选数据
           var checkStatus = layui.table.checkStatus('currentTableId');
           var checkData = checkStatus.data;
           if(checkData.length == 0){
            layer.msg('请选择要入库的辅料');
            return;
           }

           // 提取选中数据的ID列表
           var detailIds;
           try {
               detailIds = checkData.map(function(item) {
                   // 验证必要字段
                   if (!item.id || item.id.trim() === '') {
                       throw new Error('存在无效的记录ID');
                   }
                   
                   // 基本数据验证（前端展示用）
                   if (item.quantity === null || item.quantity === undefined || item.quantity === '') {
                       throw new Error('物料 ' + (item.materialCode || '未知') + ' 的入库数量为空');
                   }
                   
                   var quantity = parseInt(item.quantity);
                   if (isNaN(quantity) || quantity <= 0) {
                       throw new Error('物料 ' + (item.materialCode || '未知') + ' 的入库数量无效: ' + item.quantity);
                   }
                   
                   if (!item.contractNo || item.contractNo.trim() === '') {
                       throw new Error('物料 ' + (item.materialCode || '未知') + ' 的合约号为空');
                   }
                   
                   if (!item.materialCode || item.materialCode.trim() === '') {
                       throw new Error('物料编码不能为空');
                   }
                   
                   return item.id; // 只返回ID
               });
           } catch (error) {
               layer.msg('数据验证失败: ' + error.message);
               return;
           }

           //将勾选的数据转换为json字符串，并调用后端预装箱接口
           // var checkDataJson = JSON.stringify(checkData); // 旧代码
           var preboxDataJson = JSON.stringify(detailIds); // 映射后的数据
           //加载Loading
           layer.msg('加载中...', {icon: 16});
           $.ajax({
            url: ctx + '/auxiliary/inbound/prebox',
            type: 'post',
            contentType: 'application/json', // 添加 contentType
            data: preboxDataJson, // 发送映射后的 JSON 字符串
            success: function(res) {
                 // 处理成功响应，例如根据后端的返回决定是否打开弹窗
                 if (res.code == 200) { // 假设后端成功返回 code 200
                    //获取接口返回的批次号
                    var stockBatchNo = res.data;
                    //将批次号存储到本地存储中
                    localStorage.setItem('stockBatchNo', stockBatchNo);
                    openAuxiliaryPrePacking();
                 } else {
                    layer.msg('预装箱请求失败：' + res.message);
                 }
            },
            error: function(xhr, status, error) {
                // 处理错误情况
                layer.msg('请求预装箱接口失败: ' + error);
                console.error("Prebox request failed:", status, error);
            }
           });

           /* // 原来的直接打开弹窗逻辑，现在移到 ajax success 回调中
           layer.open({
                type: 2, // 类型改为 2，表示是 iframe 层
                area: ['100%', '100%'], // 调整弹出层宽高，使用百分比可以自适应
                title: '预装箱详情', // 设置一个更合适的标题
                shade: 0.6,
                shadeClose: false, // iframe 层建议设置为 false，避免误触关闭
                maxmin: true,
                anim: 0,
                // content 直接设置为目标页面的 URL
                // 假设 auxiliaryInboundList.html 和 auxiliaryPrePacking.html 在同一目录下
                content: 'auxiliaryPrePacking.html',
                // 可选：可以添加 success 回调，在 iframe 加载完毕后执行操作
                success: function(layero, index){
                     console.log('预装箱详情页面加载成功');
                     // var iframeWindow = window['layui-layer-iframe' + index]; // 获取iframe的window对象
                     // 你可以在这里调用 iframe 页面中的方法或操作其 DOM (如果需要且同源)
                }
            });
            */
        };
       function openAuxiliaryPrePacking(){
        layer.open({
                        type: 2, // 类型改为 2，表示是 iframe 层
                        area: ['100%', '100%'], // 调整弹出层宽高，使用百分比可以自适应
                        title: '预装箱详情', // 设置一个更合适的标题
                        shade: 0.6,
                        shadeClose: false, // iframe 层建议设置为 false，避免误触关闭
                        maxmin: true,
                        anim: 0,
                        // content 直接设置为目标页面的 URL
                        content: 'auxiliaryPrePacking.html',
                        // 可选：可以添加 success 回调，在 iframe 加载完毕后执行操作
                        success: function(layero, index){
                             console.log('预装箱详情页面加载成功');
                             // 后续可能需要将 preboxData 传递给 iframe 页面
                             // var iframeWindow = window['layui-layer-iframe' + index];
                             // iframeWindow.initData(preboxData); // 示例：调用 iframe 页面的方法传递数据
                        }
                    });
       }
        // 虚拟入库按钮点击事件
       function virtualInbound(){
            $.modal.openDrawer({
                title: '虚拟入库',
                url: "auxiliaryInfo.html?virtualInbound=true",
                layerCallBack: function (returnVal) {
                    table.reload('currentTableId');
                }
            });
        };
</script>
</body>
</html>
