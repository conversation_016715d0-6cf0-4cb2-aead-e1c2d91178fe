<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .layui-form .layui-form-label{
            width:110px;
            padding: 9px 5px;
        }
        .layui-form .layui-input-block{
            margin-left: 130px;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">主表ID：</label>
                    <div class="layui-input-block">
                        <input type="text" name="mainid" lay-filter="mainid" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">站点编码：</label>
                    <div class="layui-input-block">
                        <input type="text" name="siteCode" lay-filter="siteCode" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">货位编码：</label>
                    <div class="layui-input-block">
                        <input type="text" name="code" lay-filter="code" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">货位：</label>
                    <div class="layui-input-block">
                        <input type="text" name="location" lay-filter="location" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">层位总数：</label>
                    <div class="layui-input-block">
                        <input type="text" name="levelsum" lay-filter="levelsum" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">层位：</label>
                    <div class="layui-input-block">
                        <input type="text" name="level" lay-filter="level" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">行位：</label>
                    <div class="layui-input-block">
                        <input type="text" name="rowNum" lay-filter="rowNum" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">列位：</label>
                    <div class="layui-input-block">
                        <input type="text" name="columnNumber" lay-filter="columnNumber" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">区域：</label>
                    <div class="layui-input-block">
                        <input type="text" name="area" lay-filter="area" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">货位高度：</label>
                    <div class="layui-input-block" id="heightBlock">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">优先级：</label>
                    <div class="layui-input-block">
                        <input type="text" name="priority" lay-filter="priority" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">智能设备：</label>
                    <div class="layui-input-block">
                        <select id="robot" name="robot"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">锁定：</label>
                    <div class="layui-input-block" id="lockedBlock">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">调度状态：</label>
                    <div class="layui-input-block" id="dispatchStateBlock">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">货位异常：</label>
                    <div class="layui-input-block" id="errorBlock">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">货位状态：</label>
                    <div class="layui-input-block" id="stateBlock">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">是否有板：</label>
                    <div class="layui-input-block" id="ispalletBlock">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">托板类型：</label>
                    <div class="layui-input-block" >
                        <select id="palletType" name="palletType"></select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">托板高度：</label>
                    <div class="layui-input-block" >
                        <select id="palletHeight" name="palletHeight"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">布匹号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="clothNo" lay-filter="clothNo" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">拉布单号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="billno" lay-filter="billno" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">铺床：</label>
                    <div class="layui-input-block">
                        <input type="text" name="latheno" lay-filter="latheno" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">缸号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="vatno" lay-filter="vatno" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">计划日期：</label>
                    <div class="layui-input-block">
                        <input type="text" id="planDate" name="planDate" lay-filter="planDate" lay-verify="required" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateBy" lay-filter="updateBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新时间：</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateTime" lay-filter="updateTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

    </form>

    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 130px;">
            <button type="button" class="layui-btn" id="btnSave" ><i class="fa fa-save"></i>保存</button>
            <button type="button" class="layui-btn layui-btn-primary" id="btnClose" ><i class="layui-icon layui-icon-close"></i>关闭</button>
        </div>
    </div>
</div>

<script>
    var urlPath = "storage/cacheLocationRowBase/";

    $(document).ready(function(){
        initPage();
        $("#btnSave").click(function () {
            saveFun();
        });
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
    });

    function initPage(){
        $.operate.initDictRadio("location_height", "heightBlock", "height");
        $.operate.initDictDropDown("wms_commncation_device,deviceName,deviceNo", "robot");
        $.operate.initDictRadio("locked_status", "lockedBlock", "locked");
        $.operate.initDictRadio("yn", "dispatchStateBlock", "dispatchState");
        $.operate.initDictRadio("yn", "errorBlock", "error");
        $.operate.initDictRadio("location_status", "stateBlock", "state");
        $.operate.initDictRadio("yn", "ispalletBlock", "ispallet");
        $.operate.initDictDropDown("pallet_type", "palletType");
        $.operate.initDictDropDown("pallet_height", "palletHeight");

        //常规用法
        layui.laydate.render({
            elem: '#planDate'
            ,theme: 'molv'
        });

        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: urlPath + "queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }

    function saveFun(){
        var postData = layui.form.val('inputForm');
        var id = $.common.getURLParameter("id");

        var url = urlPath + "add";
        if($.common.isNotEmpty(id)){
            url = urlPath + "edit";
            postData.id = id;
        }

        $.operate.postJson({
            url: url,
            data: JSON.stringify(postData),
            isShowMsg:true,
            callback: function(result){
                if(result.code==200){
                    $.modal.closeDrawer(result);
                }
            }
        });
    }
</script>
</body>
</html>