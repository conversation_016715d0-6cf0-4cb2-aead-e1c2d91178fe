<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">任务类型：</label>
                    <div class="layui-input-inline">
                        <select name="eq_taskType" id="taskType"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">任务状态：</label>
                    <div class="layui-input-inline">
                        <select name="eq_taskStatus" id="taskStatus"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">容器号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_boxNo" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
<!--        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">-->
<!--            <i class="layui-icon layui-icon-add-1"></i>增加-->
<!--        </button>-->
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon layui-icon-refresh-3"></i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script>
    layui.use(['form', 'table','miniTab'], function () {
        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab;

        var tableId = "dataTable";

        var dictTable = createTable({
            elem: tableId,
            url:  'task/taskBox/rootList',
            toolbar: "tableBar",
            where: {
                searchParams: form.val('inputForm')
            },
            cols: [[
                {type:'radio', fixed: 'left'},
                {type:'numbers'},
                {field: 'taskOrder', width: 220, title: '任务号'},
                {field: 'taskType', width: 150, title: '任务类型',templet: function(d){
                        if(d.taskType == '1'){
                            return '调拨入库';
                        }else if(d.taskType == '2'){
                            return '生产退料回库';
                        }else if(d.taskType == '3'){
                            return '领料出库';
                        }else if(d.taskType == '4'){
                            return '调拨出库';
                        }else if(d.taskType == '5'){
                            return '采购退货出库';
                        }else if(d.taskType == '6'){
                            return '指定出库';
                        }else if(d.taskType == '7'){
                            return '指定入库';
                        }else if(d.taskType == '8'){
                            return '紧急出库';
                        }else if(d.taskType == '10'){
                            return '盘点出库';
                        }else if(d.taskType == '0'){
                            return '采购入库';
                        }
                    }},
                {field: 'boxNo',width: 100,title: '容器号'},
                {field: 'fromSite', width: 100, title: '起始位置'},
                {field: 'toSite', width: 100, title: '目的位置'},
                {field: 'column', width: 80, title: '列'},
                {field: 'row', width: 80, title: '行'},
                {field: 'level', width: 80, title: '层'},
                {field: 'taskStatus', width: 120, title: '任务状态',templet: function(d){
                        if(d.taskStatus == '1'){
                            return '任务已下发';
                        }else if(d.taskStatus == '2'){
                            return '执行搬运';
                        }else if(d.taskStatus == '3'){
                            return '输送线运输';
                        }else if(d.taskStatus == '0'){
                            return '创建';
                        }else if(d.taskStatus == '4'){
                            return '执行搬运中';
                        }
                    }},//原网页缺少对应字典
                {field: 'boxType', width: 100, title: '容器类型',templet: function(d){
                        if(d.boxType == '1'){
                            return '料箱';
                        }else if(d.boxType == '2'){
                            return '料架';
                        }
                    }},
                {field: 'deviceNo', width: 120, title: '设备编号'},
                {field: 'deviceType', width: 120, title: '设备类型'},
                {field: 'priority', width: 150, title: '优先级'},
                {field: 'createTime', width: 180, title: '创建时间'},
                {field: 'updateTime', width: 180, title: '更新时间'},
                {title: '操作', width: 160, minWidth: 160, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload(tableId, {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
            switch(obj.event){
                //增加
                case 'add':
                    $.modal.openDrawer({
                        title: '新增',
                        width: '800px',
                        url: "agvDispatchInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;

                //刷新
                case 'refresh':
                    table.reload(tableId);
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑',
                        width: '800px',
                        url: "agvDispatchInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            debugger;
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'delete':
                    layer.confirm('真的删除行么', function (index) {
                        $.operate.postJson({
                            url:  'task/taskBox/delete',
                            data: data.id,
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }
        });
        $.operate.initEnumDropDown("BoxTaskType", "type");
        $.operate.initEnumDropDown("BoxTaskStatus", "taskStatus");
    });
</script>
</body>
</html>