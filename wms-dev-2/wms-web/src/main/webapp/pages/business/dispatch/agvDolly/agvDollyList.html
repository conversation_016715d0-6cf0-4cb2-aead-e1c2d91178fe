<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label required">站点编码：</label>
                    <div class="layui-input-block">
                        <select name="eq_siteCode" id="siteCode"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">智能设备：</label>
                    <div class="layui-input-block">
                        <select name="eq_robot" id="robot"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">拉布单号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="lk_billno" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">布匹编号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="lk_clothNo" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">松布架号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="lk_dollyNo" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon layui-icon-refresh-3"></i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script>
    layui.use(['form', 'table','miniTab'], function () {
        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab;

        var tableId = "dataTable";
        var urlPath = "dispatch/agvDolly/";

        var dictTable = createTable({
            elem: tableId,
            url: urlPath +'list',
            toolbar: "tableBar",
            where: {
                searchParams: form.val('inputForm')
            },
            cols: [[
                {type:'numbers'},
                {field: 'siteCode_dictText', width: 100, title: '站点编码', sort: true},
                {field: 'dollyNo', width: 150, title: '松布架号', sort: true},
                {field: 'dollyLayers_dictText', width: 150, title: '松布架层数'},
                {field: 'robot', width: 150, title: '智能设备', sort: true},
                {field: 'robot_dictText', width: 150, title: '智能设备', sort: true},
                {field: 'level', width: 80, title: '层位'},
                {field: 'height_dictText', width: 80, title: '层高度'},
                {field: 'locked_dictText', width: 80, title: '锁定'},
                {field: 'operatetag_dictText', width: 120, title: '操作标记'},
                {field: 'disable_dictText', width: 100, title: '是否搬运'},
                {field: 'toLatheNo', width: 100, title: '铺床'},
                {field: 'clothNo', width: 100, title: '布匹条码'},
                {field: 'billno', width: 100, title: '拉布单号'},
                {field: 'ispallet_dictText', width: 100, title: '是否有板'},
                {field: 'palletType_dictText', width: 100, title: '装载类型'},
                {field: 'palletHeight_dictText', width: 100, title: '板高度'},
                {field: 'createTime', width: 180, title: '创建时间'},
                {field: 'updateTime', width: 180, title: '更新时间'},
                {title: '操作', width: 160, minWidth: 160, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload(tableId, {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
            switch(obj.event){
                //增加
                case 'add':
                    $.modal.openDrawer({
                        title: '新增',
                        width: '800px',
                        url: "agvDollyInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;

                //刷新
                case 'refresh':
                    table.reload(tableId);
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑',
                        width: '800px',
                        url: "agvDollyInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'delete':
                    layer.confirm('真的删除行么', function (index) {
                        $.operate.postJson({
                            url: urlPath +'delete',
                            data: data.id,
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }
        });
        $.operate.initDictDropDown("wms_vritual_site,site_code,site_code", "siteCode");
        $.operate.initDictDropDown("wms_commncation_device,deviceName,deviceNo", "robot");
    });
</script>
</body>
</html>