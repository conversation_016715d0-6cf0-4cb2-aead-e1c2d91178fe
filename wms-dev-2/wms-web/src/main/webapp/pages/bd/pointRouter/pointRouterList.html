<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">源节点:</label>
                    <div class="layui-input-inline">
                        <select id="fromPoint" name="eq_fromPoint"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">目的节点:</label>
                    <div class="layui-input-inline">
                        <select id="toPoint" name="eq_toPoint"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">路由类型:</label>
                    <div class="layui-input-inline">
                        <select id="type" name="eq_type"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary" lay-reset ><i class="layui-icon layui-icon-refresh-1"></i>重置</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dictTable" lay-filter="dictTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script>
    layui.use(['form', 'table','miniTab'], function () {
        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab;
        $.operate.initDictDropDown("wms_point,point_name,point_no,point_type=3", "fromPoint,toPoint");
        $.operate.initEnumDropDown("Router", "type");

        var tableId = "dictTable";
        var urlPath = "bd/pointRouter/";

        var dictTable = createTable({
            elem: tableId,
            url: urlPath +'list',
            toolbar: "tableBar",
            cols: [[
                {field: 'fromPoint_dictText', width: 150, title: '源节点', sort: true},
                {field: 'toPoint_dictText', width: 150, title: '目的节点', sort: true},
                {field: 'fromFloor', width: 100, title: '源楼层', sort: true},
                {field: 'toFloor', width: 100, title: '目的楼层', sort: true},
                {field: 'fromArea_dictText', width: 150, title: '源区域'},
                {field: 'toArea_dictText', width: 150, title: '目的区域'},
                {field: 'type_dictText', width: 120, title: '路由类型', sort: true},
                {field: 'deviceno_dictText', width: 120, title: '路由设备', sort: true},
                {field: 'routerPoint_dictText', width: 120, title: '路由节点'},
                {field: 'description', width: 180, title: '描述', sort: true},
                {field: 'createTime', width: 180, title: '创建时间'},
                {field: 'updateTime', width: 180, title: '更新时间'},
                {title: '操作', width: 230, minWidth: 230, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('dictTable', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            switch(obj.event){
                case 'add':
                    $.modal.openDrawer({
                        title: '新增',
                        width: '800px',
                        url: "pointRouterInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'refresh':
                    table.reload(tableId);
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑',
                        width: '800px',
                        url: "pointRouterInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'delete':
                    layer.confirm('真的删除行么', function (index) {
                        $.operate.postJson({
                            url: urlPath +'delete',
                            data: data.id,
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }
        });
    });
</script>
</body>
</html>