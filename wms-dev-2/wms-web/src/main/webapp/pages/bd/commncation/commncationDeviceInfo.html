<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>角色信息</title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../css/font-awesome.min.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">设备编号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="deviceno" lay-filter="deviceno" lay-verify="required" autocomplete="off" placeholder="请输入设备编号" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">设备名称：</label>
                    <div class="layui-input-block">
                        <input type="text" name="devicename" lay-filter="devicename" lay-verify="required" autocomplete="off" placeholder="请输入设备编号" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">所属系统：</label>
                    <div class="layui-input-block">
                        <select name="sysid" id="sysid"></select>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">IP地址：</label>
                    <div class="layui-input-block">
                        <input type="text" name="ipaddress" lay-filter="ipaddress" lay-verify="required" autocomplete="off" placeholder="请输入IP地址" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">端口：</label>
                    <div class="layui-input-block">
                        <input type="text" name="port" lay-filter="port" lay-verify="required" autocomplete="off" placeholder="请输入端口" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">序列号：</label>
                    <div class="layui-input-block">
                        <input type="text" name="license" lay-filter="license" autocomplete="off" placeholder="请输入URL地址" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">是否监控：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="monitorcontroller" value="1" lay-filter="runstate" title="是">
                        <input type="radio" name="monitorcontroller" value="0" lay-filter="runstate" title="否" >
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">监控间隔：</label>
                    <div class="layui-input-block">
                        <input type="text" name="monitorinterval" lay-filter="monitorinterval" autocomplete="off" placeholder="请输入用户名" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">重连次数：</label>
                    <div class="layui-input-block">
                        <input type="text" name="monitormaxretrycount" lay-filter="monitormaxretrycount" autocomplete="off" placeholder="请输入重连次数" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">超时时间：</label>
                    <div class="layui-input-block">
                        <input type="text" name="timeout" lay-filter="timeout" autocomplete="off" placeholder="请输入超时时间" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">设备类型：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="devicetype" value="1" lay-filter="devicetype" title="PLC">
                        <input type="radio" name="devicetype" value="2" lay-filter="devicetype" title="ABB" >
                        <input type="radio" name="devicetype" value="3" lay-filter="devicetype" title="系统" >
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">描述：</label>
                    <div class="layui-input-block">
                        <input type="text" name="description" lay-filter="description" autocomplete="off" placeholder="请输入描述" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label required">消息类别：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="msgtype" value="1" lay-filter="msgtype" title="文本">
                        <input type="radio" name="msgtype" value="2" lay-filter="msgtype" title="JSON" >
                        <input type="radio" name="msgtype" value="3" lay-filter="msgtype" title="字节" >
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">状态：</label>
                    <div class="layui-input-block">
                        <select name="state" id="state" disabled="disabled">
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">设备状态：</label>
                    <div class="layui-input-block">
                        <input type="text" name="deviceState" lay-filter="deviceState" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">设备故障：</label>
                    <div class="layui-input-block">
                        <input type="text" name="deviceError" lay-filter="deviceError" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createBy" lay-filter="createBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createTime" lay-filter="createTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateBy" lay-filter="updateBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新时间：</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateTime" lay-filter="updateTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

    </form>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <input type="button" value="保存" class="layui-btn" id="btnSave" />
            <input type="button" value="关闭" class="layui-btn layui-btn-primary" id="btnClose" />
        </div>
    </div>
</div>

<script>
    layui.use(['form','tree'], function () {
        var form = layui.form;
        var tree = layui.tree;
    });


    $(document).ready(function(){
        $("#btnSave").click(function () {
            saveFun();
        });
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
        initPage();
    });

    function saveFun(){
        var postData = layui.form.val('inputForm');
        var id = $.common.getURLParameter("id");

        var url = "commncation/commncationDevice/add";
        if($.common.isNotEmpty(id)){
            url = "commncation/commncationDevice/edit";
            postData.id = id;
        }

        $.operate.postJson({
            url: url,
            data: JSON.stringify(postData),
            isShowMsg:true,
            callback: function(result){
                if(result.code==200){
                    $.modal.closeDrawer(result);
                }
            }
        });
    }
    function initPage(){
        $.operate.initEnumDropDown("ConnectStatus", "state");
        $.operate.initDictDropDown("wms_commncation_config,sysname,sysid", "sysid");

        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: "commncation/commncationDevice/queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }
</script>
</body>
</html>