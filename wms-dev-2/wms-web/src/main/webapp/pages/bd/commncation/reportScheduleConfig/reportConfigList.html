<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../../css/public.css" media="all">
    <script src="../../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">配置名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_configName" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">任务类型：</label>
                    <div class="layui-input-block">
                        <select name="eq_taskType">
                            <option value="">全部</option>
                            <option value="入库上报">入库上报</option>
                            <option value="出库上报">出库上报</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">启用状态：</label>
                    <div class="layui-input-block">
                        <select name="eq_isEnabled">
                            <option value="">全部</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn">
                        <i class="layui-icon layui-icon-search"></i>搜索
                    </button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="configTable" lay-filter="configTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" lay-event="add">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" lay-event="refresh">
            <i class="layui-icon">&#xe9aa;</i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    {{# if(d.isEnabled == 1){ }}
        <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="disable">禁用</a>
    {{# } else { }}
        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="enable">启用</a>
    {{# } }}
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script type="text/html" id="statusTpl">
    {{# if(d.isEnabled == 1){ }}
        <span class="layui-badge layui-bg-green">启用</span>
    {{# } else { }}
        <span class="layui-badge">禁用</span>
    {{# } }}
</script>

<script type="text/html" id="taskTypeTpl">
    {{# if(d.taskType == '入库上报'){ }}
        <span class="layui-badge layui-bg-blue">{{d.taskType}}</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-orange">{{d.taskType}}</span>
    {{# } }}
</script>

<script>
    layui.use(['form', 'table'], function () {
        var form = layui.form,
            table = layui.table;

        var tableId = "configTable";
        var urlPath = ctx+"config/report-schedule/";

        var configTable = createTable({
            elem: tableId,
            url: 'config/report-schedule/list',
            toolbar: "tableBar",
            cols: [[
                {type: 'numbers'},
                {field: 'configName', width: 200, title: '配置名称', sort: true},
                {field: 'taskType', width: 100, title: '任务类型', align: 'center', templet: function(d) {
                    if (d.taskType == '入库上报') {
                        return '<span class="layui-badge layui-bg-green">入库上报</span>';
                    } else if (d.taskType == '出库上报') {
                        return '<span class="layui-badge layui-bg-orange">出库上报</span>';
                    } else {
                        return '<span class="layui-badge layui-bg-gray">' + (d.taskType || '未知') + '</span>';
                    }
                }},
                {field: 'isEnabled', width: 80, title: '状态', align: 'center', templet: function(d) {
                    return d.isEnabled == 1 ? '<span class="layui-badge layui-bg-blue">启用</span>' : '<span class="layui-badge">禁用</span>';
                }},
                {field: 'intervalMinutes', width: 120, title: '执行间隔(分钟)', sort: true},
                {field: 'batchSize', width: 120, title: '批处理数量', sort: true},
                {field: 'retryCount', width: 100, title: '重试次数', sort: true},
                {field: 'createBy', width: 120, title: '创建人'},
                {field: 'createTime', width: 160, title: '创建时间'},
                {field: 'updateBy', width: 120, title: '更新人'},
                {field: 'updateTime', width: 160, title: '更新时间'},
                {title: '操作', width: 200, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            table.reload(tableId, {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');
            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(' + tableId + ')', function (obj) {
            switch(obj.event){
                case 'add':
                    $.modal.openDrawer({
                        title: '新增配置',
                        width: '800px',
                        url: "reportConfigInfo.html",
                        layerCallBack: function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'refresh':
                    table.reload(tableId);
                    break;
            }
        });

        // 监听行工具事件
        table.on('tool(' + tableId + ')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑配置',
                        width: '800px',
                        url: "reportConfigInfo.html?id=" + data.id,
                        layerCallBack: function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'enable':
                case 'disable':
                    var actionText = obj.event === 'enable' ? '启用' : '禁用';
                    var newStatus = obj.event === 'enable' ? 1 : 0;
                    var confirmMsg = '确定要' + actionText + '此配置吗？';
                    
                    // 如果是启用操作，添加互斥提示
                    if (newStatus == 1) {
                        confirmMsg = '确定要启用此配置吗？<br/><span style="color: #ff5722;">注意：启用后将自动停用同类型的其他配置！</span>';
                    }
                    
                    layer.confirm(confirmMsg, {
                        icon: 3, 
                        title: '提示'
                    }, function(index) {
                        $.ajax({
                            url: ctx + 'config/report-schedule/status/' + data.id + '?enabled=' + (newStatus == 1),
                            type: 'PUT',
                            success: function(result) {
                                if (result.success) {
                                    layer.msg(result.message || actionText + '成功');
                                    table.reload(tableId);
                                } else {
                                    layer.msg('操作失败：' + (result.message || '未知错误'));
                                }
                                layer.close(index);
                            },
                            error: function() {
                                layer.msg('网络错误，请重试');
                                layer.close(index);
                            }
                        });
                    });
                    break;
                case 'delete':
                    layer.confirm('确认要删除该配置吗？', function (index) {
                        $.ajax({
                            url: ctx + 'config/report-schedule/delete/' + data.id,
                            type: 'POST',
                            success: function(result) {
                                if (result.code == 200) {
                                    layer.msg('删除成功');
                                    table.reload(tableId);
                                } else {
                                    layer.msg('删除失败：' + (result.message || '未知错误'));
                                }
                                layer.close(index);
                            },
                            error: function() {
                                layer.msg('网络错误，请重试');
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }
        });
    });
</script>
</body>
</html> 