<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../css/font-awesome.min.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
    <style>
        .layui-form .layui-form-label{
            width:110px;
            padding: 9px 5px;
        }
        .layui-form .layui-input-block{
            margin-left: 130px;
        }
    </style>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-form-item">
            <label class="layui-form-label required">用户姓名:</label>
            <div class="layui-input-block">
                <input type="text" name="realname" lay-filter="realname" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">用户账号:</label>
            <div class="layui-input-block">
                <input type="text" name="username" lay-filter="username" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" id="blockPwd">
            <label class="layui-form-label required">登录密码:</label>
            <div class="layui-input-block">
                <input type="password" name="password" lay-filter="password" lay-verify="required" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">性别:</label>
            <div class="layui-input-block">
                <select id="sex" name="sex"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">用户状态:</label>
            <div class="layui-input-block">
                <select id="status" name="status"></select>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createBy" lay-filter="createBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">创建时间:</label>
                    <div class="layui-input-block">
                        <input type="text" name="createTime" lay-filter="createTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新人:</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateBy" lay-filter="updateBy" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
            <div class="layui-col-xs6">
                <div class="layui-form-item">
                    <label class="layui-form-label">更新时间：</label>
                    <div class="layui-input-block">
                        <input type="text" name="updateTime" lay-filter="updateTime" autocomplete="off" disabled class="layui-input layui-disabled">
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="button" value="保存" class="layui-btn" id="btnSave" />
            <input type="button" value="关闭" class="layui-btn layui-btn-primary" id="btnClose" />
        </div>
    </div>
</div>


<script>
    $(document).ready(function(){
        initPage();
        $("#btnSave").click(function () {
            saveFun();
        });
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
    });

    function initPage(){
        $.operate.initDictDropDown("sex", "sex");
        $.operate.initDictDropDown("user_status", "status");

        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $("#blockPwd").hide();
            $.operate.postJson({
                url: "system/user/queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }

    function saveFun(){
        var postData = layui.form.val('inputForm');
        var id = $.common.getURLParameter("id");

        var url = "system/user/add";
        if($.common.isNotEmpty(id)){
            url = "system/user/edit";
            postData.id = id;
        }

        $.operate.postJson({
            url: url,
            data: JSON.stringify(postData),
            isShowMsg:true,
            callback: function(result){
                if(result.code==200){
                    $.modal.closeDrawer(result);
                }
            }
        });
    }
</script>
</body>
</html>