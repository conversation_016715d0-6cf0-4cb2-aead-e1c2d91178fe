<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>绑定工位信息</title>
    <link rel="stylesheet" href="../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../css/public.css" media="all">
    <link rel="stylesheet" href="../../css/font-awesome.min.css" media="all">
    <script src="../../js/base-config.js" charset="utf-8"></script>
    <script src="../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../js/common-util.js" charset="utf-8"></script>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-form-item">
            <label class="layui-form-label">当前账号：</label>
            <div class="layui-input-block">
                <input type="text" disabled name="username" class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item required">
            <label class="layui-form-label required">工位编号：</label>
            <div class="layui-input-block">
                <select name="pointNo" id="pointNo"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <input type="button" value="保存" class="layui-btn" lay-submit/>
                <input type="button" value="关闭" class="layui-btn layui-btn-primary" id="btnClose"/>
            </div>
        </div>
    </form>
</div>
<script>
    layui.use("form", function () {
        let $ = layui.jquery;
        let form = layui.form;
        $.operate.initDictDropDown("wms_point,point_name,point_no", "pointNo");
        $.operate.postJson({
            url: "pda/getPointNo",
            callback: function (result) {
                if (result.code == 200) {
                    layui.form.val('inputForm', result.data)
                }
            }
        });
        form.on("submit", function (data) {
            let url = "pda/bindingPoint";
            $.operate.postJson({
                url: url,
                data: JSON.stringify(data.field),
                isShowMsg: true,
                callback: function (result) {
                    if (result.code == 200) {
                        $.modal.closeDrawer(result);
                    }
                }
            });
        })
        $("#btnClose").click(function () {
            window.history.back();
        });
    })
</script>
</body>
</html>