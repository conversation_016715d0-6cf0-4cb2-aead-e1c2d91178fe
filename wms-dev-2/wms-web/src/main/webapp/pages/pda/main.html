<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>主页</title>
    <link rel="stylesheet" href="../../js/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="../../css/common.css" media="all">
    <link rel="stylesheet" href="../../css/font-awesome.min.css" media="all">
</head>
<body class="white-bg">
<div class="layui-row">
    <div class="layui-col-md6" style="padding:0px 0px 0px 0px;">
        <div style="border:1px #ccc solid;height:150px;background-color: #fff">
            <div style="background-color: #1391ff;height: 50px;line-height: 50px;color:#fff;padding-left: 15px;">
                <h2>扫码管理</h2>
            </div>
            <div class="layui-btn-container" style="text-align: center;">
                <button type="button" onclick="skip('materialBinding')" class="layui-btn">料箱物料绑定</button>
                <button type="button" onclick="skip('materialUnbinding')" class="layui-btn">料箱物料解绑</button>
                <button type="button" onclick="skip('trayBinding')" class="layui-btn">库位托盘绑定</button>
                <button type="button" onclick="skip('trayUnbinding')" class="layui-btn">库位托盘解绑</button>
            </div>
        </div>
    </div>
    <div class="layui-col-md6" style="padding:0px 0px 0px 0px">
        <div style="border:1px #ccc solid;height:150px;background-color: #fff">
            <div style="background-color: #1391ff;height: 50px;line-height: 50px;color:#fff;padding-left: 15px;">
                <h2>呼叫管理</h2>
            </div>
            <div class="layui-btn-container" style="text-align: center">
                <button type="button" onclick="skip('callRealBoxIn')" class="layui-btn">呼叫实箱入库</button>
                <button type="button" onclick="skip('callEmptyBoxIn')" class="layui-btn">呼叫空箱入库</button>
                <button type="button" onclick="skip('callHandlingTray')" class="layui-btn">呼叫AGV搬运托盘</button>
            </div>
            <div class="layui-btn-container" style="text-align: center">
                <button type="button" id="callEmptyBoxOut" class="layui-btn">呼叫空箱出库</button>
                <button type="button" id="bindingPoint" class="layui-btn">绑定呼叫工位</button>
            </div>
        </div>
    </div>
</div>
<script src="../../js/base-config.js" charset="utf-8"></script>
<script src="../../js/jquery.min.js" charset="utf-8"></script>
<script src="../../js/layui/layui.js" charset="utf-8"></script>
<script src="../../js/common-util.js" charset="utf-8"></script>
<script>
    layui.use(['form', 'table', 'echarts'], function () {
        var $ = layui.jquery,
            form = layui.form;
        $.operate.postJson({
            url: "pda/getPointNo",
            callback: function (result) {
                if (result.code == 200) {
                    username=result.data.username;
                }
            }
        })
        $("#callEmptyOut").click(function () {
            $.operate.postJson({
                url: "pda/callEmptyOut",
                callback: function (result) {
                    layer.msg(result.message)
                }
            })
        })
        $("#bindingPoint").click(function () {
            location.href = 'bindingPoint.html';
        })

    })
    var username;
    function skip(type) {
        android.skip(username,type);
    }

</script>
</body>
</html>