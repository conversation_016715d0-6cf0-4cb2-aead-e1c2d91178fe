<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../css/font-awesome.min.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
</head>
<body class="white-bg">
<div class="layui-block">
    <form class="layui-form" action="" lay-filter="inputForm">
        <div class="layui-form-item">
            <label class="layui-form-label required">字典编码：</label>
            <div class="layui-input-block">
                <input type="text" name="dictCode" lay-filter="dictCode" lay-verify="required" autocomplete="off" placeholder="请输入字典编码" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">字典名称:</label>
            <div class="layui-input-block">
                <input type="text" name="dictName" lay-filter="dictName" lay-verify="required" autocomplete="off" placeholder="请输入字典名称" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入内容" class="layui-textarea" name="description"></textarea>
            </div>
        </div>
    </form>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <input type="button" value="保存" class="layui-btn" id="btnSave" />
            <input type="button" value="关闭" class="layui-btn layui-btn-primary" id="btnClose" />
        </div>
    </div>
</div>


<script>
    layui.use(['form','tree'], function () {
        var form = layui.form;
        var tree = layui.tree;
    });


    $(document).ready(function(){
        $("#btnSave").click(function () {
            saveFun();
        });
        $("#btnClose").click(function () {
            $.modal.closeLayer();
        });
        initPage();
    });

    function saveFun(){
        var postData = layui.form.val('inputForm');
        var id = $.common.getURLParameter("id");

        var url = "sys/dict/add";
        if($.common.isNotEmpty(id)){
            url = "sys/dict/edit";
            postData.id = id;
        }

        $.operate.postJson({
            url: url,
            data: JSON.stringify(postData),
            isShowMsg:true,
            callback: function(result){
                if(result.code==200){
                    $.modal.closeDrawer(result);
                }
            }
        });
    }
    function initPage(){
        var id = $.common.getURLParameter("id");
        if($.common.isNotEmpty(id)){
            $.operate.postJson({
                url: "sys/dict/queryById",
                data: id,
                callback: function(result){
                    if(result.code==200){
                        layui.form.val('inputForm', result.data)
                    }
                }
            });
        }
    }
</script>
</body>
</html>