<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">编码：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_itemValue" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_itemText" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>

                    <button type="button" class="layui-btn layui-btn-primary layui-border-red" id="btnClose"><i class="layui-icon layui-icon-close"></i>关闭</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dictTable" lay-filter="dictTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script>
    layui.use(['form', 'table','miniTab'], function () {
        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab;

        var tableId = "dictTable";
        var urlPath = "sys/dictItem/"
        var dictTable = createTable({
            elem: tableId,
            url: urlPath +'list?dictId='+$.common.getURLParameter("dictId"),
            toolbar: "tableBar",
            cols: [[
                {field: 'itemValue', width: 180, title: '编码', sort: true},
                {field: 'itemText', width: 250, title: '名称', sort: true},
                {field: 'status', width: 120, title: '状态', sort: true,templet: function (d) {
                        if (d.status == 1) {
                            return '启用';
                        }
                        if (d.status == 1) {
                            return '停用';
                        }
                        return "--"
                    },
                },
                {field: 'sortOrder', width: 120, title: '显示排序', sort: true},
                {field: 'description', width: 400, title: '描述'},
                {title: '操作', width: 140, minWidth: 140, toolbar: '#tableTool', align: "center"}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('dictTable', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        $("#btnClose").click(function () {
            $.modal.closeTab();
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            switch(obj.event){
                case 'add':
                    $.modal.openDrawer({
                        title: '新增',
                        url: "dictItemInfo.html?dictId="+$.common.getURLParameter("dictId"),
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'refresh':
                    table.reload(tableId);
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑',
                        width: '800px',
                        url: "dictItemInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'delete':
                    layer.confirm('真的删除行么', function (index) {
                        $.operate.postJson({
                            url: urlPath +'delete',
                            data: data.id,
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }
        });
    });
</script>
</body>
</html>