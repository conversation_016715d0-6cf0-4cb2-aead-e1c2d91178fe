<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <script src="../../../js/base-config.js" charset="utf-8"></script>
    <script src="../../../js/jquery.min.js" charset="utf-8"></script>
    <script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
    <script src="../../../js/common-util.js" charset="utf-8"></script>
</head>
<body>

<div class="layuimini-container">

    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">账号：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_username" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">姓名：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_realname" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">用户状态:</label>
                    <div class="layui-input-block">
                        <select id="status" name="eq_status"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                </div>
            </div>
        </form>
    </div>
    <table class="layui-hide" id="dictTable" lay-filter="dictTable"></table>
</div>

<script type="text/html" id="tableBar">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="add">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>

<script type="text/html" id="tableTool">
    <a class="layui-btn layui-btn-normal layui-btn-xs layui-icon layui-icon-edit" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-icon layui-icon-key" lay-event="password">修改密码</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs layui-icon layui-icon-delete" lay-event="delete">删除</a>
</script>

<script>
    layui.use(['form', 'table','miniTab'], function () {
        var form = layui.form,
            table = layui.table,
            miniTab = layui.miniTab;

        var tableId = "dictTable";
        var urlPath = "system/user/";

        var dictTable = createTable({
            elem: tableId,
            url: urlPath +'list',
            toolbar: "tableBar",
            cols: [[
                {type: 'numbers'},
                {field: 'username', width: 150, title: '用户账号', sort: true},
                {field: 'realname', width: 150, title: '用户姓名', sort: true},
                {field: 'sex_dictText', width: 120, title: '性别', sort: true},
                {field: 'status_dictText', width: 120, title: '状态', sort: true},
                {field: 'createBy', width: 120, title: '创建人'},
                {field: 'createTime', width: 160, title: '创建时间'},
                {field: 'updateBy', width: 120, title: '更新人'},
                {field: 'updateTime', width: 160, title: '更新时间'},
                {title: '操作', width: 230, minWidth: 230, toolbar: '#tableTool', align: "center", fixed: 'right'}
            ]]
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('dictTable', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar('+tableId+')', function (obj) {
            switch(obj.event){
                case 'add':
                    $.modal.openDrawer({
                        title: '新增',
                        width: '800px',
                        url: "userInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'refresh':
                    table.reload(tableId);
                    break;
            };
        });

        table.on('tool('+tableId+')', function (obj) {
            var data = obj.data;

            switch(obj.event){
                case 'edit':
                    $.modal.openDrawer({
                        title: '编辑',
                        width: '800px',
                        url: "userInfo.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'password':
                    $.modal.openDrawer({
                        title: '修改密码',
                        width: '800px',
                        url: "userPassword.html?id="+data.id,
                        layerCallBack:function(returnVal){
                            table.reload(tableId);
                        }
                    });
                    break;
                case 'delete':
                    layer.confirm('真的删除行么', function (index) {
                        $.operate.postJson({
                            url: urlPath +'delete',
                            data: data.id,
                            isShowMsg:true,
                            callback: function(result){
                                if(result.code==200){
                                    table.reload(tableId);
                                }
                                layer.close(index);
                            }
                        });
                    });
                    break;
            }
        });

        $.operate.initDictDropDown("user_status", "status");
    });
</script>
</body>
</html>