<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
</head>
<body>



<div class="layuimini-container">
    <div class="layuimini-main">
        <form class="layui-form" action="" lay-filter="inputForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">搜索日志：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="lk_logContent" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">创建时间：</label>
                    <div class="layui-input-inline" style="width: 190px;">
                        <input type="text" name="ge_createTime" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline" style="width: 190px;">
                        <input type="text" name="le_createTime" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn layui-btn-primary layui-border-green" lay-submit lay-filter="data-search-btn"><i class="layui-icon layui-icon-search"></i>搜索</button>
                </div>
            </div>
        </form>
    </div>

    <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
</div>
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="refresh">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>

<script src="../../../js/base-config.js" charset="utf-8"></script>
<script src="../../../js/jquery.min.js" charset="utf-8"></script>
<script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
<script src="../../../js/common-util.js" charset="utf-8"></script>
<script>
    layui.use(['form', 'table'], function () {
        layui.laydate.render({
            elem: 'input[name="ge_createTime"],input[name="le_createTime"]'
            ,type: 'datetime'
        });
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        var tableObj = table.render({
            elem: '#currentTableId',
            url: ctx +'system/loginLog/list',
            method:'post',
            date:{searchParams: {logType:1}},
            contentType:'application/json',
            toolbar: '#toolbarDemo',
            parseData:function(result){
                return {
                    "code": (result.code ==200 ? '0': result.code),
                    "data": result.data,
                    "count": result.total
                }
            },
            cols: [[
                {field: 'logContent', width: 200, title: '日志内容'},
                {field: 'userid', width: 200, title: '操作人id'},
                {field: 'username', width: 200, title: '操作用户名称'},
                {field: 'ip', width: 200, title: 'IP'},
                {field: '', width: 200, title: '耗时'},
                {field: 'logType_dictText', width: 200, title: '日志类型'},
                {field: 'createTime', width: 200, title: '创建时间'}
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                },
                where: {
                    searchParams: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            switch(obj.event){
                case 'add':
                    $.modal.openDrawer({
                        title: '新增',
                        url: "dollyInfo.html",
                        layerCallBack:function(returnVal){
                            table.reload('currentTableId');
                        }
                    });
                    break;
                case 'refresh':
                    table.reload('currentTableId');
                    break;
            };
        });

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {

                $.modal.openDrawer({
                    title: '修改菜单',
                    url: "dollyInfo.html?id="+data.id,
                    layerCallBack:function(returnVal){
                        table.reload('currentTableId');
                    }
                });
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除行么', function(index){
                    $.operate.postJson({
                        url: 'system/dolly/delete',
                        data: data.id,
                        isShowMsg:true,
                        callback: function(result){
                            if(result.code==200){
                                table.reload('currentTableId');
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });

    });
</script>
</body>
</html>