<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>menu</title>
    <link rel="stylesheet" href="../../../lib/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../../css/public.css" media="all">
    <link rel="stylesheet" href="../../../css/font-awesome.min.css" media="all">
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div>
            <div class="layui-btn-group">
                <button class="layui-btn layui-btn-sm" id="btn-expand">全部展开</button>
                <button class="layui-btn layui-btn-sm layui-btn-normal" id="btn-fold">全部折叠</button>
            </div>
            <table id="menu-table" class="layui-table" lay-filter="menu-table"></table>
        </div>
    </div>
</div>
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-normal"  lay-event="btnAddClick">
            <i class="layui-icon">&#xe654;</i>增加
        </button>
        <button type="button" class="layui-btn layui-btn-sm  layui-btn-primary"  lay-event="btnRefreshClick">
            <i class="layui-icon">&#xe9aa</i>刷新
        </button>
    </div>
</script>
<!-- 操作列 -->
<script type="text/html" id="auth-state">
    {{# if(d.menuType != "F"){ }}
    <a class="layui-btn layui-btn-primary layui-border-blue layui-btn-xs" lay-event="addChild">增加下级</a><span lay-separator="|"></span>
    {{# }else{ }}
    <a class="layui-btn layui-btn-primary layui-border-blue layui-btn-xs layui-btn-disabled" >增加下级</a><span lay-separator="|"></span>
    {{# } }}
    <a class="layui-btn layui-btn-xs" lay-event="more">更多 <i class="layui-icon layui-icon-down"></i></a>
</script>

<script src="../../../js/base-config.js" charset="utf-8"></script>
<script src="../../../js/jquery.min.js" charset="utf-8"></script>
<script src="../../../lib/layui-v2.6.8/layui.js" charset="utf-8"></script>
<script src="../../../js/common-util.js" charset="utf-8"></script>
<script>


$(document).ready(function(){
    createTreeTable()
});

function createTreeTable(){
    layui.use(['table', 'treetable','form','dropdown'], function () {
        var table = layui.table;
        var treetable = layui.treetable;
        var form = layui.form;
        var dropdown = layui.dropdown;

        var treetable = layui.treetable;
        // 渲染表格
        treetable.render({
            treeColIndex: 1,
            treeSpid: 0,
            treeIdName: 'menuId',
            treePidName: 'parentId',
            elem: '#menu-table',
            data:[],
            toolbar: '#toolbarDemo',
            // height: 'full-100',
            page: false,
            cols: [[
                {type: 'numbers'},
                {field: 'menuName', minWidth: 200, title: '权限名称'},
                {field: 'url', title: '菜单url'},
                {field: 'perms', title: '权限标识'},
                {field: 'orderNum', width: 80, align: 'center', title: '排序号'},
                {field: 'menuType', width: 80, align: 'center', templet: function (d) {
                        if (d.menuType == "M") {
                            return '<span class="layui-badge layui-bg-yellow">目录</span>';
                        }
                        if (d.menuType == "C") {
                            return '<span class="layui-badge layui-bg-blue">菜单</span>';
                        }
                        if (d.menuType == "F") {
                            return '<span class="layui-badge layui-bg-gray">按钮</span>';
                        }
                    }, title: '类型'
                },
                {templet: '#auth-state', width:150, minWidth: 150, align: 'center', title: '操作'}
            ]],
            done: function () {
                layer.closeAll('loading');
            }
        });

        $('#btn-expand').click(function () {
            treetable.expandAll('#menu-table');
        });

        $('#btn-fold').click(function () {
            treetable.foldAll('#menu-table');
        });

        var refreshTable = function(){
            $.operate.post(
                "system/menu/list",
                null,
                function(result){
                    treetable.reload(result.data)
                }
            )
        }
        refreshTable();
        //头工具栏事件
        table.on('toolbar(menu-table)', function(obj){
            switch(obj.event){
                case 'btnAddClick':
                    $.modal.openDrawer({
                        title: '新增菜单',
                        url: "menuInfo.html",
                        layerCallBack:function(returnVal){
                            refreshTable();
                        }
                    });
                    break;
                case 'btnRefreshClick':
                    refreshTable();
                    break;
            };
        });

        //监听工具条
        table.on('tool(menu-table)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            var that = this;

            if (layEvent === 'addChild') {
                $.modal.openDrawer({
                    title: '新增菜单',
                    url: "menuInfo.html?parentId="+data.menuId,
                    layerCallBack:function(returnVal){
                        refreshTable();
                    }
                });
            } else if(obj.event === 'more'){
                //更多下拉菜单
                dropdown.render({
                    elem: that
                    ,show: true //外部事件触发即显示
                    ,data: [{
                        title: '<i class="layui-icon">&#xe642;</i>修改',
                        id: 'edit'
                    }, {
                        type: '-' //分割线
                    }, {
                        title: '<i class="layui-icon">&#xe640;</i>删除',
                        id: 'delete'
                    }]
                    ,click: function(event, othis){
                        //根据 id 做出不同操作
                        if(event.id =="edit"){
                            $.modal.openDrawer({
                                title: '修改菜单',
                                url: "menuInfo.html?menuId="+data.menuId,
                                layerCallBack:function(returnVal){
                                    refreshTable();
                                }
                            });
                        }else if(event.id === 'delete'){
                            layer.confirm('真的删除行么', function(index){
                                $.operate.postJson({
                                    url: 'system/menu/delete',
                                    data: data.id,
                                    isShowMsg:true,
                                    callback: function(result){
                                        if(result.code==200){
                                            refreshTable();
                                        }
                                        layer.close(index);
                                    }
                                });
                            });
                        }
                    }
                    ,align: 'right' //右对齐弹出（v2.6.8 新增）
                    ,style: 'box-shadow: 1px 1px 10px rgb(0 0 0 / 12%);' //设置额外样式
                });
            }
        });
    });
}

</script>
</body>
</html>