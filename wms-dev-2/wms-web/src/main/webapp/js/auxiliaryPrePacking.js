layui.use(['form', 'table', 'element'], function () {
    var $ = layui.jquery,
        form = layui.form,
        table = layui.table,
        element = layui.element;

    // 全局变量存储PO数据
    var allPoMaterialsData = null;
    
    // 标志变量，避免重复绑定事件
    var poEventsbound = false;
    
    // 标志变量，避免重复调用getPoList
    var poListLoaded = false;
    
    // 标志变量，标识是否为本地数据更新操作
    var isLocalDataUpdate = false;
    
    // 添加存储托盘初始数据的变量
    var palletInitialData = null;
    
    // 添加存储料箱初始数据的变量
    var boxInitialData = null;
    
    // 严格验证入库数量的函数
    function validateInboundQuantities(tableData, containerType) {
        console.log('开始验证入库数量:', {
            tableData: tableData.length + '条记录',
            containerType: containerType
        });
        
        var validData = [];
        var errorMessages = [];
        
        // 遍历所有表格数据进行验证
        for (var i = 0; i < tableData.length; i++) {
            var item = tableData[i];
            var rowNumber = i + 1;
            
            // 检查是否有输入入库数量
            if (!item.actualInboundQty || item.actualInboundQty.toString().trim() === '') {
                // 如果没有输入入库数量，跳过这条记录（不算错误）
                continue;
            }
            
            // 验证入库数量是否为有效数值
            var inboundQty = parseFloat(item.actualInboundQty);
            if (isNaN(inboundQty)) {
                errorMessages.push('第' + rowNumber + '行：入库数量"' + item.actualInboundQty + '"不是有效数值');
                continue;
            }
            
            // 验证入库数量不能为负数或零
            if (inboundQty <= 0) {
                errorMessages.push('第' + rowNumber + '行：入库数量必须大于0，当前值为' + inboundQty);
                continue;
            }
            
            // 验证入库数量不能为小数（根据业务需求，如果允许小数可以移除此验证）
            if (inboundQty % 1 !== 0) {
                errorMessages.push('第' + rowNumber + '行：入库数量必须为整数，当前值为' + inboundQty);
                continue;
            }
            
            // 对于款式属性，验证入库数量不能大于库存数量
            var originalQuantity = parseFloat(item.originalQuantity) || 0;
            if (inboundQty > originalQuantity) {
                var materialInfo = '';
                if (item.materialCode) {
                    materialInfo = '(物料编码: ' + item.materialCode + ')';
                }
                if (item.styleColor) {
                    materialInfo += '(款式颜色: ' + item.styleColor + ')';
                }
                errorMessages.push('第' + rowNumber + '行：入库数量(' + inboundQty + ')不能大于库存数量(' + originalQuantity + ')' + materialInfo);
                continue;
            }
            
            // 验证必要的基础数据字段
            if (!item.materialCode || item.materialCode.trim() === '') {
                errorMessages.push('第' + rowNumber + '行：物料编码不能为空');
                continue;
            }
            
            if (!item.contractNo || item.contractNo.trim() === '') {
                errorMessages.push('第' + rowNumber + '行：合约号不能为空');
                continue;
            }
            
            if (!item.itemNo || item.itemNo.trim() === '') {
                errorMessages.push('第' + rowNumber + '行：款号不能为空');
                continue;
            }
            
            // 验证通过，添加到有效数据列表
            validData.push(item);
        }
        
        // 检查是否有任何有效数据
        if (validData.length === 0 && errorMessages.length === 0) {
            return {
                isValid: false,
                errorMessage: '请先输入要入库的数量',
                validData: []
            };
        }
        
        // 如果有错误信息，返回第一个错误
        if (errorMessages.length > 0) {
            return {
                isValid: false,
                errorMessage: errorMessages[0], // 只显示第一个错误，避免信息过多
                validData: [],
                allErrors: errorMessages // 保留所有错误信息以备调试
            };
        }
        
        console.log('验证通过:', {
            总记录数: tableData.length,
            有效记录数: validData.length,
            containerType: containerType
        });
        
        return {
            isValid: true,
            errorMessage: '',
            validData: validData
        };
    }

    // 单个入库数量验证函数（用于实时编辑验证）
    function validateSingleInboundQty(value, rowData, containerType) {
        console.log('验证单个入库数量:', {
            value: value,
            materialCode: rowData.materialCode,
            containerType: containerType
        });
        
        // 检查是否为空
        if (!value || value.toString().trim() === '') {
            return {
                isValid: false,
                errorMessage: '入库数量不能为空',
                correctedValue: '',
                validatedValue: ''
            };
        }
        
        // 验证是否为有效数值
        var numericValue = parseFloat(value);
        if (isNaN(numericValue)) {
            return {
                isValid: false,
                errorMessage: '入库数量必须是有效数值，当前输入: "' + value + '"',
                correctedValue: '',
                validatedValue: ''
            };
        }
        
        // 验证不能为负数或零
        if (numericValue <= 0) {
            return {
                isValid: false,
                errorMessage: '入库数量必须大于0，当前值: ' + numericValue,
                correctedValue: '',
                validatedValue: ''
            };
        }
        
        // 验证必须为整数（根据业务需求）
        if (numericValue % 1 !== 0) {
            return {
                isValid: false,
                errorMessage: '入库数量必须为整数，当前值: ' + numericValue,
                correctedValue: Math.floor(numericValue), // 提供修正值
                validatedValue: Math.floor(numericValue)
            };
        }
        
        // 验证不能超过库存数量（款式属性）
        var originalQuantity = parseFloat(rowData.originalQuantity) || 0;
        if (numericValue > originalQuantity) {
            var materialInfo = '';
            if (rowData.materialCode) {
                materialInfo = ' (物料: ' + rowData.materialCode + ')';
            }
            if (rowData.styleColor) {
                materialInfo += ' (颜色: ' + rowData.styleColor + ')';
            }
            
            return {
                isValid: false,
                errorMessage: '入库数量(' + numericValue + ')不能超过库存数量(' + originalQuantity + ')' + materialInfo,
                correctedValue: originalQuantity, // 提供修正值为库存数量
                validatedValue: originalQuantity
            };
        }
        
        // 验证通过
        return {
            isValid: true,
            errorMessage: '',
            correctedValue: numericValue,
            validatedValue: numericValue
        };
    }

    // 托盘容量检查函数
    function checkPalletCapacity(inboundQty, rowData) {
        // 简化的实时容量检查
        if (palletInitialData && palletInitialData.boxProperty) {
            var palletCapacity = parseFloat(palletInitialData.boxProperty.capacity) || 0;
            var currentUsedQty = parseFloat(palletInitialData.boxProperty.usedCapacity) || 0;
            
            if (palletCapacity > 0) {
                var tableData = layui.table.getData('auxPrePack_palletMaterialTableId');
                var totalPlannedInbound = tableData.reduce(function(sum, item) {
                    return sum + (parseFloat(item.actualInboundQty) || 0);
                }, 0);
                
                var usageRate = ((currentUsedQty + totalPlannedInbound) / palletCapacity * 100).toFixed(1);
                
                if ((currentUsedQty + totalPlannedInbound) > palletCapacity) {
                    $.modal.msgWarning('当前计划入库数量超过托盘容量！' +
                        '\n托盘容量: ' + palletCapacity +
                        '\n已使用: ' + currentUsedQty +
                        '\n计划入库: ' + totalPlannedInbound +
                        '\n使用率: ' + usageRate + '%');
                } else if (usageRate > 80) {
                    // 当使用率超过80%时给出提醒
                    $.modal.msgSuccess('托盘使用率: ' + usageRate + '%');
                }
            }
        }
    }

    // 通用的parseData处理函数
    function createParseDataHandler(config) {
        return function(res) {
            try {
                // 统一的数据结构验证
                var isValidData = res && res.success && res.data && 
                                res.data.boxProperty && res.data.boxProperty.length > 0 && 
                                res.data.boxProperty[0].boxItemList;
                
                if (isValidData) {
                    var boxProperty = res.data.boxProperty[0];
                    var boxItemList = boxProperty.boxItemList;
                    
                    // 更新UI显示元素
                    updateUIElements(config.uiElements, boxProperty, boxItemList, true);
                    
                    // 处理特殊逻辑
                    if (config.specialHandler) {
                        config.specialHandler(boxProperty, boxItemList);
                    }
                    
                    // 返回标准格式数据
                    return {
                        "code": res.code === 200 || res.code == 200 ? 0 : res.code,
                        "msg": res.message,
                        "count": boxItemList.length,
                        "data": boxItemList
                    };
                } else {
                    // 数据无效时的处理
                    updateUIElements(config.uiElements, null, null, false);
                    
                    return {
                        "code": res && res.code ? (res.code === 200 || res.code == 200 ? 0 : res.code) : 500,
                        "msg": res && res.message ? res.message : "数据格式错误或无数据",
                        "count": 0,
                        "data": []
                    };
                }
            } catch (error) {
                console.error('parseData处理异常:', error);
                updateUIElements(config.uiElements, null, null, false);
                
                return {
                    "code": 500,
                    "msg": "数据处理异常: " + error.message,
                    "count": 0,
                    "data": []
                };
            }
        };
    }
    
    // 更新UI元素的通用函数
    function updateUIElements(uiConfig, boxProperty, boxItemList, hasData) {
        if (!uiConfig) return;
        
        // 更新容器显示/隐藏
        if (uiConfig.container) {
            var containerElement = $(uiConfig.container);
            if (hasData) {
                containerElement.attr('class', 'layui-show').removeClass('layui-hide');
            } else {
                containerElement.attr('class', 'layui-hide').removeClass('layui-show');
            }
        }
        
        // 更新各种显示字段
        if (uiConfig.fields && Array.isArray(uiConfig.fields)) {
            uiConfig.fields.forEach(function(field) {
                var element = $(field.selector);
                var value = '--';
                
                if (hasData && boxProperty) {
                    switch (field.type) {
                        case 'boxNo':
                            value = boxProperty.boxNo || '--';
                            break;
                        case 'containerType':
                            if (boxProperty.containerType === 1) {
                                value = '料箱';
                            } else if (boxProperty.containerType === 2) {
                                value = '托盘';
                            } else {
                                value = '--';
                            }
                            break;
                        case 'originalQuantity':
                            value = (boxItemList && boxItemList.length > 0) ? (boxItemList[0].originalQuantity || '--') : '--';;
                            break;
                        case 'pendingQty':
                            value = (boxItemList && boxItemList.length > 0) ? (boxItemList[0].actualInboundQty || '--') : '--';;
                            break;
                        case 'capacity':
                            value = boxProperty.capacity || '--';
                            break;
                        case 'itemNo':
                            value = (boxItemList && boxItemList.length > 0) ? (boxItemList[0].itemNo || '--') : '--';
                            break;
                        case 'contractNo':
                            value = (boxItemList && boxItemList.length > 0) ? (boxItemList[0].contractNo || '--') : '--';
                            break;
                        case 'containerNo':
                            value = boxProperty.container_no || '--';
                            break;
                        case 'custom':
                            if (field.getValue) {
                                value = field.getValue(boxProperty, boxItemList);
                            }
                            break;
                        default:
                            value = '--';
                    }
                }
                
                element.text(value);
            });
        }
    }

    // 初始化料箱信息表格
    var boxInfoTable = table.render({
        height: 'full-200',
        elem: '#auxPrePack_boxInfoTableId',
        page: true,
        toolbar: '#auxPrePack_boxInfoToolbar',
        defaultToolbar: ['filter'],
        url:ctx + '/auxiliary/inbound/queryPrebox',
        method: 'post',
        contentType: 'application/json',
        dataType : "json",
       
        where: {
            searchParams: {
                stockBatchId: localStorage.getItem('stockBatchNo'),
                containerType: 1,
                materialProperty: 0,
              }
        },
        cols: [[
            {field: 'gridId', title: '格号', width: 80, sort: true},
            {field: 'contractNo', title: '合约号', width: 120},
            {field: 'itemNo', title: '款号', width: 120},
            {field: 'materialCode', title: '物料编码', width: 120},
            {field: 'materialName', title: '物料名称', width: 150},
            {field: 'materialModel', title: '物料规格', width: 100},
            {field: 'materialColor', title: '物料颜色', width: 100},
           // {field: 'originalQuantity', title: '原数量', width: 100},
             {field: 'originalQuantity', title: '待入库数量', width: 120}, 
            {field: 'actualInboundQty', title: '实际入库数量', width: 120, edit: 'text'}
        ]],
        limits: [10, 15, 20, 25, 50, 100],
        limit: 15,
        text: {
            none: '暂无相关数据'
        },
        parseData: createParseDataHandler({
            uiElements: {
                container: '#auxPrePack_boxInfoColla',
                fields: [
                    { selector: '#auxPrePack_boxNoDisplay', type: 'boxNo' },
                    { selector: '#auxPrePack_boxTypeDisplay', type: 'containerType' }
                ]
            }
        })
    });

    // 初始化料箱款式属性表格
    var boxAttributeTable = table.render({
        elem: '#auxPrePack_boxMaterialTableId',
        url:ctx + '/auxiliary/inbound/queryPrebox',
        method: 'post',
        contentType: 'application/json',
        dataType : "json",
        height: 'full-200',
        parseData: function(res) {
            try {
                // 统一的数据结构验证
                var isValidData = res && res.success && res.data && 
                                res.data.boxProperty && res.data.boxProperty.length > 0 && 
                                res.data.boxProperty[0].boxItemList;
                
                if (isValidData) {
                    var boxProperty = res.data.boxProperty[0];
                    var boxItemList = boxProperty.boxItemList;
                    
                    // 更新UI显示元素（只更新容器信息，不显示具体数据）
                    updateUIElements({
                        container: '#auxPrePack_boxMaterialInfoColla',
                        fields: [
                            { selector: '#auxPrePack_boxMaterialInfoBoxNo', type: 'boxNo' },
                            { selector: '#auxPrePack_boxMaterialInfoContainerType', type: 'containerType' },
                            { selector: '#auxPrePack_boxMaterialInfoPendingQty', type: 'originalQuantity' }
                        ]
                    }, boxProperty, boxItemList, true);
                    
                    // 存储初始数据供后续使用（支持多条记录）
                    boxInitialData = {
                        boxProperty: boxProperty,
                        boxItemList: boxItemList // 保持完整的数组
                    };
                    
                    console.log('存储料箱初始数据:', boxInitialData.boxItemList.length + '条记录', boxInitialData);
                    
                    // 提取合约号列表（支持多条记录）
                    var contractNoList = [];
                    if (boxItemList && boxItemList.length > 0) {
                        for (var i = 0; i < boxItemList.length; i++) {
                            var contractNo = boxItemList[i].contractNo;
                            if (contractNo && contractNoList.indexOf(contractNo) === -1) {
                                contractNoList.push(contractNo);
                            }
                        }
                        console.log('提取到合约号列表:', contractNoList);
                        
                        // 只在第一次或者数据未加载时调用获取PO列表，且不是本地数据更新操作
                        if ((!poListLoaded || !allPoMaterialsData) && !isLocalDataUpdate) {
                            getPoList(contractNoList);
                        }
                    }
                    
                    // 不返回数据给表格渲染，让表格保持空状态
                    return {
                        "code": 0,
                        "msg": "数据加载完成，请选择PO号查看详细信息",
                        "count": 0,
                        "data": []
                    };
                } else {
                    // 数据无效时的处理
                    updateUIElements({
                        container: '#auxPrePack_boxMaterialInfoColla',
                        fields: [
                            { selector: '#auxPrePack_boxMaterialInfoBoxNo', type: 'boxNo' },
                            { selector: '#auxPrePack_boxMaterialInfoContainerType', type: 'containerType' },
                            { selector: '#auxPrePack_boxMaterialInfoPendingQty', type: 'originalQuantity' }
                        ]
                    }, null, null, false);
                    
                    // 清空初始数据
                    boxInitialData = null;
                    
                    return {
                        "code": res && res.code ? (res.code === 200 || res.code == 200 ? 0 : res.code) : 500,
                        "msg": res && res.message ? res.message : "数据格式错误或无数据",
                        "count": 0,
                        "data": []
                    };
                }
            } catch (error) {
                console.error('料箱parseData处理异常:', error);
                updateUIElements({
                    container: '#auxPrePack_boxMaterialInfoColla',
                    fields: [
                        { selector: '#auxPrePack_boxMaterialInfoBoxNo', type: 'boxNo' },
                        { selector: '#auxPrePack_boxMaterialInfoContainerType', type: 'containerType' },
                        { selector: '#auxPrePack_boxMaterialInfoPendingQty', type: 'originalQuantity' }
                    ]
                }, null, null, false);
                
                // 清空初始数据
                boxInitialData = null;
                
                return {
                    "code": 500,
                    "msg": "数据处理异常: " + error.message,
                    "count": 0,
                    "data": []
                };
            }
        },
        where: {
            searchParams: {
                stockBatchId: localStorage.getItem('stockBatchNo'),
                containerType: 1,
                materialProperty: 1,
              },
            pageParams: {
                pageSize: 10,
                pageNum: 1
            }
        },
        cols: [[
            {field: 'gridId', title: '格号', width: 80},
            {field: 'poNo', title: 'PO号', width: 120},
            {field: 'contractNo', title: '合约号', width: 120},
            {field:'itemNo', title: '款号', width: 120},
            {field:'styleColor', title: '款式颜色', width: 100},
            {field:'materialCode', title: '物料编码', width: 120},
            {field:'materialName', title: '物料名称', width: 150},
            {field:'materialModel', title: '物料规格', width: 100},
            {field:'materialColor', title: '物料颜色', width: 100},
            {field:'originalQuantity', title: '库存数量', width: 100},
            {field: 'actualInboundQty', title: '实际入库数量', width: 100, edit: 'text', event: 'editInboundQty'}
        ]],
        text: {
            none: '暂无相关数据，请先选择PO号'
        }
    })

    //初始化托盘自身属性表格
    var boxSelfTable = table.render({
        elem: '#auxPrePack_palletInfoTableId',
        url:ctx + '/auxiliary/inbound/queryPrebox',
        method: 'post',
        page: true,
        contentType: 'application/json',
        dataType : "json",
        height: 'full-200',
        defaultToolbar: ['filter', 'print', 'exports'],
        parseData: createParseDataHandler({
            uiElements: {
                container: '#auxPrePack_palletInfoColla',
                fields: [
                    { selector: '#auxPrePack_palletContractNoDisplay', type: 'containerNo' },
                    { selector: '#auxPrePack_palletTypeDisplay', type: 'containerType' }
                ]
            }
        }),
        where: {
            searchParams: {
                stockBatchId: localStorage.getItem('stockBatchNo'),
                containerType: 2,
                materialProperty: 0,
              },
            pageParams: {
                pageSize: 10,
                pageNum: 1
            }
        },
        cols: [[
            {field: 'contractNo', title: '合约号', width: 120},
            {field: 'itemNo', title: '款号', width: 120},
            {field: 'materialCode', title: '物料编码', width: 120},
            {field: 'materialName', title: '物料名称', width: 150},
            {field:'materialModel', title: '物料规格', width: 100},
            {field:'materialColor', title: '物料颜色', width: 100},
            //{field: 'originalQuantity', title: '原数量', width: 100},
            {field: 'originalQuantity', title: '待入库数量', width: 120}, 
            {field: 'actualInboundQty', title: '实际入库数量', width: 120, edit: 'text'}
        ]],
        limits: [10, 15, 20, 25, 50, 100],
        limit: 15,
        text: {
            none: '暂无相关数据'
        } 
    })
    // 初始化托盘款式属性表格
    var boxMaterialTable = table.render({
        height: 'full-200',
        elem: '#auxPrePack_palletMaterialTableId',
        url:ctx + '/auxiliary/inbound/queryPrebox',
        method: 'post',
        page: true,
        contentType: 'application/json',
        dataType : "json",
        defaultToolbar: ['filter', 'print', 'exports'],
        parseData: function(res) {
            try {
                // 统一的数据结构验证
                var isValidData = res && res.success && res.data && 
                                res.data.boxProperty && res.data.boxProperty.length > 0 && 
                                res.data.boxProperty[0].boxItemList;
                
                if (isValidData) {
                    var boxProperty = res.data.boxProperty[0];
                    var boxItemList = boxProperty.boxItemList;
                    
                    // 更新UI显示元素（只更新容器信息，不显示具体数据）
                    updateUIElements({
                        container: '#auxPrePack_palletMateriaInfoColla',
                        fields: [
                            { selector: '#auxPrePack_palletMaterialNoDisplay', type: 'boxNo' },
                            { selector: '#auxPrePack_palletMaterialTypeDisplay', type: 'containerType' },
                            { selector: '#auxPrePack_palletMaterialStyleNoDisplay', type: 'itemNo' },
                            { selector: '#auxPrePack_palletMaterialContractNoDisplay', type: 'contractNo' },
                            { selector: '#auxPrePack_palletMaterialCapacityDisplay', type: 'capacity' },
                            { selector: '#auxPrePack_palletMaterialPendingQty', type: 'pendingQty' }
                        ]
                    }, boxProperty, boxItemList, true);
                    
                    // 存储初始数据供后续使用（支持多条记录）
                    palletInitialData = {
                        boxProperty: boxProperty,
                        boxItemList: boxItemList // 保持完整的数组
                    };
                    
                    console.log('存储托盘初始数据:', palletInitialData.boxItemList.length + '条记录', palletInitialData);
                    
                    // 提取合约号列表
                    var contractNoList = [];
                    if (boxItemList && boxItemList.length > 0) {
                        for (var i = 0; i < boxItemList.length; i++) {
                            var contractNo = boxItemList[i].contractNo;
                            if (contractNo && contractNoList.indexOf(contractNo) === -1) {
                                contractNoList.push(contractNo);
                            }
                        }
                        // 只在第一次或者数据未加载时调用获取PO列表，且不是本地数据更新操作
                        if ((!poListLoaded || !allPoMaterialsData) && !isLocalDataUpdate) {
                            getPoList(contractNoList);
                        }
                    }
                    
                    // 不返回数据给表格渲染，让表格保持空状态
                    return {
                        "code": 0,
                        "msg": "数据加载完成，请选择PO号查看详细信息",
                        "count": 0,
                        "data": []
                    };
                } else {
                    // 数据无效时的处理
                    updateUIElements({
                        container: '#auxPrePack_palletMateriaInfoColla',
                        fields: [
                            { selector: '#auxPrePack_palletMaterialNoDisplay', type: 'boxNo' },
                            { selector: '#auxPrePack_palletMaterialTypeDisplay', type: 'containerType' },
                            { selector: '#auxPrePack_palletMaterialStyleNoDisplay', type: 'itemNo' },
                            { selector: '#auxPrePack_palletMaterialContractNoDisplay', type: 'contractNo' },
                            { selector: '#auxPrePack_palletMaterialCapacityDisplay', type: 'capacity' }
                        ]
                    }, null, null, false);
                    
                    // 清空初始数据
                    palletInitialData = null;
                    
                    return {
                        "code": res && res.code ? (res.code === 200 || res.code == 200 ? 0 : res.code) : 500,
                        "msg": res && res.message ? res.message : "数据格式错误或无数据",
                        "count": 0,
                        "data": []
                    };
                }
            } catch (error) {
                console.error('parseData处理异常:', error);
                updateUIElements({
                    container: '#auxPrePack_palletMateriaInfoColla',
                    fields: [
                        { selector: '#auxPrePack_palletMaterialNoDisplay', type: 'boxNo' },
                        { selector: '#auxPrePack_palletMaterialTypeDisplay', type: 'containerType' },
                        { selector: '#auxPrePack_palletMaterialStyleNoDisplay', type: 'itemNo' },
                        { selector: '#auxPrePack_palletMaterialContractNoDisplay', type: 'contractNo' },
                        { selector: '#auxPrePack_palletMaterialCapacityDisplay', type: 'capacity' }
                    ]
                }, null, null, false);
                
                // 清空初始数据
                palletInitialData = null;
                
                return {
                    "code": 500,
                    "msg": "数据处理异常: " + error.message,
                    "count": 0,
                    "data": []
                };
            }
        },
        where: {
            searchParams: {
                stockBatchId: localStorage.getItem('stockBatchNo'),
                containerType: 2,
                materialProperty: 1,
              },
            pageParams: {
                pageSize: 10,
                pageNum: 1
            }
        },
        cols: [[
            
             {field: 'poNo', title: 'PO号', width: 120},
            {field: 'contractNo', title: '合约号', width: 120},
            {field: 'itemNo', title: '款号', width: 120},
            {field: 'styleColor', title: '款式颜色', width: 100},
            {field: 'materialCode', title: '物料编码', width: 120},
            {field: 'materialName', title: '物料名称', width: 150},
            {field: 'materialModel', title: '物料规格', width: 100},
            {field: 'materialColor', title: '物料颜色', width: 100},
            {field: 'originalQuantity', title: '库存数量', width: 100},
            {field: 'actualInboundQty', title: '实际入库数量', width: 100, edit: 'text', event: 'editInboundQty'}
        ]],
        limits: [10, 15, 20, 25, 50, 100],
        limit: 15,
        text: {
            none: '暂无相关数据，请先选择PO号'
        }
    });
    
    // 绑定料箱自身属性刷新按钮事件
    $('#auxPrePack_btnBoxRefresh').on('click', function() {
        boxInfoTable.reload();
    });

    //绑定托盘自身属性刷新按钮事件
    $('#auxPrePack_btnPalletRefresh').on('click', function() {
        boxSelfTable.reload();
    });

    //绑定料箱款式属性刷新按钮事件
    $('#auxPrePack_btnBoxMaterialRefresh').on('click', function() {
        // 刷新时清空累积数据，重新加载初始数据
        boxInitialData = null;
        poListLoaded = false;
        allPoMaterialsData = null;
        
        boxAttributeTable.reload();
        
        // 清空PO选择框
        $('#auxPrePack_boxMaterialPoSelect').empty().append('<option value="">请选择PO号</option>');
        form.render('select');
        
        $.modal.msgSuccess('已刷新料箱款式属性数据');
    });

    //绑定托盘款式属性刷新按钮事件
    $('#auxPrePack_btnPalletMaterialRefresh').on('click', function() {
        // 刷新时清空累积数据，重新加载初始数据
        palletInitialData = null;
        poListLoaded = false;
        allPoMaterialsData = null;
        
        boxMaterialTable.reload();
        
        // 清空PO选择框
        $('#auxPrePack_palletMaterialPoSelect').empty().append('<option value="">请选择PO号</option>');
        form.render('select');
        
        $.modal.msgSuccess('已刷新托盘款式属性数据');
    });




    // 绑定自身属性料箱绑定入库按钮事件
    $('#auxPrePack_btnBoxBind').on('click', function() {
        // 获取表格数据
        var tableData = layui.table.getData("auxPrePack_boxInfoTableId");
        
        if (!tableData || tableData.length === 0) {
            $.modal.msgWarning('暂无料箱数据可以入库');
            return;
        }
        
        // 检查是否有输入入库数量的数据，并验证数据
        var validData = [];
        var hasInputData = false;
        
        for (var i = 0; i < tableData.length; i++) {
            var item = tableData[i];
            
            // 检查是否有输入入库数量
            if (item.actualInboundQty && item.actualInboundQty.toString().trim() !== '') {
                hasInputData = true;
                
                // 验证入库数量
                var inboundQty = parseFloat(item.actualInboundQty);
                if (isNaN(inboundQty) || inboundQty <= 0) {
                    $.modal.msgError('第' + (i + 1) + '行：入库数量必须为大于0的数值');
                    return;
                }
                
                // 验证入库数量不能超过原数量
                var originalQty = parseFloat(item.originalQuantity) || 0;
                if (inboundQty > originalQty) {
                    $.modal.msgError('第' + (i + 1) + '行：入库数量(' + inboundQty + ')不能大于待入库数量(' + originalQty + ')');
                    return;
                }
                
                validData.push(item);
            }
        }
        
        // 如果没有输入数据，默认全部入库
        if (!hasInputData) {
            // 使用原数量作为入库数量
            validData = tableData.map(function(item) {
                return Object.assign({}, item, {
                    actualInboundQty: item.originalQuantity || 0
                });
            });
        }
        
        if (validData.length === 0) {
            $.modal.msgWarning('没有可入库的数据');
            return;
        }
        
        // 计算总入库数量
        var totalInboundQty = validData.reduce(function(sum, item) {
            return sum + (parseFloat(item.actualInboundQty) || 0);
        }, 0);
        
        var confirmMessage = '确定要绑定入库 ' + validData.length + ' 种物料，总数量 ' + totalInboundQty + ' 件吗？';
        
        $.modal.confirm(confirmMessage, function() {
            $.modal.loading("正在处理中，请稍候...");
            
            // 准备提交数据 - 包含完整的入库信息
            var submitData = validData.map(function(item) {
                return {
                    id: item.id,
                    materialCode: item.materialCode,
                    materialName: item.materialName,
                    contractNo: item.contractNo,
                    itemNo: item.itemNo,
                    materialModel: item.materialModel || '',
                    materialColor: item.materialColor || '',
                    originalQuantity: parseFloat(item.originalQuantity) || 0,
                    actualInboundQty: parseFloat(item.actualInboundQty) || 0,
                    unit: item.unit || '',
                    gridId: item.gridId || '',
                    containerNo: boxInitialData ? boxInitialData.boxProperty.boxNo : '',
                    containerType: 1, // 料箱
                    locationCode: item.locationCode || '',
                    remark: '料箱自身属性绑定入库'
                };
            });
            
            // 调用后端接口
            $.ajax({
                url: ctx + '/auxiliary/inbound/bindPrebox',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(submitData),
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == 200) {
                        $.modal.msgSuccess("料箱自身属性绑定入库成功！");
                        boxInfoTable.reload();
                    } else {
                        $.modal.msgError(result.msg || "绑定入库失败");
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.closeLoading();
                    console.error('料箱自身属性绑定入库请求失败:', error, xhr);
                    
                    // 提供更详细的错误信息
                    var errorMsg = "绑定入库失败";
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg += ": " + xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            var errorResponse = JSON.parse(xhr.responseText);
                            errorMsg += ": " + (errorResponse.message || errorResponse.msg || '服务器错误');
                        } catch (e) {
                            errorMsg += ": " + error;
                        }
                    } else {
                        errorMsg += ": " + error;
                    }
                    
                    $.modal.msgError(errorMsg);
                }
            });
        });
    });


    // 绑定料箱款式绑定按钮事件
    $('#auxPrePack_btnBoxMaterialBind').on('click', function() {
        // 获取料箱表格数据
        var tableData = layui.table.getData('auxPrePack_boxMaterialTableId');
        
        if (!tableData || tableData.length === 0) {
            $.modal.msgWarning('请先选择PO号加载数据');
            return;
        }
        
        // 严格验证入库数量
        var validationResult = validateInboundQuantities(tableData, 'box');
        if (!validationResult.isValid) {
            $.modal.msgError(validationResult.errorMessage);
            return;
        }
        
        var validData = validationResult.validData;
        
        var totalInboundQty = validData.reduce(function(sum, item) {
            return sum + (parseFloat(item.actualInboundQty) || 0);
        }, 0);
        
        var confirmMessage = '确定要绑定入库 ' + validData.length + ' 种物料，总数量 ' + totalInboundQty + ' 件吗？';
        
        $.modal.confirm(confirmMessage, function() {
            $.modal.loading("正在处理中，请稍候...");
            
            // 准备提交数据（与托盘格式保持一致）
            var submitData = validData.map(function(item) {
                return {
                    materialCode: item.materialCode,
                    materialName: item.materialName,
                    contractNo: item.contractNo,
                    itemNo: item.itemNo,
                    poNo: item.poNo,
                    styleColor: item.styleColor,
                    materialColor: item.materialColor,
                    materialModel: item.materialModel,
                    gridId: item.gridId,
                    actualInboundQty: parseFloat(item.actualInboundQty) || 0,
                    // 可以添加其他需要的字段
                    _initialItem: item._initialItem,
                    _poItem: item._poItem
                };
            });
            
            // 料箱款式拆分绑定入库数据结构（与托盘格式统一）
            var bindData = {
                // 容器基础信息
                boxNo: boxInitialData.boxProperty.boxNo,
                containerType: boxInitialData.boxProperty.containerType,
                
                // 入库单信息
                inboundId: boxInitialData.boxItemList.length > 0 ? boxInitialData.boxItemList[0].stockInId || '' : '',
                taskType: 0, // 默认为采购入库，后端会根据入库单号查询实际任务类型
                
                // 拆分操作元信息
                splitOperation: {
                    operationType: 'STYLE_SPLIT',
                    splitTime: new Date().toISOString(),
                    operatorId: null, // 后端从session获取
                    remark: '料箱款式属性拆分绑定入库'
                },
                
                // 源数据信息（支持多条预装记录）
                sourceDataList: boxInitialData.boxItemList.map(function(sourceItem) {
                    return {
                        sourcePreboxItemId: sourceItem.id,
                        sourceMaterialCode: sourceItem.materialCode,
                        sourceMaterialName: sourceItem.materialName,
                        sourceContractNo: sourceItem.contractNo,
                        sourceItemNo: sourceItem.itemNo,
                        sourceOriginalQty: sourceItem.originalQuantity || 0,
                        sourceGridId: sourceItem.gridId || '',
                        
                        // 查找该源数据对应的拆分明细
                        splitDetails: submitData.filter(function(splitItem) {
                            return splitItem._initialItem && 
                                   splitItem._initialItem.materialCode === sourceItem.materialCode &&
                                   splitItem._initialItem.contractNo === sourceItem.contractNo &&
                                   splitItem._initialItem.itemNo === sourceItem.itemNo;
                        }).map(function(splitItem) {
                            return {
                                // 拆分后的款式信息
                                poNo: splitItem.poNo,
                                styleColor: splitItem.styleColor || '',
                                materialModel: splitItem.materialModel || '',
                                materialColor: splitItem.materialColor || '',
                                inboundQty: parseFloat(splitItem.actualInboundQty) || 0,
                                
                                // 关联的PO源数据
                                poSourceData: splitItem._poItem
                            };
                        })
                    };
                }),
                
                // 汇总信息
                summary: {
                    totalSourceItems: boxInitialData.boxItemList.length,
                    totalSplitItems: submitData.length,
                    totalInboundQty: totalInboundQty,
                    poNo: submitData.length > 0 ? submitData[0].poNo : ''
                }
            };
            
            // 调用后端接口
            $.ajax({
                url: ctx + '/auxiliary/inbound/bindBoxMaterial',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(bindData),
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == 200) {
                        $.modal.msgSuccess("料箱款式属性绑定入库成功！");
                        
                        // 清空表格数据
                        table.reloadData('auxPrePack_boxMaterialTableId', {
                            data: [],
                            page: false
                        });
                        
                        // 清空PO选择
                        $('#auxPrePack_boxMaterialPoSelect').val('');
                        form.render('select');
                        
                        // 可以刷新相关数据
                        boxAttributeTable.reload();
                        
                    } else {
                        $.modal.msgError(result.msg || "绑定入库失败");
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.closeLoading();
                    $.modal.msgError("绑定入库失败: " + error);
                }
            });
        });
    });

    // 绑定托盘自身属性入库按钮事件
    $('#auxPrePack_btnPalletBindStatus').on('click', function() {
        // 获取托盘自身属性表格数据
        var tableData = layui.table.getData('auxPrePack_palletInfoTableId');
        
        if (!tableData || tableData.length === 0) {
            $.modal.msgWarning('暂无托盘数据可以入库');
            return;
        }
        
        // 检查是否有输入入库数量的数据
        var validData = [];
        var hasInputData = false;
        
        for (var i = 0; i < tableData.length; i++) {
            var item = tableData[i];
            
            // 检查是否有输入入库数量
            if (item.actualInboundQty && item.actualInboundQty.toString().trim() !== '') {
                hasInputData = true;
                
                // 验证入库数量
                var inboundQty = parseFloat(item.actualInboundQty);
                if (isNaN(inboundQty) || inboundQty <= 0) {
                    $.modal.msgError('第' + (i + 1) + '行：入库数量必须为大于0的数值');
                    return;
                }
                
                // 验证入库数量不能超过原数量
                var originalQty = parseFloat(item.originalQuantity) || 0;
                if (inboundQty > originalQty) {
                    $.modal.msgError('第' + (i + 1) + '行：入库数量(' + inboundQty + ')不能大于待入库数量(' + originalQty + ')');
                    return;
                }
                
                validData.push(item);
            }
        }
        
        // 如果没有输入数据，默认全部入库
        if (!hasInputData) {
            // 使用原数量作为入库数量
            validData = tableData.map(function(item) {
                return Object.assign({}, item, {
                    actualInboundQty: item.originalQuantity || 0
                });
            });
        }
        
        if (validData.length === 0) {
            $.modal.msgWarning('没有可入库的数据');
            return;
        }
        
        // 计算总入库数量
        var totalInboundQty = validData.reduce(function(sum, item) {
            return sum + (parseFloat(item.actualInboundQty) || 0);
        }, 0);
        
        // 检查托盘容量设置（如果有设置容量的话）
        var palletCapacitySelect = $('#auxPrePack_palletCapacityDisplay').val();
        if (palletCapacitySelect) {
            // 这里可以添加容量验证逻辑
            console.log('托盘容量设置:', palletCapacitySelect);
        }
        
        var confirmMessage = '确定要绑定入库 ' + validData.length + ' 种物料，总数量 ' + totalInboundQty + ' 件吗？';
        
        $.modal.confirm(confirmMessage, function() {
            $.modal.loading("正在处理中，请稍候...");
            
            // 准备提交数据 - 提取ID列表（与料箱自身属性保持一致）
            var submitIds = validData.map(function(item) {
                if (!item.id) {
                    console.warn('数据项缺少ID:', item);
                }
                return item.id;
            }).filter(function(id) {
                return id !== null && id !== undefined && id !== '';
            });
            
            if (submitIds.length === 0) {
                $.modal.closeLoading();
                $.modal.msgError('数据异常：缺少有效的记录ID');
                return;
            }
            
            // 准备提交数据 - 包含完整的入库信息（不仅仅是ID）
            var submitData = validData.map(function(item) {
                return {
                    id: item.id,
                    materialCode: item.materialCode,
                    materialName: item.materialName,
                    contractNo: item.contractNo,
                    itemNo: item.itemNo,
                    materialModel: item.materialModel || '',
                    materialColor: item.materialColor || '',
                    originalQuantity: parseFloat(item.originalQuantity) || 0,
                    actualInboundQty: parseFloat(item.actualInboundQty) || 0,
                    unit: item.unit || '',
                    gridId: item.gridId || '',
                    containerNo: palletInitialData ? palletInitialData.boxProperty.boxNo : '',
                    containerType: 2, // 托盘
                    locationCode: item.locationCode || '',
                    remark: '托盘自身属性绑定入库'
                };
            });
            
                         // 调用后端接口（支持实际入库数量）
             $.ajax({
                 url: ctx + '/auxiliary/inbound/bindPrebox',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(submitData),
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code === 200) {
                        $.modal.msgSuccess("托盘自身属性绑定入库成功！");
                        
                        // 刷新托盘自身属性表格
                        boxSelfTable.reload();
                        
                    } else {
                        $.modal.msgError(result.msg || "绑定入库失败");
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.closeLoading();
                    console.error('托盘自身属性绑定入库请求失败:', error, xhr);
                    
                    // 提供更详细的错误信息
                    var errorMsg = "绑定入库失败";
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg += ": " + xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            var errorResponse = JSON.parse(xhr.responseText);
                            errorMsg += ": " + (errorResponse.message || errorResponse.msg || '服务器错误');
                        } catch (e) {
                            errorMsg += ": " + error;
                        }
                    } else {
                        errorMsg += ": " + error;
                    }
                    
                    $.modal.msgError(errorMsg);
                }
            });
        });
    });

    // 绑定托盘款式绑定按钮事件
    $('#auxPrePack_btnPalletMaterialBind').on('click', function() {
        // 获取托盘表格数据
        var tableData = layui.table.getData('auxPrePack_palletMaterialTableId');
        
        if (!tableData || tableData.length === 0) {
            $.modal.msgWarning('请先选择PO号加载数据');
            return;
        }
        
        // 严格验证入库数量
        var validationResult = validateInboundQuantities(tableData, 'pallet');
        if (!validationResult.isValid) {
            $.modal.msgError(validationResult.errorMessage);
            return;
        }
        
        var validData = validationResult.validData;
        
        // 验证是否有初始数据（支持多条主数据）
        if (!palletInitialData || !palletInitialData.boxProperty || !palletInitialData.boxItemList || palletInitialData.boxItemList.length === 0) {
            $.modal.msgError('缺少托盘基础数据，请刷新页面重试');
            return;
        }
        
        // 验证数据完整性
        var invalidItems = validData.filter(function(item) {
            return !item.materialCode || !item.contractNo || !item.itemNo;
        });
        
        if (invalidItems.length > 0) {
            $.modal.msgError('存在数据不完整的记录，请检查物料编码、合约号、款号是否完整');
            return;
        }
        
        // 获取界面设置的容量和计算入库数量
        var totalInboundQty = validData.reduce(function(sum, item) {
            return sum + (parseFloat(item.actualInboundQty) || 0);
        }, 0);
        
         //获取界面上容量设置，如果为空，则提示请先设置容量
         var palletCapacity = $('#auxPrePack_palletMaterialCapacitySelect').val();
         if (!palletCapacity) {
             $.modal.msgError('请先设置托盘容量');
             return;
         }
        var confirmMessage = '确定要绑定入库 ' + validData.length + ' 种物料，总数量 ' + totalInboundQty + ' 件吗？';
        
        $.modal.confirm(confirmMessage, function() {
            $.modal.loading("正在处理中，请稍候...");
            
            // 准备提交数据
            var submitData = validData.map(function(item) {
                return {
                    materialCode: item.materialCode,
                    materialName: item.materialName,
                    contractNo: item.contractNo,
                    itemNo: item.itemNo,
                    poNo: item.poNo,
                    styleColor: item.styleColor,
                    materialColor: item.materialColor,
                    materialModel: item.materialModel,
                    actualInboundQty: parseFloat(item.actualInboundQty) || 0,
                    // 可以添加其他需要的字段
                    _initialItem: item._initialItem,
                    _poItem: item._poItem
                };
            });
            
            // 款式拆分绑定入库数据结构（改进版）
            var bindData = {
                // 容器基础信息
                boxNo: palletInitialData.boxProperty.boxNo,
                containerType: palletInitialData.boxProperty.containerType,
                capacity: parseFloat(palletCapacity) || 0,
                
                // 入库单信息
                inboundId: palletInitialData.boxItemList.length > 0 ? palletInitialData.boxItemList[0].stockInId || '' : '',
                taskType: 0, // 默认为采购入库，后端会根据入库单号查询实际任务类型
                
                // 拆分操作元信息
                splitOperation: {
                    operationType: 'STYLE_SPLIT',
                    splitTime: new Date().toISOString(),
                    operatorId: null, // 后端从session获取
                    remark: '托盘款式属性拆分绑定入库'
                },
                
                // 源数据信息（支持多条预装记录）
                sourceDataList: palletInitialData.boxItemList.map(function(sourceItem) {
                    return {
                        sourcePreboxItemId: sourceItem.id,
                        sourceMaterialCode: sourceItem.materialCode,
                        sourceMaterialName: sourceItem.materialName,
                        sourceContractNo: sourceItem.contractNo,
                        sourceItemNo: sourceItem.itemNo,
                        sourceOriginalQty: sourceItem.originalQuantity || 0,
                        sourceGridId: sourceItem.gridId || '',
                        
                        // 查找该源数据对应的拆分明细
                        splitDetails: submitData.filter(function(splitItem) {
                            return splitItem._initialItem && 
                                   splitItem._initialItem.materialCode === sourceItem.materialCode &&
                                   splitItem._initialItem.contractNo === sourceItem.contractNo &&
                                   splitItem._initialItem.itemNo === sourceItem.itemNo;
                        }).map(function(splitItem) {
                            return {
                                // 拆分后的款式信息
                                poNo: splitItem.poNo,
                                styleColor: splitItem.styleColor || '',
                                materialModel: splitItem.materialModel || '',
                                materialColor: splitItem.materialColor || '',
                                inboundQty: parseFloat(splitItem.actualInboundQty) || 0,
                                
                                // 关联的PO源数据
                                poSourceData: splitItem._poItem
                            };
                        })
                    };
                }),
                
                // 汇总信息
                summary: {
                    totalSourceItems: palletInitialData.boxItemList.length,
                    totalSplitItems: submitData.length,
                    totalInboundQty: totalInboundQty,
                    poNo: submitData.length > 0 ? submitData[0].poNo : ''
                }
            };
            
            // 调用后端接口
            $.ajax({
                url: ctx + '/auxiliary/inbound/bindPalletStyleSplit', 
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(bindData),
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == 200) {
                        $.modal.msgSuccess("托盘款式属性绑定入库成功！");
                        
                        // 清空表格数据
                        table.reloadData('auxPrePack_palletMaterialTableId', {
                            data: [],
                            page: false
                        });
                        
                        // 清空PO选择
                        $('#auxPrePack_palletMaterialPoSelect').val('');
                        form.render('select');
                        
                        // 刷新相关数据
                        boxMaterialTable.reload();
                        
                    } else {
                        $.modal.msgError(result.msg || "绑定入库失败");
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.closeLoading();
                    $.modal.msgError("绑定入库失败: " + error);
                }
            });
                 });
     });

    //自动刷新
    setInterval(function() {
        //判断当前页面是否有箱号显示，如果没有则刷新，如果有则不刷新
            if ($('#auxPrePack_boxNoDisplay').text() == '--') {//料箱自身属性
                boxInfoTable.reload();
            }
            if($('#auxPrePack_palletNoDisplay').text() == '--') {//托盘自身属性
                boxSelfTable.reload();
            }
            if($('#auxPrePack_boxMaterialInfoBoxNo').text() == '--') {//料箱款式属性
                boxAttributeTable.reload();
            }  
            if($('#auxPrePack_palletMaterialNoDisplay').text() == '--') {//托盘款式属性
                boxMaterialTable.reload();
            }
        }, 10000);

    // 获取PO列表数据
    function getPoList(contractNoList){
        if (!contractNoList || contractNoList.length === 0) {
            return;
        }
        
        $.ajax({
            url: ctx + '/manual-report/query-MatInfos',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(contractNoList),
            success: function(result) {
                console.log('getPoList 成功回调:', result);
                
                if (result.code == 200 && result.data) {
                    // 存储完整的PO数据
                    allPoMaterialsData = result.data;
                    
                    console.log('存储PO数据:', allPoMaterialsData);
                    
                    // 填充PO下拉框
                    populatePoSelects(result.data.poOptions);
                    
                    // 标记PO列表已加载
                    poListLoaded = true;
                    
                    console.log('PO数据加载完成，事件绑定状态:', poEventsbound);
                    
                    // 确保事件绑定
                    if (!poEventsbound) {
                        bindPoSelectEvents();
                    }
                } else {
                    console.error('获取PO数据失败:', result);
                    $.modal.msgError("获取PO数据失败: " + (result.msg || "未知错误"));
                }
            },
            error: function(xhr, status, error) {
                $.modal.msgError("获取PO列表失败: " + error);
                poListLoaded = false; // 失败时重置标志
            }
        });
    }

    // 填充PO下拉框
    function populatePoSelects(poOptions) {
        var boxPoSelect = $('#auxPrePack_boxMaterialPoSelect');
        var palletPoSelect = $('#auxPrePack_palletMaterialPoSelect');
        
        // 清空现有选项
        boxPoSelect.empty().append('<option value="">请选择PO号</option>');
        palletPoSelect.empty().append('<option value="">请选择PO号</option>');
        
        // 添加PO选项
        if (poOptions && poOptions.length > 0) {
            poOptions.forEach(function(po) {
                var option = '<option value="' + po.poNo + '">' + po.poDisplay + '</option>';
                boxPoSelect.append(option);
                palletPoSelect.append(option);
            });
        }
        
        // 重新渲染下拉框
        form.render('select');
        
        // 延迟绑定事件，确保DOM完全渲染
        setTimeout(function() {
            bindPoSelectEvents();
        }, 100);
    }

    // 绑定PO选择事件
    function bindPoSelectEvents() {
        console.log('开始绑定PO选择事件，当前状态:', poEventsbound);
        
        // 检查元素是否存在
        var palletSelect = $('#auxPrePack_palletMaterialPoSelect');
        console.log('托盘PO选择框元素:', palletSelect.length > 0 ? '存在' : '不存在');
        
        // 先解绑旧事件，避免重复绑定
        $('#auxPrePack_boxMaterialPoSelect').off('change.poSelect');
        $('#auxPrePack_palletMaterialPoSelect').off('change.poSelect');
        
        // 监听料箱PO选择事件 
        $('#auxPrePack_boxMaterialPoSelect').on('change.poSelect', function() {
            var selectedValue = $(this).val();
            console.log('料箱PO选择事件触发:', selectedValue);
            handlePoSelection(selectedValue, 'box');
        });
        
        // 监听托盘PO选择事件
        $('#auxPrePack_palletMaterialPoSelect').on('change.poSelect', function() {
            var selectedValue = $(this).val();
            console.log('托盘PO选择事件触发:', selectedValue);
            handlePoSelection(selectedValue, 'pallet');
        });
        
        // 标记已绑定
        poEventsbound = true;
        console.log('PO选择事件绑定完成');
    }

    // 初始绑定事件（页面加载时）
    bindPoSelectEvents();

    // 监听Tab切换事件，确保在切换到托盘Tab时重新渲染表单
    element.on('tab(docDemoTabBrief)', function(data){
        console.log('Tab切换到:', data.index);
        if (data.index == 1) { // 托盘Tab
            // 重新渲染托盘相关的表单元素
            setTimeout(function() {
                form.render('select');
                console.log('托盘Tab激活，重新渲染表单');
                // 重新绑定PO选择事件，确保在切换Tab后事件仍然有效
                bindPoSelectEvents();
            }, 50);
        }
    });

    // 处理PO选择
    function handlePoSelection(selectedPoNo, containerType) {
        console.log('=== 开始处理PO选择 ===');
        console.log('选择参数:', { selectedPoNo: selectedPoNo, containerType: containerType });
        console.log('当前数据状态:', {
            hasPoData: !!allPoMaterialsData,
            hasInitialData: !!palletInitialData
        });
        
        if (!selectedPoNo) {
            console.log('未选择PO号');
            if (containerType === 'box') {
                clearMaterialTable(containerType);
            }
            return;
        }
        
        if (!allPoMaterialsData) {
            console.log('PO数据未加载');
            $.modal.msgWarning('PO数据未加载完成，请稍后再试');
            return;
        }
        
        if (containerType === 'box') {
            console.log('调用料箱专用处理函数');
            // 料箱的特殊处理逻辑
            handleBoxPoSelection(selectedPoNo);
        } else if (containerType === 'pallet') {
            console.log('调用托盘专用处理函数');
            // 托盘的特殊处理逻辑
            handlePalletPoSelection(selectedPoNo);
        }
        
        console.log('=== PO选择处理完成 ===');
    }
    
    // 处理料箱PO选择的专用函数
    function handleBoxPoSelection(selectedPoNo) {
        console.log('处理料箱PO选择:', selectedPoNo);
        console.log('料箱初始数据状态:', boxInitialData ? '已加载' : '未加载');
        console.log('PO数据状态:', allPoMaterialsData ? '已加载' : '未加载');
        
        if (!boxInitialData || !allPoMaterialsData) {
            console.error('料箱数据未加载完成', {
                boxInitialData: boxInitialData,
                allPoMaterialsData: allPoMaterialsData
            });
            $.modal.msgWarning('初始数据或PO数据未加载完成');
            return;
        }
        
        // 从PO数据中获取对应PO的物料数据
        var poMaterials = allPoMaterialsData.materials ? allPoMaterialsData.materials[selectedPoNo] : null;
        
        console.log('料箱PO数据结构:', allPoMaterialsData);
        console.log('查找PO:', selectedPoNo, '结果:', poMaterials);
        
        if (!poMaterials || poMaterials.length === 0) {
            console.warn('料箱PO号无对应数据:', selectedPoNo);
            $.modal.msgWarning('PO号 ' + selectedPoNo + ' 没有对应的物料数据');
            return;
        }
        
        console.log('料箱PO物料数据:', poMaterials.length + '条记录', poMaterials);
        
        // 基于初始数据和PO数据进行匹配（支持多条主数据）
        var matchedData = [];
        var initialBoxItemList = boxInitialData.boxItemList;
        
        console.log('料箱初始数据列表:', initialBoxItemList.length + '条记录', initialBoxItemList);
        
        // 遍历初始数据中的每一条记录（支持多条主数据）
        initialBoxItemList.forEach(function(initialItem, itemIndex) {
            console.log('处理料箱初始项 [' + itemIndex + ']:', initialItem);
            
            // 在PO数据中查找匹配的记录（根据合约号、款号）
            var matchingPoItems = poMaterials.filter(function(poItem) {
                var contractMatch = initialItem.contractNo === poItem.contractNo;
                var itemMatch = initialItem.itemNo === poItem.itemNo;
                console.log('料箱匹配检查:', {
                    initialContract: initialItem.contractNo,
                    poContract: poItem.contractNo,
                    contractMatch: contractMatch,
                    initialItem: initialItem.itemNo,
                    poItem: poItem.itemNo,
                    itemMatch: itemMatch
                });
                return contractMatch && itemMatch;
            });
            
            console.log('料箱找到匹配项:', matchingPoItems.length + '条');
            
            // 为每个匹配的PO物料创建新记录
            matchingPoItems.forEach(function(poItem) {
                var combinedItem = {
                    // 从PO数据中获取
                    poNo: selectedPoNo,
                    contractNo: poItem.contractNo,
                    itemNo: poItem.itemNo,
                    styleColor: poItem.styleColor || poItem.materialColor || '',
                    materialModel: poItem.materialModel || poItem.specification || '',
                    materialColor: poItem.materialColor || '',
                    originalQuantity: poItem.originalQuantity || poItem.quantity || 0,
                    
                    // 从初始数据中获取（优先使用初始数据的物料编码和名称）
                    materialCode: initialItem.materialCode,
                    materialName: initialItem.materialName,
                    gridId: initialItem.gridId || '',
                    
                    // 将originalQuantity赋值给materialQuantity（待入库数量）
                    materialQuantity: poItem.originalQuantity || poItem.quantity || 0,
                    
                    // 默认入库数量为0，需要手动输入
                    actualInboundQty: 0,
                    
                    // 其他可能的字段
                    pendingQty: initialItem.pendingQty || initialItem.originalQuantity || 0,
                    
                    // 标记数据来源
                    _dataSource: 'box_combined',
                    _initialItem: initialItem,
                    _poItem: poItem
                };
                
                console.log('创建料箱组合项:', combinedItem);
                matchedData.push(combinedItem);
            });
        });
        
        console.log('料箱最终匹配到的数据:', matchedData.length + '条记录', matchedData);
        
        if (matchedData.length === 0) {
            $.modal.msgWarning('所选PO号与当前料箱数据无匹配项');
            return;
        }
        
        // 累加到现有表格数据中
        addDataToBoxTable(matchedData);
    }
    
    // 处理托盘PO选择的专用函数
    function handlePalletPoSelection(selectedPoNo) {
        console.log('处理托盘PO选择:', selectedPoNo);
        console.log('初始数据状态:', palletInitialData ? '已加载' : '未加载');
        console.log('PO数据状态:', allPoMaterialsData ? '已加载' : '未加载');
        
        if (!palletInitialData || !allPoMaterialsData) {
            console.error('数据未加载完成', {
                palletInitialData: palletInitialData,
                allPoMaterialsData: allPoMaterialsData
            });
            $.modal.msgWarning('初始数据或PO数据未加载完成');
            return;
        }
        
        // 从PO数据中获取对应PO的物料数据
        var poMaterials = allPoMaterialsData.materials ? allPoMaterialsData.materials[selectedPoNo] : null;
        
        console.log('PO数据结构:', allPoMaterialsData);
        console.log('查找PO:', selectedPoNo, '结果:', poMaterials);
        
        if (!poMaterials || poMaterials.length === 0) {
            console.warn('PO号无对应数据:', selectedPoNo);
            $.modal.msgWarning('PO号 ' + selectedPoNo + ' 没有对应的物料数据');
            return;
        }
        
        console.log('PO物料数据:', poMaterials.length + '条记录', poMaterials);
        
        // 基于初始数据和PO数据进行匹配（支持多条主数据）
        var matchedData = [];
        var initialBoxItemList = palletInitialData.boxItemList;
        
        console.log('托盘初始数据列表:', initialBoxItemList.length + '条记录', initialBoxItemList);
        
        // 遍历初始数据中的每一条记录（支持多条主数据）
        initialBoxItemList.forEach(function(initialItem, itemIndex) {
            console.log('处理托盘初始项 [' + itemIndex + ']:', initialItem);
            
            // 在PO数据中查找匹配的记录（根据合约号、款号）
            var matchingPoItems = poMaterials.filter(function(poItem) {
                var contractMatch = initialItem.contractNo === poItem.contractNo;
                var itemMatch = initialItem.itemNo === poItem.itemNo;
                console.log('匹配检查:', {
                    initialContract: initialItem.contractNo,
                    poContract: poItem.contractNo,
                    contractMatch: contractMatch,
                    initialItem: initialItem.itemNo,
                    poItem: poItem.itemNo,
                    itemMatch: itemMatch
                });
                return contractMatch && itemMatch;
            });
            
            console.log('找到匹配项:', matchingPoItems.length + '条');
            
            // 为每个匹配的PO物料创建新记录
            matchingPoItems.forEach(function(poItem) {
                var combinedItem = {
                    // 从PO数据中获取
                    poNo: selectedPoNo,
                    contractNo: poItem.contractNo,
                    itemNo: poItem.itemNo,
                    styleColor: poItem.styleColor || poItem.materialColor || '',
                    materialModel: poItem.materialModel || poItem.specification || '',
                    materialColor: poItem.materialColor || '',
                    originalQuantity: poItem.originalQuantity || poItem.quantity || 0,
                    
                    // 从初始数据中获取（优先使用初始数据的物料编码和名称）
                    materialCode: initialItem.materialCode,
                    materialName: initialItem.materialName,
                    
                    // 将originalQuantity赋值给materialQuantity（待入库数量）
                    materialQuantity: poItem.originalQuantity || poItem.quantity || 0,
                    
                    // 默认入库数量为0，需要手动输入
                    actualInboundQty: 0,
                    
                    // 其他可能的字段
                    gridId: initialItem.gridId || '',
                    pendingQty: initialItem.pendingQty || initialItem.originalQuantity || 0,
                    
                    // 标记数据来源
                    _dataSource: 'pallet_combined',
                    _initialItem: initialItem,
                    _poItem: poItem
                };
                
                console.log('创建组合项:', combinedItem);
                matchedData.push(combinedItem);
            });
        });
        
        console.log('最终匹配到的数据:', matchedData.length + '条记录', matchedData);
        
        if (matchedData.length === 0) {
            $.modal.msgWarning('所选PO号与当前托盘数据无匹配项');
            return;
        }
        
        // 累加到现有表格数据中
        addDataToPalletTable(matchedData);
    }
    
    // 将数据累加到料箱表格中
    function addDataToBoxTable(newData) {
        console.log('addDataToBoxTable 开始处理:', newData.length + '条新数据');
        
        var tableId = 'auxPrePack_boxMaterialTableId';
        
        // 获取当前表格数据
        var currentData = layui.table.getData(tableId) || [];
        console.log('料箱当前表格数据:', currentData.length + '条记录');
        
        // 合并数据，避免重复
        var mergedData = currentData.slice(); // 复制现有数据
        
        var addedCount = 0;
        newData.forEach(function(newItem, index) {
            console.log('处理料箱新数据项 ' + (index + 1) + ':', newItem);
            
            // 检查是否已存在相同的记录
            var isDuplicate = mergedData.some(function(existingItem) {
                var duplicate = existingItem.poNo === newItem.poNo &&
                       existingItem.contractNo === newItem.contractNo &&
                       existingItem.itemNo === newItem.itemNo &&
                       existingItem.materialCode === newItem.materialCode &&
                       existingItem.styleColor === newItem.styleColor &&
                       existingItem.materialColor === newItem.materialColor;
                
                if (duplicate) {
                    console.log('发现料箱重复数据，跳过:', newItem);
                }
                return duplicate;
            });
            
            if (!isDuplicate) {
                mergedData.push(newItem);
                addedCount++;
                console.log('添加料箱新数据项:', newItem);
            }
        });
        
        console.log('料箱数据合并完成:', {
            原有数据: currentData.length,
            新增数据: newData.length,
            实际添加: addedCount,
            合并后总数: mergedData.length
        });
        
        // 更新表格数据
        safeBoxTableUpdate(tableId, mergedData);
        
        if (addedCount > 0) {
            $.modal.msgSuccess('成功添加 ' + addedCount + ' 条数据到料箱表格');
        } else {
            $.modal.msgInfo('没有新数据添加，可能数据已存在');
        }
    }
    
    // 安全的料箱表格数据更新方法
    function safeBoxTableUpdate(tableId, newData) {
        console.log('开始更新料箱表格:', {
            tableId: tableId,
            dataCount: newData.length,
            sampleData: newData.length > 0 ? newData[0] : null
        });
        
        isLocalDataUpdate = true;
        
        // 直接使用替代方法，避免reloadData的问题
        console.log('使用直接渲染方法更新料箱表格');
        tryAlternativeBoxTableUpdate(tableId, newData);
        
        setTimeout(function() {
            isLocalDataUpdate = false;
        }, 1000);
    }
    
    // 替代的料箱表格更新方法
    function tryAlternativeBoxTableUpdate(tableId, newData) {
        console.log('使用替代方法更新料箱表格:', tableId);
        
        try {
            // 重新渲染表格，使用纯本地数据配置
            table.render({
                elem: '#' + tableId,
                data: newData,
                page: false,
                                    cols: [[
                        {field: 'gridId', title: '格号', width: 80},
                        {field: 'poNo', title: 'PO号', width: 120},
                        {field: 'contractNo', title: '合约号', width: 120},
                        {field:'itemNo', title: '款号', width: 120},
                        {field:'styleColor', title: '款式颜色', width: 100},
                        {field:'materialCode', title: '物料编码', width: 120},
                        {field:'materialName', title: '物料名称', width: 150},
                        {field:'materialModel', title: '物料规格', width: 100},
                        {field:'materialColor', title: '物料颜色', width: 100},
                        {field:'originalQuantity', title: '库存数量', width: 100},
                        {field: 'actualInboundQty', title: '实际入库数量', width: 100, edit: 'text', event: 'editActualInboundQty'}
                    ]],
                text: {
                    none: '暂无相关数据，请先选择PO号'
                }
            });
            
            console.log('替代方法更新料箱表格成功');
            
            // 重新绑定编辑事件
            setTimeout(function() {
                table.on('edit(auxPrePack_boxMaterialTable)', function(obj) {
                    var value = obj.value;
                    var data = obj.data;
                    var field = obj.field;
                    
                    if (field === 'actualInboundQty') {
                        // 使用严格验证
                        var validationResult = validateSingleInboundQty(value, data, 'box');
                        
                        if (!validationResult.isValid) {
                            $.modal.msgError(validationResult.errorMessage);
                            obj.update({
                                actualInboundQty: validationResult.correctedValue
                            });
                            return;
                        }
                        
                        console.log('更新料箱入库数量:', {
                            row: data,
                            field: field,
                            oldValue: data[field],
                            newValue: validationResult.validatedValue
                        });
                    }
                });
            }, 200);
            
        } catch (altError) {
            console.error('料箱替代方法也失败:', altError);
            $.modal.msgError('料箱表格更新失败，请刷新页面重试');
        }
    }
    
    // 将数据累加到托盘表格中
    function addDataToPalletTable(newData) {
        console.log('addDataToPalletTable 开始处理:', newData.length + '条新数据');
        
        var tableId = 'auxPrePack_palletMaterialTableId';
        
        // 获取当前表格数据
        var currentData = layui.table.getData(tableId) || [];
        console.log('当前表格数据:', currentData.length + '条记录');
        
        // 合并数据，避免重复
        var mergedData = currentData.slice(); // 复制现有数据
        
        var addedCount = 0;
        newData.forEach(function(newItem, index) {
            console.log('处理新数据项 ' + (index + 1) + ':', newItem);
            
            // 检查是否已存在相同的记录
            var isDuplicate = mergedData.some(function(existingItem) {
                var duplicate = existingItem.poNo === newItem.poNo &&
                       existingItem.contractNo === newItem.contractNo &&
                       existingItem.itemNo === newItem.itemNo &&
                       existingItem.materialCode === newItem.materialCode &&
                       existingItem.styleColor === newItem.styleColor &&
                       existingItem.materialColor === newItem.materialColor;
                
                if (duplicate) {
                    console.log('发现重复数据，跳过:', newItem);
                }
                return duplicate;
            });
            
            if (!isDuplicate) {
                mergedData.push(newItem);
                addedCount++;
                console.log('添加新数据项:', newItem);
            }
        });
        
        console.log('数据合并完成:', {
            原有数据: currentData.length,
            新增数据: newData.length,
            实际添加: addedCount,
            合并后总数: mergedData.length
        });
        
        // 更新表格数据
        safePalletTableUpdate(tableId, mergedData);
        
        if (addedCount > 0) {
            $.modal.msgSuccess('成功添加 ' + addedCount + ' 条数据到表格');
        } else {
            $.modal.msgInfo('没有新数据添加，可能数据已存在');
        }
    }
    
    // 安全的托盘表格数据更新方法
    function safePalletTableUpdate(tableId, newData) {
        console.log('开始更新托盘表格:', {
            tableId: tableId,
            dataCount: newData.length,
            sampleData: newData.length > 0 ? newData[0] : null
        });
        
        isLocalDataUpdate = true;
        
        // 直接使用替代方法，避免reloadData的问题
        console.log('使用直接渲染方法更新表格');
        tryAlternativeTableUpdate(tableId, newData);
        
        setTimeout(function() {
            isLocalDataUpdate = false;
        }, 1000);
    }
    
    // 替代的表格更新方法
    function tryAlternativeTableUpdate(tableId, newData) {
        console.log('使用替代方法更新表格:', tableId);
        
        try {
            // 重新渲染表格，使用纯本地数据配置
            table.render({
                elem: '#' + tableId,
                data: newData,
                page: false,
                cols: [[
                    {field: 'poNo', title: 'PO号', width: 120},
                    {field: 'contractNo', title: '合约号', width: 120},
                    {field: 'itemNo', title: '款号', width: 120},
                    {field: 'styleColor', title: '款式颜色', width: 100},
                    {field: 'materialCode', title: '物料编码', width: 120},
                    {field: 'materialName', title: '物料名称', width: 150},
                    {field: 'materialModel', title: '物料规格', width: 100},
                    {field: 'materialColor', title: '物料颜色', width: 100},
                    {field: 'originalQuantity', title: '库存数量', width: 100},
                    {field: 'actualInboundQty', title: '实际入库数量', width: 100, edit: 'text', event: 'editActualInboundQty'}
                ]],
                text: {
                    none: '暂无相关数据，请先选择PO号'
                }
            });
            
            console.log('替代方法更新表格成功');
            
            // 重新绑定编辑事件
            setTimeout(function() {
                table.on('edit(auxPrePack_palletMaterialTable)', function(obj) {
                    var value = obj.value;
                    var data = obj.data;
                    var field = obj.field;
                    
                    if (field === 'actualInboundQty') {
                        // 使用严格验证
                        var validationResult = validateSingleInboundQty(value, data, 'pallet');
                        
                        if (!validationResult.isValid) {
                            $.modal.msgError(validationResult.errorMessage);
                            obj.update({
                                actualInboundQty: validationResult.correctedValue
                            });
                            return;
                        }
                        
                        // 额外的托盘容量检查
                        checkPalletCapacity(validationResult.validatedValue, data);
                        
                        console.log('更新托盘入库数量:', {
                            row: data,
                            field: field,
                            oldValue: data[field],
                            newValue: validationResult.validatedValue
                        });
                    }
                });
            }, 200);
            
        } catch (altError) {
            console.error('替代方法也失败:', altError);
            $.modal.msgError('表格更新失败，请刷新页面重试');
        }
    }

    // 数据格式标准化函数
    function normalizeDataItem(item, isBaseItem) {
        if (!item) return {};
        
        return {
            // 基础字段 - 确保类型和格式正确
            gridId: item.gridId || item.grid_id || '',
            poNo: item.poNo || item.po_no || '',
            contractNo: item.contractNo || item.contract_no || '',
            itemNo: item.itemNo || item.item_no || '',
            materialCode: item.materialCode || item.material_code || '',
            materialName: item.materialName || item.material_name || '',
            
            // 款式和规格字段
            styleColor: item.styleColor || item.style_color || '',
            materialModel: item.materialModel || item.material_model || '',
            materialColor: item.materialColor || item.material_color || '',
            
            // 数量字段 - 确保为数字格式
            originalQuantity: parseFloat(item.originalQuantity || item.original_quantity || 0) || 0,
            materialQuantity: parseFloat(item.materialQuantity || item.material_quantity || item.originalQuantity || item.original_quantity || 0) || 0,
            pendingQty: parseFloat(item.pendingQty || item.pending_qty || item.originalQuantity || item.original_quantity || 0) || 0,
            actualInboundQty: parseFloat(item.actualInboundQty || item.actual_inbound_qty || 0) || 0,
            
            // 采购数量字段
            xPurchaseQty: parseFloat(item.xPurchaseQty || item.x_purchase_qty || 0) || 0,
            mPurchaseQty: parseFloat(item.mPurchaseQty || item.m_purchase_qty || 0) || 0,
            sPurchaseQty: parseFloat(item.sPurchaseQty || item.s_purchase_qty || 0) || 0,
            lPurchaseQty: parseFloat(item.lPurchaseQty || item.l_purchase_qty || 0) || 0,
            
            // 保留原始ID等字段
            id: item.id || '',
            
            // 标记数据来源
            _dataSource: isBaseItem ? 'base' : 'new'
        };
    }

    // 更新物料表格数据
    function updateMaterialTable(materials, containerType) {
        var tableId = containerType === 'box' ? 
            'auxPrePack_boxMaterialTableId' : 'auxPrePack_palletMaterialTableId';
        
        // 设置本地数据更新标志，防止触发parseData中的getPoList调用
        isLocalDataUpdate = true;
        
        try {
            // 获取当前表格的现有数据
            var currentData = layui.table.getData(tableId);
            
            // 标准化输入数据
            var normalizedMaterials = materials.map(function(item) {
                return normalizeDataItem(item, false);
            });
            
            // 处理数据匹配和扩展逻辑
            var expandedData = [];
            
            if (currentData.length === 0) {
                // 如果当前表格为空，直接使用标准化后的新数据
                expandedData = normalizedMaterials;
            } else {
                // 标准化现有数据并保留
                var normalizedCurrentData = currentData.map(function(item) {
                    return normalizeDataItem(item, true);
                });
                expandedData = expandedData.concat(normalizedCurrentData);
                
                // 遍历当前表格中的每一条数据作为基础模板
                normalizedCurrentData.forEach(function(baseItem) {
                    // 在新数据中查找匹配的记录（根据合约号、款号、物料编码）
                    var matchingItems = normalizedMaterials.filter(function(newItem) {
                        return baseItem.contractNo === newItem.contractNo && 
                               baseItem.itemNo === newItem.itemNo 
                              
                    });
                    
                    // 为每个匹配的款式颜色创建新记录
                    matchingItems.forEach(function(matchItem) {
                        // 检查是否已存在相同的记录（避免重复）
                        var isDuplicate = expandedData.some(function(existingItem) {
                            return existingItem.contractNo === matchItem.contractNo &&
                                   existingItem.itemNo === matchItem.itemNo &&
                                   existingItem.materialCode === matchItem.materialCode &&
                                   existingItem.styleColor === matchItem.styleColor &&
                                   existingItem.materialColor === matchItem.materialColor;
                        });
                        
                        if (!isDuplicate) {
                            // 创建新的数据记录，合并基础信息和新的款式信息
                            var combinedItem = {
                                // 从基础数据中复制的信息
                                contractNo: baseItem.contractNo,
                                itemNo: baseItem.itemNo,
                                materialCode: baseItem.materialCode,
                                materialName: baseItem.materialName,
                                poNo: baseItem.poNo || matchItem.poNo,
                                gridId: matchItem.gridId || baseItem.gridId,
                                
                                // 从新数据中获取的款式信息
                                styleColor: matchItem.styleColor,
                                materialModel: matchItem.materialModel,
                                materialColor: matchItem.materialColor,
                                originalQuantity: matchItem.originalQuantity,
                                materialQuantity: matchItem.originalQuantity, // 将originalQuantity赋值给materialQuantity
                                actualInboundQty: matchItem.actualInboundQty,
                                
                                // 其他字段
                                xPurchaseQty: matchItem.xPurchaseQty,
                                mPurchaseQty: matchItem.mPurchaseQty,
                                sPurchaseQty: matchItem.sPurchaseQty,
                                lPurchaseQty: matchItem.lPurchaseQty,
                                
                                // 标记为合并数据
                                _dataSource: 'combined'
                            };
                            
                            expandedData.push(combinedItem);
                        }
                    });
                });
            }
            
            // 数据最终验证和清理
            var finalData = expandedData.map(function(item) {
                // 确保所有必要字段都存在且格式正确
                return {
                    gridId: item.gridId || '',
                    poNo: item.poNo || '',
                    contractNo: item.contractNo || '',
                    itemNo: item.itemNo || '',
                    styleColor: item.styleColor || '',
                    materialCode: item.materialCode || '',
                    materialName: item.materialName || '',
                    materialModel: item.materialModel || '',
                    materialColor: item.materialColor || '',
                    originalQuantity: item.originalQuantity || 0,
                    materialQuantity: item.materialQuantity || item.originalQuantity || 0, // 添加materialQuantity字段
                    pendingQty: item.pendingQty || item.originalQuantity || 0, // 添加待入库数量字段
                    actualInboundQty: item.actualInboundQty || 0,
                    xPurchaseQty: item.xPurchaseQty || 0,
                    mPurchaseQty: item.mPurchaseQty || 0,
                    sPurchaseQty: item.sPurchaseQty || 0,
                    lPurchaseQty: item.lPurchaseQty || 0
                };
            });
            
            console.log('更新表格数据:', {
                tableId: tableId,
                currentDataCount: currentData.length,
                newDataCount: normalizedMaterials.length,
                finalDataCount: finalData.length
            });
            
            // 使用安全的表格数据更新方法
            safeUpdateTableData(tableId, finalData);
            
        } catch (error) {
            console.error('更新物料表格数据时发生错误:', error);
            $.modal.msgError("更新表格数据失败: " + error.message);
            // 重置标志变量
            isLocalDataUpdate = false;
        }
    }

    // 更新物料信息显示（注意：此函数不应该修改托盘号显示）
    function updateMaterialInfo(material, containerType) {
        // 此函数现在主要用于更新统计信息，不修改容器编号显示
        if (containerType === 'box') {
            // 只更新待入库数量等统计信息，不修改箱号
            $('#auxPrePack_boxMaterialInfoPendingQty').text(material.originalQuantity || '--');
            // 箱号和容器类型应该保持原有显示，不被PO选择影响
        } else if (containerType === 'pallet') {
            // 托盘同样只更新统计信息，不修改托盘号
            // 托盘号和类型应该保持原有显示，不被PO选择影响
        }
    }

    // 清空物料表格
    function clearMaterialTable(containerType) {
        var tableId = containerType === 'box' ? 
            'auxPrePack_boxMaterialTableId' : 'auxPrePack_palletMaterialTableId';
        
        // 使用安全的表格数据更新方法
        safeUpdateTableData(tableId, []);
        
        // 清空信息显示
        if (containerType === 'box') {
            $('#auxPrePack_boxMaterialInfoBoxNo').text('--');
            $('#auxPrePack_boxMaterialInfoContainerType').text('--');
            $('#auxPrePack_boxMaterialInfoPendingQty').text('--');
        } else if (containerType === 'pallet') {
            $('#auxPrePack_palletMaterialNoDisplay').text('--');
            $('#auxPrePack_palletMaterialTypeDisplay').text('--');
        }
    }
    
    // 添加重置PO数据的方法
    function resetPoData() {
        allPoMaterialsData = null;
        poListLoaded = false;
        poEventsbound = false;
    }
    
    // 安全的表格数据更新方法，避免触发parseData重新渲染
    function safeUpdateTableData(tableId, newData) {
        isLocalDataUpdate = true;
        
        console.log('开始更新表格数据:', {
            tableId: tableId,
            dataCount: newData.length,
            sampleData: newData.length > 0 ? newData[0] : null
        });
        
        try {
            // 方法1: 使用reloadData，清空远程配置
            table.reloadData(tableId, {
                data: newData,
                page: false,
                url: '', // 清空URL
                where: {}, // 清空查询条件
                method: 'get' // 避免POST请求
            });
            
            console.log('表格数据更新完成:', tableId);
            
        } catch (error) {
            console.error('reloadData方法失败:', error);
            
            // 方法2: 尝试使用reload方法
            try {
                console.log('尝试使用reload方法...');
                
                // 先获取表格当前配置
                var currentConfig = {
                    data: newData,
                    page: false,
                    url: '', // 移除URL配置
                    where: {},
                    method: 'get',
                    // 保持原有的列配置
                    cols: tableId === 'auxPrePack_boxMaterialTableId' ? 
                        [[
                            {field: 'gridId', title: '格号', width: 80},
                            {field: 'poNo', title: 'PO号', width: 120},
                            {field: 'contractNo', title: '合约号', width: 120},
                            {field: 'itemNo', title: '款号', width: 120},
                            {field: 'styleColor', title: '款式颜色', width: 100},
                            {field: 'materialCode', title: '物料编码', width: 120},
                            {field: 'materialName', title: '物料名称', width: 150},
                            {field: 'materialModel', title: '物料规格', width: 100},
                            {field: 'materialColor', title: '物料颜色', width: 100},
                            {field: 'originalQuantity', title: '库存数量', width: 100}
                        ]] :
                        [[
                            {field: 'poNo', title: 'PO号', width: 120},
                            {field: 'contractNo', title: '合约号', width: 120},
                            {field: 'itemNo', title: '款号', width: 120},
                            {field: 'styleColor', title: '款式颜色', width: 100},
                            {field: 'materialCode', title: '物料编码', width: 120},
                            {field: 'materialName', title: '物料名称', width: 150},
                            {field: 'materialModel', title: '物料规格', width: 100},
                            {field: 'materialColor', title: '物料颜色', width: 100},
                            {field: 'originalQuantity', title: '库存数量', width: 100}
                        ]]
                };
                
                table.reload(tableId, currentConfig);
                console.log('使用reload方法更新表格成功:', tableId);
                
            } catch (secondError) {
                console.error('reload方法也失败了:', secondError);
                
                // 方法3: 最后的降级处理 - 直接重新render
                try {
                    console.log('尝试重新render表格...');
                    
                    // 获取表格元素
                    var tableElem = $('#' + tableId);
                    if (tableElem.length > 0) {
                        // 清空表格
                        tableElem.empty();
                        
                        // 重新渲染，使用纯本地数据配置
                        table.render({
                            elem: '#' + tableId,
                            data: newData,
                            page: false,
                            cols: tableId === 'auxPrePack_boxMaterialTableId' ? 
                                [[
                                    {field: 'gridId', title: '格号', width: 80},
                                    {field: 'poNo', title: 'PO号', width: 120},
                                    {field: 'contractNo', title: '合约号', width: 120},
                                    {field: 'itemNo', title: '款号', width: 120},
                                    {field: 'styleColor', title: '款式颜色', width: 100},
                                    {field: 'materialCode', title: '物料编码', width: 120},
                                    {field: 'materialName', title: '物料名称', width: 150},
                                    {field: 'materialModel', title: '物料规格', width: 100},
                                    {field: 'materialColor', title: '物料颜色', width: 100},
                                    {field: 'originalQuantity', title: '库存数量', width: 100}
                                ]] :
                                [[
                                    {field: 'poNo', title: 'PO号', width: 120},
                                    {field: 'contractNo', title: '合约号', width: 120},
                                    {field: 'itemNo', title: '款号', width: 120},
                                    {field: 'styleColor', title: '款式颜色', width: 100},
                                    {field: 'materialCode', title: '物料编码', width: 120},
                                    {field: 'materialName', title: '物料名称', width: 150},
                                    {field: 'materialModel', title: '物料规格', width: 100},
                                    {field: 'materialColor', title: '物料颜色', width: 100},
                                    {field: 'originalQuantity', title: '库存数量', width: 100}
                                ]],
                            text: {
                                none: '暂无相关数据'
                            }
                        });
                        
                        console.log('重新render表格成功:', tableId);
                    }
                } catch (thirdError) {
                    console.error('所有方法都失败了:', thirdError);
                    $.modal.msgError("表格数据更新失败，请刷新页面重试");
                }
            }
        } finally {
            // 延迟重置标志，确保操作完成
            setTimeout(function() {
                isLocalDataUpdate = false;
                console.log('重置isLocalDataUpdate标志');
            }, 1000);
        }
    }
    

    // 在全局作用域中暴露测试函数
    window.testTableUpdate = testTableUpdate;
    
    // 暴露验证函数供测试使用
    window.testValidation = function() {
        console.log('=== 测试验证功能 ===');
        
        // 测试各种验证场景
        var testCases = [
            { value: '', expected: false, desc: '空值' },
            { value: 'abc', expected: false, desc: '非数值' },
            { value: '-5', expected: false, desc: '负数' },
            { value: '0', expected: false, desc: '零' },
            { value: '3.5', expected: false, desc: '小数' },
            { value: '10', expected: true, desc: '正整数' },
            { value: '100', expected: false, desc: '超过库存' } // 假设库存为50
        ];
        
        var mockRowData = {
            materialCode: 'TEST001',
            materialName: '测试物料',
            styleColor: '红色',
            originalQuantity: 50
        };
        
        testCases.forEach(function(testCase, index) {
            var result = validateSingleInboundQty(testCase.value, mockRowData, 'test');
            var success = result.isValid === testCase.expected;
            
            console.log('测试 ' + (index + 1) + ' (' + testCase.desc + '):', {
                输入值: testCase.value,
                预期结果: testCase.expected ? '通过' : '失败',
                实际结果: result.isValid ? '通过' : '失败',
                错误信息: result.errorMessage,
                测试结果: success ? '✅ 通过' : '❌ 失败'
            });
        });
        
        console.log('=== 验证功能测试完成 ===');
    };
    
    // 添加调试函数，检查数据状态（支持多条主数据）
    window.debugPalletData = function() {
        console.log('=== 调试托盘数据状态 ===');
        console.log('palletInitialData:', palletInitialData);
        console.log('allPoMaterialsData:', allPoMaterialsData);
        console.log('poListLoaded:', poListLoaded);
        console.log('poEventsbound:', poEventsbound);
        
        if (palletInitialData && palletInitialData.boxItemList) {
            console.log('托盘主数据记录数:', palletInitialData.boxItemList.length);
            palletInitialData.boxItemList.forEach(function(item, index) {
                console.log('主数据 [' + index + ']:', {
                    contractNo: item.contractNo,
                    itemNo: item.itemNo,
                    materialCode: item.materialCode,
                    materialName: item.materialName
                });
            });
        }
        
        var tableData = layui.table.getData('auxPrePack_palletMaterialTableId');
        console.log('当前表格数据:', tableData.length + '条记录');
        
        var selectedPo = $('#auxPrePack_palletMaterialPoSelect').val();
        console.log('当前选择的PO:', selectedPo);
        
        return {
            palletInitialData: palletInitialData,
            allPoMaterialsData: allPoMaterialsData,
            tableData: tableData,
            selectedPo: selectedPo,
            mainDataCount: palletInitialData ? palletInitialData.boxItemList.length : 0
        };
    };
    
    // 添加调试料箱数据的函数
    window.debugBoxData = function() {
        console.log('=== 调试料箱数据状态 ===');
        console.log('boxInitialData:', boxInitialData);
        
        if (boxInitialData && boxInitialData.boxItemList) {
            console.log('料箱主数据记录数:', boxInitialData.boxItemList.length);
            boxInitialData.boxItemList.forEach(function(item, index) {
                console.log('主数据 [' + index + ']:', {
                    contractNo: item.contractNo,
                    itemNo: item.itemNo,
                    materialCode: item.materialCode,
                    materialName: item.materialName
                });
            });
        }
        
        var tableData = layui.table.getData('auxPrePack_boxMaterialTableId');
        console.log('当前表格数据:', tableData.length + '条记录');
        
        var selectedPo = $('#auxPrePack_boxMaterialPoSelect').val();
        console.log('当前选择的PO:', selectedPo);
        
        return {
            boxInitialData: boxInitialData,
            allPoMaterialsData: allPoMaterialsData,
            tableData: tableData,
            selectedPo: selectedPo,
            mainDataCount: boxInitialData ? boxInitialData.boxItemList.length : 0
        };
    };
    
    // 添加测试托盘PO选择的函数
    window.testPalletPoSelection = function(poNo) {
        if (!poNo && $('#auxPrePack_palletMaterialPoSelect option').length > 1) {
            // 如果没有指定PO号，使用第一个可用的PO号
            poNo = $('#auxPrePack_palletMaterialPoSelect option:eq(1)').val();
        }
        
        if (!poNo) {
            console.error('没有可用的PO号进行测试');
            return;
        }
        
        console.log('=== 开始测试PO选择: ' + poNo + ' ===');
        
        // 设置PO选择框的值
        $('#auxPrePack_palletMaterialPoSelect').val(poNo);
        
        // 手动触发选择事件
        handlePalletPoSelection(poNo);
        
                 console.log('=== 测试完成 ===');
    };
    
    // 添加测试料箱PO选择的函数
    window.testBoxPoSelection = function(poNo) {
        if (!poNo && $('#auxPrePack_boxMaterialPoSelect option').length > 1) {
            // 如果没有指定PO号，使用第一个可用的PO号
            poNo = $('#auxPrePack_boxMaterialPoSelect option:eq(1)').val();
        }
        
        if (!poNo) {
            console.error('没有可用的PO号进行料箱测试');
            return;
        }
        
        console.log('=== 开始测试料箱PO选择: ' + poNo + ' ===');
        
        // 设置PO选择框的值
        $('#auxPrePack_boxMaterialPoSelect').val(poNo);
        
        // 手动触发选择事件
        handleBoxPoSelection(poNo);
        
        console.log('=== 料箱测试完成 ===');
    };
    
    // 添加托盘容量验证测试函数
    window.testPalletCapacity = function() {
        console.log('=== 测试托盘容量验证 ===');
        
        if (!palletInitialData || !palletInitialData.boxProperty) {
            console.error('没有托盘初始数据');
            return;
        }
        
        var palletCapacity = parseFloat(palletInitialData.boxProperty.capacity) || 0;
        var currentUsedQty = parseFloat(palletInitialData.boxProperty.usedCapacity) || 0;
        var remainingCapacity = palletCapacity - currentUsedQty;
        
        console.log('托盘容量信息:', {
            总容量: palletCapacity,
            已使用: currentUsedQty,
            剩余容量: remainingCapacity,
            使用率: ((currentUsedQty / palletCapacity) * 100).toFixed(1) + '%'
        });
        
        // 模拟测试不同的入库数量
        var testQuantities = [remainingCapacity * 0.5, remainingCapacity, remainingCapacity * 1.2];
        
        testQuantities.forEach(function(qty, index) {
            var wouldExceed = qty > remainingCapacity;
            var newUsageRate = ((currentUsedQty + qty) / palletCapacity * 100).toFixed(1);
            
            console.log('测试数量 ' + (index + 1) + ':', {
                入库数量: qty,
                是否超容量: wouldExceed,
                新使用率: newUsageRate + '%',
                验证结果: wouldExceed ? '❌ 超出容量' : '✅ 容量充足'
            });
        });
        
        return {
            palletCapacity: palletCapacity,
            currentUsedQty: currentUsedQty,
            remainingCapacity: remainingCapacity
        };
    };
    
    // 添加直接更新表格数据的测试函数
    window.testDirectTableUpdate = function() {
        var testData = [{
            poNo: 'TEST001',
            contractNo: 'SO2308-0003',
            itemNo: 'BCH22G088',
            styleColor: '测试颜色',
            materialCode: 'F052293',
            materialName: '测试物料',
            materialModel: 'S',
            materialColor: '黑色',
            originalQuantity: 100,
            actualInboundQty: 0
        }];
        
        console.log('=== 直接测试表格更新 ===');
        console.log('测试数据:', testData);
        
        // 尝试使用替代方法直接更新
        tryAlternativeTableUpdate('auxPrePack_palletMaterialTableId', testData);
        
        // 验证结果
        setTimeout(function() {
            var currentData = layui.table.getData('auxPrePack_palletMaterialTableId');
            console.log('直接更新后表格数据:', currentData.length + '条记录');
            console.log('表格数据详情:', currentData);
        }, 1000);
    };

    // 监听料箱自身属性表格的入库数量编辑事件
    table.on('edit(auxPrePack_boxInfoTable)', function(obj) {
        var value = obj.value; // 得到修改后的值
        var data = obj.data; // 得到所在行所有键值
        var field = obj.field; // 得到字段
        
        if (field === 'actualInboundQty') {
            // 严格验证入库数量
            var validationResult = validateSingleInboundQty(value, data, 'box');
            
            if (!validationResult.isValid) {
                $.modal.msgError(validationResult.errorMessage);
                obj.update({
                    actualInboundQty: validationResult.correctedValue
                });
                return;
            }
            
            console.log('更新料箱自身属性入库数量:', {
                row: data,
                field: field,
                oldValue: data[field],
                newValue: validationResult.validatedValue
            });
        }
    });

    // 监听料箱款式属性表格的入库数量编辑事件
    table.on('edit(auxPrePack_boxMaterialTable)', function(obj) {
        var value = obj.value; // 得到修改后的值
        var data = obj.data; // 得到所在行所有键值
        var field = obj.field; // 得到字段
        
        if (field === 'actualInboundQty') {
            // 严格验证入库数量
            var validationResult = validateSingleInboundQty(value, data, 'box');
            
            if (!validationResult.isValid) {
                $.modal.msgError(validationResult.errorMessage);
                obj.update({
                    actualInboundQty: validationResult.correctedValue
                });
                return;
            }
            
            console.log('更新料箱款式属性入库数量:', {
                row: data,
                field: field,
                oldValue: data[field],
                newValue: validationResult.validatedValue
            });
        }
    });

    // 监听托盘自身属性表格的入库数量编辑事件
    table.on('edit(auxPrePack_palletInfoTable)', function(obj) {
        var value = obj.value; // 得到修改后的值
        var data = obj.data; // 得到所在行所有键值
        var field = obj.field; // 得到字段
        
        if (field === 'actualInboundQty') {
            // 严格验证入库数量
            var validationResult = validateSingleInboundQty(value, data, 'pallet');
            
            if (!validationResult.isValid) {
                $.modal.msgError(validationResult.errorMessage);
                obj.update({
                    actualInboundQty: validationResult.correctedValue
                });
                return;
            }
            
            // 额外的托盘容量检查
            checkPalletCapacity(validationResult.validatedValue, data);
            
            console.log('更新托盘自身属性入库数量:', {
                row: data,
                field: field,
                oldValue: data[field],
                newValue: validationResult.validatedValue
            });
        }
    });

    // 监听托盘款式属性表格的入库数量编辑事件（含容量验证）
    table.on('edit(auxPrePack_palletMaterialTable)', function(obj) {
        var value = obj.value; // 得到修改后的值
        var data = obj.data; // 得到所在行所有键值
        var field = obj.field; // 得到字段
        
        if (field === 'actualInboundQty') {
            // 严格验证入库数量
            var validationResult = validateSingleInboundQty(value, data, 'pallet');
            
            if (!validationResult.isValid) {
                $.modal.msgError(validationResult.errorMessage);
                obj.update({
                    actualInboundQty: validationResult.correctedValue
                });
                return;
            }
            
            // 额外的托盘容量检查
            checkPalletCapacity(validationResult.validatedValue, data);
            
            console.log('更新托盘款式属性入库数量:', {
                row: data,
                field: field,
                oldValue: data[field],
                newValue: validationResult.validatedValue
            });
        }
    });
});

