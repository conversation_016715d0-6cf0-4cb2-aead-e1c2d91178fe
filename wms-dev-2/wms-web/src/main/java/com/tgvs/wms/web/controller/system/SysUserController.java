package com.tgvs.wms.web.controller.system;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.tgvs.wms.common.util.bean.BeanUtils;
import com.tgvs.wms.system.entity.SysRole;
import com.tgvs.wms.system.vo.SysUserVo;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.CommonConstant;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.constant.UserConstants;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.util.PasswordUtil;
import com.tgvs.wms.common.util.ShiroUtils;
import com.tgvs.wms.common.util.oConvertUtils;
import com.tgvs.wms.system.dto.SysUserDto;
import com.tgvs.wms.system.entity.SysUser;
import com.tgvs.wms.system.service.system.SysRoleService;
import com.tgvs.wms.system.service.system.SysUserRoleService;
import com.tgvs.wms.system.service.system.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/system/user")
@Api(tags = "用户管理")
@Slf4j
public class SysUserController {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private SysUserRoleService sysUserRoleService;

    /**
     * 新增保存菜单
     */
    @PostMapping("/updateMultiModule")
    @ResponseBody
    public Result updateMultiModule(@RequestBody boolean multiModule) {

        sysUserService.updateMultiModule(ShiroUtils.getUserId(), multiModule);
        ShiroUtils.setMultiModule(multiModule);
        return Result.ok();
    }

    @AutoLog("用户管理-查询列表")
    @ApiOperation(value = "用户管理-查询列表", notes = "用户管理-查询列表")
    @PostMapping({ "/list" })
    public Result getUserList(@RequestBody(required = false) QueryModel queryModel) {
        // 获取SysUser分页数据
        IPage<SysUser> userPage = sysUserService.pageList(queryModel);

        // 创建一个新的Page对象来存储SysUserDto
        IPage<SysUserDto> dtoPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
                userPage.getCurrent(), userPage.getSize(), userPage.getTotal());

        // 转换记录并设置角色信息
        if (userPage.getRecords() != null && !userPage.getRecords().isEmpty()) {
            List<SysUserDto> dtoList = userPage.getRecords().stream().map(user -> {
                // 转换SysUser到SysUserDto
                SysUserDto dto = new SysUserDto();
                // 基本属性
                dto.setId(user.getId());
                dto.setUsername(user.getUsername());
                dto.setRealname(user.getRealname());
                dto.setAvatar(user.getAvatar());
                dto.setBirthday(user.getBirthday());
                dto.setSex(user.getSex());
                dto.setEmail(user.getEmail());
                dto.setPhone(user.getPhone());
                dto.setStatus(user.getStatus());
                dto.setDelFlag(user.getDelFlag());
                dto.setCreateBy(user.getCreateBy());
                dto.setCreateTime(user.getCreateTime());
                dto.setUpdateBy(user.getUpdateBy());
                dto.setUpdateTime(user.getUpdateTime());
                dto.setOrgCode(user.getOrgCode());
                dto.setWorkNo(user.getWorkNo());
                dto.setPost(user.getPost());
                dto.setActivitiSync(user.getActivitiSync());
                dto.setUserIdentity(user.getUserIdentity());
                dto.setDepartIds(user.getDepartIds());
                dto.setRelTenantIds(user.getRelTenantIds());
                dto.setTelephone(user.getTelephone());
                dto.setClientId(user.getClientId());
                dto.setMultiModule(user.getMultiModule());

                // 设置角色信息，仅包含id和roleName
                List<SysRole> roles = sysRoleService.getRolesByUserId(user.getId());
                if (roles != null && !roles.isEmpty()) {
                    List<Map<String, Object>> simplifiedRoles = roles.stream()
                            .map(role -> {
                                Map<String, Object> roleMap = new HashMap<>();
                                roleMap.put("id", role.getId());
                                roleMap.put("roleName", role.getRoleName());
                                return roleMap;
                            })
                            .collect(Collectors.toList());
                    dto.setRoleIds(simplifiedRoles);
                }
                return dto;
            }).collect(Collectors.toList());

            dtoPage.setRecords(dtoList);
        }

        Result result = Result.ok(dtoPage.getRecords());
        result.setTotal(dtoPage.getTotal());
        return result;
    }

    @AutoLog("用户管理-添加")
    @ApiOperation(value = "用户管理-添加", notes = "用户管理-添加")
    @RequiresPermissions({ "point:add" })
    @PostMapping({ "/add" })
    @Transactional
    public Result<?> add(@RequestBody SysUserVo vo) {
        String salt = oConvertUtils.randomGen(8);
        vo.setSalt(salt);
        // 密码如果不在指定范围内 错误
        if (vo.getPassword().length() < UserConstants.PASSWORD_MIN_LENGTH
                || vo.getPassword().length() > UserConstants.PASSWORD_MAX_LENGTH) {
            return Result.error(MessageFormat.format("新密码长度在{0}到{1}", UserConstants.PASSWORD_MIN_LENGTH,
                    UserConstants.PASSWORD_MAX_LENGTH));
        }
        String passwordEncode = PasswordUtil.encrypt(vo.getUsername(), vo.getPassword(), salt);
        vo.setPassword(passwordEncode);
        vo.setDelFlag(CommonConstant.DEL_FLAG_0);
        // 需要手动转换成SysUser
        SysUser user = new SysUser();
        BeanUtils.copyBeanProp(user, vo);
        boolean saveResult = this.sysUserService.save(user);
        if (saveResult) {
            // 检查ID是否成功回填
            if (user.getId() != null && !user.getId().isEmpty()) {
                // 添加角色
                if (vo.getRoleIds() != null && vo.getRoleIds().length() > 0) {
                    List<String> roleIdList = Arrays.asList(vo.getRoleIds().split(","));
                    sysUserRoleService.insertUserRoles(user.getId(), roleIdList);
                }
                return Result.OK("添加成功！");
            } else {
                log.error("用户保存成功但未获取到ID");
                return Result.error("用户保存成功但角色关联失败");
            }
        }
        return Result.error("添加失败！");
    }

    @AutoLog("用户管理-编辑")
    @ApiOperation(value = "用户管理-编辑", notes = "用户管理-编辑")
    @RequiresPermissions({ "point:edit" })
    @PostMapping({ "/edit" })
    @Transactional
    public Result<?> edit(@RequestBody SysUserVo vo) {

        LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysUser::getId, vo.getId());
        updateWrapper.set(SysUser::getUsername, vo.getUsername());
        updateWrapper.set(SysUser::getRealname, vo.getRealname());
        updateWrapper.set(SysUser::getSex, vo.getSex());
        updateWrapper.set(SysUser::getStatus, vo.getStatus());
        this.sysUserService.update(updateWrapper);
        // 删除角色
        sysUserRoleService.deleteUserRolesByUserId(vo.getId());
        // 添加角色
        if (vo.getRoleIds() != null && vo.getRoleIds().length() > 0) {
            List<String> roleIdList = Arrays.asList(vo.getRoleIds().split(","));
            sysUserRoleService.insertUserRoles(vo.getId(), roleIdList);
        }
        return Result.OK("编辑成功!");
    }

    @AutoLog("用户管理-通过id删除")
    @ApiOperation(value = "用户管理-通过id删除", notes = "用户管理-通过id删除")
    @RequiresPermissions({ "point:delete" })
    @PostMapping({ "/delete" })
    public Result<?> delete(@RequestBody String id) {
        this.sysUserService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("用户管理-通过id查询")
    @ApiOperation(value = "用户管理-通过id查询", notes = "用户管理-通过id查询")
    @PostMapping({ "/queryById" })
    public Result<?> queryById(@RequestBody String id) {
        SysUser user = this.sysUserService.getById(id);
        if (user == null)
            return Result.error("未找到对应数据");
        SysUserDto dto = new SysUserDto();
        dto.setId(user.getId());
        dto.setPassword(user.getPassword());
        dto.setUsername(user.getUsername());
        dto.setRealname(user.getRealname());
        dto.setAvatar(user.getAvatar());
        dto.setBirthday(user.getBirthday());
        dto.setSex(user.getSex());
        dto.setEmail(user.getEmail());
        dto.setPhone(user.getPhone());
        dto.setStatus(user.getStatus());
        dto.setDelFlag(user.getDelFlag());
        dto.setCreateBy(user.getCreateBy());
        dto.setCreateTime(user.getCreateTime());
        dto.setUpdateBy(user.getUpdateBy());
        dto.setUpdateTime(user.getUpdateTime());

        // 设置角色信息，仅包含id和roleName
        List<SysRole> roles = sysRoleService.getRolesByUserId(user.getId());
        if (roles != null && !roles.isEmpty()) {
            List<Map<String, Object>> simplifiedRoles = roles.stream()
                    .map(role -> {
                        Map<String, Object> roleMap = new HashMap<>();
                        roleMap.put("id", role.getId());
                        roleMap.put("roleName", role.getRoleName());
                        return roleMap;
                    })
                    .collect(Collectors.toList());
            dto.setRoleIds(simplifiedRoles);
        }
        return Result.OK(dto);
    }

    /**
     * 通过用户ID查询用户角色
     */
    @AutoLog("用户管理-查询用户角色")
    @ApiOperation(value = "用户管理-查询用户角色", notes = "用户管理-通过用户ID查询用户角色")
    @PostMapping({ "/queryRolesById" })
    public Result<?> queryRolesById(@RequestBody String userId) {
        if (oConvertUtils.isEmpty(userId)) {
            return Result.error("用户ID不能为空");
        }
        return Result.OK(sysRoleService.getRolesByUserId(userId));
    }

    /**
     * 修改密码
     */
    @AutoLog("用户管理-修改密码")
    @ApiOperation(value = "用户管理-修改密码", notes = "用户管理-修改密码")
    @PostMapping({ "/changePassword" })
    public Result<?> changePassword(@RequestBody SysUser sysUser) {
        SysUser u = this.sysUserService
                .getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, sysUser.getUsername()));
        if (u == null) {
            return Result.error("用户不存在！");
        }
        sysUser.setId(u.getId());
        return sysUserService.changePassword(sysUser);
    }
}
