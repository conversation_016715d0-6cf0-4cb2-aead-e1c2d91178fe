package com.tgvs.wms.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 登录请求DTO
 */
@Data
@Schema(description = "登录请求")
public class LoginRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    @Schema(description = "用户名", required = true, example = "api_user")
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码", required = true, example = "password123")
    private String password;
} 