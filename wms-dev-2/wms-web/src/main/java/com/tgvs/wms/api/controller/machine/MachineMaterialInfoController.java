package com.tgvs.wms.api.controller.machine;

import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInfo;
import com.tgvs.wms.business.modules.machineMaterials.service.IMaterialInfoService;
import com.tgvs.wms.business.modules.machineMaterials.vo.MachineMaterialVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 机物料基础信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/machine")
@RequiredArgsConstructor
@Tag(name = "机物料管理", description = "提供机物料管理相关接口")
public class MachineMaterialInfoController {

    private final IMaterialInfoService materialInfoService;

    @Operation(
            summary = "新增机物料基础信息",
            description = "新增机物料基础信息"
    )
    @PostMapping("/materialInfo/add")
    public Result<Void> addMachineMaterial(
            @Parameter(description = "机物料基础信息", required = true)
            @Validated @RequestBody MachineMaterialVo machineMaterialVo) {
        
        log.info("收到新增机物料基础信息请求: {}", machineMaterialVo.getMaterialCode());
        
        try {
            WmsMachineInfo wmsMachineInfo = new WmsMachineInfo();
            BeanUtils.copyProperties(machineMaterialVo, wmsMachineInfo);
            
            boolean result = materialInfoService.insertWmsMaterialInfo(wmsMachineInfo);
            if (result) {
                return Result.ok();
            } else {
                return Result.error("新增失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("新增机物料基础信息异常", e);
            return Result.error("请求处理异常：" + e.getMessage());
        }
    }

    @Operation(
            summary = "修改机物料基础信息",
            description = "修改机物料基础信息"
    )
    @PostMapping("/materialInfo/update")
    public Result<Void> updateMachineMaterial(
            @Parameter(description = "机物料基础信息", required = true)
            @Validated @RequestBody MachineMaterialVo machineMaterialVo) {

        log.info("收到修改机物料基础信息请求: {}", machineMaterialVo.getMaterialCode());

        try {
            WmsMachineInfo wmsMachineInfo = new WmsMachineInfo();
            BeanUtils.copyProperties(machineMaterialVo, wmsMachineInfo);

            boolean result = materialInfoService.updateWmsMaterialInfo(wmsMachineInfo);
            if (result) {
                return Result.ok();
            } else {
                return Result.error("修改失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("修改机物料基础信息异常", e);
            return Result.error("请求处理异常：" + e.getMessage());
        }
    }
//
//    @Operation(
//            summary = "删除机物料基础信息",
//            description = "删除机物料基础信息"
//    )
//    @DeleteMapping("/materialInfo/delete")
//    public Result<Void> deleteMachineMaterial(
//            @Parameter(description = "机物料基础信息ID", required = true)
//            @RequestParam String id) {
//
//        log.info("收到删除机物料基础信息请求: {}", id);
//
//        try {
//            boolean result = materialInfoService.deleteWmsMaterialInfoByIds(new String[]{id});
//            if (result) {
//                return Result.ok();
//            } else {
//                return Result.error("删除失败，请稍后重试");
//            }
//        } catch (Exception e) {
//            log.error("删除机物料基础信息异常", e);
//            return Result.error("请求处理异常：" + e.getMessage());
//        }
//    }
//
//    @Operation(
//            summary = "批量删除机物料基础信息",
//            description = "批量删除机物料基础信息"
//    )
//    @DeleteMapping("/materialInfo/batch-delete")
//    public Result<Void> batchDeleteMachineMaterial(
//            @Parameter(description = "机物料基础信息ID列表", required = true)
//            @Validated @RequestBody MachineMaterialBatchVo batchVo) {
//
//        log.info("收到批量删除机物料基础信息请求，数量: {}", batchVo.getIds().length);
//
//        try {
//            boolean result = materialInfoService.deleteWmsMaterialInfoByIds(batchVo.getIds());
//            if (result) {
//                return Result.ok();
//            } else {
//                return Result.error("批量删除失败，请稍后重试");
//            }
//        } catch (Exception e) {
//            log.error("批量删除机物料基础信息异常", e);
//            return Result.error("请求处理异常：" + e.getMessage());
//        }
//    }
//
//    @Operation(
//            summary = "获取机物料基础信息详情",
//            description = "根据ID获取机物料基础信息详情"
//    )
//    @GetMapping("/materialInfo/detail")
//    public Result<WmsMachineInfo> getMachineMaterialDetail(
//            @Parameter(description = "机物料基础信息ID", required = true)
//            @RequestParam String id) {
//
//        log.info("收到获取机物料基础信息详情请求: {}", id);
//
//        try {
//            WmsMachineInfo wmsMachineInfo = materialInfoService.selectWmsMaterialInfoById(id);
//            if (wmsMachineInfo != null) {
//                return Result.ok(wmsMachineInfo);
//            } else {
//                return Result.error("未找到相关信息");
//            }
//        } catch (Exception e) {
//            log.error("获取机物料基础信息详情异常", e);
//            return Result.error("请求处理异常：" + e.getMessage());
//        }
//    }
} 