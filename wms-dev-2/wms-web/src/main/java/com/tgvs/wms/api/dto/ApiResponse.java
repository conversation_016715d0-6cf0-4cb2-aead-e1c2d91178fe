package com.tgvs.wms.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


import java.time.LocalDateTime;

/**
 * API统一响应结构
 */
@Data
public class ApiResponse<T>  {

 
        /**     * 响应码     */

        @JsonProperty("Code")
        private String code;

    /**
     * 响应消息
     */
    private String message;

    private  boolean success;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    /**
     * 成功状态码
     */
    public static final String SUCCESS_CODE ="1";

    public static  final  String Erro_CODE ="0";

    /**
     * 参数错误状态码
     */
    public static final int PARAM_ERROR_CODE = 400;

    /**
     * 未授权状态码
     */
    public static final int UNAUTHORIZED_CODE = 401;

    /**
     * 禁止访问状态码
     */
    public static final int FORBIDDEN_CODE = 403;

    /**
     * 资源不存在状态码
     */
    public static final int NOT_FOUND_CODE = 404;

    /**
     * 服务器错误状态码
     */
    public static final int SERVER_ERROR_CODE = 500;

    /**
     * 私有构造方法
     */
    private ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }

        /**     * 成功响应（无数据）     */
        public static <T> ApiResponse<T> success() {
            ApiResponse<T> response = new ApiResponse<>();
            response.setCode(SUCCESS_CODE);
            response.setMessage("操作成功");
            response.setSuccess(true);
            return response;    }

    /**
     * 成功响应（有数据）
     */
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(SUCCESS_CODE);
        response.setMessage("操作成功");
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    /**
     * 成功响应（有数据和消息）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(SUCCESS_CODE);
        response.setMessage(message);
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(String.valueOf(code));
        response.setMessage(message);
        response.setSuccess(false);
        return response;
    }

    /**
     * 参数错误响应
     */
    public static <T> ApiResponse<T> paramError(String message) {
        return error(PARAM_ERROR_CODE, message);
    }

    /**
     * 未授权响应
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return error(UNAUTHORIZED_CODE, message);
    }

    /**
     * 禁止访问响应
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return error(FORBIDDEN_CODE, message);
    }

    /**
     * 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return error(NOT_FOUND_CODE, message);
    }

    /**
     * 服务器错误响应
     */
    public static <T> ApiResponse<T> serverError(String message) {
        return error(SERVER_ERROR_CODE, message);
    }
} 