package com.tgvs.wms.api.controller.machine;

import com.alibaba.fastjson.JSON;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.business.modules.machineMaterials.service.IMachineMaterialOutboundService;
import com.tgvs.wms.business.modules.machineMaterials.vo.MachineOutboundVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机物料出库控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/machine")
@RequiredArgsConstructor
@Tag(name = "机物料管理", description = "提供机物料管理相关接口")
public class MachineMaterialOutboundController {

    private final IMachineMaterialOutboundService IMachineMaterialOutboundService;

    @Operation(
            summary = "机物料出库单信息",
            description = ""
    )
    @PostMapping("/outMaterial/")
    @Transactional
    public Result<Void> outMachineMaterial(
            @Parameter(description = "出库物料信息", required = true)
            @Validated @RequestBody MachineOutboundVo request) {
        
        log.info("收到机物料出库单信息请求: {}", JSON.toJSONString(request));
        
        try {
            int result = IMachineMaterialOutboundService.insertWmsMachineOutbound(request);
            if (result>0) {
                return Result.ok();
            } else {
                return Result.error("1","处理失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("处理机物料出库单信息异常", e);
            return Result.error("请求处理异常：" + e.getMessage());
        }
    }
} 