package com.tgvs.wms.api.controller.task;

import com.alibaba.fastjson.JSON;
import com.tgvs.wms.business.httpservice.baseBean.agv.TaskExecutionNoticeRequest;
import com.tgvs.wms.business.httpservice.baseBean.agv.TaskExecutionNoticeResponse;
import com.tgvs.wms.business.modules.mqlog.entity.MqLog;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.common.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * AGV任务执行回馈接口
 */
@Slf4j
@Api(tags = "任务管理 - AGV任务执行回馈")
@RestController
@RequestMapping("/api/robot")
public class AgvTaskExecutionController {

    @Autowired
    private MainService mainService;


    /**
     * 任务执行过程回馈接口
     * 
     * @param request 任务执行过程回馈请求
     * @return 响应结果
     */
    @ApiOperation(value = "任务执行过程回馈接口")
    @PostMapping("/reporter/task/executionNotice")
    @Log(title = "任务执行过程回馈接口")
    public TaskExecutionNoticeResponse taskExecutionNotice(@RequestBody TaskExecutionNoticeRequest request) {
        
        if (request== null) {
            return TaskExecutionNoticeResponse.Error("","入参格式异常或入参空值");
        }
        
        log.warn("任务执行阶段: {}, 入参: {}", "executionNotice",JSON.toJSONString(request));

        // 防御性检查，确保调用链不为空
        if (this.mainService== null || this.mainService.agvHkService == null) {
            log.error("MainService调用链异常: mainServiceutil或agvHkService为空");
            //return TaskExecutionNoticeResponse.Error(request.getRobotTaskCode(), "系统内部服务异常");
            this.mainService= new MainService();
            this.mainService.StartService();
        }
        
        TaskExecutionNoticeResponse result =this.mainService.agvHkService.taskExecutionNotice(request);
        if (result != null && (result.getCode().equals("SUCCESS") || result.getCode().equals("200") || result.getCode().equals("0"))) {
            return TaskExecutionNoticeResponse.success(request.getRobotTaskCode());
        }
        log.error("AgvTaskExecutionController:任务执行回馈接口调用失败:入参 {}，接口返回结果：{}", JSON.toJSONString(request), JSON.toJSONString(result));
        return TaskExecutionNoticeResponse.Error(request.getRobotTaskCode(), "WMS系统内部服务异常:"+result);
        // 返回成功响应
    }
    
    /**
     * 插入MQ日志记录
     * 
     * @param sender 发送者
     * @param reciver 接收者
     * @param sysid 系统ID
     * @param taskid 任务ID
     * @param functionName 功能名称
     * @param text 消息内容
     */
    private void insertMQ(String sender, String reciver, String sysid, String taskid, String functionName, String text) {
        try {
            this.mainService.init();
            if (null != functionName) {
                Date nowtime = new Date();
                MqLog log = new MqLog();
                log.setSender(sender);
                log.setReceive(reciver);
                log.setType(functionName);
                log.setMqNumber(taskid);
                log.setSysid(sysid);
                log.setMsg(text);
                log.setMqTime(nowtime);
                log.setCreateTime(nowtime);
                log.setDevice(sysid);
                this.mainService.mqLogMapper.insert(log);
            }
        } catch (Exception exception) {
            log.error("插入MQ日志失败", exception);
        }
    }
}
