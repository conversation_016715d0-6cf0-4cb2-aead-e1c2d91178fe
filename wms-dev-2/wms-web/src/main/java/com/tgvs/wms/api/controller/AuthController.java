//package com.tgvs.wms.api.controller;
//
//
//import com.tgvs.wms.api.dto.LoginRequest;
//import com.tgvs.wms.api.dto.TokenResponse;
//import com.tgvs.wms.api.security.JwtTokenUtil;
//import com.tgvs.wms.common.constant.Result;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletRequest;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 认证控制器
// * 提供登录和令牌刷新功能
// */
//@RestController
//@RequestMapping("/api/v1/auth")
//@Tag(name = "认证管理", description = "提供登录认证和令牌刷新功能")
//@Slf4j
//public class AuthController {
//
//
//    private JwtTokenUtil jwtTokenUtil;
//
//    /**
//     * 模拟存储用户名和密码（实际应从数据库获取）
//     */
//    private static final Map<String, String> USERS = new HashMap<>();
//
//    static {
//        // 添加测试用户
//        USERS.put("api_user", "password123");
//        USERS.put("admin", "admin123");
//    }
//
//    @PostMapping("/login")
//    @Operation(summary = "用户登录", description = "通过用户名和密码获取访问令牌")
//    public Result<TokenResponse> login(@RequestBody LoginRequest loginRequest) {
//        log.info("API login attempt for user: {}", loginRequest.getUsername());
//
//        // 验证用户名和密码
//        if (!USERS.containsKey(loginRequest.getUsername()) ||
//            !USERS.get(loginRequest.getUsername()).equals(loginRequest.getPassword())) {
//            return Result.error("用户名或密码错误");
//        }
//
//        // 生成token
//        final String token = jwtTokenUtil.generateToken(loginRequest.getUsername());
//
//        // 返回token
//        TokenResponse tokenResponse = new TokenResponse();
//        tokenResponse.setToken(token);
//        tokenResponse.setTokenType("Bearer");
//        tokenResponse.setExpiresIn(JwtTokenUtil.JWT_TOKEN_VALIDITY);
//        tokenResponse.setUsername(loginRequest.getUsername());
//
//        return Result.ok(tokenResponse);
//    }
//
//    @PostMapping("/refresh")
//    @Operation(summary = "刷新令牌", description = "使用现有的有效令牌获取新的访问令牌")
//    public Result<TokenResponse> refreshToken(HttpServletRequest request) {
//        String authHeader = request.getHeader("Authorization");
//
//        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
//            return Result.error("未提供有效的令牌");
//        }
//
//        String token = authHeader.substring(7);
//        String username;
//
//        try {
//            username = jwtTokenUtil.getUsernameFromToken(token);
//
//            if (jwtTokenUtil.isTokenExpired(token)) {
//                return Result.error("令牌已过期，请重新登录");
//            }
//
//            // 生成新token
//            final String newToken = jwtTokenUtil.generateToken(username);
//
//            // 返回新token
//            TokenResponse tokenResponse = new TokenResponse();
//            tokenResponse.setToken(newToken);
//            tokenResponse.setTokenType("Bearer");
//            tokenResponse.setExpiresIn(JwtTokenUtil.JWT_TOKEN_VALIDITY);
//            tokenResponse.setUsername(username);
//
//            return Result.ok(tokenResponse);
//        } catch (Exception e) {
//            log.error("刷新令牌失败", e);
//            return Result.error("无效的令牌");
//        }
//    }
//}