package com.tgvs.wms.api.controller.task; // 假设任务相关的API放在task子包下


import org.springframework.web.bind.annotation.*;
// 引入服务

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 自动上报任务管理 API
 */
@Slf4j
@Api(tags = "任务管理 - 自动上报任务")
@RestController
@RequestMapping("/api/auto-report-tasks")
public class AutoReportTaskApiController {

 //   @Autowired
 //   private QuartzTriggerService quartzTriggerService;

//    @ApiOperation("手动刷新指定类型的自动上报任务触发器")
//    @PostMapping("/refresh/{taskType}")
//    public ApiResponse<?> refreshTaskTrigger() {
//
//        log.info("收到手动刷新任务触发器请求，类型: {}", taskType);
//
////        if (!QuartzTriggerService.INBOUND_TASK_TYPE.equals(taskType) && !QuartzTriggerService.OUTBOUND_TASK_TYPE.equals(taskType)) {
////            return ResponseEntity.badRequest().body("无效的任务类型，请使用 '1' 或 '2'");
////        }
////
////        boolean success = quartzTriggerService.syncTaskTrigger(taskType);
////
////        if (success) {
////            log.info("任务类型 [{}] 触发器刷新成功。", taskType);
////            return ResponseEntity.ok().body("任务类型 [" + taskType + "] 触发器刷新成功。");
////        } else {
////            log.error("任务类型 [{}] 触发器刷新失败。", taskType);
////            return ResponseEntity.internalServerError().body("任务类型 [" + taskType + "] 触发器刷新失败，请查看日志。");
////        }
//        return  null;
//    }

//    @ApiOperation("手动刷新所有自动上报任务触发器")
//    @PostMapping("/refresh/all")
//    public ResponseEntity<?> refreshAllTaskTriggers() {
//        log.info("收到手动刷新所有任务触发器请求...");
////        boolean success = quartzTriggerService.syncAllTaskTriggers();
////        if (success) {
////            log.info("所有自动上报任务触发器刷新成功。");
////            return ResponseEntity.ok().body("所有自动上报任务触发器刷新成功。");
////        } else {
////            log.error("刷新部分或所有自动上报任务触发器失败。");
////            return ResponseEntity.internalServerError().body("刷新部分或所有自动上报任务触发器失败，请查看日志。");
////        }
//        return  null;
//
//    }
}
