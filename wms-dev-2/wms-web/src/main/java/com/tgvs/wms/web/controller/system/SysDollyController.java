package com.tgvs.wms.web.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.core.domain.TreeNode;
import com.tgvs.wms.system.entity.SysDolly;
import com.tgvs.wms.system.service.system.SysDollyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Controller
@RequestMapping("system/dolly")
public class SysDollyController {

    @Resource
    private SysDollyService sysDollyService;

    @PostMapping("/list")
    @ResponseBody
    public Result getDollyList(@RequestBody(required = false) QueryModel queryModel){
        IPage<SysDolly> list = sysDollyService.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }



    /**
     * 获取菜单树
     */
    @PostMapping("/getDollyMenuTree")
    @ResponseBody
    public Result getDollyMenuTree(@RequestBody(required = false) String id){
        List<TreeNode> menuList = sysDollyService.getDollyMenuTree(id);
        return Result.ok(menuList);
    }

    /**
     *
     * @param queryModel
     * @return
     */

    @PostMapping("/add")
    @ResponseBody
    public Result add(@RequestBody SysDolly sysDolly){
        sysDollyService.add(sysDolly);
        return Result.ok();
    }

    /**
     *
     * @param queryModel
     * @return
     */

    @PostMapping("/update")
    @ResponseBody
    public Result update(@RequestBody SysDolly sysDolly){
        sysDollyService.update(sysDolly);
        return Result.ok();
    }

    /**
     *
     * @param queryModel
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    public Result delete(@RequestBody String id){
        sysDollyService.delete(id);
        return Result.ok();
    }

    /**
     *
     * @param queryModel
     * @return
     */
    @PostMapping("/queryById")
    @ResponseBody
    public Result queryById(@RequestBody String id){
        SysDolly sys = sysDollyService.getById(id);
        return Result.ok(sys);
    }
}
