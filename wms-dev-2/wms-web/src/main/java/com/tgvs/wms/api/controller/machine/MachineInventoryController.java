package com.tgvs.wms.api.controller.machine;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInventoryList;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.business.modules.machineMaterials.service.IMachineInventoryService;
import com.tgvs.wms.business.modules.machineMaterials.vo.MachineInventoryVo;
import com.tgvs.wms.common.core.domain.QueryModel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 机物料盘点控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/machine")
@RequiredArgsConstructor
@Tag(name = "机物料管理", description = "提供机物料管理相关接口")
public class MachineInventoryController {

    @Autowired
    private IMachineInventoryService IMachineInventoryService;

    /**
     * 分页查询机物料盘点单
     */
    @GetMapping("/inventory/list")
    @Operation(summary = "分页查询机物料盘点单列表", description = "分页查询机物料盘点单列表")
    @RequiresPermissions("system:machineInventory:list")
    public Result<IPage<WmsMachineInventoryList>> list(QueryModel queryModel) {
        IPage<WmsMachineInventoryList> page = IMachineInventoryService.pageList(queryModel);
        return Result.ok(page);
    }

//    /**
//     * 查询机物料盘点单详细信息
//     */
//    @GetMapping(value = "/inventory/{id}")
//    @Operation(summary = "获取机物料盘点单详细信息", description = "根据盘点单ID获取详细信息")
//    @RequiresPermissions("system:machineInventory:query")
//    public Result<WmsMachineInventoryList> getInfo(@PathVariable("id") String id) {
//        return Result.ok(machineInventoryService.selectMachineInventoryListById(id));
//    }
//
    /**
     * 推送机物料盘点单
     */
    @PostMapping("/inventory/add")
    @Operation(summary = "新增机物料盘点单", description = "新增机物料盘点单")
    @RequiresPermissions("system:machineInventory:add")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> add(@Valid @RequestBody MachineInventoryVo inventoryVo)
    {
        log.info("新增机物料盘点单："+ JSON.toJSONString(inventoryVo));
        boolean success=false;
        if(inventoryVo.getInventoryType()>0 && inventoryVo.getInventoryType()<4) {
             success = IMachineInventoryService.insertMachineInventoryList(inventoryVo);
            return success ? Result.ok() : Result.error("新增机物料盘点单失败");
        }else {
            return success ? Result.ok() : Result.error("新增机物料盘点单类型不正确");
        }
    }
//
//    /**
//     * 修改机物料盘点单
//     */
//    @PostMapping("/inventory/edit")
//    @Operation(summary = "修改机物料盘点单", description = "修改机物料盘点单")
//    @RequiresPermissions("system:machineInventory:edit")
//    public Result<?> edit(@Valid @RequestBody MachineInventoryVo inventoryVo) {
//        boolean success = machineInventoryService.updateMachineInventoryList(inventoryVo);
//        return success ? Result.ok() : Result.error("修改机物料盘点单失败");
//    }
//
//    /**
//     * 删除机物料盘点单
//     */
//    @DeleteMapping("/inventory/{ids}")
//    @Operation(summary = "删除机物料盘点单", description = "删除机物料盘点单")
//    @RequiresPermissions("system:machineInventory:remove")
//    public Result<?> remove(@PathVariable("ids") String ids) {
//        boolean success = machineInventoryService.deleteMachineInventoryListByIds(ids.split(","));
//        return success ? Result.ok() : Result.error("删除机物料盘点单失败");
//    }
//
//    /**
//     * 根据条件查询机物料盘点单
//     */
//    @PostMapping("/inventory/query")
//    @Operation(summary = "根据条件查询机物料盘点单", description = "根据条件查询机物料盘点单")
//    @RequiresPermissions("system:machineInventory:query")
//    public Result<?> query(@RequestBody MachineInventoryQueryVo queryVo) {
//        return Result.ok(machineInventoryService.selectMachineInventoryListList(queryVo));
//    }
} 