package com.tgvs.wms.web.controller.system;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.LoginUser;
import com.tgvs.wms.common.util.ShiroUtils;
import com.tgvs.wms.common.util.StringUtils;
import com.tgvs.wms.system.entity.SysUser;
import com.tgvs.wms.system.service.system.SysUserService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/sys")
@Api(tags="用户登录")
@Slf4j
public class SysLoginController {

    @Resource
    private SysUserService sysUserService;

    @PostMapping("/login")
    @ResponseBody
    public Result login(String username, String password)
    {
        UsernamePasswordToken token = new UsernamePasswordToken(username, password);
        Subject subject = SecurityUtils.getSubject();
        try{
            subject.login(token);
            return Result.ok();
        }
        catch (AuthenticationException e){
            log.error("登录异常", e);
            String msg = "用户或密码错误";
            if (StringUtils.isNotEmpty(e.getMessage()))
            {
                msg = e.getMessage();
            }
            return Result.error(msg);
        }
    }

    @GetMapping("/getLoginInfo")
    @ResponseBody
    public Result getLoginInfo(){
        return Result.ok(sysUserService.getLoginInfo());
    }

    /**
     * 退出登录
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/logout")
    public Result<Object> logout(HttpServletRequest request, HttpServletResponse response) {
        //用户退出逻辑
//        String token = request.getHeader(CommonConstant.X_ACCESS_TOKEN);
//        if(oConvertUtils.isEmpty(token)) {
//            return Result.error("退出登录失败！");
//        }
        LoginUser sysUser = ShiroUtils.getLoginUser();
        if(sysUser!=null) {
            String username = sysUser.getUsername();
            //update-begin--Author:wangshuai  Date:20200714  for：登出日志没有记录人员
//            baseCommonService.addLog("用户名: "+sysUser.getRealname()+",退出成功！", CommonConstant.LOG_TYPE_1, null,sysUser);
//            //update-end--Author:wangshuai  Date:20200714  for：登出日志没有记录人员
//            log.info(" 用户名:  "+sysUser.getRealname()+",退出成功！ ");
//            //清空用户登录Token缓存
//            redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + token);
//            //清空用户登录Shiro权限缓存
//            redisUtil.del(CommonConstant.PREFIX_USER_SHIRO_CACHE + sysUser.getId());
//            //清空用户的缓存信息（包括部门信息），例如sys:cache:user::<username>
//            redisUtil.del(String.format("%s::%s", CacheConstant.SYS_USERS_CACHE, sysUser.getUsername()));
            //调用shiro的logout
            ShiroUtils.getSubject().logout();
            return Result.ok("退出登录成功！");
        }else {
            return Result.ok("退出登录成功！");
        }
    }


    /**
     * 首页用户重置密码
     */
    //@RequiresRoles({"admin"})
    @RequestMapping(value = "/updatePassword", method = RequestMethod.POST)
    public Result<?> updatePassword(String oldpassword, String password, String confirmpassword) {
//        String oldpassword = json.getString("oldpassword");
//        String password = json.getString("password");
//        String confirmpassword = json.getString("confirmpassword");
        LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
        if(sysUser == null){
            return Result.error("请重新登录！");
        }
        String username = sysUser.getUsername();
        SysUser user = this.sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, username));
        if(user==null) {
            return Result.error("用户不存在！");
        }
        return sysUserService.resetPassword(username,oldpassword,password,confirmpassword);
    }

}
