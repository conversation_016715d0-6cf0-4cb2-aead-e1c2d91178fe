package com.tgvs.wms.api.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;

/**
 * API安全配置
 */
@Configuration
@EnableWebSecurity
public class WebSecurityConfig {

    /**
     * API安全过滤链
     * 为了简化集成，暂时允许API接口匿名访问
     * 实际生产环境应该根据安全需求实现适当的认证机制
     */
    @Bean
    @Order(1)
    public SecurityFilterChain apiSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf().disable()
            .antMatcher("/api/**")
            .authorizeRequests()
                .anyRequest().permitAll()
            .and()
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS);
        
        return http.build();
    }
    
    /**
     * 默认安全过滤链
     * 这里保持对其他URL的原有安全控制，避免干扰现有系统
     */
    @Bean
    public SecurityFilterChain defaultSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf().disable()
            .authorizeRequests()
                // 允许访问Swagger和API文档
                .antMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                // 其他请求遵循原有安全规则，这里简化为允许所有
                // 实际项目中应该根据现有安全配置适当调整
                .anyRequest().permitAll();
        
        return http.build();
    }
} 