spring.profiles.active=test
server.port=8087


# 是否启用定时任务
# 心跳间隔（毫秒）
# 监控间隔（毫秒）
logging.config=classpath:logback-spring.xml
logging.file.path=D:\\tmp\\log\\wms
wms.logging.format=text
wms.system.id=WMS
spring.jackson.date-format=yyyy-MM-dd HH\:mm\:ss
spring.jackson.default-property-inclusion=non_null



spring.messages.basename=i18n/messages

# SpringDoc OpenAPI配置
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
springdoc.packages-to-scan=com.tgvs.wms.api.controller




