package com.tgvs.wms.common.trace;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import com.tgvs.wms.common.trace.logging.util.LoggerUtils;
import org.springframework.core.Ordered;


import com.tgvs.wms.common.util.CommonUtil;
import lombok.Getter;

public class ServiceContextFilter implements Filter {

    @Getter
    private int order = Ordered.HIGHEST_PRECEDENCE;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        String requestId = CommonUtil.getUuid();

        try {
            LoggerUtils.putRequestId(requestId);
            filterChain.doFilter(servletRequest, servletResponse);
        }finally {
            LoggerUtils.removeRequestId();
        }
    }
}
