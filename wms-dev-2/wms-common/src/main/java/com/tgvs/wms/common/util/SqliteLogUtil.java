package com.tgvs.wms.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * SQLite日志工具类
 */
@Slf4j
@Component
public class SqliteLogUtil {
    
    private static final String DB_PATH = "d:/tmp/log/api_log.db";
    private static final String DB_URL = "jdbc:sqlite:" + DB_PATH;
    
    @PostConstruct
    public void init() {
        // 显式加载SQLite JDBC驱动
        try {
            Class.forName("org.sqlite.JDBC");
            log.info("SQLite JDBC驱动加载成功");
        } catch (ClassNotFoundException e) {
            log.error("SQLite JDBC驱动加载失败", e);
            return;
        }
        createTableIfNotExists();
    }
    
    /**
     * 创建表（如果不存在）
     */
    private void createTableIfNotExists() {
        // 确保日志目录存在
        java.io.File dbFile = new java.io.File(DB_PATH);
        java.io.File parentDir = dbFile.getParentFile();
        if (!parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (created) {
                log.info("创建日志目录: {}", parentDir.getAbsolutePath());
            } else {
                log.warn("创建日志目录失败: {}", parentDir.getAbsolutePath());
            }
        }
        
        String sql = "CREATE TABLE IF NOT EXISTS api_log (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "request_id TEXT," +
                "module TEXT," +
                "class_name TEXT," +
                "method_name TEXT," +
                "request_url TEXT," +
                "request_method TEXT," +
                "client_ip TEXT," +
                "user_agent TEXT," +
                "request_params TEXT," +
                "response_data TEXT," +
                "execution_time INTEGER," +
                "status TEXT," +
                "error_message TEXT," +
                "create_time TEXT" +
                ")";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            log.info("SQLite日志表初始化成功: {}", DB_PATH);
        } catch (SQLException e) {
            log.error("创建SQLite日志表失败", e);
        }
    }
    
    /**
     * 异步保存日志到SQLite
     */
    public void saveLogAsync(String requestId, String module, String className, String methodName,
                           String requestUrl, String requestMethod, String clientIp, String userAgent,
                           String requestParams, String responseData, Long executionTime,
                           String status, String errorMessage) {
        
        // 使用线程池异步执行，避免影响接口性能
        new Thread(() -> {
            try {
                // 确保驱动已加载
                Class.forName("org.sqlite.JDBC");
            } catch (ClassNotFoundException e) {
                log.error("SQLite JDBC驱动未找到", e);
                return;
            }
            
            String sql = "INSERT INTO api_log (request_id, module, class_name, method_name, request_url, " +
                    "request_method, client_ip, user_agent, request_params, response_data, " +
                    "execution_time, status, error_message, create_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            try (Connection conn = DriverManager.getConnection(DB_URL);
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {
                
                pstmt.setString(1, requestId);
                pstmt.setString(2, module);
                pstmt.setString(3, className);
                pstmt.setString(4, methodName);
                pstmt.setString(5, requestUrl);
                pstmt.setString(6, requestMethod);
                pstmt.setString(7, clientIp);
                pstmt.setString(8, userAgent);
                pstmt.setString(9, requestParams);
                pstmt.setString(10, responseData);
                pstmt.setLong(11, executionTime != null ? executionTime : 0);
                pstmt.setString(12, status);
                pstmt.setString(13, errorMessage);
                pstmt.setString(14, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                
                pstmt.executeUpdate();
                log.debug("接口日志保存到SQLite成功: {}", requestId);
                
            } catch (SQLException e) {
                log.error("保存接口日志到SQLite失败: {}", e.getMessage(), e);
            }
        }).start();
    }
} 