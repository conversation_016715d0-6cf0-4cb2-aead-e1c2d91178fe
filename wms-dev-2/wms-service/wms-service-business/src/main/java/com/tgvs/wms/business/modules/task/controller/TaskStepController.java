package com.tgvs.wms.business.modules.task.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.storage.entity.CacheLocationRowBase;
import com.tgvs.wms.business.modules.task.entity.TaskStep;
import com.tgvs.wms.business.modules.task.service.ITaskStepService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"作业任务步序"})
@RestController
@RequestMapping({"/task/taskStep"})
@Slf4j
public class TaskStepController extends BaseController<TaskStep, ITaskStepService> {

    @Autowired
    private ITaskStepService taskStepService;

    @ApiOperation(value = "作业任务步序-分页列表查询", notes = "作业任务步序-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        queryModel.descSort("end_time");
        IPage<TaskStep> pageList = taskStepService.pageList(queryModel);
        Result result = Result.ok(pageList.getRecords());
        result.setTotal(pageList.getTotal());
        return result;
    }

//    @AutoLog("作业任务步序-添加")
//    @ApiOperation(value = "作业任务步序-添加", notes = "作业任务步序-添加")
//    @RequiresPermissions({"taskStep:add"})
//    @PostMapping({"/add"})
//    public Result<?> add(@RequestBody TaskStep taskStep) {
//        this.taskStepService.save(taskStep);
//        return Result.OK("添加成功！");
//    }
//
//    @AutoLog("作业任务步序-编辑")
//    @ApiOperation(value = "作业任务步序-编辑", notes = "作业任务步序-编辑")
//    @RequiresPermissions({"taskStep:edit"})
//    @PostMapping({"/edit"})
//    public Result<?> edit(@RequestBody TaskStep taskStep) {
//        this.taskStepService.updateById(taskStep);
//        return Result.OK("编辑成功!");
//    }
//
//    @AutoLog("作业任务步序-通过id删除")
//    @ApiOperation(value = "作业任务步序-通过id删除", notes = "作业任务步序-通过id删除")
//    @RequiresPermissions({"taskStep:delete"})
//    @PostMapping({"/delete"})
//    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
//        this.taskStepService.removeById(id);
//        return Result.OK("删除成功!");
//    }
//
//    @AutoLog("作业任务步序-批量删除")
//    @ApiOperation(value = "作业任务步序-批量删除", notes = "作业任务步序-批量删除")
//    @RequiresPermissions({"taskStep:deleteBatch"})
//    @PostMapping({"/deleteBatch"})
//    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
//        this.taskStepService.removeByIds(Arrays.asList(ids.split(",")));
//        return Result.OK("批量删除成功!");
//    }
//
    @AutoLog("作业任务步序-通过id查询")
    @ApiOperation(value = "作业任务步序-通过id查询", notes = "作业任务步序-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        TaskStep taskStep = (TaskStep)this.taskStepService.getById(id);
        if (taskStep == null)
            return Result.error("未找到对应数据");
        return Result.OK(taskStep);
    }
//
//    @RequestMapping({"/exportXls"})
//    public ModelAndView exportXls(HttpServletRequest request, TaskStep taskStep) {
//        return exportXls(request, taskStep, TaskStep.class, "作业任务步序");
//    }
//
//    @RequiresPermissions({"taskStep:importExcel"})
//    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return importExcel(request, response, TaskStep.class);
//    }
}