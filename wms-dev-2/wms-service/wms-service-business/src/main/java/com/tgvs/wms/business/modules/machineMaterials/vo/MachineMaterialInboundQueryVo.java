package com.tgvs.wms.business.modules.machineMaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机物料入库查询VO
 */
@Data
@Schema(description = "机物料入库查询参数")
public class MachineMaterialInboundQueryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private String id;
    
    @Schema(description = "入库单号")
    private String inStoreNumber;
    
    @Schema(description = "物料编码")
    private String materialCode;
    
    @Schema(description = "物料名称")
    private String materialName;
    
    @Schema(description = "资产类别")
    private String assetClass;
    
    @Schema(description = "资产规格")
    private String assetModel;
    
    @Schema(description = "品牌")
    private String brand;
    
    @Schema(description = "入库优先级")
    private Integer priority;
    
    @Schema(description = "入库类型（1为采购入库）")
    private Integer taskType;
    
    @Schema(description = "开始创建时间")
    private Date beginCreateTime;
    
    @Schema(description = "结束创建时间")
    private Date endCreateTime;
} 