package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.J<PERSON><PERSON>ield;

public class cuttingCallEmptyDolly {
    @<PERSON><PERSON><PERSON><PERSON>(name = "TaskID")
    private String TaskID;

    @J<PERSON><PERSON>ield(name = "SiteNo")
    private String SiteNo;

    @J<PERSON><PERSON>ield(name = "NonPartition")
    private String NonPartition;

    @JSONField(name = "LayerQty")
    private String LayerQty;

    @JSONField(name = "SysCode")
    private String SysCode;

    public void setTaskID(String TaskID) {
        this.TaskID = TaskID;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setNonPartition(String NonPartition) {
        this.NonPartition = NonPartition;
    }

    public void setLayerQty(String LayerQty) {
        this.LayerQty = LayerQty;
    }

    public void setSysCode(String SysCode) {
        this.SysCode = SysCode;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof cuttingCallEmptyDolly))
            return false;
        cuttingCallEmptyDolly other = (cuttingCallEmptyDolly)o;
        if (!other.canEqual(this))
            return false;
        Object this$TaskID = getTaskID(), other$TaskID = other.getTaskID();
        if ((this$TaskID == null) ? (other$TaskID != null) : !this$TaskID.equals(other$TaskID))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$NonPartition = getNonPartition(), other$NonPartition = other.getNonPartition();
        if ((this$NonPartition == null) ? (other$NonPartition != null) : !this$NonPartition.equals(other$NonPartition))
            return false;
        Object this$LayerQty = getLayerQty(), other$LayerQty = other.getLayerQty();
        if ((this$LayerQty == null) ? (other$LayerQty != null) : !this$LayerQty.equals(other$LayerQty))
            return false;
        Object this$SysCode = getSysCode(), other$SysCode = other.getSysCode();
        return !((this$SysCode == null) ? (other$SysCode != null) : !this$SysCode.equals(other$SysCode));
    }

    protected boolean canEqual(Object other) {
        return other instanceof cuttingCallEmptyDolly;
    }

    public int hashCode() {
        int PRIME = 59,     result = 1;
        Object $TaskID = getTaskID();
        result = result * 59 + (($TaskID == null) ? 43 : $TaskID.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $NonPartition = getNonPartition();
        result = result * 59 + (($NonPartition == null) ? 43 : $NonPartition.hashCode());
        Object $LayerQty = getLayerQty();
        result = result * 59 + (($LayerQty == null) ? 43 : $LayerQty.hashCode());
        Object $SysCode = getSysCode();
        return result * 59 + (($SysCode == null) ? 43 : $SysCode.hashCode());
    }

    public String toString() {
        return "cuttingCallEmptyDolly(TaskID=" + getTaskID() + ", SiteNo=" + getSiteNo() + ", NonPartition=" + getNonPartition() + ", LayerQty=" + getLayerQty() + ", SysCode=" + getSysCode() + ")";
    }

    public String getTaskID() {
        return this.TaskID;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getNonPartition() {
        return this.NonPartition;
    }

    public String getLayerQty() {
        return this.LayerQty;
    }

    public String getSysCode() {
        return this.SysCode;
    }
}
