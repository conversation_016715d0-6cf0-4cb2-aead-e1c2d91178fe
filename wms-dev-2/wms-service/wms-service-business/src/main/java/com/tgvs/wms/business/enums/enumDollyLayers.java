package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumDollyLayers {
    zero(Integer.valueOf(0), "zero", "0层"),
    two(Integer.valueOf(2), "two", "2层"),
    three(Integer.valueOf(3), "three", "3层"),
    four(Integer.valueOf(4), "four", "4层");

    private Integer value;

    private String code;

    private String text;

    enumDollyLayers(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumDollyLayers getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumDollyLayers val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumDollyLayers toEnum(Integer Value) {
        for (enumDollyLayers e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}
