package com.tgvs.wms.business.modules.machineMaterials.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInfo;
import com.tgvs.wms.business.modules.machineMaterials.service.IMaterialInfoService;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = {"机物料基础信息"})
@RestController
@RequestMapping({"/machineInfo"})
public class MaterialInfoController extends BaseController<WmsMachineInfo, IMaterialInfoService> {

    @Autowired
    private IMaterialInfoService materialInfoService;

    @ApiOperation(value = "机物料基础信息管理-分页列表查询", notes = "机物料基础信息管理-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<WmsMachineInfo> list = materialInfoService.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }

}
