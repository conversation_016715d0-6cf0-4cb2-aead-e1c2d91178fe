package com.tgvs.wms.business.modules.machineMaterials.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机物料出库结果视图对象
 */
@Data
@Schema(description = "机物料出库结果视图对象")
public class MachineOutboundDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 出库单号
     */
    @Schema(description = "出库单号")
    private String outStoreNumber;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String materialName;

    /**
     * 出库数量
     */
    @Schema(description = "出库数量")
    private Integer outQuantity;

    /**
     * 出库优先级
     */
    @Schema(description = "出库优先级，优先级的值越大，出库级别越高")
    private Integer priority;

    /**
     * 状态
     */
    @Schema(description = "状态(0:待处理,1:处理中,2:已完成,3:已取消)")
    private Integer status;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
} 