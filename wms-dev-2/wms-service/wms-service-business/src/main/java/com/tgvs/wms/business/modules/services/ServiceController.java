package com.tgvs.wms.business.modules.services;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.httpservice.baseBean.mes.SowWallBean;
import com.tgvs.wms.business.httpservice.baseBean.warehouse.OutOrder;
import com.tgvs.wms.business.modules.commncation.entity.CommncationConfig;
import com.tgvs.wms.business.modules.commncation.entity.CommncationDevice;
import com.tgvs.wms.business.modules.commncation.service.ICommncationConfigService;
import com.tgvs.wms.business.modules.commncation.service.ICommncationDeviceService;
import com.tgvs.wms.business.modules.commncation.service.IDpsControllerinfoService;
import com.tgvs.wms.business.modules.task.entity.TaskBox;
import com.tgvs.wms.business.modules.task.service.ITaskBoxService;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.query.QueryGenerator;
import com.tgvs.wms.common.util.httpUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@Api(tags = {"系统通讯配置"})
@RestController
@RequestMapping({"/service"})
@Slf4j
public class ServiceController extends BaseController<CommncationConfig, ICommncationConfigService> {

    @Autowired
    private ICommncationConfigService commncationConfigService;

    @Autowired
    private ICommncationDeviceService commncationDeviceService;

    @Autowired
    private IDpsControllerinfoService dpsControllerinfoService;

    @Autowired
    private MainService mainService;

    @Autowired
    private ITaskBoxService taskBoxService;

    @ApiOperation(value = "系统通讯配置-分页列表查询", notes = "系统通讯配置-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(CommncationConfig commncationConfig, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CommncationConfig> queryWrapper = QueryGenerator.initQueryWrapper(commncationConfig, req.getParameterMap());
        Page<CommncationConfig> page = new Page(pageNo.intValue(), pageSize.intValue());
        IPage<CommncationConfig> pageList = this.commncationConfigService.page((IPage)page, (Wrapper)queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog("系统通讯配置-添加")
    @ApiOperation(value = "系统通讯配置-添加", notes = "系统通讯配置-添加")
    @RequiresPermissions({"commncationConfig:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody CommncationConfig commncationConfig) {
        this.commncationConfigService.save(commncationConfig);
        return Result.OK("添加成功！");
    }

    @AutoLog("系统通讯配置-编辑")
    @ApiOperation(value = "系统通讯配置-编辑", notes = "系统通讯配置-编辑")
    @RequiresPermissions({"commncationConfig:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody CommncationConfig commncationConfig) {
        this.commncationConfigService.updateById(commncationConfig);
        return Result.OK("编辑成功!");
    }

    @AutoLog("系统通讯配置-通过id删除")
    @ApiOperation(value = "系统通讯配置-通过id删除", notes = "系统通讯配置-通过id删除")
    @RequiresPermissions({"commncationConfig:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.commncationConfigService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("系统通讯配置-批量删除")
    @ApiOperation(value = "系统通讯配置-批量删除", notes = "系统通讯配置-批量删除")
    @RequiresPermissions({"commncationConfig:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.commncationConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("系统通讯配置-通过id查询")
    @ApiOperation(value = "系统通讯配置-通过id查询", notes = "系统通讯配置-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        CommncationConfig commncationConfig = (CommncationConfig)this.commncationConfigService.getById(id);
        if (commncationConfig == null)
            return Result.error("未找到对应数据");
        return Result.OK(commncationConfig);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, CommncationConfig commncationConfig) {
        return exportXls(request, commncationConfig, CommncationConfig.class, "系统通讯配置");
    }

    @RequiresPermissions({"commncationConfig:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, CommncationConfig.class);
    }

    @AutoLog("系统通讯配置-测试接口")
    @ApiOperation(value = "系统通讯配置-测试接口", notes = "系统通讯配置-测试接口")
    @PostMapping({"/test"})
    public Result<?> test(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("11111");
            SowWallBean bean = new SowWallBean();
            bean.setCrossNo("87997787");
            bean.setPieceNo("8347873076073");
            bean.setSiteNo("A0101");
            bean.setUserNo("xz");
            String result = httpUtils.doPostHttp("MES", "", "pickWall", JSON.toJSONString(bean), "");
            log.info(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.OK("测试成功!");
    }

    @AutoLog("系统通讯配置-测试接口")
    @ApiOperation(value = "系统通讯配置-测试接口", notes = "系统通讯配置-测试接口")
    @PostMapping({"/test2"})
    public Result<?> test2(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("11111");
            SowWallBean bean = new SowWallBean();
            bean.setCrossNo("87997787");
            bean.setPieceNo("8347873076073");
            bean.setSiteNo("A0101");
            bean.setUserNo("xz");
            String result = httpUtils.doPostHttpwithToken("MES", "", "", "pickWall", JSON.toJSONString(bean));
            log.info(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.OK("测试成功!");
    }

    @AutoLog(value = "服务管理-socket连接", operateType = 3)
    @ApiOperation(value = "服务管理-socket连接", notes = "服务管理-socket连接")
    @RequiresPermissions({"socket:connect"})
    @PostMapping({"/socket/connect"})
    public Result<?> socketConnect(@RequestParam(name = "id", required = true) String id) {
        CommncationDevice commncationDevice = (CommncationDevice)this.commncationDeviceService.getById(id);
        if (null != commncationDevice)
            switch (commncationDevice.getSysid()) {
                case "MFC":
                    this.mainService.wcsServer.reconnect(commncationDevice);
                    break;
                case "CONV":
                   // this.mainService.convService.reconnect(commncationDevice);
                    break;
                case "LIFT":
                    this.mainService.liftService.reconnect(commncationDevice);
                    break;
                case "Robot":
                    this.mainService.robotService.reconnect(commncationDevice);
                    break;
            }
        return Result.OK("连接成功！");
    }

    @AutoLog(value = "服务管理-socket断开", operateType = 3)
    @ApiOperation(value = "服务管理-socket断开", notes = "服务管理-socket断开")
    @RequiresPermissions({"socket:disconnect"})
    @PostMapping({"/socket/disconnect"})
    public Result<?> socketDisConnect(@RequestParam(name = "id", required = true) String id) {
        CommncationDevice commncationDevice = (CommncationDevice)this.commncationDeviceService.getById(id);
        if (null != commncationDevice)
            switch (commncationDevice.getSysid()) {
                case "MFC":
                    this.mainService.wcsServer.disconnect(commncationDevice);
                    break;
                case "CONV":
                   // this.mainService.convService.disconnect(commncationDevice);
                    break;
                case "LIFT":
                    this.mainService.liftService.disconnect(commncationDevice);
                    break;
                case "Robot":
                    this.mainService.robotService.disconnect(commncationDevice);
                    break;
            }
        return Result.OK("连接断开！");
    }

    @AutoLog(value = "服务管理-启动服务", operateType = 3)
    @ApiOperation(value = "服务管理-启动服务", notes = "服务管理-启动服务")
    @RequiresPermissions({"server:start"})
    @PostMapping({"/system/start"})
    public Result<?> serverstart(@RequestParam(name = "id", required = true) String id) {
        CommncationConfig commncationConfig = (CommncationConfig)this.commncationConfigService.getById(id);
        if (null != commncationConfig) {
            switch (commncationConfig.getSysid()) {
                case "MFC1":
                    this.mainService.wcsServer.init();
                    this.mainService.wcsServer.Start();
                    commncationConfig.setRunstate(Integer.valueOf(1));
                    break;
                case "CONV":
//                    this.mainService.convService.init();
//                    this.mainService.convService.Start();
                    commncationConfig.setRunstate(Integer.valueOf(1));
                    break;
                case "LIFT":
                    this.mainService.liftService.init();
                    this.mainService.liftService.Start();
                    commncationConfig.setRunstate(Integer.valueOf(1));
                    break;
                case "Robot":
                    this.mainService.robotService.init();
                    this.mainService.robotService.Start();
                    commncationConfig.setState(Integer.valueOf(1));
                    break;
                case "AGV":
                    this.mainService.agvService.init();
                    this.mainService.agvService.Start();
                    commncationConfig.setRunstate(Integer.valueOf(1));
                    break;
                case "MES":
                    this.mainService.mesService.init();
                    this.mainService.mesService.Start();
                    commncationConfig.setRunstate(Integer.valueOf(1));
                    break;
                case "DPS":
                    this.mainService.dpsService.init();
                    this.mainService.dpsService.Start();
                    commncationConfig.setRunstate(Integer.valueOf(1));
                    break;
            }
            this.commncationConfigService.updateById(commncationConfig);
        }
        return Result.OK("启动服务成功！");
    }

    @AutoLog(value = "服务管理-停止服务", operateType = 3)
    @ApiOperation(value = "服务管理-停止服务", notes = "服务管理-停止服务")
    @RequiresPermissions({"server:stop"})
    @PostMapping({"/system/stop"})
    public Result<?> serverstop(@RequestParam(name = "id", required = true) String id) {
        CommncationConfig commncationConfig = (CommncationConfig)this.commncationConfigService.getById(id);
        if (null != commncationConfig) {
            switch (commncationConfig.getSysid()) {
                case "MFC1":
                    this.mainService.wcsServer.Stop();
                    commncationConfig.setRunstate(Integer.valueOf(0));
                    break;
                case "CONV":
                 //   this.mainService.convService.Stop();
                    commncationConfig.setRunstate(Integer.valueOf(0));
                    break;
                case "LIFT":
                    this.mainService.liftService.Stop();
                    commncationConfig.setRunstate(Integer.valueOf(0));
                    break;
                case "Robot":
                    this.mainService.robotService.Stop();
                    commncationConfig.setRunstate(Integer.valueOf(0));
                    break;
                case "AGV":
                    this.mainService.agvService.Stop();
                    commncationConfig.setRunstate(Integer.valueOf(0));
                    break;
                case "MES":
                    this.mainService.mesService.Stop();
                    commncationConfig.setRunstate(Integer.valueOf(0));
                    break;
                case "DPS":
                    this.mainService.dpsService.Stop();
                    commncationConfig.setRunstate(Integer.valueOf(0));
                    break;
            }
            this.commncationConfigService.updateById(commncationConfig);
        }
        return Result.OK("停止服务成功！");
    }

    @AutoLog("取消已经下达的出库任务")
    @ApiOperation(value = "取消已经下达的出库任务", notes = "取消已经下达的出库任务")
    @PostMapping({"/task/cancle"})
    public Result<?> cancleBoxtask(@RequestParam(name = "id", required = true) String id) {
        TaskBox taskBox = (TaskBox)this.taskBoxService.getById(id);
        if (null != taskBox) {
            OutOrder outOrder = this.mainService.wcsServer.getOutOrdercancle(taskBox);
            this.mainService.wcsServer.sendOrder(JSON.toJSONString(outOrder));
        }
        return Result.OK("成功发送消息到MFC！");
    }
}
