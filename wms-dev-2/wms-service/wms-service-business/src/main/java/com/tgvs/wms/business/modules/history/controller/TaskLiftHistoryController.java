package com.tgvs.wms.business.modules.history.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.history.entity.TaskLiftHistory;
import com.tgvs.wms.business.modules.history.service.ITaskLiftHistoryService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = {"提升机任务管理"})
@RestController
@RequestMapping({"/history/taskLift"})
public class TaskLiftHistoryController extends BaseController<TaskLiftHistory, ITaskLiftHistoryService> {

    @Autowired
    private ITaskLiftHistoryService taskLiftHistoryService;

    @ApiOperation(value = "提升机任务管理-分页列表查询", notes = "提升机任务管理-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(TaskLiftHistory taskLift, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<TaskLiftHistory> queryWrapper = QueryGenerator.initQueryWrapper(taskLift, req.getParameterMap());
        Page<TaskLiftHistory> page = new Page(pageNo.intValue(), pageSize.intValue());
        IPage<TaskLiftHistory> pageList = this.taskLiftHistoryService.page((IPage)page, (Wrapper)queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog("提升机任务管理-通过id查询")
    @ApiOperation(value = "提升机任务管理-通过id查询", notes = "提升机任务管理-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        TaskLiftHistory taskLift = (TaskLiftHistory)this.taskLiftHistoryService.getById(id);
        if (taskLift == null)
            return Result.error("未找到对应数据");
        return Result.OK(taskLift);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, TaskLiftHistory taskLift) {
        return exportXls(request, taskLift, TaskLiftHistory.class, "提升机任务管理");
    }

    @RequiresPermissions({"taskLift:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, TaskLiftHistory.class);
    }
}