package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import java.util.List;

/**
 * 外设执行通知请求对象
 * 对应WCS通知RCS外设执行完成的接口
 */
@Data
public class EquipmentNotifyRequest {
    /**
     * 设备编号/设备名称 (如电梯编号、自动门编号)
     */
    private String eqptCode;
    
    /**
     * 任务号，唯一编号
     */
    private String taskCode;
    
    /**
     * 任务执行状态
     * 1: 开门到位(自动门,风淋门)
     * 2: 关门到位(自动门,风淋门)
     * 3: 开门到位(电梯)
     * 4: 物料到达目标楼层(电梯)
     * 5: 取货(装卸机、输送线)
     * 6: 放货(装卸机、输送线)
     * 7: 到达
     */
    private String actionStatus;
    
    /**
     * 分配的站点编号
     */
    private String siteCode;
    
    /**
     * 载具的信息，包含载具编号、层号(CTU装卸机使用)
     */
    private List<CarrierInfo> carrierInfo;
    
    /**
     * 自定义扩展字段
     */
    private JSONObject extra;
    
    /**
     * 载具信息对象
     */
    @Data
    public static class CarrierInfo {
        /**
         * 载具类型
         */
        private String carrierType;
        
        /**
         * 载具编号
         */
        private String carrierCode;
        
        /**
         * 层号(如有需要)
         */
        private String layer;
    }
} 