package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 任务执行通知请求类，用于RCS-2000任务执行过程回馈接口的入参
 */
@Data
public class TaskExecutionNoticeRequest {




    /**
     * 任务号
     */
    @JSONField(name = "robotTaskCode")
    private String robotTaskCode;

    /**
     * 当前执行任务的机器人唯一标识
     */
    @JSONField(name = "singleRobotCode")
    private String singleRobotCode;
    /**
     * 设备编码
     */
    @JSONField(name = "eqptCode")
    private String eqptCode;

    /**
     * 设备名称
     */
    @JSONField(name = "eqptName")
    private String eqptName;

    /**
     * 任务编码
     */
    @JSONField(name = "taskCode")
    private String taskCode;

    /**
     * 方法名（可能在顶层或extra.values内）
     */
    @JSONField(name = "method")
    private String method;

    /**
     * 自定义扩展字段
     */
    @J<PERSON>NField(name = "extra")
    private JSONObject extra;
}