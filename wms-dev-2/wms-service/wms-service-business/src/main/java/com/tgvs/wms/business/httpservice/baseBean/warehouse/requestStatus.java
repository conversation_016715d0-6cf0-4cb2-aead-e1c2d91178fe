package com.tgvs.wms.business.httpservice.baseBean.warehouse;


public class requestStatus {
    private Integer id;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof requestStatus))
            return false;
        requestStatus other = (requestStatus)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        return !((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName));
    }

    protected boolean canEqual(Object other) {
        return other instanceof requestStatus;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        return result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
    }

    public String toString() {
        return "requestStatus(id=" + getId() + ", messageName=" + getMessageName() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    private String messageName = "getAllStatus";

    public String getMessageName() {
        return this.messageName;
    }
}
