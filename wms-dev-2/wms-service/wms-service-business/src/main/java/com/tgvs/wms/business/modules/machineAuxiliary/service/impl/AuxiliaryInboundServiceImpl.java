package com.tgvs.wms.business.modules.machineAuxiliary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.container.entity.AuxiliaryPrebox;
import com.tgvs.wms.business.modules.container.entity.BoxItem;
import com.tgvs.wms.business.modules.container.service.IBoxItemService;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryInBoundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryPreboxDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.SelfPropertyInboundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.StyleSplitInboundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryDetail;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInBound;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInfo;
import com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryInListMapper;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryDetailService;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryInboundService;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryInfoService;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryPreboxService;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.AuxiliaryMaterialVo;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.InMaterialCancel;
import com.tgvs.wms.business.modules.machineMaterials.vo.InContractNoListVo;
import com.tgvs.wms.business.modules.machineMaterials.vo.InMaterialVo;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import com.tgvs.wms.business.modules.task.service.impl.WmsBoxTaskListServiceImpl;
import com.tgvs.wms.common.annotation.Log;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.enums.BusinessType;
import com.tgvs.wms.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 辅料入库服务实现类
 */
@Slf4j
@Service
public class AuxiliaryInboundServiceImpl extends BaseServiceImpl<WmsAuxiliaryInListMapper, WmsAuxiliaryInBound> implements IAuxiliaryInboundService {

    @Autowired
    private IAuxiliaryPreboxService auxiliaryPreboxService;
    @Autowired
    private IAuxiliaryInfoService auxiliaryInfoService;

    @Autowired
    private IAuxiliaryDetailService auxiliaryDetailService;

    @Autowired
    private IBoxItemService boxItemService;
    @Autowired
    private WmsBoxTaskListServiceImpl wmsBoxTaskListServiceImpl;


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "辅料入库", businessType = BusinessType.INSERT)
    public boolean processInMaterial(InMaterialVo request) {
        log.info("开始处理辅料入库物料信息: {}", request.getInStoreNumber());

        try {
            //验证单号是否唯一
            if (StringUtils.isEmpty(request.getInStoreNumber())) {
                log.error("入库单号不能为空");
                throw new IllegalArgumentException("入库单号不能为空");
            } else {
                WmsAuxiliaryInBound existingHeader = this.getOne(new LambdaQueryWrapper<WmsAuxiliaryInBound>()
                        .eq(WmsAuxiliaryInBound::getInStoreNumber, request.getInStoreNumber())
                                .eq(WmsAuxiliaryInBound::getDeleteFlag, 0) // 确保未删除
                        .ne(WmsAuxiliaryInBound::getStatus, 3), false);//状态不等于3表示已取消

                if (existingHeader != null) {
                    log.error("辅料物料入库单，单号: {},已存在", request.getInStoreNumber());
                    String errorMessage = String.format("辅料物料收料单，单号: %s,已存在", request.getInStoreNumber());
                    throw new IllegalArgumentException(errorMessage);
                }
            }
            // 1. 创建并保存入库单头信息
            //转换入库单类型值；1.采购入库，2.一般入库，3.产线退料回库对应转换为0采购入库,1一般入库 2，2生产退料回库
            Integer taskType = request.getTaskType();
            if (taskType == null) {
                log.error("入库类型不能为空");
                throw new IllegalArgumentException("入库类型不能为空");
            }
            if (taskType == 1) {
                taskType = 0; // 采购入库
            } else if (taskType == 2) {
                taskType = 1; // 一般入库
            } else if (taskType == 3) {
                taskType = 2; // 产线退料回库
            } else {
                log.error("不支持的入库类型: {}", request.getTaskType());
                throw new IllegalArgumentException("不支持的入库类型");
            }

            WmsAuxiliaryInBound inboundHeader = new WmsAuxiliaryInBound();
            inboundHeader.setInStoreNumber(request.getInStoreNumber());
            inboundHeader.setTaskType(taskType);
            inboundHeader.setStatus(0); // 初始状态：待处理
            inboundHeader.setCreateTime(new Date());

            boolean headerSaved = this.save(inboundHeader);
            if (!headerSaved) {
                log.error("保存辅料入库单头信息失败: {}", request.getInStoreNumber());
                // 可以选择抛出异常或返回 false
                throw new RuntimeException("保存辅料入库单信息失败");
            }
            log.info("辅料入库单头信息保存成功: {}", request.getInStoreNumber());

            // 2. 保存入库明细信息
            saveInboundDetails(request);

            log.info("辅料入库物料信息处理成功: {}", request.getInStoreNumber());
            return true;
        } catch (Exception e) {
            log.error("辅料入库物料信息处理失败: {}, 原因: {}", request.getInStoreNumber(), e.getMessage(), e);
            // 让 @Transactional 生效，必须抛出 RuntimeException 或其子类，或者在注解中指定 rollbackFor
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 保存入库明细数据
     */
    private void saveInboundDetails(InMaterialVo request) {
        log.info("保存入库明细数据: 入库单号={}", request.getInStoreNumber());

        // 解析入参，准备保存到明细表
        List<WmsAuxiliaryDetail> detailList = new ArrayList<>();

        // 验证请求参数
        if (request.getContractNoList() == null || request.getContractNoList().isEmpty()) {
            log.error("入库数据合约号列表为空: {}", request.getInStoreNumber());
            throw new IllegalArgumentException("入库数据合约号列表不能为空");
        }

        // 遍历合约号列表
        for (InContractNoListVo contractItem : request.getContractNoList()) {
            // 验证每个合约号的物料列表
            if (contractItem.getItemList() == null || contractItem.getItemList().isEmpty()) {
                log.warn("合约号 {} 的物料列表为空，跳过处理", contractItem.getContractNo());
                String errorMessage = String.format("保存失败，合约号 %s 的物料列表为空", contractItem.getContractNo());
                throw new IllegalArgumentException(errorMessage);
            }
            if (contractItem.getContractNo().isEmpty()) {
                log.warn("合约号为空，跳过处理");
                String errorMessage = "保存失败，合约号不能为空";
                throw new IllegalArgumentException(errorMessage);
            }
            //验证物料是否已经建档
            //获取所有的物料编码
            List<String> materialCodes = contractItem.getItemList().stream()
                    .map(AuxiliaryMaterialVo::getMaterialCode)
                    .filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            if (materialCodes.isEmpty()) {
                log.warn("合约号 {} 的物料编码列表为空，跳过处理", contractItem.getContractNo());
                String errorMessage = String.format("保存失败，合约号 %s 的物料编码不能为空", contractItem.getContractNo());
                throw new IllegalArgumentException(errorMessage);
            }
            //根据物料编码集合查询物料基本信息，如果不存在则发出异常提示
            List<WmsAuxiliaryInfo> auxiliaryInfoList = auxiliaryInfoService.list(
                    new LambdaQueryWrapper<WmsAuxiliaryInfo>()
                            .in(WmsAuxiliaryInfo::getMaterialCode, materialCodes)
            );
            if (auxiliaryInfoList.isEmpty()) {
                log.warn("合约号 {} 的物料编码列表不存在", contractItem.getContractNo());
                String errorMessage = String.format("保存失败，合约号 %s 的物料编码不存在，请先维护物料基础信息", contractItem.getContractNo());
                throw new IllegalArgumentException(errorMessage);
            }
            //找出不存在的物料编码
            List<String> existingMaterialCodes = auxiliaryInfoList.stream()
                    .map(WmsAuxiliaryInfo::getMaterialCode)
                    .collect(Collectors.toList());
            List<String> missingMaterialCodes = materialCodes.stream()
                    .filter(code -> !existingMaterialCodes.contains(code))
                    .collect(Collectors.toList());
            if (!missingMaterialCodes.isEmpty()) {
                log.warn("合约号 {} 的物料编码列表中存在未维护的物料编码: {}", contractItem.getContractNo(), missingMaterialCodes);
                String errorMessage = String.format("保存失败，合约号 %s 的物料编码 %s 未维护，请先维护物料基础信息", contractItem.getContractNo(), String.join(", ", missingMaterialCodes));
                throw new IllegalArgumentException(errorMessage);
            }

            // 遍历物料列表
            for (AuxiliaryMaterialVo materialItem : contractItem.getItemList()) {
                //验证款号
                if (StringUtils.isEmpty(materialItem.getItemNo())) {
                    log.warn("合约号 {} 的款号为空，跳过处理", contractItem.getContractNo());
                    String errorMessage = String.format("保存失败，合约号 %s 的款号不能为空", contractItem.getContractNo());
                    throw new IllegalArgumentException(errorMessage);
                }
                // 验证物料项
                if (StringUtils.isEmpty(materialItem.getMaterialCode()) || StringUtils.isEmpty(materialItem.getMaterialName())) {
                    log.warn("合约号 {} 存在无效物料项，跳过: {}", contractItem.getContractNo(), materialItem);
                    String errorMessage = String.format("保存失败，合约号 %s 存在无效物料项: 物料编码，物料名称不能为空", contractItem.getContractNo());
                    throw new IllegalArgumentException(errorMessage);
                }
                if (materialItem.getInQuantity() == null || materialItem.getInQuantity() <= 0) {
                    log.warn("合约号 {} 的物料 {} 数量无效，跳过处理", contractItem.getContractNo(), materialItem.getMaterialCode());
                    String errorMessage = String.format("保存失败，合约号 %s 的物料 %s 数量无效", contractItem.getContractNo(), materialItem.getMaterialCode());
                    throw new IllegalArgumentException(errorMessage);
                }

                WmsAuxiliaryDetail detailRecord = new WmsAuxiliaryDetail();
                detailRecord.setId(UUID.randomUUID().toString());

                detailRecord.setRefNumber(request.getInStoreNumber());
                // 设置操作类型 (0: 入库)
                detailRecord.setOperationType(0);
                // 从 contractItem 获取合约信息
                detailRecord.setContractNo(contractItem.getContractNo());
                // 从 materialItem 获取物料信息
                detailRecord.setReqListId(materialItem.getROListID());
                detailRecord.setItemNo(materialItem.getItemNo());
                detailRecord.setMaterialCode(materialItem.getMaterialCode());
                detailRecord.setMaterialName(materialItem.getMaterialName());
                detailRecord.setMaterialColor(materialItem.getMaterialColor());
                detailRecord.setMaterialColorCode(materialItem.getMaterialColorCode());
                detailRecord.setMaterialModel(materialItem.getMaterialModel());
                detailRecord.setQuantity(materialItem.getInQuantity());
                detailRecord.setMaterialUnit(materialItem.getMaterialUnit());
                detailRecord.setCreateTime(new Date());


                detailList.add(detailRecord);

                log.debug("准备添加入库明细记录: 入库单号={}, 合约号={}, 物料编码={}, 数量={}",
                        request.getInStoreNumber(), contractItem.getContractNo(),
                        materialItem.getMaterialCode(), materialItem.getInQuantity());
            }
        }

        // 检查是否有记录需要保存
        if (detailList.isEmpty()) {
            log.warn("没有有效的入库明细记录需要保存: {}", request.getInStoreNumber());
            return;
        }

        log.info("准备保存 {} 条入库明细记录: {}", detailList.size(), request.getInStoreNumber());
        // 批量保存入库明细数据
        auxiliaryDetailService.saveBatch(detailList);
        log.info("成功保存 {} 条入库明细记录: {}", detailList.size(), request.getInStoreNumber());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "辅料入库取消", businessType = BusinessType.UPDATE)
    public boolean processInMaterialCancel(InMaterialCancel request) {
        log.info("开始处理辅料入库单取消: {}", request.getInStoreNumber());
        
        try {
            // 1. 验证请求参数
            if (StringUtils.isBlank(request.getInStoreNumber())) {
                throw new IllegalArgumentException("入库单号不能为空");
            }
            
            if (request.getTaskType() == null) {
                throw new IllegalArgumentException("入库类型不能为空");
            }
            
            // 2. 根据入库单号查询入库单信息（可能有多个结果）
            //入库类型转换
            int taskType = request.getTaskType();
            if (taskType == 1) {
                taskType = 0; // 采购入库
            } else if (taskType == 2) {
                taskType = 1; // 一般入库
            } else if (taskType == 3) {
                taskType = 2; // 产线退料回库
            } else {
                log.error("不支持的入库类型: {}", request.getTaskType());
                throw new IllegalArgumentException("不支持的入库类型");
            }
            List<WmsAuxiliaryInBound> inboundList = this.list(new LambdaQueryWrapper<WmsAuxiliaryInBound>()
                    .eq(WmsAuxiliaryInBound::getInStoreNumber, request.getInStoreNumber())
                    .eq(WmsAuxiliaryInBound::getTaskType, taskType));
            
            if (inboundList == null || inboundList.isEmpty()) {
                log.warn("入库单不存在: {}", request.getInStoreNumber());
                throw new IllegalArgumentException("辅料收料入库不存在: " + request.getInStoreNumber());
            }
            
            // 如果有多个记录，记录警告日志
            if (inboundList.size() > 1) {
                log.warn("发现多个入库单记录: 入库单号={}, 入库类型={}, 记录数量={}", 
                        request.getInStoreNumber(), request.getTaskType(), inboundList.size());
            }
            
            // 3. 检查所有入库单的状态，只有状态为0（待处理）的才可以取消
            List<WmsAuxiliaryInBound> validInbounds = new ArrayList<>();
            for (WmsAuxiliaryInBound inbound : inboundList) {
                if (inbound.getStatus() == null) {
                    log.warn("入库单状态异常: 入库单号={}, ID={}", request.getInStoreNumber(), inbound.getId());
                    throw new IllegalArgumentException("辅料收料入库状态异常");
                }
                
                if (inbound.getStatus() == 0) {
                    validInbounds.add(inbound);
                } else {
                    log.warn("入库单状态不允许取消: 入库单号={}, ID={}, 当前状态={}", 
                            request.getInStoreNumber(), inbound.getId(), inbound.getStatus());
                }
            }
            
            // 如果没有可以取消的入库单
            if (validInbounds.isEmpty()) {
                throw new IllegalArgumentException("未查到有效的辅料收料入库单无法撤销");
            }
            
            // 4. 批量更新入库单状态为取消
            for (WmsAuxiliaryInBound inbound : validInbounds) {
                inbound.setStatus(3); // 3表示已取消
                inbound.setDeleteFlag(1); // 设置删除标志
                inbound.setUpdateTime(new Date());
            }
            
            boolean updateResult = this.updateBatchById(validInbounds);
            
            if (!updateResult) {
                log.error("更新入库单状态失败: {}", request.getInStoreNumber());
                throw new RuntimeException("更新辅料收料入库单状态失败");
            }
            
            // 5. 更新明细表状态，设置删除标志
            WmsAuxiliaryDetail detail = new WmsAuxiliaryDetail();
            detail.setDeleteFlag(1);
            auxiliaryDetailService.update(detail, new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                    .eq(WmsAuxiliaryDetail::getRefNumber, request.getInStoreNumber())
                    .eq(WmsAuxiliaryDetail::getOperationType, 0)); // 0表示入库
            
            log.info("辅料入库单取消成功: 入库单号={}, 处理记录数={}", request.getInStoreNumber(), validInbounds.size());
            return true;
            
        } catch (Exception e) {
            log.error("处理辅料入库单取消失败: {}", request.getInStoreNumber(), e);
            throw new RuntimeException("处理辅料收料入库单撤销失败: " + e.getMessage(), e);
        }
    }

    @Override
    public IPage<AuxiliaryInBoundDto> queryPageList(QueryModel queryModel) {
        log.info("辅料入库查询开始，参数: {}", queryModel);
        
        Page<AuxiliaryInBoundDto> page = new Page<>(queryModel.getPage(), queryModel.getLimit());
        
        // 构建查询条件
        QueryWrapper<AuxiliaryInBoundDto> queryWrapper = new QueryWrapper<>();
        
        // 基本条件：只查询入库操作、未删除、待处理状态的记录
        queryWrapper.eq("d.operation_type", 0)    // 入库操作
                    .eq("d.delete_flag",0)// 未删除
                   .eq("h.delete_flag", 0)
                   .in("h.status", 0,1);            // 待处理状态
        
        // 处理查询参数
        if (queryModel.getSearchParams() != null && !queryModel.getSearchParams().isEmpty()) {
            Map<String, String> searchParams = queryModel.getSearchParams();
            
            for (Map.Entry<String, String> entry : searchParams.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                
                // 跳过空值
                if (StringUtils.isEmpty(value)) {
                    continue;
                }
                
                // 解析查询规则和字段名
                if (key.contains("_")) {
                    String[] parts = key.split("_", 2);
                    if (parts.length == 2) {
                        String rule = parts[0];  // 查询规则：lk(like), eq(equal), gt(greater than)等
                        String fieldName = parts[1];  // 字段名
                        
                        // 将字段名映射到数据库表别名字段
                        String dbField = mapFieldToDbColumn(fieldName);
                        if (dbField != null) {
                            applyQueryCondition(queryWrapper, rule, dbField, value);
                        } else {
                            log.warn("未找到字段映射: {}", fieldName);
                        }
                    }
                }
            }
        }
        
        // 处理排序
        if (StringUtils.isNotEmpty(queryModel.getSortList())) {
            for (QueryModel.SortModel sortModel : queryModel.getSortList()) {
                String sqlColumn = mapSortFieldToSqlColumn(sortModel.getColumn());
                if (sqlColumn != null) {
                    if (sortModel.getSortType() == 1) {
                        queryWrapper.orderByAsc(sqlColumn);
                    } else if (sortModel.getSortType() == 2) {
                        queryWrapper.orderByDesc(sqlColumn);
                    }
                }
            }
        } else {
            // 默认按创建时间倒序
            queryWrapper.orderByDesc("h.create_time");
        }
        
        // 记录最终的SQL条件用于调试
        log.info("辅料入库查询SQL条件: {}", queryWrapper.getCustomSqlSegment());
        
        // 执行查询
        IPage<AuxiliaryInBoundDto> resultPage = this.baseMapper.selectDtoPage(page, queryWrapper);
        
        // 记录查询结果
        if (resultPage != null) {
            log.info("查询结果 - 总记录数: {}, 当前页记录数: {}", 
                    resultPage.getTotal(), 
                    resultPage.getRecords() != null ? resultPage.getRecords().size() : 0);
        } else {
            log.error("查询结果为null");
        }
        
        return resultPage;
    }
    
    /**
     * 将前端字段名映射到数据库表别名字段
     */
    private String mapFieldToDbColumn(String fieldName) {
        if (fieldName == null) return null;
        
        switch (fieldName) {
            // 头表字段 (h表)
            case "inStoreNumber":
                return "h.in_store_number";
            case "taskType":
                return "h.task_type";
            case "status":
                return "h.status";
            case "createTime":
                return "h.create_time";
            case "updateTime":
                return "h.update_time";
            case "createBy":
                return "h.create_by";
            case "updateBy":
                return "h.update_by";
                
            // 明细表字段 (d表)
            case "contractNo":
                return "d.contract_no";
            case "itemNo":
                return "d.item_no";
            case "materialCode":
                return "d.material_code";
            case "materialName":
                return "d.material_name";
            case "materialColor":
                return "d.material_color";
            case "materialColorCode":
                return "d.material_color_code";
            case "materialModel":
                return "d.material_model";
            case "quantity":
                return "d.quantity";
            case "materialUnit":
                return "d.material_unit";
            case "reqListId":
                return "d.req_list_id";
                
            // 物料信息表字段 (i表)
            case "materialType":
                return "i.material_type";
            case "isStore":
                return "i.is_store";
            default:
                log.warn("未知字段名: {}", fieldName);
                return null;
        }
    }
    
    /**
     * 根据查询规则应用查询条件
     */
    private void applyQueryCondition(QueryWrapper<AuxiliaryInBoundDto> queryWrapper, 
                                   String rule, String dbField, String value) {
        switch (rule.toLowerCase()) {
            case "lk":  // 模糊查询
                queryWrapper.like(dbField, value);
                log.info("应用模糊查询条件: {} LIKE '%{}%'", dbField, value);
                break;
            case "eq":  // 等于
                queryWrapper.eq(dbField, value);
                log.info("应用等于条件: {} = '{}'", dbField, value);
                break;
            case "ne":  // 不等于
                queryWrapper.ne(dbField, value);
                log.info("应用不等于条件: {} != '{}'", dbField, value);
                break;
            case "gt":  // 大于
                queryWrapper.gt(dbField, value);
                log.info("应用大于条件: {} > '{}'", dbField, value);
                break;
            case "ge":  // 大于等于
                queryWrapper.ge(dbField, value);
                log.info("应用大于等于条件: {} >= '{}'", dbField, value);
                break;
            case "lt":  // 小于
                queryWrapper.lt(dbField, value);
                log.info("应用小于条件: {} < '{}'", dbField, value);
                break;
            case "le":  // 小于等于
                queryWrapper.le(dbField, value);
                log.info("应用小于等于条件: {} <= '{}'", dbField, value);
                break;
            case "in":  // 包含
                // 假设多个值用逗号分隔
                String[] values = value.split(",");
                queryWrapper.in(dbField, Arrays.asList(values));
                log.info("应用包含条件: {} IN ({})", dbField, value);
                break;
            default:
                log.warn("未知查询规则: {}, 字段: {}, 值: {}", rule, dbField, value);
                break;
        }
    }

    private String mapSortFieldToSqlColumn(String fieldName) {
        if (fieldName == null) return null;
        // Basic mapping - extend as needed
        switch (fieldName) {
            case "inStoreNumber":
                return "h.in_store_number";
            case "contractNo":
                return "d.contract_no";
            case "materialCode":
                return "d.material_code";
            case "quantity":
                return "d.quantity";
            case "createTime":
                return "h.create_time";
            default:
                return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AuxiliaryPreboxDto> processInPrebox(List<String> detailIds) {
        log.info("开始处理辅料入库预装箱，明细ID数量: {}", detailIds != null ? detailIds.size() : 0);

        // 参数验证
        if (detailIds == null || detailIds.isEmpty()) {
            log.warn("辅料入库预装箱明细ID列表为空");
            return new ArrayList<>();
        }

        // 验证ID格式
        for (int i = 0; i < detailIds.size(); i++) {
            String id = detailIds.get(i);
            if (StringUtils.isEmpty(id)) {
                log.error("第{}个明细ID为空", i + 1);
                throw new IllegalArgumentException(String.format("第%d个明细ID不能为空", i + 1));
            }
        }

        log.info("查询辅料明细信息，ID列表: {}", detailIds);

        // 根据ID列表查询辅料明细数据
        List<WmsAuxiliaryDetail> detailList = auxiliaryDetailService.getBaseMapper().selectList(
                new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                        .in(WmsAuxiliaryDetail::getId, detailIds)
                        .eq(WmsAuxiliaryDetail::getOperationType, 0) // 只查询入库类型
        );

        if (detailList == null || detailList.isEmpty()) {
            log.warn("根据提供的ID列表未查询到任何辅料明细数据");
            throw new IllegalArgumentException("未找到对应的辅料明细数据");
        }

        log.info("查询到辅料明细数据: {} 条", detailList.size());

        // 验证查询结果与输入ID的一致性
        if (detailList.size() != detailIds.size()) {
            log.warn("查询到的明细数量({})与输入ID数量({})不一致", detailList.size(), detailIds.size());
            // 找出哪些ID没有查到对应数据
            List<String> foundIds = detailList.stream()
                    .map(WmsAuxiliaryDetail::getId)
                    .collect(Collectors.toList());
            List<String> notFoundIds = detailIds.stream()
                    .filter(id -> !foundIds.contains(id))
                    .collect(Collectors.toList());
            throw new IllegalArgumentException("以下明细ID未找到对应数据: " + String.join(", ", notFoundIds));
        }

        // *** 新增：检查是否有明细已被预装处理 ***
        List<WmsAuxiliaryDetail> alreadyPreboxedDetails = detailList.stream()
                .filter(detail -> detail.getPreboxStatus() != null && detail.getPreboxStatus() == 1)
                .collect(Collectors.toList());

        if (!alreadyPreboxedDetails.isEmpty()) {
            log.info("发现已预装的明细 {} 条，开始检查出库状态", alreadyPreboxedDetails.size());
            
            // 检查已预装明细的出库状态
            List<AuxiliaryPreboxDto> existingPreboxData = checkAndHandleExistingPrebox(alreadyPreboxedDetails);
            
            if (!existingPreboxData.isEmpty()) {
                log.info("发现已预装且有出库记录的明细，返回现有预装数据供查看");
                return existingPreboxData;
            }
            
            // 如果没有出库记录，则抛出异常
            List<String> alreadyPreboxedInfo = alreadyPreboxedDetails.stream()
                    .map(detail -> String.format("ID:%s,物料:%s,预装箱:%s,批次:%s", 
                            detail.getId(), 
                            detail.getMaterialCode(), 
                            detail.getPreboxId() != null ? detail.getPreboxId() : "未知",
                            detail.getPreboxBatchNo()))
                    .collect(Collectors.toList());
            
            log.error("发现已预装但无出库记录的明细: {}", alreadyPreboxedInfo);
            throw new IllegalArgumentException("以下明细已被预装处理但无出库记录，不能重复预装: " + String.join("; ", alreadyPreboxedInfo));
        }

        log.info("预装重复检查通过，所有明细均未被预装过");

        // 获取所有物料编码，用于查询物料基本信息
        List<String> materialCodes = detailList.stream()
                .map(WmsAuxiliaryDetail::getMaterialCode)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        log.info("查询物料基本信息，物料编码数量: {}", materialCodes.size());

        // 查询物料基本信息
        List<WmsAuxiliaryInfo> auxiliaryInfoList = new ArrayList<>();
        if (!materialCodes.isEmpty()) {
            auxiliaryInfoList = auxiliaryInfoService.list(
                    new LambdaQueryWrapper<WmsAuxiliaryInfo>()
                            .in(WmsAuxiliaryInfo::getMaterialCode, materialCodes)
            );
        }

        log.info("查询到物料基本信息数量: {}", auxiliaryInfoList.size());

        //查询auxiliaryInfoList物料信息中关键的信息，容积，物料属性是否有维护数据
        for (WmsAuxiliaryInfo itemInfo : auxiliaryInfoList) {
            if (itemInfo.getCapacity() == null || itemInfo.getCapacity() <= 0) {
                log.warn("物料编码 {} 的容积为空或无效: {}", itemInfo.getMaterialCode(), itemInfo.getCapacity());
                throw new IllegalArgumentException(String.format("物料编码 %s 的容积为空或无效", itemInfo.getMaterialCode()));
            }
            if (itemInfo.getMaterialType() == null || (itemInfo.getMaterialType() != 0 && itemInfo.getMaterialType() != 1)) {
                log.warn("物料编码 {} 的物料属性无效: {}，应该为0(自身属性)或1(款式属性)", itemInfo.getMaterialCode(), itemInfo.getMaterialType());
                throw new IllegalArgumentException(String.format("物料编码 %s 的物料属性无效，应该为0(自身属性)或1(款式属性)", itemInfo.getMaterialCode()));
            }
        }

        // 创建物料编码到物料信息的映射
        Map<String, WmsAuxiliaryInfo> materialInfoMap = auxiliaryInfoList.stream()
                .collect(Collectors.toMap(
                        WmsAuxiliaryInfo::getMaterialCode,
                        info -> info,
                        (existing, replacement) -> existing // 如果有重复，保留第一个
                ));

        // 构造预装箱DTO列表
        List<AuxiliaryPreboxDto> preboxDtoList = new ArrayList<>();

        for (WmsAuxiliaryDetail detail : detailList) {
            try {
                // 验证明细数据的完整性
                if (detail.getQuantity() == null || detail.getQuantity() <= 0) {
                    log.error("明细ID {} 的数量无效: {}", detail.getId(), detail.getQuantity());
                    throw new IllegalArgumentException(String.format("明细ID %s 的数量无效: %s",
                            detail.getId(), detail.getQuantity()));
                }

                if (StringUtils.isEmpty(detail.getMaterialCode())) {
                    log.error("明细ID {} 的物料编码为空", detail.getId());
                    throw new IllegalArgumentException(String.format("明细ID %s 的物料编码为空", detail.getId()));
                }

                if (StringUtils.isEmpty(detail.getContractNo())) {
                    log.error("明细ID {} 的合约号为空", detail.getId());
                    throw new IllegalArgumentException(String.format("明细ID %s 的合约号为空", detail.getId()));
                }

                // 创建预装箱DTO
                AuxiliaryPreboxDto preboxDto = new AuxiliaryPreboxDto();

                // 设置基本信息
                preboxDto.setStockInId(detail.getId()); // 使用明细ID作为入库单ID
                preboxDto.setStockInNo(detail.getRefNumber()); // 关联的入库单号
                preboxDto.setContractNo(detail.getContractNo());
                preboxDto.setStyleNo(detail.getItemNo());
                preboxDto.setMaterialCode(detail.getMaterialCode());
                preboxDto.setMaterialName(detail.getMaterialName());
                preboxDto.setInQuantity(detail.getQuantity());
                preboxDto.setMaterialModel(detail.getMaterialModel());
                preboxDto.setMaterialColor(detail.getMaterialColor());

                // 设置物料属性
                WmsAuxiliaryInfo materialInfo = materialInfoMap.get(detail.getMaterialCode());
                if (materialInfo != null) {
                    preboxDto.setMaterialProperty(materialInfo.getMaterialType());
                    log.debug("为物料 {} 设置物料属性: {}", detail.getMaterialCode(), materialInfo.getMaterialType());
                } else {
                    log.warn("未找到物料 {} 的基本信息", detail.getMaterialCode());
                    // 设置默认物料属性
                    preboxDto.setMaterialProperty(0); // 默认为自身属性
                }

                preboxDtoList.add(preboxDto);

                log.debug("构造预装箱DTO - 明细ID: {}, 物料编码: {}, 合约号: {}, 数量: {}",
                        detail.getId(), detail.getMaterialCode(), detail.getContractNo(), detail.getQuantity());

            } catch (Exception e) {
                log.error("处理明细ID {} 时发生异常: {}", detail.getId(), e.getMessage(), e);
                throw new RuntimeException(String.format("处理明细ID %s 时发生异常: %s", detail.getId(), e.getMessage()));
            }
        }

        log.info("辅料入库预装箱处理完成，返回预装箱DTO数量: {}", preboxDtoList.size());
        return preboxDtoList;
    }

    // 处理辅料自身属性入库
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processInBound(List<SelfPropertyInboundDto> inboundItems) {
        
        if (inboundItems == null || inboundItems.isEmpty()) {
            log.error("辅料入库数据列表为空");
            return false;
        }
        
        log.info("开始处理自身属性绑定入库: 数据数量={}", inboundItems.size());
        
        // 提取ID列表进行查询
        List<String> preboxIds = inboundItems.stream()
                .map(SelfPropertyInboundDto::getId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        
        if (preboxIds.isEmpty()) {
            log.error("辅料入库数据中未找到有效的ID");
            throw new IllegalArgumentException("入库数据中未找到有效的ID");
        }
        
        //1、查询预装箱数据
        List<AuxiliaryPrebox> preboxDataList = auxiliaryPreboxService.listByIds(preboxIds);

        if (preboxDataList == null || preboxDataList.isEmpty()) {
            log.warn("根据提供的ID列表 {} 未查询到任何预装箱数据", preboxIds);
            throw new IllegalArgumentException("根据提供的ID列表未查询到任何预装箱数据: " + preboxIds);
        }

        if (preboxDataList.size() != preboxIds.size()) {
            List<String> foundIds = preboxDataList.stream().map(AuxiliaryPrebox::getId).collect(Collectors.toList());
            List<String> notFoundIds = preboxIds.stream().filter(id -> !foundIds.contains(id)).collect(Collectors.toList());
            log.error("部分预装箱ID未找到对应数据: {}", notFoundIds);
            throw new IllegalArgumentException("部分预装箱ID未找到数据: " + String.join(", ", notFoundIds));
        }
        
        // 创建ID到入库数据的映射，支持实际入库数量验证和更新
        Map<String, SelfPropertyInboundDto> inboundDataMap = inboundItems.stream()
                .collect(Collectors.toMap(SelfPropertyInboundDto::getId, item -> item));
        
        log.info("入库数据映射建立完成: 映射数量={}", inboundDataMap.size());

        // 收集需要更新状态的入库单信息（避免在循环中重复查询和更新）
        Set<String> processedInboundNumbers = new HashSet<>();
        
        for (AuxiliaryPrebox preboxDto : preboxDataList) {
            // 防止重复处理已完成的预装箱
            if (preboxDto.getStatus() != null && preboxDto.getStatus() == 1) {
                log.info("预装箱 {} (入库单号 {}) 已处理完成，跳过。", preboxDto.getId(), preboxDto.getStockInNo());
                continue;
            }
            
            // 获取前端传递的入库数据，包含实际入库数量
            SelfPropertyInboundDto inboundData = inboundDataMap.get(preboxDto.getId());
            if (inboundData == null) {
                log.warn("预装箱 {} 未找到对应的入库数据，跳过处理", preboxDto.getId());
                continue;
            }
            
            // 验证并使用前端传递的实际入库数量
            Integer actualInboundQty = inboundData.getActualInboundQty() != null ? 
                inboundData.getActualInboundQty().intValue() : 
                (preboxDto.getMaterialQuantity() != null ? preboxDto.getMaterialQuantity() : 0);
            
            // 数量验证：不能超过原始数量
            Integer originalQty = inboundData.getOriginalQuantity() != null ? 
                inboundData.getOriginalQuantity().intValue() : 
                (preboxDto.getMaterialQuantity() != null ? preboxDto.getMaterialQuantity() : 0);
                
            if (actualInboundQty > originalQty) {
                log.error("预装箱 {} 实际入库数量 {} 超过原始数量 {}", 
                    preboxDto.getId(), actualInboundQty, originalQty);
                throw new IllegalArgumentException(String.format(
                    "物料[%s]实际入库数量(%d)不能超过原始数量(%d)", 
                    inboundData.getMaterialCode(), actualInboundQty, originalQty));
            }
            
            // 如果实际入库数量为0，跳过处理
            if (actualInboundQty <= 0) {
                log.info("预装箱 {} 实际入库数量为 {}，跳过处理", preboxDto.getId(), actualInboundQty);
                continue;
            }
            
            log.info("处理预装箱 {}: 物料编码={}, 原始数量={}, 实际入库数量={}, 容器类型={}", 
                preboxDto.getId(), inboundData.getMaterialCode(), originalQty, actualInboundQty,
                inboundData.getContainerType() != null ? 
                    (inboundData.getContainerType() == 1 ? "料箱" : "托盘") : "未知");

            // *** 改进的容器查找和创建逻辑（支持混装场景和托盘）***
            BoxItem boxItem = findOrCreateBoxItem(preboxDto);
            
            // 更新 BoxItem 信息：使用实际入库数量而非原始数量
            Integer existingQuantity = boxItem.getMaterialQuantity() == null ? 0 : boxItem.getMaterialQuantity();
            boxItem.setMaterialQuantity(existingQuantity + actualInboundQty);
            
            // 同时更新预装箱的数量为实际入库数量，确保数据一致性
            preboxDto.setMaterialQuantity(actualInboundQty);

            // 更新网格状态
            if (boxItem.getMaterialQuantity() > 0 && (boxItem.getGridStatus() == null || boxItem.getGridStatus() == 0)) {
                boxItem.setGridStatus(1); // 有物料但未满
            } else if (boxItem.getMaterialQuantity() == 0 && (boxItem.getGridStatus() != null && boxItem.getGridStatus() != 0)) {
                boxItem.setGridStatus(0); // 空
            }
            
            boxItem.setUpdateTime(new Date());
            try {
                String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                boxItem.setUpdateBy(com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId) ? userId : "system_update");
            } catch (Exception e) {
                log.warn("更新BoxItem {} 时获取当前用户ID失败: {}", boxItem.getId(), e.getMessage());
                boxItem.setUpdateBy("system_update");
            }

            boolean boxItemUpdated = boxItemService.updateById(boxItem);
            if (!boxItemUpdated) {
                log.error("更新BoxItem失败, ID: {}", boxItem.getId());
                throw new RuntimeException("更新BoxItem失败, ID: " + boxItem.getId());
            }

            //查询入库单信息
            WmsAuxiliaryInBound inBound = this.getOne(
                    new LambdaQueryWrapper<WmsAuxiliaryInBound>()
                            .eq(WmsAuxiliaryInBound::getInStoreNumber, preboxDto.getStockInNo())
                            .eq(WmsAuxiliaryInBound::getDeleteFlag, 0)
            );

            if (inBound == null) {
                log.error("未找到与预装箱 {} (入库单号 {}) 关联的有效入库单。", preboxDto.getId(), preboxDto.getStockInNo());
                throw new RuntimeException("未找到关联的入库单: " + preboxDto.getStockInNo());
            }

            // 如果入库单已完成或已取消，则不应再处理其预装箱
            if (inBound.getStatus() == 2 || inBound.getStatus() == 3) {
                log.warn("入库单 {} 状态为 {} (2:已完成, 3:已取消)，预装箱 {} (ID: {}) 不应再被处理。",
                         inBound.getInStoreNumber(), inBound.getStatus(), preboxDto.getContainerNo(), preboxDto.getId());
                throw new RuntimeException("入库单 " + inBound.getInStoreNumber() + " 已是最终状态，无法继续处理其预装箱。");
            }
            
            //创建入库任务数据
            WmsBoxTaskList task =new WmsBoxTaskList();
            task.setTaskStatus(0);
            task.setBoxNo(preboxDto.getContainerNo());
            task.setBoxType(preboxDto.getContainerType());
            task.setTaskType(inBound.getTaskType());
            WmsBoxTaskList wmsBoxTaskList = wmsBoxTaskListServiceImpl.createTask(task);
            if (wmsBoxTaskList == null || StringUtils.isEmpty(wmsBoxTaskList.getTaskOrder())) {
                log.error("创建入库任务失败，预装箱ID: {}", preboxDto.getId());
                throw new RuntimeException("创建入库任务失败，预装箱ID: " + preboxDto.getId());
            }

            //更新预装箱状态
            preboxDto.setStatus(1); // 1 表示已处理或已生成入库任务
            preboxDto.setTaskOrder(wmsBoxTaskList.getTaskOrder());
            preboxDto.setUpdateTime(new Date());
            try {
                String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                preboxDto.setUpdateBy(com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId) ? userId : "system_update");
            } catch (Exception e) {
                log.warn("更新预装箱 {} 时获取当前用户ID失败: {}", preboxDto.getId(), e.getMessage());
                preboxDto.setUpdateBy("system_update");
            }
            boolean preboxUpdated = auxiliaryPreboxService.updateById(preboxDto);
            if(!preboxUpdated){
                log.error("更新预装箱状态失败, ID: {}", preboxDto.getId());
                throw new RuntimeException("更新预装箱状态失败, ID: " + preboxDto.getId());
            }

            // 记录已处理的入库单号，避免在循环中重复更新入库单状态
            processedInboundNumbers.add(preboxDto.getStockInNo());
            
            // 只在循环中更新入库单状态为处理中(1)，避免过早设置为已完成(2)
            if (inBound.getStatus() == 0) { // 仅当状态为待处理(0)时，更新为处理中(1)
                log.info("入库单 {} (ID: {}) 开始处理预装箱，更新状态为处理中(1)。", 
                        inBound.getInStoreNumber(), inBound.getId());
                inBound.setStatus(1); // 处理中
                inBound.setUpdateTime(new Date());
                try {
                    String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                    inBound.setUpdateBy(com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId) ? userId : "system_update_inbound");
                } catch (Exception e) {
                    log.warn("更新入库单 {} 时获取当前用户ID失败: {}", inBound.getInStoreNumber(), e.getMessage());
                    inBound.setUpdateBy("system_update_inbound");
                }
                this.updateById(inBound);
            }
        } // end of for loop for preboxDataList

        // --- 循环结束后，统一检查并更新入库单状态为已完成 ---
        log.info("所有预装箱处理完成，开始检查入库单状态更新，涉及入库单数量: {}", processedInboundNumbers.size());
        
        for (String inboundNumber : processedInboundNumbers) {
            try {
                // 重新查询入库单信息（获取最新状态）
                WmsAuxiliaryInBound inBound = this.getOne(
                        new LambdaQueryWrapper<WmsAuxiliaryInBound>()
                                .eq(WmsAuxiliaryInBound::getInStoreNumber, inboundNumber)
                                .eq(WmsAuxiliaryInBound::getDeleteFlag, 0)
                );
                
                if (inBound == null) {
                    log.warn("重新查询入库单 {} 时未找到记录，跳过状态更新", inboundNumber);
                    continue;
                }
                
                // 如果已经是最终状态，跳过
                if (inBound.getStatus() == 2 || inBound.getStatus() == 3) {
                    log.info("入库单 {} 状态已是最终状态 {}，跳过状态检查", inboundNumber, inBound.getStatus());
                    continue;
                }
                
                // 查询该入库单下的所有未删除的入库明细
                List<WmsAuxiliaryDetail> allDetailsForInbound = auxiliaryDetailService.list(
                        new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                                .eq(WmsAuxiliaryDetail::getRefNumber, inBound.getInStoreNumber())
                                .eq(WmsAuxiliaryDetail::getOperationType, 0) // 入库操作
                                .eq(WmsAuxiliaryDetail::getDeleteFlag, 0)    // 未删除
                );

                String currentUserId = "system_update_inbound";
                try {
                    String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                    if (com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId)) {
                        currentUserId = userId;
                    }
                } catch (Exception e) {
                    log.warn("更新入库单 {} 时获取当前用户ID失败: {}", inBound.getInStoreNumber(), e.getMessage());
                }

                if (allDetailsForInbound.isEmpty()) {
                    log.warn("入库单 {} (ID: {}) 没有任何有效的入库明细。这可能是一个数据问题。",
                            inBound.getInStoreNumber(), inBound.getId());
                    continue;
                }
                
                // *** 使用明细表的预装状态直接判断，更准确可靠 ***
                boolean allAssociatedPreboxesProcessed = true;
                
                for (WmsAuxiliaryDetail detail : allDetailsForInbound) {
                    // 检查明细的预装状态：1表示已预装，0或null表示未预装
                    boolean isDetailPreboxed = detail.getPreboxStatus() != null && detail.getPreboxStatus() == 1;
                    
                    if (!isDetailPreboxed) {
                        log.info("入库单 {} 的明细 {} (物料 {}, ID: {}) 预装状态为 {}，尚未完成预装处理。",
                                inBound.getInStoreNumber(), detail.getMaterialName(), detail.getMaterialCode(), 
                                detail.getId(), detail.getPreboxStatus());
                        allAssociatedPreboxesProcessed = false;
                        break;
                    }
                    
                    // 如果明细已预装，进一步检查对应的预装箱是否已完成入库
                    boolean isPreboxCompleted = false;
                    
                    if (StringUtils.isNotEmpty(detail.getPreboxId())) {
                        // 优先使用preboxId查询
                        AuxiliaryPrebox preboxRecord = auxiliaryPreboxService.getById(detail.getPreboxId());
                        if (preboxRecord != null && preboxRecord.getStatus() != null && preboxRecord.getStatus() == 1) {
                            isPreboxCompleted = true;
                        } else {
                            log.info("入库单 {} 的明细 {} (物料 {}, ID: {}) 关联的预装箱 {} 状态为 {}，尚未完成入库处理。",
                                    inBound.getInStoreNumber(), detail.getMaterialName(), detail.getMaterialCode(), 
                                    detail.getId(), detail.getPreboxId(), 
                                    preboxRecord != null ? preboxRecord.getStatus() : "记录不存在");
                        }
                    } else if (StringUtils.isNotEmpty(detail.getPreboxBatchNo())) {
                        // 兼容旧数据：如果没有preboxId，通过批次号和detail_ids字段查找
                        List<AuxiliaryPrebox> preboxList = auxiliaryPreboxService.list(
                                new LambdaQueryWrapper<AuxiliaryPrebox>()
                                        .eq(AuxiliaryPrebox::getStockBatchId, detail.getPreboxBatchNo())
                                        .like(AuxiliaryPrebox::getDetailIds, detail.getId())
                                        .eq(AuxiliaryPrebox::getStatus, 1)
                                        .eq(AuxiliaryPrebox::getIsDeleted,0)// 直接查询已完成的
                        );
                        
                        if (!preboxList.isEmpty()) {
                            isPreboxCompleted = true;
                        } else {
                            log.info("入库单 {} 的明细 {} (物料 {}, ID: {}) 在批次 {} 中未找到已完成的预装箱。",
                                    inBound.getInStoreNumber(), detail.getMaterialName(), detail.getMaterialCode(), 
                                    detail.getId(), detail.getPreboxBatchNo());
                        }
                    } else {
                        log.warn("入库单 {} 的明细 {} (物料 {}, ID: {}) 预装状态为已预装，但既没有preboxId也没有批次号，数据异常。",
                                inBound.getInStoreNumber(), detail.getMaterialName(), detail.getMaterialCode(), detail.getId());
                    }
                    
                    if (!isPreboxCompleted) {
                        allAssociatedPreboxesProcessed = false;
                        break;
                    }
                }

                if (allAssociatedPreboxesProcessed) {
                    log.info("入库单 {} (ID: {}) 的所有明细均已完成关联预装箱的处理，更新状态为已完成(2)。",
                            inBound.getInStoreNumber(), inBound.getId());
                    inBound.setStatus(2); // 已完成
                    inBound.setUpdateTime(new Date());
                    inBound.setUpdateBy(currentUserId);
                    this.updateById(inBound);
                } else {
                    log.info("入库单 {} (ID: {}) 仍有明细的预装箱未完成处理，保持处理中状态(1)。",
                            inBound.getInStoreNumber(), inBound.getId());
                }
                
            } catch (Exception e) {
                log.error("检查入库单 {} 状态时发生异常: {}", inboundNumber, e.getMessage(), e);
                // 不抛出异常，避免影响其他入库单的状态更新
            }
        }
        // --- 入库单状态更新逻辑结束 ---

        // 统计处理结果
        long materialBoxCount = inboundItems.stream()
            .filter(item -> item.getContainerType() != null && item.getContainerType() == 1)
            .count();
        long materialPalletCount = inboundItems.stream()
            .filter(item -> item.getContainerType() != null && item.getContainerType() == 2)
            .count();
            
        log.info("成功处理自身属性绑定入库: 总数量={}, 料箱数量={}, 托盘数量={}, 涉及入库单数量={}", 
            inboundItems.size(), materialBoxCount, materialPalletCount, processedInboundNumbers.size());
        return true;
    }

    /**
     * 处理料箱款式属性绑定入库
     *
     * @param splitData 料箱款式拆分入库数据
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> processBoxStyleInbound(StyleSplitInboundDto splitData) {
        log.info("开始处理料箱款式属性绑定入库: 容器={}, 源数据数量={}", 
                splitData.getBoxNo(), splitData.getSourceDataList() != null ? splitData.getSourceDataList().size() : 0);
        
        try {
            // 1. 使用前端传递的sourceDataList获取原预装数据，并验证数据有效性
            log.info("开始处理料箱款式拆分: 容器={}, 源数据数量={}", 
                    splitData.getBoxNo(), splitData.getSourceDataList() != null ? splitData.getSourceDataList().size() : 0);
            
            // 验证sourceDataList不为空
            if (splitData.getSourceDataList() == null || splitData.getSourceDataList().isEmpty()) {
                log.error("料箱款式拆分失败: 源数据列表为空, 容器={}", splitData.getBoxNo());
                return Result.error("源数据列表不能为空");
            }
            
            // 提取源数据ID列表
            List<String> sourcePreboxIds = splitData.getSourceDataList().stream()
                    .map(StyleSplitInboundDto.SourceDataDTO::getSourcePreboxItemId)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            
            if (sourcePreboxIds.isEmpty()) {
                log.error("料箱款式拆分失败: 源数据ID列表为空, 容器={}", splitData.getBoxNo());
                return Result.error("源数据ID列表不能为空");
            }
            
            // 验证这些预装数据确实存在且状态正确
            List<AuxiliaryPrebox> originalPreboxList = auxiliaryPreboxService.listByIds(sourcePreboxIds);
            if (originalPreboxList.size() != sourcePreboxIds.size()) {
                List<String> foundIds = originalPreboxList.stream().map(AuxiliaryPrebox::getId).collect(Collectors.toList());
                List<String> notFoundIds = sourcePreboxIds.stream().filter(id -> !foundIds.contains(id)).collect(Collectors.toList());
                log.error("料箱款式拆分失败: 部分源数据不存在, 容器={}, 未找到的ID={}, 请检查数据是否已被删除或确认前端数据是否最新", 
                        splitData.getBoxNo(), notFoundIds);
                return Result.error("部分预装箱数据不存在，可能已被删除或数据不同步。请刷新页面重新操作。缺失ID: " + String.join(", ", notFoundIds));
            }
            
            // 验证所有预装数据都是未处理状态
            List<AuxiliaryPrebox> invalidStatusPrebox = originalPreboxList.stream()
                    .filter(prebox -> prebox.getStatus() == null || prebox.getStatus() != 0)
                    .collect(Collectors.toList());
            if (!invalidStatusPrebox.isEmpty()) {
                List<String> invalidInfo = invalidStatusPrebox.stream()
                        .map(prebox -> String.format("ID:%s,状态:%s,容器:%s", prebox.getId(), prebox.getStatus(), prebox.getContainerNo()))
                        .collect(Collectors.toList());
                log.error("料箱款式拆分失败: 部分源数据状态不正确, 容器={}, 无效数据={}", splitData.getBoxNo(), invalidInfo);
                return Result.error("部分预装箱已被处理，无法重复操作。如需重新拆分，请先取消原有操作。详情: " + String.join("; ", invalidInfo));
            }
            
            log.info("源数据验证通过: 容器={}, 有效源数据数量={}", splitData.getBoxNo(), originalPreboxList.size());
            
            if (originalPreboxList.isEmpty()) {
                return Result.error("未找到容器 " + splitData.getBoxNo() + " 的未处理预装数据");
            }
            
            // 2. 先逻辑删除原预装数据，避免容器容量检查时的冲突
            log.info("先逻辑删除原预装数据: 容器={}, 删除数量={}", splitData.getBoxNo(), originalPreboxList.size());
            
            for (AuxiliaryPrebox prebox : originalPreboxList) {
                prebox.setIsDeleted(1);
                prebox.setStatus(1);
                prebox.setUpdateTime(new java.util.Date());
                try {
                    String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                    prebox.setUpdateBy(StringUtils.isNotEmpty(userId) ? userId : "system_delete");
                } catch (Exception e) {
                    log.warn("获取当前用户ID失败，使用系统默认值: {}", e.getMessage());
                    prebox.setUpdateBy("system_delete");
                }
            }
            
            boolean deleteResult = auxiliaryPreboxService.updateBatchById(originalPreboxList);
            if (!deleteResult) {
                log.error("逻辑删除原预装数据失败: 容器={}", splitData.getBoxNo());
                return Result.error("逻辑删除原预装数据失败");
            }
            
            log.info("逻辑删除原预装数据成功: 容器={}, 影响记录数={}", splitData.getBoxNo(), sourcePreboxIds.size());
            
            // 3. 基于原预装数据和拆分数据创建新预装数据
            log.info("基于拆分数据创建新预装数据: 容器={}", splitData.getBoxNo());
            List<AuxiliaryPreboxDto> preboxDataList = 
                createPreboxDataFromSplit(splitData, originalPreboxList);
            
            if (preboxDataList.isEmpty()) {
                return Result.error("转换拆分数据为预装数据失败或数据为空");
            }
            
            // 调用预装服务写入预装表
            String preboxBatchNo = auxiliaryPreboxService.auxiliaryPreboxSplit(preboxDataList);
            if (preboxBatchNo == null || preboxBatchNo.isEmpty()) {
                return Result.error("写入预装表失败");
            }
            
            // 4. 获取新创建的预装数据ID列表，调用原来的 processInBound 入库方法
            log.info("获取新预装数据并调用入库方法: 批次号={}", preboxBatchNo);
            List<AuxiliaryPrebox> newPreboxList = 
                auxiliaryPreboxService.list(
                    new LambdaQueryWrapper<AuxiliaryPrebox>()
                        .eq(AuxiliaryPrebox::getStockBatchId, preboxBatchNo)
                        .eq(AuxiliaryPrebox::getStatus, 0)
                );
            
            if (newPreboxList == null || newPreboxList.isEmpty()) {
                return Result.error("未找到新创建的预装数据");
            }
            
            // 提取预装数据ID列表
            List<String> preboxIds = newPreboxList.stream()
                .map(AuxiliaryPrebox::getId)
                .collect(Collectors.toList());
            
            // 调用原来的入库处理方法
            boolean inboundResult =processInBoundInternal(preboxIds);
            if (!inboundResult) {
                return Result.error("入库处理失败");
            }
            
            log.info("料箱款式拆分绑定入库完成: 容器={}, 批次号={}", splitData.getBoxNo(), preboxBatchNo);
            return Result.ok("料箱款式拆分绑定入库成功");
            
        } catch (Exception e) {
            log.error("料箱款式属性绑定入库失败: 容器={}, 错误={}", splitData.getBoxNo(), e.getMessage(), e);
            return Result.error("料箱款式属性绑定入库失败: " + e.getMessage());
        }
    }

    /**
     * 基于拆分数据和原预装数据创建新预装数据列表
     * 
     * @param splitData 拆分数据
     * @param originalPreboxList 原预装数据列表
     * @return 预装数据列表
     */
    private List<AuxiliaryPreboxDto> createPreboxDataFromSplit(
            StyleSplitInboundDto splitData, List<AuxiliaryPrebox> originalPreboxList) {
        
        List<AuxiliaryPreboxDto> preboxDataList = new ArrayList<>();
        
        if (splitData.getSourceDataList() == null || splitData.getSourceDataList().isEmpty()) {
            log.warn("拆分数据为空，无法创建预装数据");
            return preboxDataList;
        }
        
        if (originalPreboxList == null || originalPreboxList.isEmpty()) {
            log.warn("原预装数据为空，无法创建预装数据");
            return preboxDataList;
        }
        
        // 创建原预装数据的映射关系，便于根据ID快速查找
        HashMap<String, AuxiliaryPrebox> originalPreboxMap = new HashMap<>();
        for (AuxiliaryPrebox prebox : originalPreboxList) {
            originalPreboxMap.put(prebox.getId(), prebox);
        }
        
        log.info("开始基于拆分数据创建预装数据，原预装数据数量: {}", originalPreboxList.size());
        
        // 遍历拆分数据，为每个拆分明细创建预装数据
        for (StyleSplitInboundDto.SourceDataDTO sourceData : splitData.getSourceDataList()) {
            if (sourceData.getSplitDetails() == null || sourceData.getSplitDetails().isEmpty()) {
                continue;
            }
            
            // 根据sourcePreboxItemId找到对应的原预装数据
            String sourcePreboxItemId = sourceData.getSourcePreboxItemId();
            AuxiliaryPrebox correspondingPrebox = originalPreboxMap.get(sourcePreboxItemId);
            
            if (correspondingPrebox == null) {
                log.error("未找到对应的原预装数据: sourcePreboxItemId={}", sourcePreboxItemId);
                continue;
            }
            
            // 从对应的原预装数据中获取关键信息
            String originalDetailIdList = correspondingPrebox.getDetailIds(); // 入库单明细id集合
            String originalStockInNo = correspondingPrebox.getStockInNo(); // 入库单号
            String originalStockInId = correspondingPrebox.getStockInId(); // 入库单id
            String originalTaskOrder = correspondingPrebox.getTaskOrder(); // 任务顺序
            String orginalTaskOrder=correspondingPrebox.getStockBatchId();
            
            log.debug("处理拆分源数据: sourcePreboxItemId={}, 明细ID列表={}, 入库单号={}", 
                    sourcePreboxItemId, originalDetailIdList, originalStockInNo);
            
            // 为每个拆分明细创建预装数据
            for (StyleSplitInboundDto.SplitDetailDTO splitDetail : sourceData.getSplitDetails()) {
                AuxiliaryPreboxDto preboxDto = new AuxiliaryPreboxDto();
                
                // 设置关联信息（使用对应原预装数据的信息）
                preboxDto.setStockInId(originalStockInId); 
                preboxDto.setStockInNo(originalStockInNo);
                preboxDto.setDetailIds(originalDetailIdList); 
                preboxDto.setTaskOrder(originalTaskOrder);
                preboxDto.setStockBatchId(orginalTaskOrder);
                
                // 设置容器信息
                preboxDto.setContainerNo(splitData.getBoxNo());
                preboxDto.setContainerType(splitData.getContainerType());
                preboxDto.setGridId(sourceData.getSourceGridId());
                preboxDto.setMaterialProperty(1); // 款式属性
                
                // 设置物料信息
                preboxDto.setMaterialCode(sourceData.getSourceMaterialCode());
                preboxDto.setMaterialName(sourceData.getSourceMaterialName());
                preboxDto.setContractNo(sourceData.getSourceContractNo());
                preboxDto.setStyleNo(sourceData.getSourceItemNo());
                
                // 设置拆分后的信息
                preboxDto.setPoNo(splitDetail.getPoNo());
                preboxDto.setMaterialModel(splitDetail.getMaterialModel());
                preboxDto.setMaterialColor(splitDetail.getMaterialColor());
                preboxDto.setInQuantity(splitDetail.getInboundQty());
                // 在款式拆分场景下，初始化实际入库数量为0，支持页面手动输入
                preboxDto.setActualInboundQty(0);
                
                preboxDataList.add(preboxDto);
                
                log.debug("创建拆分预装数据: 容器={}, 原预装ID={}, 物料={}, PO={}, 规格={}, 颜色={}, 数量={}", 
                        splitData.getBoxNo(), sourcePreboxItemId, sourceData.getSourceMaterialCode(), 
                        splitDetail.getPoNo(), splitDetail.getMaterialModel(), 
                        splitDetail.getMaterialColor(), splitDetail.getInboundQty());
            }
        }
        
        log.info("创建拆分预装数据完成: 容器={}, 预装数据数量={}", 
                splitData.getBoxNo(), preboxDataList.size());
        
        return preboxDataList;
    }

    /**
     * 处理托盘款式属性绑定入库
     * 包含款式拆分逻辑和入库处理
     *
     * @param splitData 托盘款式拆分入库数据
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> processPalletStyleInbound(StyleSplitInboundDto splitData) {
        log.info("开始处理托盘款式拆分绑定入库: 容器={}, 源数据数量={}", 
                splitData.getBoxNo(), splitData.getSourceDataList() != null ? splitData.getSourceDataList().size() : 0);
        
        try {
            // 提取源数据ID列表
            List<String> sourcePreboxIds = splitData.getSourceDataList().stream()
                    .map(StyleSplitInboundDto.SourceDataDTO::getSourcePreboxItemId)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            
            // 验证这些预装数据确实存在且状态正确
            List<AuxiliaryPrebox> originalPreboxList = auxiliaryPreboxService.listByIds(sourcePreboxIds);
            if (originalPreboxList.size() != sourcePreboxIds.size()) {
                List<String> foundIds = originalPreboxList.stream().map(AuxiliaryPrebox::getId).collect(Collectors.toList());
                List<String> notFoundIds = sourcePreboxIds.stream().filter(id -> !foundIds.contains(id)).collect(Collectors.toList());
                log.error("托盘款式拆分失败: 部分源数据不存在, 容器={}, 未找到的ID={}", splitData.getBoxNo(), notFoundIds);
                return Result.error("部分源数据不存在: " + String.join(", ", notFoundIds));
            }
            
            // 验证所有预装数据都是未处理状态
            List<AuxiliaryPrebox> invalidStatusPrebox = originalPreboxList.stream()
                    .filter(prebox -> prebox.getStatus() == null || prebox.getStatus() != 0)
                    .collect(Collectors.toList());
            if (!invalidStatusPrebox.isEmpty()) {
                List<String> invalidInfo = invalidStatusPrebox.stream()
                        .map(prebox -> String.format("ID:%s,状态:%s", prebox.getId(), prebox.getStatus()))
                        .collect(Collectors.toList());
                log.error("托盘款式拆分失败: 部分源数据状态不正确, 容器={}, 无效数据={}", splitData.getBoxNo(), invalidInfo);
                return Result.error("部分源数据状态不正确，只能处理未处理状态(0)的数据: " + String.join("; ", invalidInfo));
            }
            
            log.info("源数据验证通过: 容器={}, 有效源数据数量={}", splitData.getBoxNo(), originalPreboxList.size());
            
            if (originalPreboxList.isEmpty()) {
                return Result.error("未找到容器 " + splitData.getBoxNo() + " 的未处理预装数据");
            }
            
            // 先逻辑删除原预装数据，避免容器容量检查时的冲突
            log.info("先逻辑删除原预装数据: 容器={}, 删除数量={}", splitData.getBoxNo(), originalPreboxList.size());
            
            for (AuxiliaryPrebox prebox : originalPreboxList) {
                prebox.setIsDeleted(1);
                prebox.setStatus(1);
                prebox.setUpdateTime(new java.util.Date());
                try {
                    String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                    prebox.setUpdateBy(StringUtils.isNotEmpty(userId) ? userId : "system_delete");
                } catch (Exception e) {
                    log.warn("获取当前用户ID失败，使用系统默认值: {}", e.getMessage());
                    prebox.setUpdateBy("system_delete");
                }
            }
            
            boolean deleteResult = auxiliaryPreboxService.updateBatchById(originalPreboxList);
            if (!deleteResult) {
                log.error("逻辑删除原预装数据失败: 容器={}", splitData.getBoxNo());
                return Result.error("逻辑删除原预装数据失败");
            }
            
            log.info("逻辑删除原预装数据成功: 容器={}, 影响记录数={}", splitData.getBoxNo(), sourcePreboxIds.size());
            
            // 基于原预装数据和拆分数据创建新预装数据
            log.info("基于拆分数据创建新预装数据: 容器={}", splitData.getBoxNo());
            List<AuxiliaryPreboxDto> preboxDataList = 
                createPreboxDataFromSplit(splitData, originalPreboxList);
            
            if (preboxDataList.isEmpty()) {
                return Result.error("转换拆分数据为预装数据失败或数据为空");
            }
            
            // 调用预装服务写入预装表
            String preboxBatchNo = auxiliaryPreboxService.auxiliaryPreboxSplit(preboxDataList);
            if (preboxBatchNo == null || preboxBatchNo.isEmpty()) {
                return Result.error("写入预装表失败");
            }
            
            // 获取新创建的预装数据ID列表，调用原来的 processInBound 入库方法
            log.info("获取新预装数据并调用入库方法: 批次号={}", preboxBatchNo);
            List<AuxiliaryPrebox> newPreboxList = 
                auxiliaryPreboxService.list(
                    new LambdaQueryWrapper<AuxiliaryPrebox>()
                        .eq(AuxiliaryPrebox::getStockBatchId, preboxBatchNo)
                        .eq(AuxiliaryPrebox::getStatus, 0)
                );
            
            if (newPreboxList == null || newPreboxList.isEmpty()) {
                return Result.error("未找到新创建的预装数据");
            }
            
            // 提取预装数据ID列表
            List<String> preboxIds = newPreboxList.stream()
                .map(AuxiliaryPrebox::getId)
                .collect(Collectors.toList());
            
            // 调用原来的入库处理方法
            boolean inboundResult = processInBoundInternal(preboxIds);
            if (!inboundResult) {
                return Result.error("入库处理失败");
            }
            
            log.info("托盘款式拆分绑定入库完成: 容器={}, 批次号={}", splitData.getBoxNo(), preboxBatchNo);
            return Result.ok("托盘款式拆分绑定入库成功");
            
        } catch (Exception e) {
            log.error("处理托盘款式拆分逻辑失败: {}", e.getMessage(), e);
            return Result.error("处理托盘款式拆分逻辑失败: " + e.getMessage());
        }
    }

    /**
     * 内部的入库处理方法，从原 processPalletStyleInbound 逻辑中提取
     * 
     * @param preboxIds 预装箱ID列表
     * @return 处理结果
     */
    private boolean processInBoundInternal(List<String> preboxIds) {
        log.info("开始处理预装箱入库: 预装箱ID数量={}", preboxIds != null ? preboxIds.size() : 0);
        
        try {
            // 参数验证
            if (preboxIds == null || preboxIds.isEmpty()) {
                log.error("预装箱ID列表为空");
                throw new IllegalArgumentException("预装箱ID列表为空: " + preboxIds);
            }
            
            // 1、查询预装箱数据
            List<AuxiliaryPrebox> preboxDataList = auxiliaryPreboxService.listByIds(preboxIds);

            if (preboxDataList == null || preboxDataList.isEmpty()) {
                log.warn("根据提供的ID列表 {} 未查询到任何预装箱数据", preboxIds);
                throw new IllegalArgumentException("根据提供的ID列表未查询到任何预装箱数据: " + preboxIds);
            }

            if (preboxDataList.size() != preboxIds.size()) {
                List<String> foundIds = preboxDataList.stream().map(AuxiliaryPrebox::getId).collect(Collectors.toList());
                List<String> notFoundIds = preboxIds.stream().filter(id -> !foundIds.contains(id)).collect(Collectors.toList());
                log.error("部分预装箱ID未找到对应数据: {}", notFoundIds);
                throw new IllegalArgumentException("部分预装箱ID未找到数据: " + String.join(", ", notFoundIds));
            }

            // 收集需要更新状态的入库单信息（避免在循环中重复查询和更新）
            Set<String> processedInboundNumbers = new HashSet<>();
            
            // 处理每个预装箱数据（与 processInBound 主逻辑一致）
            for (AuxiliaryPrebox preboxDto : preboxDataList) {
                // 防止重复处理已完成的预装箱
                if (preboxDto.getStatus() != null && preboxDto.getStatus() == 1) {
                    log.info("预装箱 {} (入库单号 {}) 已处理完成，跳过。", preboxDto.getId(), preboxDto.getStockInNo());
                    continue;
                }

                // *** 改进的容器查找和创建逻辑（支持混装场景）***
                BoxItem boxItem = findOrCreateBoxItem(preboxDto);
                
                // 更新 BoxItem 信息：累加数量，更新时间和操作人
                Integer existingQuantity = boxItem.getMaterialQuantity() == null ? 0 : boxItem.getMaterialQuantity();
                Integer incomingQuantity = preboxDto.getMaterialQuantity() == null ? 0 : preboxDto.getMaterialQuantity();
                boxItem.setMaterialQuantity(existingQuantity + incomingQuantity);

                // 更新网格状态
                if (boxItem.getMaterialQuantity() > 0 && (boxItem.getGridStatus() == null || boxItem.getGridStatus() == 0)) {
                    boxItem.setGridStatus(1); // 有物料但未满
                } else if (boxItem.getMaterialQuantity() == 0 && (boxItem.getGridStatus() != null && boxItem.getGridStatus() != 0)) {
                    boxItem.setGridStatus(0); // 空
                }
                
                boxItem.setUpdateTime(new Date());
                try {
                    String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                    boxItem.setUpdateBy(com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId) ? userId : "system_update");
                } catch (Exception e) {
                    log.warn("更新BoxItem {} 时获取当前用户ID失败: {}", boxItem.getId(), e.getMessage());
                    boxItem.setUpdateBy("system_update");
                }

                boolean boxItemUpdated = boxItemService.updateById(boxItem);
                if (!boxItemUpdated) {
                    log.error("更新BoxItem失败, ID: {}", boxItem.getId());
                    throw new RuntimeException("更新BoxItem失败, ID: " + boxItem.getId());
                }

                //查询入库单信息
                WmsAuxiliaryInBound inBound = this.getOne(
                        new LambdaQueryWrapper<WmsAuxiliaryInBound>()
                                .eq(WmsAuxiliaryInBound::getInStoreNumber, preboxDto.getStockInNo())
                                .eq(WmsAuxiliaryInBound::getDeleteFlag, 0)
                );

                if (inBound == null) {
                    log.error("未找到与预装箱 {} (入库单号 {}) 关联的有效入库单。", preboxDto.getId(), preboxDto.getStockInNo());
                    throw new IllegalArgumentException("未找到关联的入库单: " + preboxDto.getStockInNo());
                }

                // 如果入库单已完成或已取消，则不应再处理其预装箱
                if (inBound.getStatus() == 2 || inBound.getStatus() == 3) {
                    log.warn("入库单 {} 状态为 {} (2:已完成, 3:已取消)，预装箱 {} (ID: {}) 不应再被处理。",
                             inBound.getInStoreNumber(), inBound.getStatus(), preboxDto.getContainerNo(), preboxDto.getId());
                    throw new IllegalArgumentException("入库单 " + inBound.getInStoreNumber() + " 已是最终状态，无法继续处理其预装箱");
                }
                
                //创建入库任务数据
                WmsBoxTaskList task = new WmsBoxTaskList();
                task.setTaskStatus(0);
                task.setBoxNo(preboxDto.getContainerNo());
                task.setBoxType(preboxDto.getContainerType());
                task.setTaskType(inBound.getTaskType());
                WmsBoxTaskList wmsBoxTaskList = wmsBoxTaskListServiceImpl.createTask(task);
                if (wmsBoxTaskList == null || StringUtils.isEmpty(wmsBoxTaskList.getTaskOrder())) {
                    log.error("创建入库任务失败，预装箱ID: {}", preboxDto.getId());
                    throw new IllegalArgumentException("创建入库任务失败，预装箱ID: " + preboxDto.getId());
                }

                //更新预装箱状态
                preboxDto.setStatus(1); // 1 表示已处理或已生成入库任务
                preboxDto.setTaskOrder(wmsBoxTaskList.getTaskOrder());
                preboxDto.setUpdateTime(new Date());
                try {
                    String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                    preboxDto.setUpdateBy(com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId) ? userId : "system_update");
                } catch (Exception e) {
                    log.warn("更新预装箱 {} 时获取当前用户ID失败: {}", preboxDto.getId(), e.getMessage());
                    preboxDto.setUpdateBy("system_update");
                }
                boolean preboxUpdated = auxiliaryPreboxService.updateById(preboxDto);
                if(!preboxUpdated){
                    log.error("更新预装箱状态失败, ID: {}", preboxDto.getId());
                    throw new IllegalArgumentException("更新预装箱状态失败, ID: " + preboxDto.getId());
                }

                // 记录已处理的入库单号，避免在循环中重复更新入库单状态
                processedInboundNumbers.add(preboxDto.getStockInNo());
                
                // 只在循环中更新入库单状态为处理中(1)，避免过早设置为已完成(2)
                if (inBound.getStatus() == 0) { // 仅当状态为待处理(0)时，更新为处理中(1)
                    log.info("入库单 {} (ID: {}) 开始处理预装箱，更新状态为处理中(1)。", 
                            inBound.getInStoreNumber(), inBound.getId());
                    inBound.setStatus(1); // 处理中
                    inBound.setUpdateTime(new Date());
                    try {
                        String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                        inBound.setUpdateBy(com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId) ? userId : "system_update_inbound");
                    } catch (Exception e) {
                        log.warn("更新入库单 {} 时获取当前用户ID失败: {}", inBound.getInStoreNumber(), e.getMessage());
                        inBound.setUpdateBy("system_update_inbound");
                    }
                    this.updateById(inBound);
                }
            } // end of for loop for preboxDataList

            // --- 循环结束后，统一检查并更新入库单状态为已完成 ---
            log.info("所有预装箱处理完成，开始检查入库单状态更新，涉及入库单数量: {}", processedInboundNumbers.size());
            
            for (String inboundNumber : processedInboundNumbers) {
                try {
                    // 重新查询入库单信息（获取最新状态）
                    WmsAuxiliaryInBound inBound = this.getOne(
                            new LambdaQueryWrapper<WmsAuxiliaryInBound>()
                                    .eq(WmsAuxiliaryInBound::getInStoreNumber, inboundNumber)
                                    .eq(WmsAuxiliaryInBound::getDeleteFlag, 0)
                    );
                    
                    if (inBound == null) {
                        log.warn("重新查询入库单 {} 时未找到记录，跳过状态更新", inboundNumber);
                        continue;
                    }
                    
                    // 如果已经是最终状态，跳过
                    if (inBound.getStatus() == 2 || inBound.getStatus() == 3) {
                        log.info("入库单 {} 状态已是最终状态 {}，跳过状态检查", inboundNumber, inBound.getStatus());
                        continue;
                    }
                    
                    // 查询该入库单下的所有未删除的入库明细
                    List<WmsAuxiliaryDetail> allDetailsForInbound = auxiliaryDetailService.list(
                            new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                                    .eq(WmsAuxiliaryDetail::getRefNumber, inBound.getInStoreNumber())
                                    .eq(WmsAuxiliaryDetail::getOperationType, 0) // 入库操作
                                    .eq(WmsAuxiliaryDetail::getDeleteFlag, 0)    // 未删除
                    );

                    String currentUserId = "system_update_inbound";
                    try {
                        String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                        if (com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId)) {
                            currentUserId = userId;
                        }
                    } catch (Exception e) {
                        log.warn("更新入库单 {} 时获取当前用户ID失败: {}", inBound.getInStoreNumber(), e.getMessage());
                    }

                    if (allDetailsForInbound.isEmpty()) {
                        log.warn("入库单 {} (ID: {}) 没有任何有效的入库明细。这可能是一个数据问题。",
                                inBound.getInStoreNumber(), inBound.getId());
                        continue;
                    }
                    
                    // *** 使用明细表的预装状态直接判断，更准确可靠 ***
                    boolean allAssociatedPreboxesProcessed = true;
                    
                    for (WmsAuxiliaryDetail detail : allDetailsForInbound) {
                        // 检查明细的预装状态：1表示已预装，0或null表示未预装
                        boolean isDetailPreboxed = detail.getPreboxStatus() != null && detail.getPreboxStatus() == 1;
                        
                        if (!isDetailPreboxed) {
                            log.info("入库单 {} 的明细 {} (物料 {}, ID: {}) 预装状态为 {}，尚未完成预装处理。",
                                    inBound.getInStoreNumber(), detail.getMaterialName(), detail.getMaterialCode(), 
                                    detail.getId(), detail.getPreboxStatus());
                            allAssociatedPreboxesProcessed = false;
                            break;
                        }
                        
                        // 如果明细已预装，进一步检查对应的预装箱是否已完成入库
                        boolean isPreboxCompleted = false;
                        
                        if (StringUtils.isNotEmpty(detail.getPreboxId())) {
                            // 优先使用preboxId查询
                            AuxiliaryPrebox preboxRecord = auxiliaryPreboxService.getById(detail.getPreboxId());
                            if (preboxRecord != null && preboxRecord.getStatus() != null && preboxRecord.getStatus() == 1) {
                                isPreboxCompleted = true;
                            } else {
                                log.info("入库单 {} 的明细 {} (物料 {}, ID: {}) 关联的预装箱 {} 状态为 {}，尚未完成入库处理。",
                                        inBound.getInStoreNumber(), detail.getMaterialName(), detail.getMaterialCode(), 
                                        detail.getId(), detail.getPreboxId(), 
                                        preboxRecord != null ? preboxRecord.getStatus() : "记录不存在");
                            }
                        } else if (StringUtils.isNotEmpty(detail.getPreboxBatchNo())) {
                            // 兼容旧数据：如果没有preboxId，通过批次号和detail_ids字段查找
                            List<AuxiliaryPrebox> preboxList = auxiliaryPreboxService.list(
                                    new LambdaQueryWrapper<AuxiliaryPrebox>()
                                            .eq(AuxiliaryPrebox::getStockBatchId, detail.getPreboxBatchNo())
                                            .like(AuxiliaryPrebox::getDetailIds, detail.getId())
                                            .eq(AuxiliaryPrebox::getStatus, 1) // 直接查询已完成的
                                            .eq(AuxiliaryPrebox::getIsDeleted, 0)
                            );
                            
                            if (!preboxList.isEmpty()) {
                                isPreboxCompleted = true;
                            } else {
                                log.info("入库单 {} 的明细 {} (物料 {}, ID: {}) 在批次 {} 中未找到已完成的预装箱。",
                                        inBound.getInStoreNumber(), detail.getMaterialName(), detail.getMaterialCode(), 
                                        detail.getId(), detail.getPreboxBatchNo());
                            }
                        } else {
                            log.warn("入库单 {} 的明细 {} (物料 {}, ID: {}) 预装状态为已预装，但既没有preboxId也没有批次号，数据异常。",
                                    inBound.getInStoreNumber(), detail.getMaterialName(), detail.getMaterialCode(), detail.getId());
                        }
                        
                        if (!isPreboxCompleted) {
                            allAssociatedPreboxesProcessed = false;
                            break;
                        }
                    }

                    if (allAssociatedPreboxesProcessed) {
                        log.info("入库单 {} (ID: {}) 的所有明细均已完成关联预装箱的处理，更新状态为已完成(2)。",
                                inBound.getInStoreNumber(), inBound.getId());
                        inBound.setStatus(2); // 已完成
                        inBound.setUpdateTime(new Date());
                        inBound.setUpdateBy(currentUserId);
                        this.updateById(inBound);
                    } else {
                        log.info("入库单 {} (ID: {}) 仍有明细的预装箱未完成处理，保持处理中状态(1)。",
                                inBound.getInStoreNumber(), inBound.getId());
                    }
                    
                } catch (Exception e) {
                    log.error("检查入库单 {} 状态时发生异常: {}", inboundNumber, e.getMessage(), e);
                    // 不抛出异常，避免影响其他入库单的状态更新
                }
            }
            // --- 入库单状态更新逻辑结束 ---

            log.info("托盘款式属性绑定入库完成，涉及的预装箱ID: {}", preboxIds);
            return true;
            
        } catch (Exception e) {
            log.error("托盘款式属性绑定入库失败: 错误={}", e.getMessage(), e);
            throw new IllegalArgumentException("托盘款式属性绑定入库失败: " + e.getMessage());
        }
    }
    



    /**
     * 查找或创建 BoxItem 记录（优化查询，减少数据库压力）
     * 
     * @param preboxDto 预装箱数据
     * @return BoxItem记录
     */
    private BoxItem findOrCreateBoxItem(AuxiliaryPrebox preboxDto) {
        // 一次性查询容器下的所有BoxItem记录
        QueryWrapper<BoxItem> containerQuery = new QueryWrapper<>();
        containerQuery.eq("box_no", preboxDto.getContainerNo())
                     .eq("box_type", preboxDto.getContainerType())
                     .eq("delete_flag", 0);
        
        // 只有料箱才按网格ID查询
        if (preboxDto.getContainerType() == 1) {
            containerQuery.eq("grid_id", preboxDto.getGridId());
        }
        
        List<BoxItem> allBoxItems = boxItemService.getBaseMapper().selectList(containerQuery);
        log.info("容器 {} 网格 {} 共查询到 {} 条BoxItem记录", 
                preboxDto.getContainerNo(), preboxDto.getGridId(), allBoxItems.size());
        
        // 在内存中进行匹配，优先级：精确匹配 > 空记录 > 创建新记录
        BoxItem targetBoxItem = null;
        
        // 步骤1：寻找精确匹配的记录（已有相同物料信息）
        for (BoxItem item : allBoxItems) {
            // 检查是否有物料编码，没有则跳过
            if (item.getMaterialCode() == null || item.getMaterialCode().trim().isEmpty()) {
                continue;
            }
            
            // 基本信息匹配
            if (!item.getMaterialCode().equals(preboxDto.getMaterialCode()) ||
                !item.getContractNo().equals(preboxDto.getContractNo()) ||
                !item.getItemNo().equals(preboxDto.getItemNo())) {
                continue;
            }
            
            // 根据物料属性进行颜色规格匹配
            boolean isMatch = false;
            if (preboxDto.getMaterialProperty() != null && preboxDto.getMaterialProperty() == 0) {
                // 自身属性：不需要精确匹配颜色规格
                isMatch = true;
                log.info("自身属性物料匹配成功: ID={}, 物料编码={}", item.getId(), item.getMaterialCode());
            } else {
                // 款式属性：需要精确匹配颜色规格
                boolean colorMatch = (item.getMaterialColor() == null && preboxDto.getMaterialColor() == null) ||
                                   (item.getMaterialColor() != null && item.getMaterialColor().equals(preboxDto.getMaterialColor()));
                boolean modelMatch = (item.getMaterialModel() == null && preboxDto.getMaterialModel() == null) ||
                                   (item.getMaterialModel() != null && item.getMaterialModel().equals(preboxDto.getMaterialModel()));
                if (colorMatch && modelMatch) {
                    isMatch = true;
                    log.info("款式属性物料匹配成功: ID={}, 物料编码={}, 颜色={}, 规格={}", 
                            item.getId(), item.getMaterialCode(), item.getMaterialColor(), item.getMaterialModel());
                }
            }
            
            if (isMatch) {
                targetBoxItem = item;
                break;
            }
        }
        
        // 步骤2：如果没有精确匹配，寻找空记录并填充
        if (targetBoxItem == null) {
            for (BoxItem item : allBoxItems) {
                // 检查是否为空记录
                if (item.getMaterialCode() == null || item.getMaterialCode().trim().isEmpty()) {
                    log.info("找到空BoxItem记录，开始填充物料信息: ID={}", item.getId());
                    
                    // 填充物料信息
                    item.setContractNoPo(preboxDto.getPoNo());
                    item.setContractNo(preboxDto.getContractNo());
                    item.setItemNo(preboxDto.getItemNo());
                    item.setMaterialCode(preboxDto.getMaterialCode());
                    item.setMaterialName(preboxDto.getMaterialName());
                    item.setMaterialColor(preboxDto.getMaterialColor());
                    item.setMaterialModel(preboxDto.getMaterialModel());
                    item.setMaterialProperty(preboxDto.getMaterialProperty());
                    item.setUpdateTime(new Date());
                    
                    try {
                        String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                        item.setUpdateBy(com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId) ? userId : "system");
                    } catch (Exception e) {
                        log.warn("获取当前用户ID失败，使用系统默认ID: {}", e.getMessage());
                        item.setUpdateBy("system");
                    }
                    
                    boolean updateResult = boxItemService.updateById(item);
                    if (updateResult) {
                        log.info("成功填充空BoxItem记录: ID={}, 物料编码={}, 颜色={}, 规格={}", 
                                item.getId(), item.getMaterialCode(), item.getMaterialColor(), item.getMaterialModel());
                        targetBoxItem = item;
                        break;
                    } else {
                        log.error("填充空BoxItem记录失败: ID={}", item.getId());
                    }
                }
            }
        }
        
        // 步骤3：如果既没有精确匹配也没有空记录，创建新记录
        if (targetBoxItem == null) {
            log.info("容器中无可用BoxItem记录，创建新记录: 容器号={}, 物料编码={}", 
                    preboxDto.getContainerNo(), preboxDto.getMaterialCode());
            targetBoxItem = createNewBoxItem(preboxDto);
        }
        
        return targetBoxItem;
    }

    /**
     * 创建新的BoxItem记录
     * 
     * @param preboxDto 预装箱数据
     * @return 创建的BoxItem，如果创建失败则返回null
     */
    private BoxItem createNewBoxItem(AuxiliaryPrebox preboxDto) {
        try {
            BoxItem newBoxItem = new BoxItem();
            newBoxItem.setId(UUID.randomUUID().toString());
            // 设置基本信息
            newBoxItem.setBoxType(preboxDto.getContainerType()); // 使用预装箱的容器类型
            newBoxItem.setBoxNo(preboxDto.getContainerNo());
            
            // 只有料箱才设置网格ID，托盘没有网格概念
            if (preboxDto.getContainerType() == 1) { // 料箱
            newBoxItem.setGridId(Integer.parseInt(preboxDto.getGridId()));
            } else { // 托盘
                newBoxItem.setGridId(0); // 托盘设置为0
            }
            newBoxItem.setGridStatus(1); // 有物料但未满
            
            // 设置物料信息
            newBoxItem.setContractNoPo(preboxDto.getPoNo());
            newBoxItem.setContractNo(preboxDto.getContractNo());
            newBoxItem.setItemNo(preboxDto.getItemNo());
            newBoxItem.setMaterialCode(preboxDto.getMaterialCode());
            newBoxItem.setMaterialName(preboxDto.getMaterialName());
            newBoxItem.setMaterialColor(preboxDto.getMaterialColor());
            newBoxItem.setMaterialModel(preboxDto.getMaterialModel());
            newBoxItem.setMaterialQuantity(0); // 初始数量为0，后续会累加
            newBoxItem.setMaterialProperty(preboxDto.getMaterialProperty());
            
            // 设置状态和时间信息
            newBoxItem.setStatus(0); // 初始状态
            newBoxItem.setDeleteFlag(0); // 未删除
            newBoxItem.setCreateTime(new Date());
            
            // 安全地获取用户ID
            try {
                String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
                newBoxItem.setCreateBy(com.tgvs.wms.common.util.StringUtils.isNotEmpty(userId) ? userId : "system");
            } catch (Exception e) {
                log.warn("获取当前用户ID失败，使用系统默认ID: {}", e.getMessage());
                newBoxItem.setCreateBy("system");
            }
            
            // 保存到数据库
            boolean saveResult = boxItemService.save(newBoxItem);
            if (saveResult) {
                String containerDesc = preboxDto.getContainerType() == 1 ? "料箱" : "托盘";
                String gridInfo = preboxDto.getContainerType() == 1 ? ", 网格ID: " + preboxDto.getGridId() : "";
                log.info("新BoxItem创建成功: 容器号={}, 容器类型: {}{}, 物料编码={}", 
                    preboxDto.getContainerNo(), containerDesc, gridInfo, preboxDto.getMaterialCode());
                return newBoxItem;
            } else {
                String containerDesc = preboxDto.getContainerType() == 1 ? "料箱" : "托盘";
                String gridInfo = preboxDto.getContainerType() == 1 ? ", 网格ID: " + preboxDto.getGridId() : "";
                log.error("保存新BoxItem失败: 容器号={}, 容器类型: {}{}", 
                    preboxDto.getContainerNo(), containerDesc, gridInfo);
                return null;
            }
            
        } catch (Exception e) {
            String containerDesc = preboxDto.getContainerType() == 1 ? "料箱" : "托盘";
            String gridInfo = preboxDto.getContainerType() == 1 ? ", 网格ID: " + preboxDto.getGridId() : "";
            log.error("创建新BoxItem时发生异常: 容器号={}, 容器类型: {}{}, 异常信息: {}", 
                preboxDto.getContainerNo(), containerDesc, gridInfo, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查并处理已存在的预装箱数据
     * 如果明细已预装且有出库记录，返回预装数据供前端展示
     */
    private List<AuxiliaryPreboxDto> checkAndHandleExistingPrebox(List<WmsAuxiliaryDetail> alreadyPreboxedDetails) {
        List<AuxiliaryPreboxDto> existingPreboxData = new ArrayList<>();
        
        try {
            // 批量查询物料信息，避免N+1查询
            List<String> materialCodes = alreadyPreboxedDetails.stream()
                    .map(WmsAuxiliaryDetail::getMaterialCode)
                    .filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            
            Map<String, WmsAuxiliaryInfo> materialInfoMap = new HashMap<>();
            if (!materialCodes.isEmpty()) {
                List<WmsAuxiliaryInfo> auxiliaryInfoList = auxiliaryInfoService.list(
                        new LambdaQueryWrapper<WmsAuxiliaryInfo>()
                                .in(WmsAuxiliaryInfo::getMaterialCode, materialCodes)
                );
                materialInfoMap = auxiliaryInfoList.stream()
                        .collect(Collectors.toMap(
                                WmsAuxiliaryInfo::getMaterialCode,
                                info -> info,
                                (existing, replacement) -> existing
                        ));
            }
            
            // 检查每个已预装明细的状态
            for (WmsAuxiliaryDetail detail : alreadyPreboxedDetails) {
                try {
                    boolean hasOutboundTask = false;
                    
                    if (StringUtils.isNotEmpty(detail.getPreboxId())) {
                        // 通过preboxId查询预装箱记录
                        AuxiliaryPrebox preboxRecord = auxiliaryPreboxService.getById(detail.getPreboxId());
                        
                        if (preboxRecord != null) {
                            // 检查是否有出库任务
                            hasOutboundTask = StringUtils.isNotEmpty(preboxRecord.getTaskOrder()) 
                                    && !preboxRecord.getTaskOrder().startsWith("TASK_FAILED_") 
                                    && !preboxRecord.getTaskOrder().startsWith("TASK_ERROR_");
                            
                            log.info("明细 {} 关联预装箱 {}，出库任务状态: {} (任务号: {})", 
                                    detail.getId(), preboxRecord.getId(), hasOutboundTask, preboxRecord.getTaskOrder());
                        } else {
                            log.warn("明细 {} 关联的预装箱 {} 不存在", detail.getId(), detail.getPreboxId());
                        }
                    } else {
                        // 如果没有preboxId，尝试通过其他方式查找（兼容旧数据）
                        log.warn("明细 {} 的preboxId为空，尝试通过批次号查找", detail.getId());
                        
                        if (StringUtils.isNotEmpty(detail.getPreboxBatchNo())) {
                            List<AuxiliaryPrebox> preboxList = auxiliaryPreboxService.list(
                                    new LambdaQueryWrapper<AuxiliaryPrebox>()
                                            .eq(AuxiliaryPrebox::getStockBatchId, detail.getPreboxBatchNo())
                                            .like(AuxiliaryPrebox::getDetailIds, detail.getId())
                                            .eq(AuxiliaryPrebox::getStatus, 0)
                            );
                            
                            hasOutboundTask = !preboxList.isEmpty() && preboxList.stream()
                                    .anyMatch(prebox -> StringUtils.isNotEmpty(prebox.getTaskOrder()) 
                                            && !prebox.getTaskOrder().startsWith("TASK_FAILED_") 
                                            && !prebox.getTaskOrder().startsWith("TASK_ERROR_"));
                        }
                    }
                    
                    if (hasOutboundTask) {
                        log.info("明细 {} 已预装且有有效的出库任务，加入返回数据", detail.getId());
                        AuxiliaryPreboxDto preboxDto = convertToPreboxDto(detail, materialInfoMap.get(detail.getMaterialCode()));
                        existingPreboxData.add(preboxDto);
                    } else {
                        log.info("明细 {} 已预装但无有效的出库任务", detail.getId());
                    }
                    
                } catch (Exception e) {
                    log.error("检查明细 {} 的预装状态时发生错误: {}", detail.getId(), e.getMessage(), e);
                }
            }
            
            if (!existingPreboxData.isEmpty()) {
                log.info("发现 {} 条已预装且有出库任务的明细，将返回给前端查看", existingPreboxData.size());
            }
            
        } catch (Exception e) {
            log.error("检查已存在预装箱数据时发生错误: {}", e.getMessage(), e);
        }
        
        return existingPreboxData;
    }

    /**
     * 将明细转换为预装箱DTO
     */
    private AuxiliaryPreboxDto convertToPreboxDto(WmsAuxiliaryDetail detail, WmsAuxiliaryInfo materialInfo) {
        AuxiliaryPreboxDto preboxDto = new AuxiliaryPreboxDto();
        
        // 设置基本信息
        preboxDto.setStockInId(detail.getId());
        preboxDto.setStockInNo(detail.getRefNumber());
        preboxDto.setContractNo(detail.getContractNo());
        preboxDto.setStyleNo(detail.getItemNo());
        preboxDto.setMaterialCode(detail.getMaterialCode());
        preboxDto.setMaterialName(detail.getMaterialName());
        preboxDto.setInQuantity(detail.getQuantity());
        preboxDto.setMaterialModel(detail.getMaterialModel());
        preboxDto.setMaterialColor(detail.getMaterialColor());
        
        // 设置物料属性
        if (materialInfo != null) {
            preboxDto.setMaterialProperty(materialInfo.getMaterialType());
        } else {
            log.warn("未找到物料 {} 的基本信息，使用默认物料属性", detail.getMaterialCode());
            preboxDto.setMaterialProperty(0); // 默认为自身属性
        }
        
        return preboxDto;
    }

}
