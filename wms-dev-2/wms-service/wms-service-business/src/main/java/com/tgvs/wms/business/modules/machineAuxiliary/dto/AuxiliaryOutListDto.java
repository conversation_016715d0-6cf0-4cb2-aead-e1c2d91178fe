package com.tgvs.wms.business.modules.machineAuxiliary.dto;
import com.baomidou.mybatisplus.annotation.TableId;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutBound;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
@EqualsAndHashCode(callSuper = true)
@Data
public class AuxiliaryOutListDto extends WmsAuxiliaryOutBound implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId
    @ApiModelProperty("出库单ID")
    private String id;

    @ApiModelProperty("物料明细ID")
    private String detailId;

    /**
     * 关联单号(入库单号或出库单号)
     */
    @ApiModelProperty("关联单号(入库单号或出库单号)")
    private String refNumber;

    /**
     * 操作类型(0入库,1出库)
     */
    @ApiModelProperty("操作类型(0入库,1出库)")
    private Integer operationType;

    /**
     * 合约号
     */
    @ApiModelProperty("合约号")
    private String contractNo;

    /**
     * 款号
     */
    @ApiModelProperty("款号")
    private String itemNo;

    /**
     * 物料唯一值
     */
    @ApiModelProperty("物料唯一值")
    private String reqListId;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty("物料名称")
    private String materialName;

    /**
     * 物料颜色
     */
    @ApiModelProperty("物料颜色")
    private String materialColor;

    /**
     * 物料颜色编码
     */
    @ApiModelProperty("物料颜色编码")
    private String materialColorCode;

    /**
     * 物料规格/型号
     */
    @ApiModelProperty("物料规格/型号")
    private String materialModel;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Integer quantity;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String materialUnit;

    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    private Integer priority;
    //物料属性
    private Integer materialType=-1;
    //PO
    private String poNumber;

}
