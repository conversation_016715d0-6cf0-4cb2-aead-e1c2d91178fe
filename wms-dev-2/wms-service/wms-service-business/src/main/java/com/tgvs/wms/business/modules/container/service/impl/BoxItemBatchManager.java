package com.tgvs.wms.business.modules.container.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tgvs.wms.business.modules.container.entity.BoxItem;
import com.tgvs.wms.business.modules.container.entity.Container;
import com.tgvs.wms.business.modules.container.service.IBoxItemService;
import com.tgvs.wms.business.modules.container.service.IContainerService;
import com.tgvs.wms.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * BoxItem 存储批次管理器
 * 解决混合存储场景下的容量检查和状态管理问题
 */
@Component
@Slf4j
public class BoxItemBatchManager {

    @Autowired
    private IBoxItemService boxItemService;
    
    @Autowired
    private IContainerService containerService;

    /**
     * 按存储批次检查容器格子容量
     * 
     * @param containerNo 容器号
     * @param gridId 格子ID
     * @param proposedBatchId 拟使用的批次ID（如果为空则表示新批次）
     * @return 容量检查结果
     */
    public CapacityCheckResult checkGridCapacityByBatch(String containerNo, Integer gridId, String proposedBatchId) {
        log.info("开始按批次检查容器容量: 容器={}, 格子={}, 拟用批次={}", containerNo, gridId, proposedBatchId);
        
        try {
            Container container = getContainerByNo(containerNo);
            if (container == null) {
                return CapacityCheckResult.failure("容器不存在: " + containerNo);
            }
            
            // 多格箱和单格箱采用不同的容量检查策略
            if (container.getBoxContainerType() != null && container.getBoxContainerType() > 1) {
                return checkMultiGridCapacity(container, gridId, proposedBatchId);
            } else {
                return checkSingleGridCapacity(container, gridId, proposedBatchId);
            }
            
        } catch (Exception e) {
            log.error("检查容器容量时发生错误: {}", e.getMessage(), e);
            return CapacityCheckResult.failure("容量检查失败: " + e.getMessage());
        }
    }

    /**
     * 多格箱容量检查
     * 多格箱可以更灵活地分配，每个格子相对独立
     */
    private CapacityCheckResult checkMultiGridCapacity(Container container, Integer gridId, String proposedBatchId) {
        String containerNo = container.getBoxNo();
        
        // 检查指定格子是否超出容器范围
        if (gridId > container.getBoxContainerType()) {
            return CapacityCheckResult.failure("格子ID超出容器范围: " + gridId + " > " + container.getBoxContainerType());
        }
        
        List<String> existingBatches = getExistingStorageBatches(containerNo, gridId);
        
        if (container.getBoxType() == 1) { // 料箱
            if (!existingBatches.isEmpty()) {
                String existingBatch = existingBatches.get(0);
                if (StringUtils.isNotEmpty(proposedBatchId) && proposedBatchId.equals(existingBatch)) {
                    log.info("多格料箱 {} 格子 {} 批次 {} 可以合并", containerNo, gridId, proposedBatchId);
                    return CapacityCheckResult.success("可以合并到现有批次", existingBatch);
                } else {
                    log.info("多格料箱 {} 格子 {} 已被批次 {} 占用，但可尝试其他格子", containerNo, gridId, existingBatch);
                    return CapacityCheckResult.failure("格子已被其他批次占用，建议分配其他格子: " + existingBatch);
                }
            } else {
                log.info("多格料箱 {} 格子 {} 为空，可以使用", containerNo, gridId);
                return CapacityCheckResult.success("格子可用", null);
            }
        } else { // 托盘
            log.info("多格托盘 {} 格子 {} 可以使用，现有批次数: {}", containerNo, gridId, existingBatches.size());
            return CapacityCheckResult.success("托盘格子可用", null);
        }
    }

    /**
     * 单格箱容量检查（原有逻辑）
     * 单格箱必须依赖存储批次来实现混合存储
     */
    private CapacityCheckResult checkSingleGridCapacity(Container container, Integer gridId, String proposedBatchId) {
        String containerNo = container.getBoxNo();
        List<String> existingBatches = getExistingStorageBatches(containerNo, gridId);
        
        if (container.getBoxType() == 1) { // 料箱
            if (!existingBatches.isEmpty()) {
                String existingBatch = existingBatches.get(0);
                if (StringUtils.isNotEmpty(proposedBatchId) && proposedBatchId.equals(existingBatch)) {
                    return CapacityCheckResult.success("可以合并到现有批次", existingBatch);
                } else {
                    return CapacityCheckResult.failure("单格箱已被其他批次占用: " + existingBatch);
                }
            } else {
                return CapacityCheckResult.success("格子可用", null);
            }
        } else { // 托盘
            return CapacityCheckResult.success("托盘格子可用", null);
        }
    }

    /**
     * 为新的存储操作分配格子和批次
     * 
     * @param containerNo 容器号
     * @param materialCode 物料编码
     * @param contractNo 合约号
     * @param itemNo 款号
     * @return 分配结果
     */
    public GridAllocationResult allocateGridAndBatch(String containerNo, String materialCode, String contractNo, String itemNo) {
        log.info("开始分配格子和批次: 容器={}, 物料={}, 合约={}, 款号={}", containerNo, materialCode, contractNo, itemNo);
        
        try {
            // 1. 查询容器信息
            Container container = getContainerByNo(containerNo);
            if (container == null) {
                return GridAllocationResult.failure("容器不存在: " + containerNo);
            }
            
            // 2. 尝试找到可以合并的现有批次
            GridAllocationResult mergeResult = tryMergeWithExistingBatch(containerNo, materialCode, contractNo, itemNo);
            if (mergeResult.isSuccess()) {
                return mergeResult;
            }
            
            // 3. 分配新的格子和批次
            return allocateNewGridAndBatch(container);
            
        } catch (Exception e) {
            log.error("分配格子和批次时发生错误: 容器={}, 错误={}", containerNo, e.getMessage(), e);
            return GridAllocationResult.failure("分配失败: " + e.getMessage());
        }
    }

    /**
     * 智能分配格子和批次（支持多格箱策略）
     * 
     * @param containerNo 容器号
     * @param materialCode 物料编码
     * @param contractNo 合约号
     * @param itemNo 款号
     * @param allocationStrategy 分配策略
     * @return 分配结果
     */
    public GridAllocationResult allocateGridAndBatchSmart(String containerNo, String materialCode, 
            String contractNo, String itemNo, AllocationStrategy allocationStrategy) {
        log.info("开始智能分配格子和批次: 容器={}, 物料={}, 策略={}", containerNo, materialCode, allocationStrategy);
        
        try {
            Container container = getContainerByNo(containerNo);
            if (container == null) {
                return GridAllocationResult.failure("容器不存在: " + containerNo);
            }
            
            // 根据容器类型选择分配策略
            if (container.getBoxContainerType() != null && container.getBoxContainerType() > 1) {
                return allocateForMultiGrid(container, materialCode, contractNo, itemNo, allocationStrategy);
            } else {
                return allocateForSingleGrid(container, materialCode, contractNo, itemNo);
            }
            
        } catch (Exception e) {
            log.error("智能分配格子和批次时发生错误: {}", e.getMessage(), e);
            return GridAllocationResult.failure("分配失败: " + e.getMessage());
        }
    }

    /**
     * 多格箱分配策略
     */
    private GridAllocationResult allocateForMultiGrid(Container container, String materialCode, 
            String contractNo, String itemNo, AllocationStrategy strategy) {
        
        switch (strategy) {
            case INDEPENDENT_GRID:
                return allocateIndependentGrid(container, materialCode, contractNo, itemNo);
            case GROUP_BY_COLOR:
                return allocateByColorGroup(container, materialCode, contractNo, itemNo);
            case GROUP_BY_SIZE:
                return allocateBySizeGroup(container, materialCode, contractNo, itemNo);
            default:
                return allocateIndependentGrid(container, materialCode, contractNo, itemNo);
        }
    }

    /**
     * 独立格子分配（每个SKU独占一个格子）
     */
    private GridAllocationResult allocateIndependentGrid(Container container, String materialCode, 
            String contractNo, String itemNo) {
        
        // 找到第一个空闲格子
        Integer availableGrid = findNextAvailableGrid(container);
        if (availableGrid == null) {
            return GridAllocationResult.failure("容器已满，无可用格子");
        }
        
        // 为每个独立格子生成独立的批次ID
        String newBatchId = generateStorageBatchId(container.getBoxNo(), availableGrid);
        
        log.info("分配独立格子: 容器={}, 格子={}, 批次={}", container.getBoxNo(), availableGrid, newBatchId);
        return GridAllocationResult.success(availableGrid, newBatchId, false);
    }

    /**
     * 按颜色分组分配
     */
    private GridAllocationResult allocateByColorGroup(Container container, String materialCode, 
            String contractNo, String itemNo) {
        // 这里需要从物料信息中获取颜色信息
        // 暂时使用简化逻辑，实际需要查询物料表获取颜色
        
        // 查找相同合约+款号的现有记录，尝试按颜色分组
        List<BoxItem> existingSameContract = boxItemService.list(
            new LambdaQueryWrapper<BoxItem>()
                .eq(BoxItem::getBoxNo, container.getBoxNo())
                .eq(BoxItem::getContractNo, contractNo)
                .eq(BoxItem::getItemNo, itemNo)
                .eq(BoxItem::getDeleteFlag, 0)
        );
        
        if (!existingSameContract.isEmpty()) {
            // 尝试找到相同颜色的记录
            BoxItem sameColorItem = existingSameContract.stream()
                .filter(item -> item.getMaterialCode().equals(materialCode))
                .findFirst()
                .orElse(null);
                
            if (sameColorItem != null && StringUtils.isNotEmpty(sameColorItem.getStorageBatchId())) {
                log.info("找到相同颜色的现有记录，使用相同批次: 容器={}, 格子={}, 批次={}", 
                    container.getBoxNo(), sameColorItem.getGridId(), sameColorItem.getStorageBatchId());
                return GridAllocationResult.success(sameColorItem.getGridId(), sameColorItem.getStorageBatchId(), true);
            }
        }
        
        // 没找到相同颜色的，分配新格子
        return allocateIndependentGrid(container, materialCode, contractNo, itemNo);
    }

    /**
     * 单格箱分配（原有逻辑）
     */
    private GridAllocationResult allocateForSingleGrid(Container container, String materialCode, 
            String contractNo, String itemNo) {
        
        // 尝试合并到现有批次
        GridAllocationResult mergeResult = tryMergeWithExistingBatch(container.getBoxNo(), materialCode, contractNo, itemNo);
        if (mergeResult.isSuccess()) {
            return mergeResult;
        }
        
        // 分配新批次（格子ID固定为1）
        String newBatchId = generateStorageBatchId(container.getBoxNo(), 1);
        log.info("单格箱分配新批次: 容器={}, 格子=1, 批次={}", container.getBoxNo(), newBatchId);
        return GridAllocationResult.success(1, newBatchId, false);
    }

    /**
     * 按尺寸分组分配的占位实现
     */
    private GridAllocationResult allocateBySizeGroup(Container container, String materialCode, 
            String contractNo, String itemNo) {
        // 简化实现，实际应该根据物料尺寸信息进行分组
        return allocateIndependentGrid(container, materialCode, contractNo, itemNo);
    }

    /**
     * 批量更新格子状态
     * 根据格子中的物料数量计算并更新状态
     * 
     * @param containerNo 容器号
     * @param gridId 格子ID
     */
    public void updateGridStatusBatch(String containerNo, Integer gridId) {
        log.info("开始批量更新格子状态: 容器={}, 格子={}", containerNo, gridId);
        
        try {
            // 1. 查询该格子的所有物料记录
            List<BoxItem> itemsInGrid = boxItemService.list(
                new LambdaQueryWrapper<BoxItem>()
                    .eq(BoxItem::getBoxNo, containerNo)
                    .eq(BoxItem::getGridId, gridId)
                    .eq(BoxItem::getDeleteFlag, 0)
            );
            
            if (itemsInGrid.isEmpty()) {
                log.info("格子 {} 中没有物料，跳过状态更新", gridId);
                return;
            }
            
            // 2. 计算格子的总数量和状态
            int totalQuantity = itemsInGrid.stream()
                .mapToInt(item -> item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0)
                .sum();
            
            // 3. 确定格子状态
            Integer newGridStatus = calculateGridStatus(totalQuantity);
            
            // 4. 批量更新同一格子的所有记录
            Date updateTime = new Date();
            String updateBy = getCurrentUserId();
            
            boolean updateResult = boxItemService.update(
                new LambdaUpdateWrapper<BoxItem>()
                    .eq(BoxItem::getBoxNo, containerNo)
                    .eq(BoxItem::getGridId, gridId)
                    .eq(BoxItem::getDeleteFlag, 0)
                    .set(BoxItem::getGridStatus, newGridStatus)
                    .set(BoxItem::getUpdateTime, updateTime)
                    .set(BoxItem::getUpdateBy, updateBy)
            );
            
            if (updateResult) {
                log.info("批量更新格子状态成功: 容器={}, 格子={}, 新状态={}, 总数量={}, 影响记录数={}", 
                    containerNo, gridId, newGridStatus, totalQuantity, itemsInGrid.size());
            } else {
                log.warn("批量更新格子状态失败: 容器={}, 格子={}", containerNo, gridId);
            }
            
        } catch (Exception e) {
            log.error("批量更新格子状态时发生错误: 容器={}, 格子={}, 错误={}", containerNo, gridId, e.getMessage(), e);
        }
    }

    /**
     * 为新记录设置存储批次ID
     * 在款式拆分等场景下，多个相关记录共享同一个批次ID
     * 
     * @param containerNo 容器号
     * @param gridId 格子ID
     * @param relatedRecordIds 相关记录ID列表
     * @return 生成的批次ID
     */
    public String assignStorageBatchId(String containerNo, Integer gridId, List<String> relatedRecordIds) {
        // 生成新的存储批次ID
        String batchId = generateStorageBatchId(containerNo, gridId);
        
        log.info("为相关记录分配存储批次ID: 批次={}, 容器={}, 格子={}, 记录数={}", 
            batchId, containerNo, gridId, relatedRecordIds != null ? relatedRecordIds.size() : 0);
        
        try {
            // 更新相关记录的存储批次ID
            if (relatedRecordIds != null && !relatedRecordIds.isEmpty()) {
                boolean updateResult = boxItemService.update(
                    new LambdaUpdateWrapper<BoxItem>()
                        .in(BoxItem::getId, relatedRecordIds)
                        .set(BoxItem::getStorageBatchId, batchId)
                        .set(BoxItem::getUpdateTime, new Date())
                        .set(BoxItem::getUpdateBy, getCurrentUserId())
                );
                
                if (updateResult) {
                    log.info("存储批次ID分配成功: 批次={}, 影响记录数={}", batchId, relatedRecordIds.size());
                } else {
                    log.warn("存储批次ID分配失败: 批次={}", batchId);
                }
            }
            
            return batchId;
            
        } catch (Exception e) {
            log.error("分配存储批次ID时发生错误: 批次={}, 错误={}", batchId, e.getMessage(), e);
            return batchId; // 即使更新失败，也返回生成的批次ID
        }
    }

    // ======== 私有辅助方法 ========

    private Container getContainerByNo(String containerNo) {
        return containerService.getOne(
            new LambdaQueryWrapper<Container>()
                .eq(Container::getBoxNo, containerNo)
                .eq(Container::getDeleteFlag, 0)
        );
    }

    private List<String> getExistingStorageBatches(String containerNo, Integer gridId) {
        return boxItemService.list(
            new LambdaQueryWrapper<BoxItem>()
                .eq(BoxItem::getBoxNo, containerNo)
                .eq(BoxItem::getGridId, gridId)
                .eq(BoxItem::getDeleteFlag, 0)
        ).stream()
        .map(BoxItem::getStorageBatchId)
        .filter(StringUtils::isNotEmpty)
        .distinct()
        .collect(Collectors.toList());
    }

    private GridAllocationResult tryMergeWithExistingBatch(String containerNo, String materialCode, String contractNo, String itemNo) {
        // 查找相同物料的现有记录
        List<BoxItem> existingItems = boxItemService.list(
            new LambdaQueryWrapper<BoxItem>()
                .eq(BoxItem::getBoxNo, containerNo)
                .eq(BoxItem::getMaterialCode, materialCode)
                .eq(BoxItem::getContractNo, contractNo)
                .eq(BoxItem::getItemNo, itemNo)
                .eq(BoxItem::getDeleteFlag, 0)
        );
        
        if (!existingItems.isEmpty()) {
            BoxItem firstItem = existingItems.get(0);
            if (StringUtils.isNotEmpty(firstItem.getStorageBatchId())) {
                log.info("找到可合并的现有批次: 容器={}, 批次={}, 格子={}", 
                    containerNo, firstItem.getStorageBatchId(), firstItem.getGridId());
                return GridAllocationResult.success(firstItem.getGridId(), firstItem.getStorageBatchId(), true);
            }
        }
        
        return GridAllocationResult.failure("未找到可合并的批次");
    }

    private GridAllocationResult allocateNewGridAndBatch(Container container) {
        // 找到第一个可用的格子
        Integer availableGrid = findNextAvailableGrid(container);
        if (availableGrid == null) {
            return GridAllocationResult.failure("容器已满，无可用格子");
        }
        
        // 生成新的存储批次ID
        String newBatchId = generateStorageBatchId(container.getBoxNo(), availableGrid);
        
        log.info("分配新格子和批次: 容器={}, 格子={}, 批次={}", container.getBoxNo(), availableGrid, newBatchId);
        return GridAllocationResult.success(availableGrid, newBatchId, false);
    }

    private Integer findNextAvailableGrid(Container container) {
        // 查询容器中已占用的格子
        List<Integer> usedGrids = boxItemService.list(
            new LambdaQueryWrapper<BoxItem>()
                .eq(BoxItem::getBoxNo, container.getBoxNo())
                .eq(BoxItem::getDeleteFlag, 0)
        ).stream()
        .map(BoxItem::getGridId)
        .distinct()
        .collect(Collectors.toList());
        
        // 查找第一个未使用的格子
        int maxGrids = container.getBoxContainerType() != null ? container.getBoxContainerType() : 1;
        for (int i = 1; i <= maxGrids; i++) {
            if (!usedGrids.contains(i)) {
                return i;
            }
        }
        
        return null; // 无可用格子
    }

    private Integer calculateGridStatus(int totalQuantity) {
        if (totalQuantity <= 0) {
            return 0; // 空
        } else if (totalQuantity <= 100) {
            return 1; // 25%
        } else if (totalQuantity <= 500) {
            return 2; // 50%
        } else if (totalQuantity <= 1000) {
            return 3; // 75%
        } else {
            return 4; // 满
        }
    }

    private String generateStorageBatchId(String containerNo, Integer gridId) {
        return String.format("BATCH_%s_G%d_%d", containerNo, gridId, System.currentTimeMillis());
    }

    private String getCurrentUserId() {
        try {
            String userId = com.tgvs.wms.common.util.ShiroUtils.getUserId();
            return StringUtils.isNotEmpty(userId) ? userId : "system_batch_manager";
        } catch (Exception e) {
            log.warn("获取当前用户ID失败: {}", e.getMessage());
            return "system_batch_manager";
        }
    }

    // ======== 内部结果类 ========

    /**
     * 容量检查结果
     */
    public static class CapacityCheckResult {
        private final boolean success;
        private final String message;
        private final String existingBatchId;

        private CapacityCheckResult(boolean success, String message, String existingBatchId) {
            this.success = success;
            this.message = message;
            this.existingBatchId = existingBatchId;
        }

        public static CapacityCheckResult success(String message, String existingBatchId) {
            return new CapacityCheckResult(true, message, existingBatchId);
        }

        public static CapacityCheckResult failure(String message) {
            return new CapacityCheckResult(false, message, null);
        }

        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getExistingBatchId() { return existingBatchId; }
    }

    /**
     * 格子分配结果
     */
    public static class GridAllocationResult {
        private final boolean success;
        private final String message;
        private final Integer gridId;
        private final String batchId;
        private final boolean merged;

        private GridAllocationResult(boolean success, String message, Integer gridId, String batchId, boolean merged) {
            this.success = success;
            this.message = message;
            this.gridId = gridId;
            this.batchId = batchId;
            this.merged = merged;
        }

        public static GridAllocationResult success(Integer gridId, String batchId, boolean merged) {
            return new GridAllocationResult(true, "分配成功", gridId, batchId, merged);
        }

        public static GridAllocationResult failure(String message) {
            return new GridAllocationResult(false, message, null, null, false);
        }

        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Integer getGridId() { return gridId; }
        public String getBatchId() { return batchId; }
        public boolean isMerged() { return merged; }
    }

    // 分配策略枚举
    public enum AllocationStrategy {
        INDEPENDENT_GRID("独立格子分配"),
        GROUP_BY_COLOR("按颜色分组"), 
        GROUP_BY_SIZE("按尺寸分组"),
        MIXED_STORAGE("混合存储");
        
        private final String description;
        
        AllocationStrategy(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 