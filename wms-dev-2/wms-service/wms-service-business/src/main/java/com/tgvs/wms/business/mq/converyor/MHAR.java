package com.tgvs.wms.business.mq.converyor;

import com.tgvs.wms.business.mq.base.HEAD;

import lombok.Data;

@Data
public class MHAR {
    private HEAD Header;

    private String Location;

    private String lcid;

    public void setMQ(String str) throws Exception {
        if (null != str && str.length() > 0) {
            String[] mqdata = str.split(";", -1);
            if (null != mqdata && mqdata.length == 6) {
                this.Header.setMQ(str);
                setLocation(mqdata[4]);
                setLcid(mqdata[5]);
            } else {
                throw new Exception("报文格式不正确！1");
            }
        } else {
            throw new Exception("报文格式不正确！2");
        }
    }
}
