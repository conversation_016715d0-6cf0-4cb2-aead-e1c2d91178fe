package com.tgvs.wms.business.modules.machineMaterials.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class MachineMaterialsBoxPrePackingDto {


    /**
     * 容器号
     */
    @Schema(description = "容器号")
    private String boxCode;

    /**
     * 容器类型
     */
    @Schema(description = "容器类型")
    private Integer boxType;

    /**
     * 容器容量
     */
    @Schema(description = "容器容量")
    private Integer trayCapacity;

   List<PreDetailsDto> preDetails;

}

