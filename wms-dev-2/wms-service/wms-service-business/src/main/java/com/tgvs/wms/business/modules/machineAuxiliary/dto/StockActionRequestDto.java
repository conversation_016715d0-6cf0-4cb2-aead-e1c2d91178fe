package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 用于接收库存操作（如校验、确认）请求的 DTO
 */
@Data // Lombok 注解，自动生成 getter, setter, toString, equals, hashCode
@NoArgsConstructor // Lombok 注解，生成无参构造函数
@AllArgsConstructor // Lombok 注解，生成全参构造函数
public class StockActionRequestDto {

    @Schema(description = "出库单ID列表")
    private List<String> outBoundId;

    private List<String> detailId;

    @Schema(description = "物料类型 (例如用于 confirmStock)")
    private Integer materialType;

    // 可以根据需要添加其他前端可能发送的字段，例如 page, limit
    // 但如果后端逻辑不需要它们，可以不加
    // private Integer page;
    // private Integer limit;

} 