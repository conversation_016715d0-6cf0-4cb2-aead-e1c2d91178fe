package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumDollyType {
    unknow(Integer.valueOf(0), "unknow", "无松布架"),
    empty_null(Integer.valueOf(1), "empty_null", "无板松布架"),
    empty_pallet(Integer.valueOf(2), "empty_pallet", "空板松布架"),
    surplus(Integer.valueOf(3), "surplus", "余布松布架"),
    full(Integer.valueOf(4), "full", "布料松布架");

    private Integer value;

    private String code;

    private String text;

    enumDollyType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumDollyType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumDollyType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumDollyType toEnum(Integer Value) {
        for (enumDollyType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}
