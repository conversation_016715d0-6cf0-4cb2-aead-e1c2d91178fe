package com.tgvs.wms.business.httpservice.api;


import java.util.ArrayList;
import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tgvs.wms.business.enums.enumFactory;
import com.tgvs.wms.business.httpservice.baseBean.agv.resultBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.BackClothScrapBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.BillBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.BookBoxOutBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.BoxOutBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.BoxPackageBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.CallClothBoardBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.CallEmptyBoxBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.ForceAwayDollyBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.GreyClothScanBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.GridWallBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.OutTaskUpdateBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.SetChaceShelfBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.SowWallBean;
import com.tgvs.wms.business.util.Logger;
import com.tgvs.wms.business.wmsservice.server.DpsService;
import com.tgvs.wms.business.wmsservice.server.MesService;
import com.tgvs.wms.common.annotation.AutoLog;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping({"/api/mes"})
public class MesApi {

    @AutoLog(value = "松布区坯布上料扫描", operateType = 2)
    @ApiOperation(value = "松布区坯布上料扫描", notes = "松布区坯布上料扫描")
    @PostMapping({"/GreyClothScan"})
    public resultBean greyClothScan(@RequestBody JSONObject jsonObject) {
        GreyClothScanBean greyClothScanBean = new GreyClothScanBean();
        if (null != jsonObject)
            greyClothScanBean = (GreyClothScanBean)JSON.toJavaObject((JSON)jsonObject, GreyClothScanBean.class);
        Logger.logFile("GreyClothScan:" + JSON.toJSONString(greyClothScanBean));
        if (null == greyClothScanBean || null == greyClothScanBean.getClothNo() || !greyClothScanBean.getClothNo().equals(""));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "GreyClothScan", JSON.toJSONString(greyClothScanBean));
        resultBean result = MesService.greyClothScan(greyClothScanBean);
        Logger.logFile("GreyClothScan:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "GreyClothScan", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "松布工位呼叫尾数松布架搬运", notes = "松布工位呼叫尾数松布架搬运")
    @PostMapping({"/ForceAwayDolly"})
    public resultBean forceAwayDolly(@RequestBody JSONObject jsonObject) {
        ForceAwayDollyBean forceAwayDollyBean = new ForceAwayDollyBean();
        if (null != jsonObject)
            forceAwayDollyBean = (ForceAwayDollyBean)JSON.toJavaObject((JSON)jsonObject, ForceAwayDollyBean.class);
        Logger.logFile("ForceAwayDolly:" + JSON.toJSONString(forceAwayDollyBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "ForceAwayDolly", JSON.toJSONString(forceAwayDollyBean));
        resultBean result = MesService.forceAwayDolly(forceAwayDollyBean);
        MesService.insertMQ("WMS1", "MES", "MES", "0", "ForceAwayDolly", JSON.toJSONString(result));
        Logger.logFile("ForceAwayDolly:" + JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "裁剪工位呼叫坯布", notes = "裁剪工位呼叫坯布")
    @PostMapping({"/CallClothBoard"})
    public resultBean CallClothBoard(@RequestBody JSONObject jsonObject) {
        CallClothBoardBean callClothBoardBean = new CallClothBoardBean();
        if (null != jsonObject)
            callClothBoardBean = (CallClothBoardBean)JSON.toJavaObject((JSON)jsonObject, CallClothBoardBean.class);
        Logger.logFile("CallClothBoard:" + JSON.toJSONString(callClothBoardBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "CallClothBoard", JSON.toJSONString(callClothBoardBean));
        resultBean result = MesService.CallClothBoard(callClothBoardBean);
        MesService.insertMQ("WMS1", "MES", "MES", "0", "CallClothBoard", JSON.toJSONString(result));
        Logger.logFile("CallClothBoard:" + JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "发送周转箱装箱信息", notes = "发送周转箱装箱信息")
    @PostMapping({"/BoxPackage"})
    public resultBean BoxPackage(@RequestBody JSONObject jsonObject) {
        BoxPackageBean boxPackageBean = new BoxPackageBean();
        if (null != jsonObject)
            boxPackageBean = (BoxPackageBean)JSON.toJavaObject((JSON)jsonObject, BoxPackageBean.class);
        Logger.logFile("BoxPackage:" + JSON.toJSONString(boxPackageBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "BoxPackage", JSON.toJSONString(boxPackageBean));
        resultBean result = MesService.BoxPackage(boxPackageBean);
        Logger.logFile("BoxPackage:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "BoxPackage", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "下达周转箱出库", notes = "下达周转箱出库")
    @PostMapping({"/BoxOut"})
    public resultBean BoxOut(@RequestBody JSONObject jsonObject) {
        BoxOutBean boxOutBean = new BoxOutBean();
        if (null != jsonObject)
            boxOutBean = (BoxOutBean)JSON.toJavaObject((JSON)jsonObject, BoxOutBean.class);
        Logger.logFile("BoxOut:" + JSON.toJSONString(boxOutBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "BoxOut", JSON.toJSONString(boxOutBean));
        resultBean result = new resultBean();
        if ("B".equals(enumFactory.two.getCode())) {
            result = MesService.boxOutTwo(boxOutBean);
        } else if ("B".equals(enumFactory.four.getCode())) {
            result = MesService.boxOutFour(boxOutBean);
        }
        Logger.logFile("BoxOut:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "BoxOut", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "下达周转箱出库不排序出库", notes = "下达周转箱出库不排序出库")
    @PostMapping({"/BoxOutNoOrder"})
    public resultBean BoxOutNoOrder(@RequestBody JSONObject jsonObject) {
        BoxOutBean boxOutBean = new BoxOutBean();
        if (null != jsonObject)
            boxOutBean = (BoxOutBean)JSON.toJavaObject((JSON)jsonObject, BoxOutBean.class);
        resultBean result = new resultBean();
        if ("B".equals(enumFactory.four.getCode())) {
            Logger.logFile("BoxOutNoOrder:" + JSON.toJSONString(boxOutBean));
            MesService.insertMQ("MES", "WMS1", "MES", "0", "BoxOut", JSON.toJSONString(boxOutBean));
            result = MesService.boxOutNoOrder(boxOutBean);
            Logger.logFile("BoxOutNoOrder:" + JSON.toJSONString(result));
            MesService.insertMQ("WMS1", "MES", "MES", "0", "BoxOut", JSON.toJSONString(result));
        } else {
            result.setFault();
            result.setErrorinfo("制衣二部没有开放该业务");
            Logger.logFile("BoxOutNoOrder:" + JSON.toJSONString(result));
        }
        return result;
    }

    @ApiOperation(value = "下达指定周转箱出库", notes = "下达指定周转箱出库")
    @PostMapping({"/BookBoxOut"})
    public resultBean BookBoxOut(@RequestBody JSONArray jsonObject) {
        List<BookBoxOutBean> bookBoxOutBeanList = new ArrayList<>();
        if (null != jsonObject) {
            String strJson = jsonObject.toJSONString();
            bookBoxOutBeanList = JSON.parseArray(strJson, BookBoxOutBean.class);
        }
        Logger.logFile("BookBoxOut:" + JSON.toJSONString(bookBoxOutBeanList));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "BookBoxOut", JSON.toJSONString(bookBoxOutBeanList));
        resultBean result = MesService.BookBoxOut(bookBoxOutBeanList);
        Logger.logFile("BookBoxOut:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "BookBoxOut", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "花片播种(四部)", notes = "花片播种(四部)")
    @PostMapping({"/PutWall"})
    public resultBean PutWall(@RequestBody JSONObject jsonObject) {
        SowWallBean sowWallBean = new SowWallBean();
        if (null != jsonObject)
            sowWallBean = (SowWallBean)JSON.toJavaObject((JSON)jsonObject, SowWallBean.class);
        Logger.logFile("PutWall:" + JSON.toJSONString(sowWallBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "PutWall", JSON.toJSONString(sowWallBean));
        resultBean result = DpsService.putWall(sowWallBean);
        Logger.logFile("PutWall:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "PutWall", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "花片对包拣选(四部)", notes = "花片对包拣选(四部)")
    @PostMapping({"/PickWall"})
    public resultBean PickWall(@RequestBody JSONObject jsonObject) {
        SowWallBean sowWallBean = new SowWallBean();
        if (null != jsonObject)
            sowWallBean = (SowWallBean)JSON.toJavaObject((JSON)jsonObject, SowWallBean.class);
        Logger.logFile("PickWall:" + JSON.toJSONString(sowWallBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "PickWall", JSON.toJSONString(sowWallBean));
        resultBean result = DpsService.pickWall(sowWallBean);
        Logger.logFile("PickWall:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "PickWall", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "查询缓存货架配置信息", notes = "查询缓存货架配置信息")
    @PostMapping({"/QueryChaceShelf"})
    public resultBean QueryChaceShelf(@RequestBody JSONObject jsonObject) {
        SetChaceShelfBean setChaceShelfBean = new SetChaceShelfBean();
        if (null != jsonObject)
            setChaceShelfBean = (SetChaceShelfBean)JSON.toJavaObject((JSON)jsonObject, SetChaceShelfBean.class);
        Logger.logFile(JSON.toJSONString(setChaceShelfBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "QueryChaceShelf", JSON.toJSONString(setChaceShelfBean));
        resultBean result = MesService.QueryChaceShelf(setChaceShelfBean);
        return result;
    }

    @ApiOperation(value = "调整缓存货架信息", notes = "调整缓存货架信息")
    @PostMapping({"/SetChaceShelf"})
    public resultBean SetChaceShelf(@RequestBody JSONObject jsonObject) {
        SetChaceShelfBean setChaceShelfBean = new SetChaceShelfBean();
        if (null != jsonObject)
            setChaceShelfBean = (SetChaceShelfBean)JSON.toJavaObject((JSON)jsonObject, SetChaceShelfBean.class);
        Logger.logFile(JSON.toJSONString(setChaceShelfBean));
        resultBean result = new resultBean();
        result.setFault();
        result.setErrorinfo("该功能已经关闭！");
        Logger.logFile(JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "呼叫空箱任务", notes = "呼叫空箱任务")
    @PostMapping({"/CallEmpty"})
    public resultBean CallEmpty(@RequestBody JSONObject jsonObject) {
        CallEmptyBoxBean callEmptyBoxBean = new CallEmptyBoxBean();
        if (null != jsonObject)
            callEmptyBoxBean = (CallEmptyBoxBean)JSON.toJavaObject((JSON)jsonObject, CallEmptyBoxBean.class);
        Logger.logFile("CallEmpty:" + JSON.toJSONString(callEmptyBoxBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "CallEmpty", JSON.toJSONString(callEmptyBoxBean));
        resultBean result = MesService.CallEmpty(callEmptyBoxBean);
        Logger.logFile("CallEmpty:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "CallEmpty", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "任务暂停和回复", notes = "任务暂停和回复")
    @PostMapping({"/OutTaskUpdate"})
    public resultBean OutTaskUpdate(@RequestBody JSONObject jsonObject) {
        OutTaskUpdateBean outTaskUpdateBean = new OutTaskUpdateBean();
        if (null != jsonObject)
            outTaskUpdateBean = (OutTaskUpdateBean)JSON.toJavaObject((JSON)jsonObject, OutTaskUpdateBean.class);
        Logger.logFile(JSON.toJSONString(outTaskUpdateBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "OutTaskUpdate", JSON.toJSONString(outTaskUpdateBean));
        resultBean result = MesService.OutTaskUpdate(outTaskUpdateBean);
        Logger.logFile(JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "OutTaskUpdate", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "呼叫余布松布架到松布区缓存位", notes = "呼叫余布松布架到松布区缓存位")
    @PostMapping({"/CallScrap"})
    public resultBean CallScrap(@RequestBody JSONObject jsonObject) {
        CallClothBoardBean callClothBoardBean = new CallClothBoardBean();
        if (null != jsonObject)
            callClothBoardBean = (CallClothBoardBean)JSON.toJavaObject((JSON)jsonObject, CallClothBoardBean.class);
        Logger.logFile("CallScrap:" + JSON.toJSONString(callClothBoardBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "CallScrap", JSON.toJSONString(callClothBoardBean));
        resultBean result = MesService.CallScrap(callClothBoardBean);
        Logger.logFile("CallScrap:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "CallScrap", JSON.toJSONString(result));
        return result;
    }

    @AutoLog(value = "松布机呼叫余布托板", operateType = 2)
    @ApiOperation(value = "松布机呼叫余布托板", notes = "松布机呼叫余布托板")
    @PostMapping({"/CallScrapBoard"})
    public resultBean CallScrapBoard(@RequestBody JSONObject jsonObject) {
        CallClothBoardBean callClothBoardBean = new CallClothBoardBean();
        if (null != jsonObject)
            callClothBoardBean = (CallClothBoardBean)JSON.toJavaObject((JSON)jsonObject, CallClothBoardBean.class);
        Logger.logFile("CallScrapBoard:" + JSON.toJSONString(callClothBoardBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "CallScrapBoard", JSON.toJSONString(callClothBoardBean));
        resultBean result = MesService.CallScrapBoard(callClothBoardBean);
        Logger.logFile("CallScrapBoard:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "CallScrapBoard", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "空箱空货位数量查询", notes = "空箱空货位数量查询")
    @PostMapping({"/QueryEmputyShelf"})
    public resultBean QueryEmputyShelf() {
        Logger.logFile("QueryEmputyShelf:MES 空箱空货位数量查询");
        MesService.insertMQ("MES", "WMS1", "MES", "0", "QueryEmputyShelf", "");
        resultBean result = MesService.QueryEmputyShelf();
        return result;
    }

    @AutoLog(value = "初始化对包格口(四部)", operateType = 3)
    @ApiOperation(value = "初始化对包格口(四部)", notes = "初始化对包格口(四部)")
    @PostMapping({"/ClearDpsLocation"})
    public resultBean clearDpsLocation(@RequestBody JSONObject jsonObject) {
        GridWallBean sowWallBean = new GridWallBean();
        if (null != jsonObject)
            sowWallBean = (GridWallBean)JSON.toJavaObject((JSON)jsonObject, GridWallBean.class);
        Logger.logFile("ClearDpsLocation:" + JSON.toJSONString(sowWallBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "ClearDpsLocation", JSON.toJSONString(sowWallBean));
        resultBean result = DpsService.clearDpsLocation(sowWallBean);
        Logger.logFile("ClearDpsLocation:" + JSON.toJSONString(result));
        MesService.insertMQ("WMS1", "MES", "MES", "0", "ClearDpsLocation", JSON.toJSONString(result));
        return result;
    }

    @AutoLog(value = "坯布回收", operateType = 3)
    @ApiOperation(value = "坯布回收", notes = "坯布回收")
    @PostMapping({"/BackClothScrap"})
    public resultBean BackClothScrap(@RequestBody JSONObject jsonObject) {
        BackClothScrapBean callClothBoardBean = new BackClothScrapBean();
        if (null != jsonObject)
            callClothBoardBean = (BackClothScrapBean)JSON.toJavaObject((JSON)jsonObject, BackClothScrapBean.class);
        Logger.logFile("BackClothScrap:" + JSON.toJSONString(callClothBoardBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "BackClothScrap", JSON.toJSONString(callClothBoardBean));
        resultBean result = MesService.BackClothBoard(callClothBoardBean);
        MesService.insertMQ("WMS1", "MES", "MES", "0", "BackClothScrap", JSON.toJSONString(result));
        Logger.logFile("BackClothScrap:" + JSON.toJSONString(result));
        return result;
    }

    @AutoLog(value = "拉布单调整优先级", operateType = 3)
    @ApiOperation(value = "拉布单调整优先级", notes = "拉布单调整优先级")
    @PostMapping({"/UpdateBill"})
    public resultBean updateBill(@RequestBody JSONObject jsonObject) {
        BillBean billBean = new BillBean();
        if (null != jsonObject)
            billBean = (BillBean) JSON.toJavaObject((JSON)jsonObject, BillBean.class);
        Logger.logFile("UpdateBill:" + JSON.toJSONString(billBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "UpdateBill", JSON.toJSONString(billBean));
        resultBean result = MesService.updateBill(billBean);
        MesService.insertMQ("WMS1", "MES", "MES", "0", "UpdateBill", JSON.toJSONString(result));
        Logger.logFile("UpdateBill:" + JSON.toJSONString(result));
        return result;
    }

    @AutoLog(value = "拉布单调整组别", operateType = 3)
    @ApiOperation(value = "拉布单调整组别", notes = "拉布单调整组别")
    @PostMapping({"/TransferBill"})
    public resultBean transferBill(@RequestBody JSONObject jsonObject) {
        BillBean billBean = new BillBean();
        if (null != jsonObject)
            billBean = (BillBean)JSON.toJavaObject((JSON)jsonObject, BillBean.class);
        Logger.logFile("TransferBill:" + JSON.toJSONString(billBean));
        MesService.insertMQ("MES", "WMS1", "MES", "0", "TransferBill", JSON.toJSONString(billBean));
        resultBean result = MesService.transferBill(billBean);
        MesService.insertMQ("WMS1", "MES", "MES", "0", "TransferBill", JSON.toJSONString(result));
        Logger.logFile("TransferBill:" + JSON.toJSONString(result));
        return result;
    }
}
