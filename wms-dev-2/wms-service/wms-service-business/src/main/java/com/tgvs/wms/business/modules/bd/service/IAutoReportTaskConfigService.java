package com.tgvs.wms.business.modules.bd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tgvs.wms.business.modules.bd.entity.AutoReportTaskConfig;

/**
 * 自动上报任务配置服务接口
 */
public interface IAutoReportTaskConfigService extends IService<AutoReportTaskConfig> {
    
    /**
     * 根据任务类型获取配置
     * @param taskType 任务类型
     * @return 任务配置
     */
    AutoReportTaskConfig getByTaskType(String taskType);
    
    /**
     * 保存配置
     * @param config 配置信息
     * @return 是否成功
     */
    boolean saveConfig(AutoReportTaskConfig config);
    
    /**
     * 更新配置状态
     * @param id 配置ID
     * @param status 状态（1-启用，0-禁用）
     * @return 是否成功
     */
    boolean updateStatus(String id, String status);
}