package com.tgvs.wms.business.modules.task.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * ROS接口响应结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RosResultDto {

    @JsonProperty("code")
    private String code;
    
    @JsonProperty("msg")
    private String msg;
    
    @JsonProperty("data")
    private String data;
    
    private String url;
    private Long timestamp;
    private Long duration;
    
    public static RosResultDto error(String message, String url) {
        RosResultDto result = new RosResultDto();
        result.setCode("0");
        result.setMsg(message);
        result.setUrl(url);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
    
    public boolean isSuccess() {
        return "1".equals(this.code);
    }
    
    public void setDurationFromStart(long startTime) {
        this.duration = System.currentTimeMillis() - startTime;
    }
}
