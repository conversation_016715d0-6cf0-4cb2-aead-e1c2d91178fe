package com.tgvs.wms.business.wmsservice.server;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.PostConstruct;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.business.enums.enumConfrimType;
import com.tgvs.wms.business.enums.enumLocationStatus;
import com.tgvs.wms.business.enums.enumLockedStatus;
import com.tgvs.wms.business.enums.enumPointType;
import com.tgvs.wms.business.httpservice.baseBean.agv.resultBean;
import com.tgvs.wms.business.httpservice.baseBean.dps.AddrAndMsg;
import com.tgvs.wms.business.httpservice.baseBean.dps.ConfirmBean;
import com.tgvs.wms.business.httpservice.baseBean.dps.ControlBean;
import com.tgvs.wms.business.httpservice.baseBean.dps.CreatControlBean;
import com.tgvs.wms.business.httpservice.baseBean.dps.HeartbeatBean;
import com.tgvs.wms.business.httpservice.baseBean.dps.LightBean;
import com.tgvs.wms.business.httpservice.baseBean.dps.ModeInfo;
import com.tgvs.wms.business.httpservice.baseBean.dps.SendLightBean;
import com.tgvs.wms.business.httpservice.baseBean.dps.SendModuleOFF;
import com.tgvs.wms.business.httpservice.baseBean.mes.GridWallBean;
import com.tgvs.wms.business.httpservice.baseBean.mes.SowWallBean;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.modules.bd.entity.PtlConfig;
import com.tgvs.wms.business.modules.commncation.entity.CommncationConfig;
import com.tgvs.wms.business.modules.commncation.entity.DpsControllerinfo;
import com.tgvs.wms.business.modules.mqlog.entity.MqLog;
import com.tgvs.wms.business.modules.storage.entity.DpsLocation;
import com.tgvs.wms.business.util.Logger;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.util.httpUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DpsService {
    public static final String sysname = "DPS";

    protected static String url;

    protected static String token;

    protected static final String apiCreate = "/Create";

    protected static final String apiConnect = "/Connect";

    protected static final String apiIsConnect = "/IsConnect";

    protected static final String apiDisconnect = "/Disconnect";

    protected static final String apiSendA = "/SendA";

    protected static final String apiSendZ = "/SendZ";

    protected static final String apiSendModuleOFF = "/SendModuleOFF";

    protected static final String apiSendAc = "/SendAc";

    protected static final String apiSendLighting = "/SendLighting";

    protected static final String Keying = "正在向硬件发送命令";

    protected static final String Keyok = "正常";

    protected static final String Keyerror = "异常";

    public static final int control1id = 1;

    protected static String control1address = "************";

    protected static int control1port = 5003;

    protected static String control1Key = "";

    protected static CreatControlBean creatControl1 = new CreatControlBean(1, control1address, control1port, control1Key);

    public static final ControlBean control1 = new ControlBean(1);

    protected static HeartbeatBean control1State = new HeartbeatBean();

    public static final int control2id = 2;

    protected static String control2address = "************";

    protected static int control2port = 5003;

    protected static String control2Key = "";

    protected static CreatControlBean creatControl2 = new CreatControlBean(2, control2address, control2port, control2Key);

    public static final ControlBean control2 = new ControlBean(2);

    protected static HeartbeatBean control2State = new HeartbeatBean();

    protected static SendLightBean light1 = new SendLightBean();

    protected static SendLightBean light2 = new SendLightBean();

    public static Integer collor_put = Integer.valueOf(2);

    public static Integer collor_pick = Integer.valueOf(2);

    public static Integer show_modle = Integer.valueOf(2);

    protected static Thread threaddps;

    protected static boolean controlerWorking = true;

    protected static boolean controler1Working = true;

    protected static boolean controler2Working = true;

    public static Lock lockdps = new ReentrantLock();

    static LinkedBlockingQueue<SendLightBean> listQueue = new LinkedBlockingQueue<>();

    private static ArrayList<CreatControlBean> listControl = new ArrayList<>();

    public static DpsService dpsService;

    @AutoLog("初始化电子标签服务参数")
    @PostConstruct
    public void init() {
        List<DpsControllerinfo> list = MainService.mainServiceutil.dpsControllerinfoMapper.selectList(null);
        if (null != list && list.size() > 0) {
            for (DpsControllerinfo control : list) {
                switch (control.getControllerid().intValue()) {
                    case 1:
                        control1address = control.getIpaddress();
                        control1port = control.getPort().intValue();
                        control1Key = control.getLicense();
                    case 2:
                        control2address = control.getIpaddress();
                        control2port = control.getPort().intValue();
                        control2Key = control.getLicense();
                }
            }
            Logger.logFile("DPS初始化配置信息！");
        }
        List<CommncationConfig> listsys = MainService.mainServiceutil.commncationConfigMapper.selectList(null);
        if (null != listsys && listsys.size() > 0)
            for (CommncationConfig sys : listsys) {
                switch (sys.getSysid()) {
                    case "DPS":
                        url = sys.getUrl();
                        token = sys.getToken();
                }
            }
        List<PtlConfig> listmodle = MainService.mainServiceutil.ptlConfigMapper.selectList(null);
        if (null != listmodle) {
            collor_put = ((PtlConfig)listmodle.get(0)).getColorValue();
            collor_pick = ((PtlConfig)listmodle.get(0)).getColorValue2();
            show_modle = ((PtlConfig)listmodle.get(0)).getShowType();
        }
    }

    public static SendModuleOFF getSendModuleOFF(String site, List<DpsLocation> list) {
        List<String> stringList = new ArrayList<>();
        int controlid = 1;
        if (null != list && list.size() > 0)
            for (DpsLocation location : list) {
                if (location.getSiteCode().equals(site)) {
                    stringList.add(location.getAddress());
                    controlid = location.getControllerid().intValue();
                }
            }
        SendModuleOFF sendModuleOFF = new SendModuleOFF();
        sendModuleOFF.setControllerID(controlid);
        sendModuleOFF.setAddresses(stringList);
        return sendModuleOFF;
    }

    @AutoLog("电子标签服务")
    public void Start() {
        Logger.logFile("-----DPS启动电子标签服务");
        controlerWorking = true;
        controler1Working = true;
        controler2Working = true;
        if (null != creatControl1) {
            creatControl1 = new CreatControlBean(1, control1address, control1port, control1Key);
            listControl.add(creatControl1);
        }
        if (null != creatControl2) {
            creatControl2 = new CreatControlBean(2, control2address, control2port, control2Key);
            listControl.add(creatControl2);
        }
        if (Create(listControl)) {
            Logger.logFile("-----DPS调用创建控制器方法--成功");
            if (Connect(control1));
            if (Connect(control2));
        } else {
            Logger.logFile("!!!!!DPS调用创建控制器方法--失败");
        }
        threaddps = new Thread(new Runnable() {
            public void run() {}
        });
        threaddps.start();
        MainService.updatecommncationServer("DPS", 1);
    }

    public void StartController(int controlid) {
        switch (controlid) {
            case 1:
                controler1Working = true;
                Connect(control1);
                break;
            case 2:
                controler2Working = true;
                Connect(control2);
                break;
        }
    }

    public void Stop() {
        controlerWorking = false;
        controler1Working = false;
        controler2Working = false;
        Disconnect(control1);
        Disconnect(control2);
        MainService.updatecommncationServer("DPS", 0);
    }

    public void StopController(int controlid) {
        switch (controlid) {
            case 1:
                controler1Working = false;
                Disconnect(control1);
                break;
            case 2:
                controler2Working = false;
                Disconnect(control2);
                break;
        }
    }

    public static void result(ConfirmBean result) {
        switch (enumConfrimType.toEnum(Integer.valueOf(result.getMsgType()))) {
            case apiexception:
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.error.getText());
                break;
            case error:
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.error.getText() + ":结果" + "正常");
                break;
            case confrimbutton:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.confrimbutton.getText() + ":结果" + "正常");
                    try {
                        LambdaQueryWrapper<DpsLocation> queryWrapperLight = new LambdaQueryWrapper();
                        queryWrapperLight.eq(DpsLocation::getAddress, result.getReturnAddress());
                        queryWrapperLight.eq(DpsLocation::getControllerid, Integer.valueOf(result.getControllerID()));
                        queryWrapperLight.eq(DpsLocation::getState, enumLocationStatus.occupy.getValue());
                        List<DpsLocation> dpsLocationListLight = MainService.mainServiceutil.dpsLocationMapper.selectList((Wrapper)queryWrapperLight);
                        if (null != dpsLocationListLight && dpsLocationListLight.size() > 0)
                            for (DpsLocation location : dpsLocationListLight) {
                                location.setState(enumLocationStatus.free.getValue());
                                MainService.mainServiceutil.dpsLocationMapper.updateById(location);
                            }
                    } catch (Exception exception) {}
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.confrimbutton.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.confrimbutton.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case confrimscreen:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.confrimscreen.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.confrimscreen.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.confrimscreen.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case sent:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.sent.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.sent.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.sent.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case connected:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.connected.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.connected.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.connected.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case closed:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.closed.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.closed.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.closed.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apicreating:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apicreating.getText() + ":结果" + "正常");
                    Connectbyid(result.getControllerID());
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apicreating.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apicreating.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apideleting:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apideleting.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apideleting.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apideleting.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apiconnecting:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiconnecting.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiconnecting.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiconnecting.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apidisconnecting:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apidisconnecting.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apidisconnecting.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apidisconnecting.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apiisconnected:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiisconnected.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiisconnected.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiisconnected.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apisendA:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendA.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendA.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendA.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apisendZ:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendZ.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendZ.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendZ.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apiclosing:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiclosing.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiclosing.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiclosing.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apiopeningscan:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiopeningscan.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiopeningscan.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiopeningscan.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apiclosingscan:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiclosingscan.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiclosingscan.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apiclosingscan.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
            case apisendinglight:
                if (result.getKey() > 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendinglight.getText() + ":结果" + "正常");
                    break;
                }
                if (result.getKey() == 0) {
                    Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendinglight.getText() + ":结果" + "正在向硬件发送命令");
                    break;
                }
                Logger.logFile("-----控制器：" + result.getControllerID() + "-" + enumConfrimType.apisendinglight.getText() + ":结果" + "异常" + "--" + result.getMsg());
                break;
        }
    }

    public static void update(ConfirmBean result, SendLightBean lightBean) {
        if (null != result &&
                result.getKey() <= 0)
            if (result.getKey() == 0);
    }

    public static void heartBeat(HeartbeatBean heartbeatBean) {
        if (controlerWorking)
            switch (heartbeatBean.getControllerID()) {
                case 1:
                    control1State = heartbeatBean;
                    if (controler1Working)
                        if (control1State.getKey() == -1)
                            Logger.logFile("-----DPS创建控制器控制器实例-" + JSON.toJSONString(heartbeatBean));
                    break;
                case 2:
                    control2State = heartbeatBean;
                    if (controler2Working)
                        if (control2State.getKey() == -1)
                            Logger.logFile("-----DPS创建控制器控制器实例-" + JSON.toJSONString(heartbeatBean));
                    break;
            }
    }

    @AutoLog("创建控制器")
    public static boolean Create(List<CreatControlBean> creatControlBeanList) {
        boolean iboolen = false;
        Logger.logFile("-----DPS:创建控制器控制器实例");
        try {
            iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/Create", JSON.toJSONString(creatControlBeanList), token);
            MesService.insertMQ("WMS1", "DPS", "DPS", "0", "Create", JSON.toJSONString(creatControlBeanList));
        } catch (IOException ioException) {
            Logger.logFile("-----DPS:创建控制器控制器实例异常-" + ioException.getMessage());
        }
        Logger.logFile("-----DPS:创建控制器控制器实例返回" + iboolen);
        return iboolen;
    }

    @AutoLog("连接控制器")
    public static boolean Connect(ControlBean controlBean) {
        boolean iboolen = false;
        Logger.logFile("-----DPS连接控制器设备-" + JSON.toJSONString(controlBean));
        try {
            iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/Connect", JSON.toJSONString(controlBean), token);
            MesService.insertMQ("WMS1", "DPS", "DPS", "0", "Connect", JSON.toJSONString(controlBean));
            if (controlBean.getControllerID() == 1) {
                controler1Working = true;
            } else if (controlBean.getControllerID() == 2) {
                controler2Working = true;
            }
            HeartbeatBean heartbeatBean = new HeartbeatBean();
            heartbeatBean.setControllerID(controlBean.getControllerID());
            heartbeatBean.setKey(0);
            MainService.updateDpsController(heartbeatBean);
        } catch (IOException ioException) {
            Logger.logFile("!!!!!DPS连接控制器失败-" + controlBean.getControllerID() + "-" + ioException.getMessage());
        }
        if (!iboolen) {
            Logger.logFile("-----DPS 重新创建控制器设备");
            Create(listControl);
            try {
                iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/Connect", JSON.toJSONString(controlBean), token);
            } catch (IOException ioException) {
                Logger.logFile("!!!!!DPS重新创建控制器设备异常-" + ioException.getMessage());
            }
        } else {
            Logger.logFile("-----DPS成功连接控制器设备-" + controlBean.getControllerID());
        }
        return iboolen;
    }

    @AutoLog("连接控制器")
    public static void Connectbyid(int controlid) {
        boolean iboolean = false;
        try {
            switch (controlid) {
                case 1:
                    iboolean = httpUtils.doPostOkHttpForDps("DPS", url, "/Connect", JSON.toJSONString(control1), token);
                    if (iboolean) {
                        Logger.logFile("-----DPS连接控制器成功-" + JSON.toJSONString(control1));
                        MesService.insertMQ("WMS1", "DPS", "DPS", "0", "Connect", JSON.toJSONString(control1));
                        break;
                    }
                    Logger.logFile("!!!!!DPS连接控制器失败-" + JSON.toJSONString(control1));
                    break;
                case 2:
                    iboolean = httpUtils.doPostOkHttpForDps("DPS", url, "/Connect", JSON.toJSONString(control2), token);
                    if (iboolean) {
                        Logger.logFile("-----DPS连接控制器成功-" + JSON.toJSONString(control2));
                        MesService.insertMQ("WMS1", "DPS", "DPS", "0", "Connect", JSON.toJSONString(control2));
                        break;
                    }
                    Logger.logFile("!!!!!DPS连接控制器失败-" + JSON.toJSONString(control2));
                    break;
            }
        } catch (IOException ioException) {
            Logger.logFile("!!!!!DPS连接控制器失败-" + controlid + "-" + ioException.getMessage());
        }
    }

    public static boolean Disconnect(ControlBean controlBean) {
        boolean iboolen = false;
        if (controlBean.getControllerID() == 1) {
            controler1Working = false;
        } else if (controlBean.getControllerID() == 2) {
            controler2Working = false;
        }
        Logger.logFile("-----DPS断开控制器设备-" + JSON.toJSONString(controlBean));
        try {
            iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/Disconnect", JSON.toJSONString(controlBean), token);
            MesService.insertMQ("WMS1", "DPS", "DPS", "0", "Disconnect", JSON.toJSONString(controlBean));
            HeartbeatBean heartbeatBean = new HeartbeatBean();
            heartbeatBean.setControllerID(controlBean.getControllerID());
            heartbeatBean.setKey(-1);
            MainService.updateDpsController(heartbeatBean);
        } catch (IOException ioException) {
            Logger.logFile("!!!!!DPS断开控制器设备失败-" + controlBean.getControllerID() + "-" + ioException.getMessage());
        }
        return iboolen;
    }

    public static boolean SendA(ControlBean controlBean) {
        boolean iboolen = false;
        Logger.logFile("-----DPS发送控制器维护命令-" + JSON.toJSONString(controlBean));
        try {
            iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/SendA", JSON.toJSONString(controlBean), token);
            MesService.insertMQ("WMS1", "DPS", "DPS", "0", "SendA", JSON.toJSONString(controlBean));
        } catch (IOException ioException) {
            Logger.logFile("-----DPS发送控制器维护命令失败-" + controlBean.getControllerID() + "-" + ioException.getMessage());
        }
        return iboolen;
    }

    public static boolean SendZ(ControlBean controlBean) {
        boolean iboolen = false;
        Logger.logFile("-----DPS发送控制器初始化命令-" + JSON.toJSONString(controlBean));
        try {
            iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/SendZ", JSON.toJSONString(controlBean), token);
            MesService.insertMQ("WMS1", "DPS", "DPS", "0", "SendZ", JSON.toJSONString(controlBean));
        } catch (IOException ioException) {
            Logger.logFile("-----DPS发送控制器初始化命令失败-" + controlBean.getControllerID() + "-" + ioException.getMessage());
        }
        return iboolen;
    }

    public static boolean SendLighting(SendLightBean sendLightBean) {
        boolean iboolen = false;
        Logger.logFile("-----DPS控制器发送亮灯命令");
        try {
            Logger.logFile("-----" + JSON.toJSONString(sendLightBean));
            iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/SendLighting", JSON.toJSONString(sendLightBean), token);
            if (iboolen);
            Logger.logFile("发送亮灯返回：" + iboolen);
            MesService.insertMQ("WMS1", "DPS", "DPS", "0", "SendLighting", JSON.toJSONString(sendLightBean));
        } catch (IOException ioException) {
            Logger.logFile("-----DPS发送亮灯命令失败-" + sendLightBean.getControllerID() + "-" + ioException.getMessage());
        }
        return iboolen;
    }

    public static void setShutlight(SendLightBean sendLightBean) {
        if (null == sendLightBean)
            return;
        try {
            lockdps.lock();
            listQueue.offer(sendLightBean, 5L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            lockdps.unlock();
        }
    }

    public static void SendLightingToApi() {
        Logger.logFile("-----DPS启动发送亮灯指令线程");
        while (controlerWorking) {
            try {
                lockdps.lock();
                SendLightBean light = listQueue.poll(5L, TimeUnit.SECONDS);
                if (null != light)
                    if (!SendLighting(light)) {
                        if (light.getControllerID() == 1) {
                            Connect(control1);
                        } else {
                            Connect(control2);
                        }
                        SendLighting(light);
                    }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                lockdps.unlock();
            }
            try {
                Thread.sleep(30L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public static SendLightBean getSendLightBean(int controlId, List<DpsLocation> dpsLocationList, Integer walltype) {
        SendLightBean sendLightBean = new SendLightBean();
        List<LightBean> lightBeans = new ArrayList<>();
        LightBean lightBean = new LightBean();
        try {
            sendLightBean.setControllerID(controlId);
            if (null != dpsLocationList && dpsLocationList.size() > 0) {
                List<AddrAndMsg> addrAndMsgList = new ArrayList<>();
                for (DpsLocation local : dpsLocationList) {
                    AddrAndMsg addrAndMsg = new AddrAndMsg();
                    addrAndMsg.setAddress(local.getAddress());
                    addrAndMsg.setMessage(local.getLocation());
                    addrAndMsgList.add(addrAndMsg);
                }
                lightBean.setAddrAndMsgList(addrAndMsgList);
                lightBean.setLMDigitNumber(5);
                ModeInfo modeInfo1 = new ModeInfo();
                if (walltype.equals(Integer.valueOf(1))) {
                    modeInfo1.setConfirmColor(collor_put.intValue());
                } else {
                    modeInfo1.setConfirmColor(collor_pick.intValue());
                }
                modeInfo1.setDisplayStyle(show_modle.intValue());
                lightBean.setM1ModeInfo(modeInfo1);
                ModeInfo modeInfo2 = new ModeInfo(2);
                lightBean.setM2ModeInfo(modeInfo2);
            }
            lightBeans.add(lightBean);
            sendLightBean.setLight(lightBeans);
            sendLightBean.setControllerID(controlId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sendLightBean;
    }

    public static void putmq(SendLightBean sendLightBean) {
        if (null == sendLightBean)
            return;
        try {
            lockdps.lock();
            listQueue.offer(sendLightBean, 5L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            lockdps.unlock();
        }
    }

    public static boolean SendModuleOFFAPI(SendModuleOFF moduleOFF) {
        boolean iboolen = false;
        try {
            iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/SendModuleOFF", JSON.toJSONString(moduleOFF), token);
            MesService.insertMQ("WMS1", "DPS", "DPS", "0", "SendModuleOFF", JSON.toJSONString(moduleOFF));
        } catch (IOException ioException) {
            Logger.logFile("-----DPS关灯失败：" + ioException.getMessage());
        }
        if (!iboolen) {
            Logger.logFile("-----DPS关灯失败：" + iboolen);
            if (moduleOFF.getControllerID() == 1) {
                Connect(control1);
            } else {
                Connect(control2);
            }
            try {
                iboolen = httpUtils.doPostOkHttpForDps("DPS", url, "/SendModuleOFF", JSON.toJSONString(moduleOFF), token);
                if (!iboolen) {
                    Logger.logFile("-----DPS二次关灯失败：" + iboolen);
                } else {
                    Logger.logFile("-----DPS关灯返回：" + iboolen);
                }
            } catch (IOException ioException) {
                Logger.logFile("-----DPS二次关灯失败：" + ioException.getMessage());
            }
        } else {
            Logger.logFile("-----DPS关灯返回：" + iboolen);
        }
        return iboolen;
    }

    public static void insertDpsLog(ConfirmBean result) {}

    public static synchronized resultBean putWall(SowWallBean request) {
        resultBean bean = new resultBean();
        if (null != request && null != request.getSiteNo() && !request.getSiteNo().equals("")) {
            LambdaQueryWrapper<Point> queryWrapperPoint = new LambdaQueryWrapper();
            queryWrapperPoint.eq(Point::getPointNo, request.getSiteNo());
            queryWrapperPoint.eq(Point::getPointType, enumPointType.matching.getValue());
            List<Point> poingListLight = MainService.mainServiceutil.pointMapper.selectList((Wrapper)queryWrapperPoint);
            if (null == poingListLight || poingListLight.size() == 0) {
                bean.setFault();
                bean.setErrorinfo("不是有效的对包工位！");
                return bean;
            }
            if (null == request.getPieceNo() || request.getPieceNo().equals("")) {
                bean.setFault();
                bean.setErrorinfo("请指定要播种的花片编码！");
                return bean;
            }
            String priCode = request.getPieceNo().substring(2);
            Logger.logFile(priCode);
            LambdaQueryWrapper<DpsLocation> queryWrapperput = new LambdaQueryWrapper();
            queryWrapperput.eq(DpsLocation::getSiteCode, request.getSiteNo());
            queryWrapperput.likeLeft(DpsLocation::getPieceNo, priCode);
            List<DpsLocation> dpsLocationListput = MainService.mainServiceutil.dpsLocationMapper.selectList((Wrapper)queryWrapperput);
            if (null != dpsLocationListput && dpsLocationListput.size() > 0) {
                Logger.logFile(priCode);
                List<String> listGrid = new ArrayList<>();
                listGrid.add(((DpsLocation)dpsLocationListput.get(0)).getLocation());
                bean.setData(listGrid);
                bean.setSuccess();
                if (((DpsLocation)dpsLocationListput.get(0)).getState().equals(enumLocationStatus.free.getValue())) {
                    LambdaQueryWrapper<DpsLocation> queryWrapperLight = new LambdaQueryWrapper();
                    queryWrapperLight.eq(DpsLocation::getSiteCode, request.getSiteNo());
                    queryWrapperLight.eq(DpsLocation::getState, enumLocationStatus.occupy.getValue());
                    List<DpsLocation> dpsLocationListLight = MainService.mainServiceutil.dpsLocationMapper.selectList((Wrapper)queryWrapperLight);
                    if (null != dpsLocationListLight && dpsLocationListLight.size() > 0) {
                        if (controlerWorking) {
                            SendModuleOFF sendModuleOFF = getSendModuleOFF(request.getSiteNo(), dpsLocationListLight);
                            if (sendModuleOFF.getControllerID() == 1) {
                                if (controler1Working)
                                    SendModuleOFFAPI(sendModuleOFF);
                            } else if (sendModuleOFF.getControllerID() == 2 &&
                                    controler2Working) {
                                SendModuleOFFAPI(sendModuleOFF);
                            }
                        }
                        for (DpsLocation location : dpsLocationListLight) {
                            location.setState(enumLocationStatus.free.getValue());
                            MainService.mainServiceutil.dpsLocationMapper.updateById(location);
                        }
                    }
                    ((DpsLocation)dpsLocationListput.get(0)).setState(enumLocationStatus.occupy.getValue());
                    MainService.mainServiceutil.dpsLocationMapper.updateById(dpsLocationListput.get(0));
                    List<DpsLocation> locations = new ArrayList<>();
                    locations.add(dpsLocationListput.get(0));
                    handlerWall(((DpsLocation)dpsLocationListput.get(0)).getControllerid().intValue(), locations, Integer.valueOf(1));
                }
            } else {
                LambdaQueryWrapper<DpsLocation> queryWrapperLight = new LambdaQueryWrapper();
                queryWrapperLight.eq(DpsLocation::getSiteCode, request.getSiteNo());
                queryWrapperLight.eq(DpsLocation::getState, enumLocationStatus.occupy.getValue());
                List<DpsLocation> dpsLocationListLight = MainService.mainServiceutil.dpsLocationMapper.selectList((Wrapper)queryWrapperLight);
                if (null != dpsLocationListLight && dpsLocationListLight.size() > 0) {
                    if (controlerWorking) {
                        SendModuleOFF sendModuleOFF = getSendModuleOFF(request.getSiteNo(), dpsLocationListLight);
                        if (sendModuleOFF.getControllerID() == 1) {
                            if (controler1Working)
                                SendModuleOFFAPI(sendModuleOFF);
                        } else if (sendModuleOFF.getControllerID() == 2 &&
                                controler2Working) {
                            SendModuleOFFAPI(sendModuleOFF);
                        }
                    }
                    for (DpsLocation location : dpsLocationListLight) {
                        location.setState(enumLocationStatus.free.getValue());
                        MainService.mainServiceutil.dpsLocationMapper.updateById(location);
                    }
                }
                if (null != request.getPieceNo() && !request.getPieceNo().equals("")) {
                    LambdaQueryWrapper<DpsLocation> queryWrapperHave = new LambdaQueryWrapper();
                    queryWrapperHave.eq(DpsLocation::getSiteCode, request.getSiteNo());
                    queryWrapperHave.eq(DpsLocation::getPieceNo, request.getPieceNo());
                    List<DpsLocation> dpsLocationListHave = MainService.mainServiceutil.dpsLocationMapper.selectList((Wrapper)queryWrapperHave);
                    if (null != dpsLocationListHave && dpsLocationListHave.size() > 0) {
                        bean.setFault();
                        bean.setErrorinfo("相同的花片编码不能多次播种");
                    } else {
                        LambdaQueryWrapper<DpsLocation> queryWrapper = new LambdaQueryWrapper();
                        queryWrapper.eq(DpsLocation::getSiteCode, request.getSiteNo());
                        queryWrapper.eq(DpsLocation::getPieceNo, "");
                        queryWrapper.eq(DpsLocation::getLocked, enumLockedStatus.normal.getValue());
                        queryWrapper.eq(DpsLocation::getState, enumLocationStatus.free.getValue());
                        queryWrapper.orderByAsc(Arrays.asList(DpsLocation::getRowNum, DpsLocation::getColumnNumber));
                        List<DpsLocation> dpsLocationList = MainService.mainServiceutil.dpsLocationMapper.selectList(queryWrapper);
                        if (null != dpsLocationList && dpsLocationList.size() > 0) {
                            ((DpsLocation)dpsLocationList.get(0)).setPieceNo(request.getPieceNo());
                            ((DpsLocation)dpsLocationList.get(0)).setUpdateBy(request.getUserNo());
                            ((DpsLocation)dpsLocationList.get(0)).setState(enumLocationStatus.occupy.getValue());
                            MainService.mainServiceutil.dpsLocationMapper.updateById(dpsLocationList.get(0));
                            List<String> listGrid = new ArrayList<>();
                            listGrid.add(((DpsLocation)dpsLocationList.get(0)).getLocation());
                            bean.setSuccess();
                            bean.setData(listGrid);
                            List<DpsLocation> locations = new ArrayList<>();
                            locations.add(dpsLocationList.get(0));
                            handlerWall(((DpsLocation)dpsLocationList.get(0)).getControllerid().intValue(), locations, Integer.valueOf(1));
                            bean.setSuccess();
                        } else {
                            bean.setFault();
                            bean.setErrorinfo("货位已满，没有多余的格口使用了");
                        }
                    }
                } else {
                    bean.setFault();
                    bean.setErrorinfo("花片编码不能空");
                }
            }
        } else {
            bean.setFault();
            bean.setErrorinfo("对包工作站必须指定");
        }
        return bean;
    }

    public static void handlerWall(int controlId, List<DpsLocation> dpsLocationList, Integer walltype) {
        SendLightBean sendLightBean = getSendLightBean(controlId, dpsLocationList, walltype);
        if (controlerWorking)
            if (sendLightBean.getControllerID() == 1) {
                if (controler1Working)
                    if (!SendLighting(sendLightBean)) {
                        Connect(control1);
                        SendLighting(sendLightBean);
                    }
            } else if (sendLightBean.getControllerID() == 2 &&
                    controler2Working) {
                if (!SendLighting(sendLightBean)) {
                    Connect(control2);
                    SendLighting(sendLightBean);
                }
            }
    }

    public static synchronized resultBean pickWall(SowWallBean request) {
        resultBean resultBean = new resultBean();
        if (null != request && null != request.getSiteNo() && !request.getSiteNo().equals("")) {
            if (null != request.getCrossNo() && !request.getCrossNo().equals("")) {
                LambdaQueryWrapper<DpsLocation> queryWrapperLight = new LambdaQueryWrapper();
                queryWrapperLight.eq(DpsLocation::getSiteCode, request.getSiteNo());
                queryWrapperLight.eq(DpsLocation::getState, enumLocationStatus.occupy.getValue());
                List<DpsLocation> dpsLocationListLight = MainService.mainServiceutil.dpsLocationMapper.selectList((Wrapper)queryWrapperLight);
                if (null != dpsLocationListLight && dpsLocationListLight.size() > 0) {
                    if (controlerWorking) {
                        SendModuleOFF sendModuleOFF = getSendModuleOFF(request.getSiteNo(), dpsLocationListLight);
                        if (sendModuleOFF.getControllerID() == 1) {
                            if (controler1Working)
                                SendModuleOFFAPI(sendModuleOFF);
                        } else if (sendModuleOFF.getControllerID() == 2 &&
                                controler2Working) {
                            SendModuleOFFAPI(sendModuleOFF);
                        }
                    }
                    for (DpsLocation location : dpsLocationListLight) {
                        location.setState(enumLocationStatus.free.getValue());
                        MainService.mainServiceutil.dpsLocationMapper.updateById(location);
                    }
                }
                String priCode = request.getCrossNo().substring(2);
                LambdaQueryWrapper<DpsLocation> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(DpsLocation::getSiteCode, request.getSiteNo());
                queryWrapper.likeLeft(DpsLocation::getPieceNo, priCode);
                List<DpsLocation> dpsLocationList = MainService.mainServiceutil.dpsLocationMapper.selectList((Wrapper)queryWrapper);
                if (null != dpsLocationList && dpsLocationList.size() > 0) {
                    List<String> listGrid = new ArrayList<>();
                    for (DpsLocation locate : dpsLocationList) {
                        locate.setPieceNo("");
                        locate.setUpdateBy(request.getUserNo());
                        locate.setState(enumLocationStatus.occupy.getValue());
                        listGrid.add(locate.getLocation());
                        MainService.mainServiceutil.dpsLocationMapper.updateById(locate);
                    }
                    resultBean.setSuccess();
                    resultBean.setData(listGrid);
                    handlerWall(((DpsLocation)dpsLocationList.get(0)).getControllerid().intValue(), dpsLocationList, Integer.valueOf(2));
                } else {
                    resultBean.setFault();
                    resultBean.setErrorinfo("没有匹配的拣选格口");
                }
            } else {
                resultBean.setFault();
                resultBean.setErrorinfo("十字架编码不能空");
            }
        } else {
            resultBean.setFault();
            resultBean.setErrorinfo("对包工作站必须指定");
        }
        return resultBean;
    }

    public static void updateDpsController(HeartbeatBean heartbeatBean) {
        List<DpsControllerinfo> list = MainService.mainServiceutil.dpsControllerinfoMapper.selectList(null);
        if (null != list && list.size() > 0)
            for (DpsControllerinfo info : list) {
                if (info.getControllerid().equals(Integer.valueOf(heartbeatBean.getControllerID()))) {
                    info.setMsg(heartbeatBean.getMsg());
                    info.setMsgtype(Integer.valueOf(heartbeatBean.getMsgType()));
                    info.setReturnkey(Integer.valueOf(heartbeatBean.getKey()));
                    info.setUpdateBy("system");
                    MainService.mainServiceutil.dpsControllerinfoMapper.updateById(info);
                }
            }
    }

    public static resultBean clearDpsLocation(GridWallBean gridWallBean) {
        resultBean result = new resultBean();
        if (null != gridWallBean && null != gridWallBean.getSiteNo() && !gridWallBean.getSiteNo().equals("")) {
            Point point = MainService.getPoint(gridWallBean.getSiteNo());
            if (null == point) {
                result.setFault();
                result.setErrorinfo("指定工位信息不正确！");
            } else if (point.getPointType().equals(enumPointType.matching.getValue())) {
                MainService.mainServiceutil.dpsLocationMapper.clean(gridWallBean.getSiteNo());
                result.setSuccess();
                result.setData("请求操作成功！");
            } else {
                result.setFault();
                result.setErrorinfo("指定工位不是对包站！");
            }
        } else {
            result.setFault();
            result.setErrorinfo("请指定对包站！");
        }
        return result;
    }

    public static void insertMQ(String sender, String reciver, String taskid, String faunctionName, String text) {
        try {
            if (null != faunctionName) {
                Date nowtime = new Date();
                MqLog log = new MqLog();
                log.setSender(sender);
                log.setReceive(reciver);
                log.setType(faunctionName);
                log.setMqNumber(taskid);
                log.setSysid("DPS");
                log.setMsg(text);
                log.setMqTime(nowtime);
                log.setCreateTime(nowtime);
                log.setDevice("DPS");
                MainService.mainServiceutil.mqLogMapper.insert(log);
            }
        } catch (Exception exception) {}
    }
}