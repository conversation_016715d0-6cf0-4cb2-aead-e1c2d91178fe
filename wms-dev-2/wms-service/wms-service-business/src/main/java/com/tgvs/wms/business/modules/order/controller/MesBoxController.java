package com.tgvs.wms.business.modules.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.order.entity.MesBox;
import com.tgvs.wms.business.modules.order.service.MesBoxService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@Api(tags = {"料箱"})
@RestController
@RequestMapping("mes/box")
public class MesBoxController extends BaseController<MesBox, MesBoxService> {
    @ApiOperation(value = "料箱-分页列表查询", notes = "料箱-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<MesBox> pageList = service.pageList(queryModel);
        Result<List<MesBox>> result = Result.ok(pageList.getRecords());
        result.setTotal(pageList.getTotal());
        return result;
    }

    @ApiOperation(value = "料箱-分页列表查询", notes = "料箱-分页列表查询")
    @PostMapping({"/listByBookNo"})
    public Result<?> listByBookNo(String bookNo) {
        List<MesBox> list = this.service.lambdaQuery()
                .eq(MesBox::getBookNo, bookNo)
                .list();
        return Result.OK(list);
    }

    @AutoLog("料箱-添加")
    @ApiOperation(value = "料箱-添加", notes = "料箱-添加")
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody MesBox mesBox) {
        this.service.save(mesBox);
        this.service.orderAdd(mesBox.getBookNo());
        return Result.OK("添加成功！");
    }

    @AutoLog("料箱-编辑")
    @ApiOperation(value = "料箱-编辑", notes = "料箱-编辑")
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody MesBox mesBox) {
        this.service.updateById(mesBox);
        return Result.OK("编辑成功!");
    }

    @AutoLog("料箱-通过id删除")
    @ApiOperation(value = "料箱-通过id删除", notes = "料箱-通过id删除")
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.service.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("料箱-批量删除")
    @ApiOperation(value = "料箱-批量删除", notes = "料箱-批量删除")
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.service.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("料箱-通过id查询")
    @ApiOperation(value = "料箱-通过id查询", notes = "料箱-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        MesBox mesBox = (MesBox) this.service.getById(id);
        if (mesBox == null)
            return Result.error("未找到对应数据");
        return Result.OK(mesBox);
    }
    @AutoLog("料箱-通过boxNo出库")
    @ApiOperation(value = "料箱-通过boxNo出库", notes = "料箱-通过boxNo出库")
    @PostMapping({"/out"})
    public Result<?> out(@RequestBody List<String> boxNoList,String siteNo) {
        boolean out=this.service.out(boxNoList,siteNo);
        if (!out)
            return Result.error("未找到对应数据");
        return Result.OK("出库中");
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, MesBox mesBox) {
        return exportXls(request, mesBox, MesBox.class, "料箱");
    }
    
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, MesBox.class);
    }
}
