package com.tgvs.wms.business.httpservice.baseBean.dps;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class SendModuleOFF {
    @JSONField(name = "Addresses")
    private List<String> Addresses;

    @JSONField(name = "AudioFileName")
    private String AudioFileName;

    @J<PERSON>NField(name = "ControllerID")
    private int ControllerID;

    public void setAddresses(List<String> Addresses) {
        this.Addresses = Addresses;
    }

    public void setAudioFileName(String AudioFileName) {
        this.AudioFileName = AudioFileName;
    }

    public void setControllerID(int ControllerID) {
        this.ControllerID = ControllerID;
    }

    public void setAsyn(boolean Asyn) {
        this.Asyn = Asyn;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof SendModuleOFF))
            return false;
        SendModuleOFF other = (SendModuleOFF)o;
        if (!other.canEqual(this))
            return false;
        List<String> this$Addresses = (List<String>)getAddresses(), other$Addresses = (List<String>)other.getAddresses();
        if ((this$Addresses == null) ? (other$Addresses != null) : !this$Addresses.equals(other$Addresses))
            return false;
        Object this$AudioFileName = getAudioFileName(), other$AudioFileName = other.getAudioFileName();
        return ((this$AudioFileName == null) ? (other$AudioFileName != null) : !this$AudioFileName.equals(other$AudioFileName)) ? false : ((getControllerID() != other.getControllerID()) ? false : (!(isAsyn() != other.isAsyn())));
    }

    protected boolean canEqual(Object other) {
        return other instanceof SendModuleOFF;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        List<String> $Addresses = (List<String>)getAddresses();
        result = result * 59 + (($Addresses == null) ? 43 : $Addresses.hashCode());
        Object $AudioFileName = getAudioFileName();
        result = result * 59 + (($AudioFileName == null) ? 43 : $AudioFileName.hashCode());
        result = result * 59 + getControllerID();
        return result * 59 + (isAsyn() ? 79 : 97);
    }

    public String toString() {
        return "SendModuleOFF(Addresses=" + getAddresses() + ", AudioFileName=" + getAudioFileName() + ", ControllerID=" + getControllerID() + ", Asyn=" + isAsyn() + ")";
    }

    public List<String> getAddresses() {
        return this.Addresses;
    }

    public String getAudioFileName() {
        return this.AudioFileName;
    }

    public int getControllerID() {
        return this.ControllerID;
    }

    @JSONField(name = "Asyn")
    private boolean Asyn = false;

    public boolean isAsyn() {
        return this.Asyn;
    }
}
