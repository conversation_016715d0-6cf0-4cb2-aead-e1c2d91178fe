package com.tgvs.wms.business.modules.machineAuxiliary.exception;

/**
 * 容器资源不足异常
 * 专门用于处理容器不足导致的预装箱失败情况
 */
public class ContainerShortageException extends RuntimeException {

    private String userFriendlyMessage;
    private String technicalDetails;

    public ContainerShortageException(String userFriendlyMessage) {
        super(userFriendlyMessage);
        this.userFriendlyMessage = userFriendlyMessage;
    }

    public ContainerShortageException(String userFriendlyMessage, String technicalDetails) {
        super(userFriendlyMessage);
        this.userFriendlyMessage = userFriendlyMessage;
        this.technicalDetails = technicalDetails;
    }

    public ContainerShortageException(String userFriendlyMessage, Throwable cause) {
        super(userFriendlyMessage, cause);
        this.userFriendlyMessage = userFriendlyMessage;
    }

    public String getUserFriendlyMessage() {
        return userFriendlyMessage;
    }

    public String getTechnicalDetails() {
        return technicalDetails;
    }

    /**
     * 检查是否为容器不足相关的异常
     */
    public static boolean isContainerShortageRelated(Throwable throwable) {
        if (throwable instanceof ContainerShortageException) {
            return true;
        }

        String message = throwable.getMessage();
        if (message != null) {
            return message.contains("容器资源不足") ||
                    message.contains("没有可用的空") ||
                    message.contains("未找到合适的空") ||
                    message.contains("预装箱操作无法完成");
        }

        return false;
    }
}