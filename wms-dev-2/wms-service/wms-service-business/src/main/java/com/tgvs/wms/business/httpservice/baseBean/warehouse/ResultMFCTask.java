package com.tgvs.wms.business.httpservice.baseBean.warehouse;


import com.fasterxml.jackson.annotation.JsonProperty;

public class ResultMFCTask {
    private Integer id;

    private String messageName;

    private String boxId;

    private String wmsId;

    @JsonProperty("type")
    private Integer type;

    private InLocation location;

    @JsonProperty("state")
    private Integer state;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public void setWmsId(String wmsId) {
        this.wmsId = wmsId;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setLocation(InLocation location) {
        this.location = location;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof ResultMFCTask))
            return false;
        ResultMFCTask other = (ResultMFCTask)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        Object this$boxId = getBoxId(), other$boxId = other.getBoxId();
        if ((this$boxId == null) ? (other$boxId != null) : !this$boxId.equals(other$boxId))
            return false;
        Object this$wmsId = getWmsId(), other$wmsId = other.getWmsId();
        if ((this$wmsId == null) ? (other$wmsId != null) : !this$wmsId.equals(other$wmsId))
            return false;
        Object this$type = getType(), other$type = other.getType();
        if ((this$type == null) ? (other$type != null) : !this$type.equals(other$type))
            return false;
        Object this$location = getLocation(), other$location = other.getLocation();
        if ((this$location == null) ? (other$location != null) : !this$location.equals(other$location))
            return false;
        Object this$state = getState(), other$state = other.getState();
        return !((this$state == null) ? (other$state != null) : !this$state.equals(other$state));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ResultMFCTask;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        Object $boxId = getBoxId();
        result = result * 59 + (($boxId == null) ? 43 : $boxId.hashCode());
        Object $wmsId = getWmsId();
        result = result * 59 + (($wmsId == null) ? 43 : $wmsId.hashCode());
        Object $type = getType();
        result = result * 59 + (($type == null) ? 43 : $type.hashCode());
        Object $location = getLocation();
        result = result * 59 + (($location == null) ? 43 : $location.hashCode());
        Object $state = getState();
        return result * 59 + (($state == null) ? 43 : $state.hashCode());
    }

    public String toString() {
        return "ResultMFCTask(id=" + getId() + ", messageName=" + getMessageName() + ", boxId=" + getBoxId() + ", wmsId=" + getWmsId() + ", type=" + getType() + ", location=" + getLocation() + ", state=" + getState() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    public String getMessageName() {
        return this.messageName;
    }

    public String getBoxId() {
        return this.boxId;
    }

    public String getWmsId() {
        return this.wmsId;
    }

    public Integer getType() {
        return this.type;
    }

    public InLocation getLocation() {
        return this.location;
    }

    public Integer getState() {
        return this.state;
    }
}
