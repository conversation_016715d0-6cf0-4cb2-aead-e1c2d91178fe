package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 料箱自身属性DTO
 */
@Data
@Schema(description = "料箱自身属性DTO")
public class BoxPropertyDto {

    //单据id
    @Schema(description = "单据id")
    private String BoundId;

    /**
     * 料箱编号
     */
    @Schema(description = "料箱编号")
    private String boxNo;
    
    /**
     * 容器类型(1-料箱 2-托盘)
     */
    @Schema(description = "容器类型(1-料箱 2-托盘)")
    private Integer containerType;
    
    /**
     * 料箱类型(与容器类型一致，为了兼容)
     */
    @Schema(description = "料箱类型")
    private Integer boxType;
    
    /**
     * 任务状态
     * 0-创建，1-任务已下发，2-执行搬运，3-输送线运输，4-完成
     */
    @Schema(description = "任务状态")
    private Integer taskStatus;
    
    /**
     * 任务编号
     */
    @Schema(description = "任务编号")
    private String taskOrder;

    /**
     * 箱内物料列表
     */
    @Schema(description = "箱内物料列表")
    private List<BoxItemDto> boxItemList;
    
    /**
     * 箱内物料列表（别名，为了兼容不同的调用方式）
     */
    @Schema(description = "箱内物料列表")
    public List<BoxItemDto> getBoxItem() {
        return this.boxItemList;
    }
    
    public void setBoxItem(List<BoxItemDto> boxItem) {
        this.boxItemList = boxItem;
    }
} 