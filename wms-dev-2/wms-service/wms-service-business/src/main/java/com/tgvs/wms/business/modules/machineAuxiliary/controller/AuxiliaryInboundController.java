package com.tgvs.wms.business.modules.machineAuxiliary.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryInBoundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryPreboxDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.QueryAuxiliaryPreboxDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.SelfPropertyInboundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.StyleSplitInboundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInBound;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryInboundService;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryPreboxService;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IStyleSplitService;
import com.tgvs.wms.business.modules.machineMaterials.vo.InMaterialVo;
import com.tgvs.wms.common.annotation.Log;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.enums.BusinessType;
import com.tgvs.wms.common.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 辅料入库管理 控制器
 */
@Slf4j
@Tag(name = "辅料入库管理")
@RestController
@RequestMapping("/auxiliary/inbound")
public class AuxiliaryInboundController extends BaseController<WmsAuxiliaryInBound, IAuxiliaryInboundService> {

    @Autowired
    private IAuxiliaryInboundService auxiliaryInboundService;

    @Autowired
    private IAuxiliaryPreboxService auxiliaryPreboxService;
    @Autowired
    private IStyleSplitService styleSplitService;

    /**
     * 查询辅料入库信息
     *
     * @param queryModel 查询模型（包含分页参数和查询条件）
     * @return 分页或列表数据
     */
    @Operation(summary = "查询辅料入库信息", description = "支持分页和条件查询辅料入库信息")
    @PostMapping("/query")
    public Result<?> queryAuxiliaryInbound(
            @Parameter(description = "查询模型") @RequestBody(required = false) QueryModel queryModel) {
        // 处理默认参数
        if (queryModel == null) {
            queryModel = new QueryModel();
        }

        // 确保searchParams不为空
        if (queryModel.getSearchParams() == null) {
            queryModel.setSearchParams(new HashMap<>());
        }

        IPage<AuxiliaryInBoundDto> page = auxiliaryInboundService.queryPageList(queryModel);

        // 处理空值和空记录，避免字典转换时的空指针异常
        if (page != null && page.getRecords() != null) {
            // 移除空记录
            page.getRecords().removeIf(item -> item == null);

            // 确保每条记录的字段不为null
            page.getRecords().forEach(item -> {
                // 设置默认值，避免空指针异常
                if (item.getMaterialType() == null) {
                    item.setMaterialType(-1);
                }
            });
        }

        Result result = Result.ok(page != null ? page.getRecords() : null);
        result.setTotal(page != null ? page.getTotal() : 0);
        return result;
    }

    /**
     * 处理第三方系统辅料入库Api
     *
     * @param inMaterialVo 入库请求
     * @return 处理结果
     */
    @Operation(summary = "处理辅料入库", description = "处理辅料入库物料信息")
    @Log(title = "辅料入库", businessType = BusinessType.INSERT)
    @PostMapping("/process")
    public Result<Boolean> processInMaterial(
            @Parameter(description = "入库请求") @Valid @RequestBody InMaterialVo inMaterialVo) {
        boolean result = auxiliaryInboundService.processInMaterial(inMaterialVo);
        return Result.ok(result);
    }

    /**
     * wms辅料入库预装箱
     *
     * @param detailIds 辅料明细ID列表
     * @return 处理结果
     */
    @Operation(summary = "wms辅料入库预装箱", description = "wms辅料入库预装箱")
    @Log(title = "wms辅料入库预装箱", businessType = BusinessType.INSERT)
    @PostMapping("/prebox")
    @Transactional
    public Result<String> processInPrebox(
            @Parameter(description = "辅料明细ID列表") @Valid @RequestBody List<String> detailIds) {
        List<AuxiliaryPreboxDto> result = auxiliaryInboundService.processInPrebox(detailIds);
        // 2、调用容器服务，将物料分配到具体的料箱/托盘，进行预装箱操作
        String preboxResult = auxiliaryPreboxService.auxiliaryPrebox(result);
        // 3、更新入库单状态
        if (preboxResult == null || preboxResult.isEmpty()) {
            return Result.error("预装箱失败，未返回有效结果");
        } else {

            // 检查并重试失败的出库任务

            // 更新入库单状态
            LambdaQueryWrapper<WmsAuxiliaryInBound> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WmsAuxiliaryInBound::getInStoreNumber, result.get(0).getStockInNo());
            queryWrapper.eq(WmsAuxiliaryInBound::getDeleteFlag,0);
            queryWrapper.orderByAsc(WmsAuxiliaryInBound::getCreateTime);
            queryWrapper.last("limit 1");
            WmsAuxiliaryInBound inBound = auxiliaryInboundService.getOne(queryWrapper);
            inBound.setStatus(1);
            boolean updateResult = auxiliaryInboundService.updateById(inBound);
            if (!updateResult) {
                return Result.error("预装箱成功，但更新入库单状态失败");
            }
        }
        // 4、返回预装箱结果
        return Result.ok(preboxResult);
    }

    /**
     * 辅料查询预装箱
     * 支持任务状态过滤，只返回输送线上的料箱数据
     *
     * @param queryModel 查询请求
     * @return 处理结果
     */
    @Operation(summary = "辅料查询预装箱", description = "查询预装箱信息，支持任务状态过滤")
    @PostMapping("/queryPrebox")
    public Result<?> queryPrebox(
            @Parameter(description = "查询请求") @Valid @RequestBody QueryModel queryModel) {

        try {
            QueryAuxiliaryPreboxDto result = auxiliaryPreboxService.queryPrebox(queryModel);

            // 设置响应消息
            String responseMessage = result.getStatusMessage() != null ? result.getStatusMessage() : "查询成功";

            return Result.ok(responseMessage, result);

        } catch (Exception e) {
            // 使用外部日志工具或者创建本地logger
            log.error("查询预装箱信息失败: " + e.getMessage());
            return Result.error("查询预装箱信息失败: " + e.getMessage());
        }
    }

    // 绑定自身属性预装箱入库（支持实际入库数量）
    @Operation(summary = "绑定自身属性预装箱入库", description = "绑定自身属性预装箱入库，支持用户输入的实际入库数量")
    @Log(title = "绑定自身属性预装箱入库", businessType = BusinessType.INSERT)
    @PostMapping("/bindPrebox")
    @Transactional
    public Result<String> bindPrebox(
            @Parameter(description = "自身属性绑定入库数据") @Valid @RequestBody List<SelfPropertyInboundDto> inboundItems) {
        try {
            // 参数验证
            if (inboundItems == null || inboundItems.isEmpty()) {
                return Result.error("入库数据不能为空");
            }
            
            // 验证入库数量不能超过原始数量
            for (SelfPropertyInboundDto item : inboundItems) {
                if (item.getActualInboundQty().compareTo(item.getOriginalQuantity()) > 0) {
                    return Result.error(String.format("物料[%s]的入库数量(%s)不能大于待入库数量(%s)", 
                        item.getMaterialCode(), 
                        item.getActualInboundQty(), 
                        item.getOriginalQuantity()));
                }
            }
            
            // 调用业务层处理
            boolean result = auxiliaryInboundService.processInBound(inboundItems);
            
            if (result) {
                return Result.ok("绑定入库成功");
            } else {
                return Result.error("绑定入库失败");
            }
            
        } catch (Exception e) {
            log.error("绑定自身属性预装箱入库失败", e);
            return Result.error("绑定入库失败: " + e.getMessage());
        }
    }


    // 新增：料箱款式拆分绑定入库接口
    @Operation(summary = "料箱款式拆分绑定入库", description = "处理料箱款式属性拆分绑定入库")
    @Log(title = "料箱款式拆分绑定入库", businessType = BusinessType.INSERT)
    @PostMapping("/bindBoxMaterial")
    @Transactional
    public Result<String> bindBoxMaterial(@Valid @RequestBody StyleSplitInboundDto splitData) {
        // 设置容器类型为料箱
        splitData.setContainerType(1);
        return processStyleSplitInbound(splitData, "料箱");
    }

    // 更新：托盘款式拆分绑定入库接口
    @Operation(summary = "托盘款式拆分绑定入库", description = "处理托盘款式属性拆分绑定入库")
    @Log(title = "托盘款式拆分绑定入库", businessType = BusinessType.INSERT)
    @PostMapping("/bindPalletStyleSplit")
    @Transactional
    public Result<String> bindPalletStyleSplit(@Valid @RequestBody StyleSplitInboundDto splitData) {
        // 设置容器类型为托盘
        splitData.setContainerType(2);
        return processStyleSplitInbound(splitData, "托盘");
    }

    /**
     * 通用的款式拆分绑定入库处理方法
     * 
     * @param splitData 拆分入库数据
     * @param containerTypeName 容器类型名称（用于日志）
     * @return 处理结果
     */
    private Result<String> processStyleSplitInbound(StyleSplitInboundDto splitData, String containerTypeName) {
        try {
            // 1. 基础参数验证和日志记录
            log.info("开始处理{}款式拆分: 容器={}, 源数据数量={}", 
                    containerTypeName, splitData.getBoxNo(), 
                    splitData.getSourceDataList() != null ? splitData.getSourceDataList().size() : 0);
            
            // 验证基础参数
            if (StringUtils.isEmpty(splitData.getBoxNo())) {
                log.error("{}款式拆分失败: 容器编号为空", containerTypeName);
                return Result.error("容器编号不能为空");
            }
            
            // 验证sourceDataList不为空
            if (splitData.getSourceDataList() == null || splitData.getSourceDataList().isEmpty()) {
                log.error("{}款式拆分失败: 源数据列表为空, 容器={}", containerTypeName, splitData.getBoxNo());
                return Result.error("源数据列表不能为空");
            }
            
            // 提取源数据ID列表进行初步验证
            List<String> sourcePreboxIds = splitData.getSourceDataList().stream()
                    .map(StyleSplitInboundDto.SourceDataDTO::getSourcePreboxItemId)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            
            if (sourcePreboxIds.isEmpty()) {
                log.error("{}款式拆分失败: 源数据ID列表为空, 容器={}", containerTypeName, splitData.getBoxNo());
                return Result.error("源数据ID列表不能为空");
            }
            
            // 验证拆分数据完整性
            for (StyleSplitInboundDto.SourceDataDTO sourceData : splitData.getSourceDataList()) {
                if (sourceData.getSplitDetails() == null || sourceData.getSplitDetails().isEmpty()) {
                    log.error("{}款式拆分失败: 存在空的拆分明细, 容器={}, 源数据ID={}", 
                            containerTypeName, splitData.getBoxNo(), sourceData.getSourcePreboxItemId());
                    return Result.error("拆分明细不能为空");
                }
                
                // 验证拆分明细数据完整性
                for (StyleSplitInboundDto.SplitDetailDTO detail : sourceData.getSplitDetails()) {
                    if (StringUtils.isEmpty(detail.getPoNo()) || 
                        StringUtils.isEmpty(detail.getMaterialModel()) || 
                        StringUtils.isEmpty(detail.getMaterialColor()) || 
                        detail.getInboundQty() == null || detail.getInboundQty() <= 0) {
                        log.error("{}款式拆分失败: 拆分明细数据不完整, 容器={}, PO={}, 规格={}, 颜色={}, 数量={}", 
                                containerTypeName, splitData.getBoxNo(), detail.getPoNo(), detail.getMaterialModel(), 
                                detail.getMaterialColor(), detail.getInboundQty());
                        return Result.error("拆分明细数据不完整，PO号、规格、颜色和数量都不能为空");
                    }
                }
            }
            
            log.info("{}款式拆分基础验证通过: 容器={}, 有效源数据数量={}", 
                    containerTypeName, splitData.getBoxNo(), sourcePreboxIds.size());
            
            // 2. 调用对应的服务层处理业务逻辑
            Result<?> result;
            if (splitData.getContainerType() == 1) {
                // 料箱类型
                result = auxiliaryInboundService.processBoxStyleInbound(splitData);
            } else if (splitData.getContainerType() == 2) {
                // 托盘类型 - 统一调用服务层方法
                result = auxiliaryInboundService.processPalletStyleInbound(splitData);
            } else {
                log.error("{}款式拆分失败: 不支持的容器类型={}", containerTypeName, splitData.getContainerType());
                return Result.error("不支持的容器类型: " + splitData.getContainerType());
            }
            
            if (!result.isSuccess()) {
                log.error("{}款式拆分绑定入库失败: 容器={}, 错误信息={}", 
                        containerTypeName, splitData.getBoxNo(), result.getMessage());
                return Result.error(containerTypeName + "款式拆分绑定入库失败: " + result.getMessage());
            }
            
            log.info("{}款式拆分绑定入库完成: 容器={}", containerTypeName, splitData.getBoxNo());
            return Result.ok(containerTypeName + "款式拆分绑定入库成功");
            
        } catch (Exception e) {
            log.error("{}款式拆分绑定入库失败: 容器={}, 异常信息={}", 
                    containerTypeName, splitData != null ? splitData.getBoxNo() : "未知", e.getMessage(), e);
            return Result.error(containerTypeName + "款式拆分绑定入库失败: " + e.getMessage());
        }
    }




    


}
