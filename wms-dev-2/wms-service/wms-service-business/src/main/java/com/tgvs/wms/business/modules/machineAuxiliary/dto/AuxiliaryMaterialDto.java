package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 辅物料入库基础信息DTO
 */
@Data
@Schema(description = "辅物料基础信息")
public class AuxiliaryMaterialDto implements Serializable {
    private static final long serialVersionUID = 1L;

    //物料唯一标识
    @Schema(description = "唯一标识")
    @JsonProperty("ROListID")
    private String ROListID;


    @NotBlank(message = "款号不能为空")
    @Schema(description = "款号",required = true)
    private String itemNo;

    @NotBlank(message = "物料编码不能为空")
    @Schema(description = "物料编码", required = true)
    private String materialCode;

    @NotBlank(message = "物料名称不能为空")
    @Schema(description = "物料名称", required = true)
    private String materialName;
    

    @Schema(description = "物料规格")
    private String materialModel;
    
    //@NotBlank(message = "物料颜色不能为空")
    @Schema(description = "物料颜色")
    private String materialColor;

    //物料颜色code
   // @NotBlank(message = "物料颜色编码不能为空")
    @Schema(description = "物料颜色编码")
    private String materialColorCode;
    
    @NotNull(message = "入库数量不能为空")
    @Schema(description = "入库数量")
    private Integer inQuantity;
    @NotBlank(message = "物料单位不能为空")
    @Schema(description = "物料单位", required = true, example = "个")
    private String materialUnit;
    @Schema(description = "备注")
    private String remark;
} 