package com.tgvs.wms.business.modules.machineAuxiliary.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.service.BaseService;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryInBoundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryPreboxDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.SelfPropertyInboundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.StyleSplitInboundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInBound;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.InMaterialCancel;
import com.tgvs.wms.business.modules.machineMaterials.vo.InMaterialVo;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;

import java.util.List;


/**
 * 辅料入库服务接口
 */
public interface IAuxiliaryInboundService extends BaseService<WmsAuxiliaryInBound> {

    /**
     * 处理辅料入库物料信息
     *
     * @param request 入库请求
     * @return 处理结果，true表示成功，false表示失败
     */
    boolean processInMaterial(InMaterialVo request);

    /**
     * 处理辅料入库取消
     *
     * @param request 入库单号
     * @return 处理结果，true表示成功，false表示失败
     */
    boolean processInMaterialCancel(InMaterialCancel request);

    //分页查询
    IPage<AuxiliaryInBoundDto> queryPageList(QueryModel queryModel);

    /**
     * 处理辅料预装箱数据
     * @param detailIds 辅料入库明细ID列表
     * @return 预装箱DTO列表
     */
    List<AuxiliaryPreboxDto> processInPrebox(List<String> detailIds);
    
    /**
     * 处理辅料自身属性入库
     *
     * @param preboxDataIds 预装箱数据ID
     * @return 处理结果，true表示成功，false表示失败
     */
    boolean processInBound(List<SelfPropertyInboundDto> preboxDataIds);

    /**
     * 处理料箱款式属性绑定入库
     *
     * @param splitData 料箱款式拆分入库数据
     * @return 处理结果
     */
    Result<?> processBoxStyleInbound(StyleSplitInboundDto splitData);

    /**
     * 处理托盘款式属性绑定入库
     *
     * @param splitData 托盘款式拆分入库数据
     * @return 处理结果
     */
    Result<?> processPalletStyleInbound(StyleSplitInboundDto splitData);
}