package com.tgvs.wms.business.modules.machineMaterials.vo;


import com.tgvs.wms.business.modules.machineMaterials.dto.MachineMaterialDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 机物料基础信息VO
 */
@Data
@Schema(description = "机物料基础信息请求")
public class MachineMaterialVo extends MachineMaterialDto implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 可以在此处添加VO特有的字段，如查询参数等
} 