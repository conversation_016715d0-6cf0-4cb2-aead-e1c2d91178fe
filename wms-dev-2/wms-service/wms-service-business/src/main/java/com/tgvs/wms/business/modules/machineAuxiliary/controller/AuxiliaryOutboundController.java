package com.tgvs.wms.business.modules.machineAuxiliary.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.*;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutBound;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryOutboundService;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 辅料出库管理 控制器
 */
@Slf4j
@Tag(name = "辅料出库管理")
@RestController
@RequestMapping("/auxiliary/outbound")
public class AuxiliaryOutboundController extends BaseController<WmsAuxiliaryOutBound, IAuxiliaryOutboundService> {

    @Autowired
    private IAuxiliaryOutboundService auxiliaryOutboundService;

    /**
     * 查询辅料出库信息
     *
     * @param queryModel 查询模型（包含分页参数和查询条件）
     * @return 分页或列表数据
     */
    @Operation(summary = "查询辅料出库信息", description = "支持分页和条件查询辅料出库信息")
    @PostMapping("/query")
    public Result<?> queryAuxiliaryOutbound(@RequestBody(required = false) QueryModel queryModel) {
        IPage<AuxiliaryOutListDto> page = auxiliaryOutboundService.queryPageList(queryModel);
        return Result.ok(page);
    }

    /**
     * 验证出库单库存
     * 
     * @param request 包含出库单ID列表和物料类型的请求对象
     * @return 验证结果,包含库存不足和有库存的物料信息
     */
    @Operation(summary = "验证出库单库存", description = "验证出库单的库存是否充足,返回库存不足和有库存的物料信息")
    @PostMapping("/verifyStock")
    public Result<?> verifyStock(@RequestBody StockActionRequestDto request) {
        if (request.getDetailId() == null || request.getDetailId().isEmpty()) {
            return Result.error("出库单ID列表不能为空");
        }
        try {
            StockVerificationResultDto result = auxiliaryOutboundService.verifyStock(request.getDetailId(), request.getMaterialType());
            return Result.ok(result);
        } catch (Exception e) {
            log.error("验证出库单库存失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 确认出库单库存信息
     * 
     * @param request 出库单ID列表
     * @return 确认结果
     */
    @Operation(summary = "确认出库单库存信息")
    @PostMapping("/confirmStock")
    public Result<?> confirmStock(@RequestBody StockActionRequestDto request) {
        List<String> detailId = request.getDetailId();
        Integer materialType = request.getMaterialType();
        if (detailId == null || detailId.isEmpty()) {
            return Result.error("出库单明细ID列表不能为空");
        }
        if (materialType == null) {
            return Result.error("物料类型不能为空");
        }
        try {

            AuxiliaryOutDetailsDto result = auxiliaryOutboundService.confirmStock(detailId, materialType);
            return Result.ok(result);
        } catch (RuntimeException e) {
            log.error("确认出库库存失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 解绑操作 - 确认实际出库
     * 
     * @param outBoundId 出库单ID列表
     * @return 操作结果
     */
    @Operation(summary = "解绑操作-确认实际出库", description = "确认预出库单的实际出库，完成出库流程")
    @PostMapping("/unbind")
    public Result<?> unbindOutbound(@RequestBody List<String> outBoundId) {
        if (outBoundId == null || outBoundId.isEmpty()) {
            return Result.error("出库单ID列表不能为空");
        }

        try {
            boolean success = auxiliaryOutboundService.confirmActualOutbound(outBoundId);
            return success ? Result.ok("解绑成功，出库已确认") : Result.error("解绑失败");
        } catch (Exception e) {
            return Result.error("解绑操作异常: " + e.getMessage());
        }
    }

    /**
     * 剩余物料入库
     * 
     * @param request 入库请求，包含出库单ID和实际出库数量
     * @return 操作结果
     */
    @Operation(summary = "剩余物料入库", description = "处理剩余物料重新入库，调整库存数量")
    @PostMapping("/remainingInbound")
    public Result<?> remainingMaterialInbound(@RequestBody RemainInboundDto request) {
        if (request.getOutBoundId() == null || request.getOutBoundId().isEmpty()) {
            return Result.error("出库单ID列表不能为空");
        }

        try {
            boolean success = auxiliaryOutboundService.processRemainingMaterialInbound(request);
            return success ? Result.ok("剩余物料入库成功") : Result.error("剩余物料入库失败");
        } catch (Exception e) {
            return Result.error("剩余物料入库异常: " + e.getMessage());
        }
    }

    /**
     * 托盘拆零入库
     * 
     * @param request 拆零入库请求
     * @return 操作结果
     */
    @Operation(summary = "托盘拆零入库", description = "将托盘中剩余物料转换为料箱入库，重新分配料箱")
    @PostMapping("/palletBreakInbound")
    public Result<?> palletBreakInbound(@RequestBody BreakInboundDto request) {
        if (request.getOutBoundId() == null || request.getOutBoundId().isEmpty()) {
            return Result.error("出库单ID列表不能为空");
        }

        try {
            boolean success = auxiliaryOutboundService.processPalletBreakInbound(request);
            return success ? Result.ok("托盘拆零入库成功") : Result.error("托盘拆零入库失败");
        } catch (Exception e) {
            return Result.error("托盘拆零入库异常: " + e.getMessage());
        }
    }

    /**
     * 取消预出库
     * 
     * @param outBoundId 出库单ID列表
     * @return 操作结果
     */
    @Operation(summary = "取消预出库", description = "取消预出库，回滚库存状态")
    @PostMapping("/cancelPreOutbound")
    public Result<?> cancelPreOutbound(@RequestBody List<String> outBoundId) {
        if (outBoundId == null || outBoundId.isEmpty()) {
            return Result.error("出库单ID列表不能为空");
        }

        try {
            boolean success = auxiliaryOutboundService.cancelPreOutbound(outBoundId);
            return success ? Result.ok("取消预出库成功") : Result.error("取消预出库失败");
        } catch (Exception e) {
            return Result.error("取消预出库异常: " + e.getMessage());
        }
    }

    /**
     * 查询辅料出库任务
     * 支持任务状态过滤，只返回输送线上的料箱数据
     *
     * @param queryModel 查询请求
     * @return 处理结果
     */
    @Operation(summary = "查询辅料出库任务", description = "查询出库任务信息，支持任务状态过滤")
    @PostMapping("/queryOutTask")
    public Result<?> queryOutTask(@RequestBody(required = false) QueryModel queryModel) {
        try {
            QueryAuxiliaryOutTaskDto result = auxiliaryOutboundService.queryOutTask(queryModel);

            // 设置响应消息
            String responseMessage = result.getStatusMessage() != null ? result.getStatusMessage() : "查询成功";

            return Result.ok(responseMessage, result);

        } catch (Exception e) {
            log.error("查询出库任务信息失败: " + e.getMessage());
            return Result.error("查询出库任务信息失败: " + e.getMessage());
        }
    }
}