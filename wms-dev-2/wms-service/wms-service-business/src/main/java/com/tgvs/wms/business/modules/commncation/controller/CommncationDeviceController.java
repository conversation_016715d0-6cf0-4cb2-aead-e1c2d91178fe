package com.tgvs.wms.business.modules.commncation.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.commncation.entity.CommncationDevice;
import com.tgvs.wms.business.modules.commncation.service.ICommncationDeviceService;
import com.tgvs.wms.business.wmsservice.server.LiftService;
import com.tgvs.wms.business.wmsservice.server.RobotService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"通讯设备配置"})
@RestController
@RequestMapping({"/commncation/commncationDevice"})
@Slf4j
public class CommncationDeviceController extends BaseController<CommncationDevice, ICommncationDeviceService> {

    @Autowired
    private ICommncationDeviceService commncationDeviceService;

    @ApiOperation(value = "通讯设备配置-分页列表查询", notes = "通讯设备配置-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<CommncationDevice> list = commncationDeviceService.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }

    @AutoLog("通讯设备配置-添加")
    @ApiOperation(value = "通讯设备配置-添加", notes = "通讯设备配置-添加")
    @RequiresPermissions({"commncationDevice:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody CommncationDevice commncationDevice) {
        this.commncationDeviceService.save(commncationDevice);
        return Result.OK("添加成功！");
    }

    @AutoLog("通讯设备配置-编辑")
    @ApiOperation(value = "通讯设备配置-编辑", notes = "通讯设备配置-编辑")
    @RequiresPermissions({"commncationDevice:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody CommncationDevice commncationDevice) {
        this.commncationDeviceService.updateById(commncationDevice);
        return Result.OK("编辑成功!");
    }

    @AutoLog("通讯设备配置-通过id删除")
    @ApiOperation(value = "通讯设备配置-通过id删除", notes = "通讯设备配置-通过id删除")
    @RequiresPermissions({"commncationDevice:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestBody String id) {
        this.commncationDeviceService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("通讯设备配置-批量删除")
    @ApiOperation(value = "通讯设备配置-批量删除", notes = "通讯设备配置-批量删除")
    @RequiresPermissions({"commncationDevice:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestBody String ids) {
        this.commncationDeviceService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("通讯设备配置-通过id查询")
    @ApiOperation(value = "通讯设备配置-通过id查询", notes = "通讯设备配置-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        CommncationDevice commncationDevice = (CommncationDevice)this.commncationDeviceService.getById(id);
        if (commncationDevice == null)
            return Result.error("未找到对应数据");
        return Result.OK(commncationDevice);
    }

    @AutoLog(value = "提升机ABB任务管理-手动清除作业", operateType = 3)
    @ApiOperation(value = "提升机ABB任务管理-手动清除作业", notes = "提升机ABB任务管理-手动清除作业")
    @RequiresPermissions({"commncationDevice:clear"})
    @PostMapping({"/clear"})
    public Result<?> clear(@RequestBody String id) {
        CommncationDevice commncationDevice = (CommncationDevice)this.commncationDeviceService.getById(id);
        if (commncationDevice == null)
            return Result.error("未找到对应数据");
        if (commncationDevice.getSysid().equals("LIFT")) {
            LiftService.handClear(commncationDevice);
        } else if (commncationDevice.getSysid().equals("Robot")) {
            RobotService.handClearTask(commncationDevice);
        } else {
            return Result.error("设备类型错误，不能清除任务");
        }
        return Result.OK("手动清除作业成功!");
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, CommncationDevice commncationDevice) {
        return exportXls(request, commncationDevice, CommncationDevice.class, "通讯设备配置");
    }

    @RequiresPermissions({"commncationDevice:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, CommncationDevice.class);
    }
}
