package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 自身属性绑定入库DTO
 * 用于料箱/托盘自身属性绑定入库时传递完整的入库信息
 */
@Data
@Schema(description = "自身属性绑定入库DTO")
public class SelfPropertyInboundDto {

    /**
     * 预装箱记录ID
     */
    @NotBlank(message = "记录ID不能为空")
    @Schema(description = "预装箱记录ID")
    private String id;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String materialName;

    /**
     * 合约号
     */
    @NotBlank(message = "合约号不能为空")
    @Schema(description = "合约号")
    private String contractNo;

    /**
     * 款号
     */
    @NotBlank(message = "款号不能为空")
    @Schema(description = "款号")
    private String itemNo;

    /**
     * 物料规格
     */
    @Schema(description = "物料规格")
    private String materialModel;

    /**
     * 物料颜色
     */
    @Schema(description = "物料颜色")
    private String materialColor;

    /**
     * 原始数量（待入库数量）
     */
    @NotNull(message = "原始数量不能为空")
    @Positive(message = "原始数量必须大于0")
    @Schema(description = "原始数量（待入库数量）")
    private BigDecimal originalQuantity;

    /**
     * 实际入库数量（用户输入）
     */
    @NotNull(message = "实际入库数量不能为空")
    @Positive(message = "实际入库数量必须大于0")
    @Schema(description = "实际入库数量（用户输入）")
    private BigDecimal actualInboundQty;

    /**
     * 单位
     */
    @Schema(description = "单位")
    private String unit;

    /**
     * 格号
     */
    @Schema(description = "格号")
    private String gridId;

    /**
     * 容器编号（料箱号/托盘号）
     */
    @Schema(description = "容器编号")
    private String containerNo;

    /**
     * 容器类型（1-料箱，2-托盘）
     */
    @NotNull(message = "容器类型不能为空")
    @Schema(description = "容器类型（1-料箱，2-托盘）")
    private Integer containerType;

    /**
     * 库位编码
     */
    @Schema(description = "库位编码")
    private String locationCode;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String remark;
} 