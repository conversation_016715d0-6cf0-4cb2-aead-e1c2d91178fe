package com.tgvs.wms.business.modules.history.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.history.entity.PrepareTaskSubHistory;
import com.tgvs.wms.business.modules.history.mapper.PrepareTaskSubHistoryMapper;
import com.tgvs.wms.business.modules.history.service.IPrepareTaskSubHistoryService;

@Service
public class PrepareTaskSubHistoryServiceImpl extends BaseServiceImpl<PrepareTaskSubHistoryMapper, PrepareTaskSubHistory> implements IPrepareTaskSubHistoryService {
}
