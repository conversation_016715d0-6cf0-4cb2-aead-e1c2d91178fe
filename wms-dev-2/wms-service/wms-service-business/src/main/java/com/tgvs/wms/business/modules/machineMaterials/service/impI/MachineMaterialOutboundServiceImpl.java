package com.tgvs.wms.business.modules.machineMaterials.service.impI;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.business.modules.machineMaterials.dto.MachineOutBoundListDto;
import com.tgvs.wms.business.modules.machineMaterials.dto.MachineOutboundDto;
import com.tgvs.wms.business.modules.machineMaterials.entity.*;
import com.tgvs.wms.business.modules.machineMaterials.mapper.WmsMachineOutboundMapper;
import com.tgvs.wms.business.modules.machineMaterials.mapper.WmsMachineOutDetailListMapper;
import com.tgvs.wms.business.modules.machineMaterials.service.IMachineMaterialOutboundService;
import com.tgvs.wms.business.modules.machineMaterials.vo.MachineOutboundDetailVo;
import com.tgvs.wms.business.modules.machineMaterials.vo.MachineOutboundVo;
import com.tgvs.wms.business.util.MachineMaterialOutboundConvert;
import com.tgvs.wms.common.annotation.Log;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.enums.BusinessType;
import com.tgvs.wms.common.query.QueryGenerator;
import com.tgvs.wms.common.util.DateUtils;
import com.tgvs.wms.business.modules.machineMaterials.mapper.MaterialInfoMapper;
import com.tgvs.wms.common.util.ShiroUtils;
import com.tgvs.wms.common.util.bean.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 机物料出库服务实现类
 */
@Slf4j
@Service
public class MachineMaterialOutboundServiceImpl extends ServiceImpl<WmsMachineOutboundMapper, WmsMachineOutbound> implements IMachineMaterialOutboundService {

    @Autowired
    private MaterialInfoMapper wmsMaterialInfoMapper;
    
    @Autowired
    private WmsMachineOutboundMapper wmsMachineOutboundMapper;

    @Autowired
    private WmsMachineOutDetailListMapper wmsMachineOutDetailListMapper;


    /**
    *机物料出库单列表
     */
    @Override
    public IPage<MachineOutBoundListDto> pageList(QueryModel queryModel) {
        // 构建查询条件
        QueryWrapper<WmsMachineOutDetailList> queryWrapper = QueryGenerator.initQueryWrapper(queryModel.getSearchParams());
        // 分页查询
        Page<WmsMachineOutDetailList> page = new Page<>(queryModel.getPage(), queryModel.getLimit());
        IPage<MachineOutBoundListDto> pageDto = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        try {
            // 设置排序
            if (com.tgvs.wms.common.util.StringUtils.isNotEmpty(queryModel.getSortList())) {
                for (QueryModel.SortModel sortModel : queryModel.getSortList()) {
                    if (sortModel.getSortType() == 1) {
                        queryWrapper.orderByAsc(sortModel.getColumn());
                    } else if (sortModel.getSortType() == 2) {
                        queryWrapper.orderByDesc(sortModel.getColumn());
                    }
                }
            } else {
                // 默认按创建时间倒序
                queryWrapper.orderByDesc("create_time");
            }
            queryWrapper.eq("delete_flag",0);

            List<WmsMachineOutDetailList> list = wmsMachineOutDetailListMapper.selectPage(page, queryWrapper).getRecords();
//            List<WmsMachineOutDetailList> list = wmsMachineOutDetailListMapper.selectList(queryWrapper);
            List<MachineOutBoundListDto> dto = list.stream().map(item -> {
                MachineOutBoundListDto machinedto = new MachineOutBoundListDto();
                BeanUtils.copyProperties(item, machinedto);
                return machinedto;
            }).collect(Collectors.toList());
            for (MachineOutBoundListDto item : dto)
            {
                QueryWrapper<WmsMachineInfo> query = new QueryWrapper<>();
                query.eq("material_Code", item.getMaterialCode());
                WmsMachineInfo wmsMachineInfo = wmsMaterialInfoMapper.selectOne(query);
                if(wmsMachineInfo!=null) {
                    QueryWrapper<WmsMachineOutbound> query1 = new QueryWrapper<>();
                    query1.eq("out_store_number", item.getOutStoreNumber());
                    List<WmsMachineOutbound> wmsMachineOutbound = wmsMachineOutboundMapper.selectList(query1);
                    if(!wmsMachineOutbound.isEmpty())
                    {
                        item.setBrand(wmsMachineInfo.getBrand());
                        item.setAssetModel(wmsMachineInfo.getSpecification());
                        item.setAssetClass(wmsMachineInfo.getBigClass());
                        item.setMaterialName(wmsMachineInfo.getMaterialName());
                        item.setType(wmsMachineOutbound.get(0).getType());
                    }
                }else{
                    log.error("找不到机物料"+item.getMaterialCode()+"的基础信息");
                }
            }
            pageDto.setRecords(dto);
            pageDto.setTotal((wmsMachineOutDetailListMapper.selectPage(page, queryWrapper).getTotal()));
        }catch (Exception ex){
            log.error(ex.toString());
        }
        return  pageDto;
    }
    
    @Override
    public List<MachineOutboundDto> selectWmsMachineOutboundList(MachineOutboundVo inputVO) {
        WmsMachineOutbound entity = MachineMaterialOutboundConvert.voToEntity(inputVO);
        List<WmsMachineOutbound> list = wmsMachineOutboundMapper.selectWmsMachineOutboundCustom(entity);
        return MachineMaterialOutboundConvert.entityListToResultVOList(list);
    }
    
    @Override
    public MachineOutboundDto selectWmsMachineOutboundById(String id) {
        WmsMachineOutbound entity = wmsMachineOutboundMapper.selectById(id);
        return MachineMaterialOutboundConvert.entityToResultVO(entity);
    }
    
    @Override
    public MachineOutboundDto selectWmsMachineOutboundByNumber(String outStoreNumber) {
        WmsMachineOutbound entity = wmsMachineOutboundMapper.selectWmsMachineOutboundByNumber(outStoreNumber);
        return MachineMaterialOutboundConvert.entityToResultVO(entity);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "机物料出库", businessType = BusinessType.INSERT)
    public int insertWmsMachineOutbound(MachineOutboundVo inputVO) {
//        WmsMachineOutbound entity = MachineMaterialOutboundConvert.voToEntity(inputVO);
//        entity.setCreateTime(DateUtils.getNowDate());
//        // 如果ID为空，生成UUID
//        if (StringUtils.isEmpty(entity.getId())) {
//            entity.setId(UUID.randomUUID().toString().replaceAll("-", ""));
//        }
//        return wmsMachineOutboundMapper.insert(entity);
        int result=0;
       try {
           if(inputVO.getType()==1)
           {
               //领料出库
               for (MachineOutboundDetailVo item : inputVO.getMaterialList()) {
                   WmsMachineOutDetailList wmsMachineOutDetailList = new WmsMachineOutDetailList();
                   wmsMachineOutDetailList.setOutStoreNumber(inputVO.getOutStoreNumber());
                   wmsMachineOutDetailList.setMaterialCode(item.getMaterialCode());
                   wmsMachineOutDetailList.setOutQuantity(item.getOutQuantity());
                   wmsMachineOutDetailList.setObjectId(item.getObjectId());
                   wmsMachineOutDetailList.setCreateBy("OA");
                   wmsMachineOutDetailList.setCreateTime(DateUtils.getDate());
                   wmsMachineOutDetailListMapper.insert(wmsMachineOutDetailList);
               }
               WmsMachineOutbound entity = new WmsMachineOutbound();
               entity.setOutStoreNumber(inputVO.getOutStoreNumber());
//               entity.setObjectId(inputVO.getObjectId());
               entity.setCreateBy("OA");
               entity.setType(inputVO.getType());
               result= wmsMachineOutboundMapper.insert(entity);

           }else if(inputVO.getType()==2){
               //虚拟出库
               return 0;
           }else if(inputVO.getType()==3){
               //核销出库
               return 0;
           }
       }catch (Exception ex){
           log.error(ex.toString());
           result= 0;
       }
       return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "机物料出库", businessType = BusinessType.UPDATE)
    public int updateWmsMachineOutbound(MachineOutboundVo inputVO) {
        WmsMachineOutbound entity = MachineMaterialOutboundConvert.voToEntity(inputVO);
        entity.setUpdateTime(DateUtils.getNowDate());
        return wmsMachineOutboundMapper.updateById(entity);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "机物料出库", businessType = BusinessType.DELETE)
    public int deleteWmsMachineOutboundByIds(String[] ids) {
        return wmsMachineOutboundMapper.deleteWmsMachineOutboundByIds(ids);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "机物料出库", businessType = BusinessType.DELETE)
    public int deleteWmsMachineOutboundById(String id) {
        String[] ids = {id};
        return this.deleteWmsMachineOutboundByIds(ids);
    }


    /**
     * 根据物料编码查找物料
     */
    private WmsMachineInfo findMaterialByCode(String materialCode) {
        WmsMachineInfo query = new WmsMachineInfo();
        query.setMaterialCode(materialCode);
        List<WmsMachineInfo> list = wmsMaterialInfoMapper.selectWmsMaterialInfoCustom(query);
        return list.isEmpty() ? null : list.get(0);
    }
} 