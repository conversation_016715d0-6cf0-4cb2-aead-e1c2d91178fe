package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class OutBoxInfo {
    @J<PERSON>NField(name = "BoxNo")
    private String BoxNo;

    @J<PERSON>NField(name = "SortNo")
    private Integer SortNo;

    @J<PERSON><PERSON>ield(name = "BoxType")
    private String BoxType;

    @J<PERSON>NField(name = "MainBoxNo")
    private String MainBoxNo;

    public void setBoxNo(String BoxNo) {
        this.BoxNo = BoxNo;
    }

    public void setSortNo(Integer SortNo) {
        this.SortNo = SortNo;
    }

    public void setBoxType(String BoxType) {
        this.BoxType = BoxType;
    }

    public void setMainBoxNo(String MainBoxNo) {
        this.MainBoxNo = MainBoxNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof OutBoxInfo))
            return false;
        OutBoxInfo other = (OutBoxInfo)o;
        if (!other.canEqual(this))
            return false;
        Object this$BoxNo = getBoxNo(), other$BoxNo = other.getBoxNo();
        if ((this$BoxNo == null) ? (other$BoxNo != null) : !this$BoxNo.equals(other$BoxNo))
            return false;
        Object this$SortNo = getSortNo(), other$SortNo = other.getSortNo();
        if ((this$SortNo == null) ? (other$SortNo != null) : !this$SortNo.equals(other$SortNo))
            return false;
        Object this$BoxType = getBoxType(), other$BoxType = other.getBoxType();
        if ((this$BoxType == null) ? (other$BoxType != null) : !this$BoxType.equals(other$BoxType))
            return false;
        Object this$MainBoxNo = getMainBoxNo(), other$MainBoxNo = other.getMainBoxNo();
        return !((this$MainBoxNo == null) ? (other$MainBoxNo != null) : !this$MainBoxNo.equals(other$MainBoxNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof OutBoxInfo;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $BoxNo = getBoxNo();
        result = result * 59 + (($BoxNo == null) ? 43 : $BoxNo.hashCode());
        Object $SortNo = getSortNo();
        result = result * 59 + (($SortNo == null) ? 43 : $SortNo.hashCode());
        Object $BoxType = getBoxType();
        result = result * 59 + (($BoxType == null) ? 43 : $BoxType.hashCode());
        Object $MainBoxNo = getMainBoxNo();
        return result * 59 + (($MainBoxNo == null) ? 43 : $MainBoxNo.hashCode());
    }

    public String toString() {
        return "OutBoxInfo(BoxNo=" + getBoxNo() + ", SortNo=" + getSortNo() + ", BoxType=" + getBoxType() + ", MainBoxNo=" + getMainBoxNo() + ")";
    }

    public String getBoxNo() {
        return this.BoxNo;
    }

    public Integer getSortNo() {
        return this.SortNo;
    }

    public String getBoxType() {
        return this.BoxType;
    }

    public String getMainBoxNo() {
        return this.MainBoxNo;
    }
}
