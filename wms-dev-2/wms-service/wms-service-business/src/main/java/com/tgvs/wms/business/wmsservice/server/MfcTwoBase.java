package com.tgvs.wms.business.wmsservice.server;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;

import com.tgvs.wms.business.httpservice.baseBean.warehouse.MFCSystemStatus;
import com.tgvs.wms.business.httpservice.baseBean.warehouse.OutOrder;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.socket.SocketClient;
import com.tgvs.wms.business.socket.SocketService;

public class MfcTwoBase {
    public static final String sysname1 = "MFC1";

    protected static SocketClient mfcClient1;

    protected static String mfcaddress1 = "************";

    protected static int mfcport1 = 1200;

    public static SocketService socketService1;

    public static int localport1 = 12345;

    public static List<Byte> listbytemfc1 = new ArrayList<>();

    public static List<Byte> listbyteclient1 = new ArrayList<>();

    public static String insite;

    public static Point outsite;

    public static MFCSystemStatus stam_A;

    public static MFCSystemStatus stam_B;

    public static Map<Integer, Map<Integer, Integer>> maplevelsum = new HashMap<>();

    public static ArrayBlockingQueue<OutOrder> out_site = new ArrayBlockingQueue<>(200);

    public static ArrayBlockingQueue<OutOrder> out_site1 = new ArrayBlockingQueue<>(200);

    public static ArrayBlockingQueue<OutOrder> out_site2 = new ArrayBlockingQueue<>(200);

    public static ArrayBlockingQueue<OutOrder> out_site3 = new ArrayBlockingQueue<>(200);

    public static ArrayBlockingQueue<OutOrder> out_site4 = new ArrayBlockingQueue<>(200);
}
