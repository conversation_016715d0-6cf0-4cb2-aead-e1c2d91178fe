package com.tgvs.wms.business.modules.machineAuxiliary.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 辅料领料出库单信息请求DTO
 */
@Data
@Schema(description = "辅料领料出库单信息请求")
public class OutMaterialVo {

    @NotBlank(message = "出库单号不能为空")
    @Schema(description = "出库单号", required = true)
    private String outStoreNumber;
    //出库类型(0领料通知 1采购退货)
    @NotNull(message = "出库类型不能为空")
    @Schema(description = "出库类型(0领料通知 1采购退货)", required = true)
    private Integer taskType;

    //出库优先级，优先级的值越大，出库级别越高
    @Schema(description = "出库优先级", required = true)
    private Integer priority;

    @NotEmpty(message = "合约号集合不能为空")
    @Schema(description = "合约号集合", required = true)
    @Valid
    private List<OutContractNoListVo> contractNoList;

} 