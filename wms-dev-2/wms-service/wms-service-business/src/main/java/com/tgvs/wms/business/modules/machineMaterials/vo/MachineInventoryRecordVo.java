package com.tgvs.wms.business.modules.machineMaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class MachineInventoryRecordVo implements Serializable{

    @Schema(description = "主键Id", required = true)
    private String id;

    @Schema(description = "盘点单号", required = true)
    private String inStoreNumber;

    /**
     * 盘点类型：1.正常盘点；2.财务盘点；3：异常盘点
     */
//    @NotBlank(message = "盘点单类型不能为空")
    @Schema(description = "盘点单类型", required = true)
    private Integer inventoryType;
}
