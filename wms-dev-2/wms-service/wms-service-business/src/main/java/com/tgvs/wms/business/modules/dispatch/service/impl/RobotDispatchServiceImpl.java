package com.tgvs.wms.business.modules.dispatch.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.dispatch.entity.RobotDispatch;
import com.tgvs.wms.business.modules.dispatch.mapper.RobotDispatchMapper;
import com.tgvs.wms.business.modules.dispatch.service.IRobotDispatchService;
import com.tgvs.wms.common.exception.JeecgBootException;
import com.tgvs.wms.common.util.oConvertUtils;

@Service
public class RobotDispatchServiceImpl extends BaseServiceImpl<RobotDispatchMapper, RobotDispatch> implements IRobotDispatchService {

    public void addRobotDispatch(RobotDispatch robotDispatch) {
        if (oConvertUtils.isEmpty(robotDispatch.getPid())) {
            robotDispatch.setPid("0");
        } else {
            RobotDispatch parent = (RobotDispatch)((RobotDispatchMapper)this.baseMapper).selectById(robotDispatch.getPid());
            if (parent != null && !"1".equals(parent.getHasChild())) {
                parent.setHasChild("1");
                ((RobotDispatchMapper)this.baseMapper).updateById(parent);
            }
        }
        ((RobotDispatchMapper)this.baseMapper).insert(robotDispatch);
    }

    public void updateRobotDispatch(RobotDispatch robotDispatch) {
        RobotDispatch entity = (RobotDispatch)getById(robotDispatch.getId());
        if (entity == null)
            throw new JeecgBootException("未找到对应实体");
        String old_pid = entity.getPid();
        String new_pid = robotDispatch.getPid();
        if (!old_pid.equals(new_pid)) {
            updateOldParentNode(old_pid);
            if (oConvertUtils.isEmpty(new_pid))
                robotDispatch.setPid("0");
            if (!"0".equals(robotDispatch.getPid()))
                ((RobotDispatchMapper)this.baseMapper).updateTreeNodeStatus(robotDispatch.getPid(), "1");
        }
        ((RobotDispatchMapper)this.baseMapper).updateById(robotDispatch);
    }

    @Transactional(rollbackFor = {Exception.class})
    public void deleteRobotDispatch(String id) throws JeecgBootException {
        id = queryTreeChildIds(id);
        if (id.indexOf(",") > 0) {
            StringBuffer sb = new StringBuffer();
            String[] idArr = id.split(",");
            for (String idVal : idArr) {
                if (idVal != null) {
                    RobotDispatch robotDispatch = (RobotDispatch)getById(idVal);
                    String pidVal = robotDispatch.getPid();
                    List<RobotDispatch> dataList = ((RobotDispatchMapper)this.baseMapper).selectList((Wrapper)((QueryWrapper)(new QueryWrapper()).eq("pid", pidVal)).notIn("id", Arrays.asList(idArr)));
                    if ((dataList == null || dataList.size() == 0) && !Arrays.<String>asList(idArr).contains(pidVal) &&
                            !sb.toString().contains(pidVal))
                        sb.append(pidVal).append(",");
                }
            }
            ((RobotDispatchMapper)this.baseMapper).deleteBatchIds(Arrays.asList(idArr));
            String[] pidArr = sb.toString().split(",");
            for (String pid : pidArr)
                updateOldParentNode(pid);
        } else {
            RobotDispatch robotDispatch = (RobotDispatch)getById(id);
            if (robotDispatch == null)
                throw new JeecgBootException("未找到对应实体");
            updateOldParentNode(robotDispatch.getPid());
            ((RobotDispatchMapper)this.baseMapper).deleteById(id);
        }
    }

    public List<RobotDispatch> queryTreeListNoPage(QueryWrapper<RobotDispatch> queryWrapper) {
        List<RobotDispatch> dataList = ((RobotDispatchMapper)this.baseMapper).selectList((Wrapper)queryWrapper);
        List<RobotDispatch> mapList = new ArrayList<>();
        for (RobotDispatch data : dataList) {
            String pidVal = data.getPid();
            if (pidVal != null && !"0".equals(pidVal)) {
                RobotDispatch rootVal = getTreeRoot(pidVal);
                if (rootVal != null && !mapList.contains(rootVal))
                    mapList.add(rootVal);
                continue;
            }
            if (!mapList.contains(data))
                mapList.add(data);
        }
        return mapList;
    }

    private void updateOldParentNode(String pid) {
        if (!"0".equals(pid)) {
            Integer count = ((RobotDispatchMapper)this.baseMapper).selectCount((Wrapper)(new QueryWrapper()).eq("pid", pid));
            if (count == null || count.intValue() <= 1)
                ((RobotDispatchMapper)this.baseMapper).updateTreeNodeStatus(pid, "0");
        }
    }

    private RobotDispatch getTreeRoot(String pidVal) {
        RobotDispatch data = (RobotDispatch)((RobotDispatchMapper)this.baseMapper).selectById(pidVal);
        if (data != null && !"0".equals(data.getPid()))
            return getTreeRoot(data.getPid());
        return data;
    }

    private String queryTreeChildIds(String ids) {
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if (pidVal != null &&
                    !sb.toString().contains(pidVal)) {
                if (sb.toString().length() > 0)
                    sb.append(",");
                sb.append(pidVal);
                getTreeChildIds(pidVal, sb);
            }
        }
        return sb.toString();
    }

    private StringBuffer getTreeChildIds(String pidVal, StringBuffer sb) {
        List<RobotDispatch> dataList = ((RobotDispatchMapper)this.baseMapper).selectList((Wrapper)(new QueryWrapper()).eq("pid", pidVal));
        if (dataList != null && dataList.size() > 0)
            for (RobotDispatch tree : dataList) {
                if (!sb.toString().contains(tree.getId()))
                    sb.append(",").append(tree.getId());
                getTreeChildIds(tree.getId(), sb);
            }
        return sb;
    }
}
