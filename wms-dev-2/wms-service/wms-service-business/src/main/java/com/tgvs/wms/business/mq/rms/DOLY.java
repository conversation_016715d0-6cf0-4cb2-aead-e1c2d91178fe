package com.tgvs.wms.business.mq.rms;

import com.tgvs.wms.business.mq.base.HEAD;
import com.tgvs.wms.business.mq.base.TelegramTools;

import lombok.Data;

@Data
public class DOLY {

    private HEAD Header;

    private String siteCode;

    private String dollyNo;

    public DOLY() {
        HEAD header = new HEAD("DOLY");
        header.setMqindex(TelegramTools.getMqNumber());
        setHeader(header);
    }

    public void setMQ(String str) throws Exception {
        if (null != str && str.length() > 0) {
            String[] mqdata = str.split(";", -1);
            if (null != mqdata && mqdata.length == 6) {
                this.Header.setMQ(str);
                setSiteCode(mqdata[4]);
                setDollyNo(mqdata[5]);
            } else {
                throw new Exception("报文格式不正确！");
            }
        } else {
            throw new Exception("报文格式不正确！");
        }
    }

}
