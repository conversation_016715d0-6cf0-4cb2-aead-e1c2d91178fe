package com.tgvs.wms.business.modules.machineMaterials.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 机物料基础信息DTO
 */
@Data
@Schema(description = "机物料基础信息")
public class MachineMaterialDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "物料编码不能为空")
    @Schema(description = "物料编码", required = true, example = "JWL001")
    private String materialCode;

    @NotBlank(message = "物料名称不能为空")
    @Schema(description = "物料名称", required = true, example = "电机")
    private String materialName;

    @NotNull(message = "容积不能为空")
    @Schema(description = "容积（箱/托盘容量）", required = true, example = "10")
    private Integer capacity;

    @Schema(description = "优先料箱类型", example = "1")
    private Integer priorityContainerType;

    /**
     * 大类
     */
    @Schema(description = "大类")
    private String bigClass;

    /**
     * 小类
     */
    @Schema(description = "小类")
    private String smallClass;

    /**
     * 品牌
     */
    @Schema(description = "品牌")
    private String brand;

    /**
     * 规格
     */
    @Schema(description = "规格")
    private String specification;

    /**
     * 单位
     */
    @Schema(description = "单位")
    private String unit;
    
    @Schema(description = "备注")
    private String remark;
} 