package com.tgvs.wms.business.modules.history.controller;


import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.history.entity.PrepareTaskHistory;
import com.tgvs.wms.business.modules.history.entity.PrepareTaskSubHistory;
import com.tgvs.wms.business.modules.history.entity.RobotDispatchHistory;
import com.tgvs.wms.business.modules.history.service.IPrepareTaskHistoryService;
import com.tgvs.wms.business.modules.history.service.IPrepareTaskSubHistoryService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"备货任务明细"})
@RestController
@RequestMapping({"/history/PrepareTaskSub"})
@Slf4j
public class PrepareTaskSubHistoryController extends BaseController<PrepareTaskSubHistory, IPrepareTaskSubHistoryService> {

    @Autowired
    private IPrepareTaskSubHistoryService PrepareTaskSubHistoryService;

    @Autowired
    private IPrepareTaskHistoryService PrepareTaskHistoryService;

    @ApiOperation(value = "备货任务明细-分页列表查询", notes = "备货任务明细-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<PrepareTaskSubHistory> pageList = PrepareTaskSubHistoryService.pageList(queryModel);
        Result result = Result.ok(pageList.getRecords());
        result.setTotal(pageList.getTotal());
        return result;
    }

    @AutoLog("备货任务明细-添加")
    @ApiOperation(value = "备货任务明细-添加", notes = "备货任务明细-添加")
    @RequiresPermissions({"PrepareTaskSubHistory:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody PrepareTaskSubHistory prepareTaskSubHistory) {
        this.PrepareTaskSubHistoryService.save(prepareTaskSubHistory);
        return Result.OK("添加成功！");
    }

    @AutoLog("备货任务明细-编辑")
    @ApiOperation(value = "备货任务明细-编辑", notes = "备货任务明细-编辑")
    @RequiresPermissions({"PrepareTaskSubHistory:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody PrepareTaskSubHistory prepareTaskSubHistory) {
        this.PrepareTaskSubHistoryService.updateById(prepareTaskSubHistory);
        return Result.OK("编辑成功!");
    }

    @AutoLog("备货任务明细-通过id删除")
    @ApiOperation(value = "备货任务明细-通过id删除", notes = "备货任务明细-通过id删除")
    @RequiresPermissions({"PrepareTaskSubHistory:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestBody String id) {
        this.PrepareTaskSubHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("备货任务明细-批量删除")
    @ApiOperation(value = "备货任务明细-批量删除", notes = "备货任务明细-批量删除")
    @RequiresPermissions({"PrepareTaskSubHistory:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestBody String ids) {
        List<PrepareTaskSubHistory> list = this.PrepareTaskSubHistoryService.listByIds(Arrays.asList(ids.split(",")));
        if (null != list && list.size() > 0) {
            this.PrepareTaskSubHistoryService.removeByIds(Arrays.asList(ids.split(",")));
            LambdaQueryWrapper<PrepareTaskHistory> queryWrapperMain = new LambdaQueryWrapper();
            queryWrapperMain.eq(PrepareTaskHistory::getBillNo, ((PrepareTaskSubHistory)list.get(0)).getBillNo());
            List<PrepareTaskHistory> listmain = this.PrepareTaskHistoryService.list((Wrapper)queryWrapperMain);
            if (null != listmain && listmain.size() > 0)
                for (PrepareTaskHistory history : listmain)
                    this.PrepareTaskHistoryService.removeById(history.getId());
            return Result.OK("还原操作成功!");
        }
        return Result.error("还原操作失败");
    }

    @AutoLog("备货任务明细-通过id查询")
    @ApiOperation(value = "备货任务明细-通过id查询", notes = "备货任务明细-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        PrepareTaskSubHistory PrepareTaskSubHistory = (PrepareTaskSubHistory)this.PrepareTaskSubHistoryService.getById(id);
        if (PrepareTaskSubHistory == null)
            return Result.error("未找到对应数据");
        return Result.OK(PrepareTaskSubHistory);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, PrepareTaskSubHistory prepareTaskSubHistory) {
        return exportXls(request, prepareTaskSubHistory, PrepareTaskSubHistory.class, "备货任务明细");
    }

    @RequiresPermissions({"PrepareTaskSubHistory:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, PrepareTaskSubHistory.class);
    }
}