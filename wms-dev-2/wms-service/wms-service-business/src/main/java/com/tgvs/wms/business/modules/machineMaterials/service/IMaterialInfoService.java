package com.tgvs.wms.business.modules.machineMaterials.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInfo;

import java.util.List;

/**
 * 机物料基础信息服务接口
 */
public interface IMaterialInfoService extends IService<WmsMachineInfo> {
    
    /**
     * 分页查询机物料基础信息
     * 
     * @param queryModel 查询参数
     * @return 分页结果
     */
    IPage<WmsMachineInfo> pageList(QueryModel queryModel);
    
    /**
     * 根据条件查询机物料基础信息
     * 
     * @param wmsMachineInfo 查询条件
     * @return 机物料基础信息列表
     */
    List<WmsMachineInfo> selectWmsMaterialInfoList(WmsMachineInfo wmsMachineInfo);
    
    /**
     * 根据ID查询机物料基础信息
     * 
     * @param id 机物料基础信息ID
     * @return 机物料基础信息
     */
    WmsMachineInfo selectWmsMaterialInfoById(String id);
    
    /**
     * 新增机物料基础信息
     * 
     * @param wmsMachineInfo 机物料基础信息
     * @return 结果
     */
    boolean insertWmsMaterialInfo(WmsMachineInfo wmsMachineInfo);
    
    /**
     * 修改机物料基础信息
     * 
     * @param wmsMachineInfo 机物料基础信息
     * @return 结果
     */
    boolean updateWmsMaterialInfo(WmsMachineInfo wmsMachineInfo);
    
    /**
     * 批量删除机物料基础信息
     * 
     * @param ids 需要删除的机物料基础信息ID数组
     * @return 结果
     */
    boolean deleteWmsMaterialInfoByIds(String[] ids);
} 