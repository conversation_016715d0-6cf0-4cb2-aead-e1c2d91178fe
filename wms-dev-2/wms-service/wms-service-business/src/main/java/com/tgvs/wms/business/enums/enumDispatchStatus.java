package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumDispatchStatus {
    create(Integer.valueOf(0), "create", "创建"),
    ready(Integer.valueOf(5), "ready", "就绪"),
    allow(Integer.valueOf(10), "allow", "允许"),
    allot(Integer.valueOf(15), "allot", "分配"),
    run(Integer.valueOf(20), "run", "申请执行"),
    execute(Integer.valueOf(25), "execute", "执行"),
    cancel(Integer.valueOf(30), "cancel", "取消"),
    complete(Integer.valueOf(35), "complete", "完成"),
    error(Integer.valueOf(40), "error", "异常"),
    finish(Integer.valueOf(255), "finish", "结束");

    private Integer value;

    private String code;

    private String text;

    enumDispatchStatus(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumDispatchStatus getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumDispatchStatus val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumDispatchStatus toEnum(Integer Value) {
        for (enumDispatchStatus e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}
