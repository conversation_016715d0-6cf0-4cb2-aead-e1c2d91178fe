package com.tgvs.wms.business.httpservice.api;

import com.tgvs.wms.business.modules.mqlog.entity.MqLog;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.business.httpservice.baseBean.agv.TaskExecutionNoticeRequest;
import com.tgvs.wms.business.httpservice.baseBean.agv.TaskExecutionNoticeResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@RestController
@RequestMapping({"wms/api/robot"})
@Slf4j
public class AgvApi {

    /**
     * 任务执行过程回馈接口
     * 
     * @param request 任务执行过程回馈请求
     * @return 响应结果
     */
    @ApiOperation(value = "任务执行过程回馈接口")
    @PostMapping("/reporter/task")
    public TaskExecutionNoticeResponse taskExecutionNotice(@RequestBody TaskExecutionNoticeRequest request) {
        insertMQ("Hikrobot", "WMS1", null, "Hikrobot","reporterTask", JSON.toJSONString(request));
        // 返回成功响应
        return TaskExecutionNoticeResponse.success(request.getRobotTaskCode());
    }

    
    public static void insertMQ(String sender, String reciver, String sysid, String taskid, String faunctionName, String text) {
        try {
            if (null != faunctionName) {
                Date nowtime = new Date();
                MqLog log = new MqLog();
                log.setSender(sender);
                log.setReceive(reciver);
                log.setType(faunctionName);
                log.setMqNumber(taskid);
                log.setSysid(sysid);
                log.setMsg(text);
                log.setMqTime(nowtime);
                log.setCreateTime(nowtime);
                log.setDevice(sysid);
                MainService.mainServiceutil.mqLogMapper.insert(log);
            }
        } catch (Exception exception) {}
    }
}