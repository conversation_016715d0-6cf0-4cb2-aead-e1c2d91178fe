package com.tgvs.wms.business.util;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import com.tgvs.wms.common.util.DateUtils;

import com.alibaba.fastjson.JSON;

public class Logger {
    private static final String filePath = "D:\\tmp\\wmslog\\";

    /**
     * 确保日志目录存在
     */
    private static void ensureDirectoryExists() {
        File directory = new File(filePath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
    }

    public static void logFile(String s) {
        try {
            // 确保日志目录存在
            ensureDirectoryExists();
            
            FileWriter fw = new FileWriter(filePath + DateUtils.formatDate("yyyyMMdd") + ".log", true);
            BufferedWriter bw = new BufferedWriter(fw);
            String text = DateUtils.formatDate("HH:mm:ss.SSS") + s + "\r\n";
            bw.write(text);
            bw.close();
            fw.close();
            System.out.println(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void logFile(Object object) {
        try {
            // 确保日志目录存在
            ensureDirectoryExists();
            
            FileWriter fw = new FileWriter(filePath + DateUtils.formatDate("yyyyMMdd") + ".log", true);
            BufferedWriter bw = new BufferedWriter(fw);
            String text = DateUtils.formatDate("HH:mm:ss.SSS") + "-----" + JSON.toJSONString(object) + "\r\n";
            bw.write(text);
            bw.close();
            fw.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
