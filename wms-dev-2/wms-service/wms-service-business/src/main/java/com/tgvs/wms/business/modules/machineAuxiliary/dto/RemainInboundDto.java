package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 剩余物料出库回库DTO
 */
@Data
@ApiModel(value = "剩余物料出库回库Dto", description = "处理剩余物料出入库的请求参数")
public class RemainInboundDto {

    @ApiModelProperty(value = "出库单ID列表", required = true)
    private List<String> outBoundId;

    @ApiModelProperty(value = "实际出库数量映射", notes = "key: 物料编码_合约号_款号, value: 实际出库数量")
    private Map<String, Integer> actualOutQuantities;

    @ApiModelProperty(value = "明细数量映射", notes = "key: BoxItem明细ID, value: 实际出库数量")
    private Map<String, Integer> detailQuantities;

    @ApiModelProperty(value = "入库类型", notes = "1-正常入库 2-异常入库")
    private Integer inboundType=7;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "操作人员")
    private String operator;
}