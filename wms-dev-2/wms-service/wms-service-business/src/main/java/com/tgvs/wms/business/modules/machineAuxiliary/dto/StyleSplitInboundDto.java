package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 款式拆分入库数据传输对象
 * 
 * <AUTHOR> System
 * @date 2024
 */
@Data
@Schema(description = "款式拆分入库数据传输对象")
public class StyleSplitInboundDto {
    
    /**
     * 容器编号（料箱号或托盘号）
     */
    @Schema(description = "容器编号")
    private String boxNo;
    
    /**
     * 容器类型（1-料箱 2-托盘）
     */
    @Schema(description = "容器类型(1-料箱 2-托盘)")
    private Integer containerType;
    

    
    /**
     * 容器容量
     */
    @Schema(description = "容器容量")
    private BigDecimal capacity;
    
    /**
     * 入库单ID
     */
    @Schema(description = "入库单ID")
    private String inboundId;
    
    /**
     * 任务类型 (0-采购入库, 1-退货入库, 2-调拨入库, 3-其他入库)
     */
    @Schema(description = "任务类型")
    private Integer taskType;
    
    /**
     * 拆分操作信息
     */
    @Schema(description = "拆分操作信息")
    private SplitOperationDTO splitOperation;
    
    /**
     * 源数据列表（支持多条预装记录拆分）
     */
    @Schema(description = "源数据列表")
    private List<SourceDataDTO> sourceDataList;
    
    /**
     * 汇总信息
     */
    @Schema(description = "汇总信息")
    private SummaryDTO summary;
    
    /**
     * 拆分操作信息
     */
    @Data
    @Schema(description = "拆分操作信息")
    public static class SplitOperationDTO {
        
        @Schema(description = "操作类型")
        private String operationType;
        
        @Schema(description = "拆分时间")
        private String splitTime;
        
        @Schema(description = "操作员ID")
        private String operatorId;

    }
    
    /**
     * 源数据信息
     */
    @Data
    @Schema(description = "源数据信息")
    public static class SourceDataDTO {
        
        @Schema(description = "源预装记录ID")
        private String sourcePreboxItemId;
        
        @Schema(description = "源物料编码")
        private String sourceMaterialCode;
        
        @Schema(description = "源物料名称")
        private String sourceMaterialName;
        
        @Schema(description = "源合约号")
        private String sourceContractNo;
        
        @Schema(description = "源款号")
        private String sourceItemNo;
        
        @Schema(description = "源原始数量")
        private Integer sourceOriginalQty;
        
        @Schema(description = "源格号")
        private String sourceGridId;
        
        @Schema(description = "拆分明细列表")
        private List<SplitDetailDTO> splitDetails;
    }
    
    /**
     * 拆分明细信息
     */
    @Data
    @Schema(description = "拆分明细信息")
    public static class SplitDetailDTO {
        
        @Schema(description = "PO号")
        private String poNo;
        
        @Schema(description = "款式颜色")
        private String styleColor;
        
        @Schema(description = "物料规格")
        private String materialModel;
        
        @Schema(description = "物料颜色")
        private String materialColor;
        
        @Schema(description = "入库数量")
        private Integer inboundQty;
        

        
        @Schema(description = "PO源数据")
        private Object poSourceData;
    }
    
    /**
     * 汇总信息
     */
    @Data
    @Schema(description = "汇总信息")
    public static class SummaryDTO {
        
        @Schema(description = "源数据条数")
        private Integer totalSourceItems;
        
        @Schema(description = "拆分明细条数")
        private Integer totalSplitItems;
        
        @Schema(description = "总入库数量")
        private Integer totalInboundQty;
        
        @Schema(description = "PO号")
        private String poNo;
    }
} 