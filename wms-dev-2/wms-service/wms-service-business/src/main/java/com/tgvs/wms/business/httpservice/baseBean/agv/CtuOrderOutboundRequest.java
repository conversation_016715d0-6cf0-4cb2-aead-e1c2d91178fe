package com.tgvs.wms.business.httpservice.baseBean.agv;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * CTU顺序出库请求类，用于RCS-2000 CTU顺序出库接口的入参
 */
@Data
public class CtuOrderOutboundRequest {
    @JSONField(name = "orderId")
    private String orderId; // 订单ID，唯一标识一个订单

    @JSONField(name = "ctuId")
    private String ctuId; // CTU ID，唯一标识一个CTU单元

    @JSONField(name = "sequence")
    private Integer sequence; // 出库顺序，指定CTU的出库顺序
}
