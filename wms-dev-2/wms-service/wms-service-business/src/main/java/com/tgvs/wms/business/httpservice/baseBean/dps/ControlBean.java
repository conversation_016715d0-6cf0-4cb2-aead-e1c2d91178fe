package com.tgvs.wms.business.httpservice.baseBean.dps;


import com.alibaba.fastjson.annotation.JSONField;

public class ControlBean {
    @JSONField(name = "ControllerID")
    private int ControllerID;

    public void setControllerID(int ControllerID) {
        this.ControllerID = ControllerID;
    }

    public void setAsyn(boolean Asyn) {
        this.Asyn = Asyn;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof ControlBean))
            return false;
        ControlBean other = (ControlBean)o;
        return !other.canEqual(this) ? false : ((getControllerID() != other.getControllerID()) ? false : (!(isAsyn() != other.isAsyn())));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ControlBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        result = result * 59 + getControllerID();
        return result * 59 + (isAsyn() ? 79 : 97);
    }

    public String toString() {
        return "ControlBean(ControllerID=" + getControllerID() + ", Asyn=" + isAsyn() + ")";
    }

    public int getControllerID() {
        return this.ControllerID;
    }

    @JSONField(name = "Asyn")
    private boolean Asyn = false;

    public boolean isAsyn() {
        return this.Asyn;
    }

    public ControlBean(int controllerID) {
        setControllerID(controllerID);
    }
}
