package com.tgvs.wms.business.modules.machineMaterials.service.impI;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.business.modules.container.entity.Container;
import com.tgvs.wms.business.modules.container.entity.WmsMachineMaterialsBoxPrePacking;
import com.tgvs.wms.business.modules.container.mapper.ContainerMapper;
import com.tgvs.wms.business.modules.machineMaterials.dto.MachineMaterialsBoxOutRecordDto;
import com.tgvs.wms.business.modules.machineMaterials.dto.MachineMaterialsBoxPrePackingDto;
import com.tgvs.wms.business.modules.machineMaterials.dto.OutDetailsDto;
import com.tgvs.wms.business.modules.machineMaterials.dto.PreDetailsDto;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInfo;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineOutDetailRecord;
import com.tgvs.wms.business.modules.machineMaterials.mapper.MaterialInfoMapper;
import com.tgvs.wms.business.modules.machineMaterials.mapper.WmsMachineOutDetailRecordMapper;
import com.tgvs.wms.business.modules.machineMaterials.service.IMachineOutDetailRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class MachineOutDetailRecordServiceImpl extends ServiceImpl<WmsMachineOutDetailRecordMapper, WmsMachineOutDetailRecord> implements IMachineOutDetailRecordService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private WmsMachineOutDetailRecordMapper wmsMachineOutDetailRecordMapper;
    @Autowired
    private MaterialInfoMapper wmsMachineInfoMapper;
    @Autowired
    private ContainerMapper containerMapper;
    /** 拣货工位料箱条码，存储当前拣货工位上料箱的编码 */
    public static final String REDIS_LIFT_BOX_CODE_KEY = "lift:box_code";
    /** 拣货工位料架条码，存储当前拣货工位上料架的编码 */
    public static final String REDIS_LIFT_TRAY_CODE_KEY = "lift:tray_code";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MachineMaterialsBoxOutRecordDto outBoxRecordList()
    {
        try {
            String boxNo=stringRedisTemplate.opsForValue().get(REDIS_LIFT_BOX_CODE_KEY);
            if(!boxNo.isEmpty())
            {
                QueryWrapper<WmsMachineOutDetailRecord> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("status", 1);
                queryWrapper.eq("container_type", 1);
                queryWrapper.eq("delete_flag", 0);
                queryWrapper.eq("box_no", boxNo);//测试数据，需要根据输送线上报的箱号来跟新此处

                List<WmsMachineOutDetailRecord> boxOutRecordList = wmsMachineOutDetailRecordMapper.selectList(queryWrapper);
                MachineMaterialsBoxOutRecordDto boxOutRecordDto = new MachineMaterialsBoxOutRecordDto();
                if (!boxOutRecordList.isEmpty()) {
                    //转换preDetails为PreDetails类型
                    List<OutDetailsDto> outDetailsDtoList = new ArrayList<>();
                    for (WmsMachineOutDetailRecord item : boxOutRecordList)
                    {
                        OutDetailsDto preDetailsDto = new OutDetailsDto();
                        QueryWrapper<WmsMachineInfo> query = new QueryWrapper<>();
                        query.eq("material_code", item.getMaterialCode());
                        WmsMachineInfo wmsMachineInfo=wmsMachineInfoMapper.selectOne(query.last("LIMIT 1"));
                        if(wmsMachineInfo!=null){
                            preDetailsDto.setMaterialName(wmsMachineInfo.getMaterialName());
                            preDetailsDto.setBrand(wmsMachineInfo.getBrand());
                            preDetailsDto.setSpecification(wmsMachineInfo.getSpecification());
                        }
                        preDetailsDto.setId(item.getId());
                        preDetailsDto.setGridCode(item.getGridNo());
                        preDetailsDto.setMaterialCode(item.getMaterialCode());
                        preDetailsDto.setContainerType(1);
//                        preDetailsDto.setOriginalOuantity(item.getOriginalOuantity());
//                        preDetailsDto.setPendingQuantity(item.getPendingQuantity());
                        preDetailsDto.setInQuantity(item.getOutQuantity());
                        preDetailsDto.setOutStoreNumber(item.getOutStoreNumber());
                        preDetailsDto.setObjectId(item.getObjectId());
                        outDetailsDtoList.add(preDetailsDto);
                    }
                    QueryWrapper<Container> query = new QueryWrapper<>();
                    query.eq("box_no", boxNo);
                    Container boxInfo = containerMapper.selectOne(query);
                    if (boxInfo != null) {
                        boxOutRecordDto.setBoxType(boxInfo.getBoxContainerType());
                    }
                    boxOutRecordDto.setBoxCode(boxNo);
                    boxOutRecordDto.setOutDetailsDtoList(outDetailsDtoList);
                }
                return boxOutRecordDto;
            }else {
                log.info("拣货位目前无料箱");
                return null;
            }
        }catch (Exception ex){
            log.error(ex.toString());
            return null;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public MachineMaterialsBoxOutRecordDto outTrayRecordList()
    {
        try {
            String trayNo=stringRedisTemplate.opsForValue().get(REDIS_LIFT_TRAY_CODE_KEY);
            QueryWrapper<WmsMachineOutDetailRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1);
            queryWrapper.eq("delete_flag", 0);
            queryWrapper.eq("container_type", 2);
            queryWrapper.eq("box_no", trayNo);//测试数据，需要根据AGV上报的托盘号来跟新此处
            List<WmsMachineOutDetailRecord> trayOutRecordList = wmsMachineOutDetailRecordMapper.selectList(queryWrapper);
            MachineMaterialsBoxOutRecordDto trayOutRecordDto = new MachineMaterialsBoxOutRecordDto();
            if (!trayOutRecordList.isEmpty())
            {
                List<OutDetailsDto> trayRecordDtoList = new ArrayList<>();
                for (WmsMachineOutDetailRecord item : trayOutRecordList)
                {
                    OutDetailsDto trayRecordDto = new OutDetailsDto();
                    QueryWrapper<WmsMachineInfo> query = new QueryWrapper<>();
                    query.eq("material_code", item.getMaterialCode());
                    WmsMachineInfo wmsMachineInfo=wmsMachineInfoMapper.selectOne(query.last("LIMIT 1"));
                    if(wmsMachineInfo!=null){
                        trayRecordDto.setMaterialName(wmsMachineInfo.getMaterialName());
                        trayRecordDto.setBrand(wmsMachineInfo.getBrand());
                        trayRecordDto.setSpecification(wmsMachineInfo.getSpecification());
                    }
                    trayRecordDto.setId(item.getId());
                    trayRecordDto.setMaterialCode(item.getMaterialCode());
                    trayRecordDto.setContainerType(2);
//                    trayRecordDto.setOriginalOuantity(item.getOriginalOuantity());
//                    trayRecordDto.setPendingQuantity(item.getPendingQuantity());
                    trayRecordDto.setInQuantity(item.getOutQuantity());
                    trayRecordDto.setOutStoreNumber(item.getOutStoreNumber());
                    trayRecordDtoList.add(trayRecordDto);
                }
                QueryWrapper<Container> query = new QueryWrapper<>();
                query.eq("box_no", trayNo);
                Container boxInfo =containerMapper.selectOne(query);
                if(boxInfo!=null){
                    trayOutRecordDto.setBoxType(boxInfo.getBoxContainerType());
                }
                trayOutRecordDto.setBoxCode(trayNo);
//                trayOutRecordDto.setTrayCapacity(preDetails.get(0).getGridStatus());
                trayOutRecordDto.setOutDetailsDtoList(trayRecordDtoList);

            }
            return trayOutRecordDto;
        }catch (Exception ex){
            log.error(ex.toString());
            return null;
        }
    }


}
