package com.tgvs.wms.business.modules.machineMaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 机物料基础信息批量操作VO
 */
@Data
@Schema(description = "机物料基础信息批量操作请求")
public class MachineMaterialBatchVo implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @NotEmpty(message = "物料ID列表不能为空")
    @Schema(description = "物料ID列表", required = true)
    private String[] ids;
} 