package com.tgvs.wms.business.modules.machineAuxiliary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryPreboxDto;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryDetail;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryDetailService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 预装箱重复检查器
 * 基于明细表的preboxStatus字段进行重复预装检查
 */
@Component
@Slf4j
public class PreboxDuplicateChecker {

    @Autowired
    private IAuxiliaryDetailService auxiliaryDetailService;

    /**
     * 检查是否存在重复预装的情况，并分离已预装和待处理的记录
     * 基于明细表的preboxStatus字段进行检查，更加准确高效
     * 
     * @param model 预装箱DTO列表
     * @return PreboxCheckResult 包含已预装和待处理记录的结果对象
     */
    public PreboxCheckResult checkDuplicatePreboxing(List<AuxiliaryPreboxDto> model) {
        log.info("开始检查重复预装，检查 {} 条记录", model.size());

        PreboxCheckResult result = new PreboxCheckResult();

        // 提取所有明细ID
        List<String> detailIds = extractValidDetailIds(model);
        if (CollectionUtils.isEmpty(detailIds)) {
            log.warn("未找到有效的明细ID，所有记录都将继续处理");
            result.setToProcessList(new ArrayList<>(model));
            return result;
        }

        log.info("检查 {} 个明细ID的预装状态", detailIds.size());

        // 查询明细的预装状态
        List<WmsAuxiliaryDetail> detailsWithPreboxStatus = queryDetailsPreboxStatus(detailIds);

        if (CollectionUtils.isEmpty(detailsWithPreboxStatus)) {
            log.warn("未查询到任何明细记录，所有记录都将继续处理");
            result.setToProcessList(new ArrayList<>(model));
            return result;
        }

        // 分析预装状态并分离记录
        return analyzePreboxStatusAndSeparateRecords(model, detailsWithPreboxStatus);
    }

    /**
     * 提取有效的明细ID
     */
    private List<String> extractValidDetailIds(List<AuxiliaryPreboxDto> model) {
        return model.stream()
                .map(AuxiliaryPreboxDto::getStockInId) // stockInId实际存储的是明细ID
                .filter(Objects::nonNull)
                .filter(id -> !id.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 查询明细的预装状态
     */
    private List<WmsAuxiliaryDetail> queryDetailsPreboxStatus(List<String> detailIds) {
        return auxiliaryDetailService.list(
                new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                        .in(WmsAuxiliaryDetail::getId, detailIds)
                        .eq(WmsAuxiliaryDetail::getOperationType, 0) // 入库操作
                        .eq(WmsAuxiliaryDetail::getDeleteFlag, 0) // 未删除
                        .select(WmsAuxiliaryDetail::getId, 
                               WmsAuxiliaryDetail::getPreboxStatus, 
                               WmsAuxiliaryDetail::getPreboxId,
                               WmsAuxiliaryDetail::getPreboxBatchNo,
                               WmsAuxiliaryDetail::getMaterialCode,
                               WmsAuxiliaryDetail::getRefNumber)
        );
    }

    /**
     * 分析预装状态并分离已预装和待处理的记录
     */
    private PreboxCheckResult analyzePreboxStatusAndSeparateRecords(List<AuxiliaryPreboxDto> model,
            List<WmsAuxiliaryDetail> detailsWithPreboxStatus) {
        
        // 创建明细ID到预装状态的映射
        Map<String, WmsAuxiliaryDetail> detailStatusMap = detailsWithPreboxStatus.stream()
                .collect(Collectors.toMap(
                        WmsAuxiliaryDetail::getId,
                        detail -> detail,
                        (existing, replacement) -> existing
                ));

        // 分离已预装和待处理的记录
        List<AuxiliaryPreboxDto> toProcessList = new ArrayList<>();
        List<AuxiliaryPreboxDto> alreadyProcessedList = new ArrayList<>();
        Set<String> existingBatchNos = new HashSet<>();

        for (AuxiliaryPreboxDto dto : model) {
            String detailId = dto.getStockInId();
            WmsAuxiliaryDetail detailStatus = detailStatusMap.get(detailId);
            
            if (detailStatus == null) {
                log.warn("明细ID {} 未找到对应记录，将作为待处理记录", detailId);
                toProcessList.add(dto);
            } else if (isAlreadyPreboxed(detailStatus)) {
                // 已预装的明细
                alreadyProcessedList.add(dto);
                if (detailStatus.getPreboxBatchNo() != null) {
                    existingBatchNos.add(detailStatus.getPreboxBatchNo());
                }
            } else {
                // 未预装的明细
                toProcessList.add(dto);
            }
        }

        // 构建结果
        PreboxCheckResult result = new PreboxCheckResult();
        result.setToProcessList(toProcessList);
        result.setAlreadyPreboxedList(convertToPreboxInfo(alreadyProcessedList, detailStatusMap));
        result.setExistingBatchNos(existingBatchNos);

        // 记录检查结果
        logCheckResult(alreadyProcessedList, toProcessList, existingBatchNos, detailStatusMap);

        return result;
    }

    /**
     * 判断明细是否已经预装
     */
    private boolean isAlreadyPreboxed(WmsAuxiliaryDetail detail) {
        return detail.getPreboxStatus() != null && detail.getPreboxStatus() == 1;
    }

    /**
     * 将已预装的DTO转换为预装信息（为了兼容现有接口）
     */
    private List<PreboxInfo> convertToPreboxInfo(List<AuxiliaryPreboxDto> alreadyProcessedList,
            Map<String, WmsAuxiliaryDetail> detailStatusMap) {
        return alreadyProcessedList.stream()
                .map(dto -> {
                    WmsAuxiliaryDetail detail = detailStatusMap.get(dto.getStockInId());
                    PreboxInfo info = new PreboxInfo();
                    info.setStockInId(dto.getStockInId());
                    info.setStockInNo(dto.getStockInNo());
                    info.setMaterialCode(dto.getMaterialCode());
                    info.setStockBatchId(detail != null ? detail.getPreboxBatchNo() : null);
                    info.setPreboxId(detail != null ? detail.getPreboxId() : null);
                    return info;
                })
                .collect(Collectors.toList());
    }

    /**
     * 记录检查结果的详细信息
     */
    private void logCheckResult(List<AuxiliaryPreboxDto> alreadyProcessedList,
            List<AuxiliaryPreboxDto> toProcessList,
            Set<String> existingBatchNos,
            Map<String, WmsAuxiliaryDetail> detailStatusMap) {
        
        log.info("预装检查结果 - 已预装: {} 条, 待处理: {} 条, 涉及批次: {}",
                alreadyProcessedList.size(), toProcessList.size(), 
                existingBatchNos.isEmpty() ? "无" : String.join(", ", existingBatchNos));

        if (!alreadyProcessedList.isEmpty()) {
            StringBuilder infoMsg = new StringBuilder("已预装的明细详情：\n");
            for (AuxiliaryPreboxDto dto : alreadyProcessedList) {
                WmsAuxiliaryDetail detail = detailStatusMap.get(dto.getStockInId());
                infoMsg.append(String.format("- 明细ID: %s, 入库单: %s, 物料: %s, 预装箱ID: %s, 批次: %s\n",
                        dto.getStockInId(),
                        dto.getStockInNo(),
                        dto.getMaterialCode(),
                        detail != null ? detail.getPreboxId() : "未知",
                        detail != null ? detail.getPreboxBatchNo() : "未知"));
            }
            log.info(infoMsg.toString());
        }

        if (!toProcessList.isEmpty()) {
            log.info("待处理明细: {} 条，明细ID列表: {}", 
                    toProcessList.size(),
                    toProcessList.stream()
                            .map(AuxiliaryPreboxDto::getStockInId)
                            .collect(Collectors.joining(", ")));
        }
    }

    /**
     * 预装信息内部类（用于兼容现有接口）
     */
    @Data
    public static class PreboxInfo {
        private String stockInId;
        private String stockInNo;
        private String materialCode;
        private String stockBatchId;
        private String preboxId;
    }

    /**
     * 预装检查结果内部类
     */
    @Data
    public static class PreboxCheckResult {
        // 需要继续处理的记录列表（未预装过的）
        private List<AuxiliaryPreboxDto> toProcessList;
        // 已预装的记录信息（改为PreboxInfo兼容现有逻辑）
        private List<PreboxInfo> alreadyPreboxedList;
        // 已预装记录对应的批次号集合
        private Set<String> existingBatchNos;

        public PreboxCheckResult() {
            this.toProcessList = new ArrayList<>();
            this.alreadyPreboxedList = new ArrayList<>();
            this.existingBatchNos = new HashSet<>();
        }

        public boolean hasAlreadyPreboxed() {
            return !CollectionUtils.isEmpty(alreadyPreboxedList);
        }

        public boolean hasToProcess() {
            return !CollectionUtils.isEmpty(toProcessList);
        }
    }
}