package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询辅料出库任务信息DTO
 */
@Data
@Schema(description = "查询辅料出库任务信息DTO")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class QueryAuxiliaryOutTaskDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 出库任务料箱/托盘属性列表
     */
    private List<BoxPropertyDto> boxProperty;
    
    /**
     * 任务状态统计信息
     */
    @Schema(description = "任务状态统计")
    private TaskStatusSummary taskStatusSummary;
    
    /**
     * 查询状态描述
     */
    @Schema(description = "查询状态描述")
    private String statusMessage;
    
    /**
     * 是否有数据在输送线上
     */
    @Schema(description = "是否有数据在输送线上")
    private Boolean hasDataOnConveyor;
    
    /**
     * 建议刷新间隔（秒）
     */
    @Schema(description = "建议刷新间隔(秒)")
    private Integer refreshInterval;
    
    /**
     * 任务状态统计内部类
     */
    @Data
    @Schema(description = "任务状态统计")
    public static class TaskStatusSummary {
        /**
         * 总料箱数量
         */
        @Schema(description = "总料箱数量")
        private Integer totalBoxes;
        
        /**
         * 输送线上的料箱数量
         */
        @Schema(description = "输送线上的料箱数量")
        private Integer onConveyorBoxes;
        
        /**
         * 等待中的料箱数量
         */
        @Schema(description = "等待中的料箱数量")
        private Integer waitingBoxes;
        
        /**
         * 已完成的料箱数量
         */
        @Schema(description = "已完成的料箱数量")
        private Integer completedBoxes;
        
        /**
         * 待出库总数量
         */
        @Schema(description = "待出库总数量")
        private Integer totalPendingQuantity;
    }
} 