package com.tgvs.wms.business.modules.machineMaterials.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tgvs.wms.business.modules.machineMaterials.dto.InMachineListDto;
import com.tgvs.wms.business.modules.machineMaterials.entity.WmsMachineInList;
import com.tgvs.wms.business.modules.machineMaterials.vo.MachineMaterialInboundQueryVo;
import com.tgvs.wms.business.modules.machineMaterials.vo.MachineMaterialInboundVo;
import com.tgvs.wms.common.core.domain.QueryModel;


import java.util.List;

/**
 * 机物料入库服务接口
 */
public interface IMachineMaterialInboundService extends IService<WmsMachineInList> {

    /**
     * 分页查询机物料入库信息
     * 
     * @param queryModel 查询参数
     * @return 分页结果
     */
    IPage<InMachineListDto>  pageList(QueryModel queryModel);
    
    /**
     * 根据条件查询机物料入库信息
     * 
     * @param queryVo 查询条件
     * @return 机物料入库信息列表
     */
//    List<WmsMachineInList> selectMachineInListList(MachineMaterialInboundQueryVo queryVo);
    
    /**
     * 根据ID查询机物料入库信息
     * 
     * @param id 机物料入库信息ID
     * @return 机物料入库信息
     */
    WmsMachineInList selectMachineInListById(String id);
    
    /**
     * 新增机物料入库信息
     * 
     * @param inboundVo 机物料入库信息
     * @return 结果
     */
    boolean insertMachineInList(MachineMaterialInboundVo inboundVo);
    
    /**
     * 修改机物料入库信息
     * 
     * @param inboundVo 机物料入库信息
     * @return 结果
     */
    boolean updateMachineInList(MachineMaterialInboundVo inboundVo);
    
    /**
     * 批量删除机物料入库信息
     * 
     * @param ids 需要删除的机物料入库信息ID数组
     * @return 结果
     */
    boolean deleteMachineInListByIds(String[] ids);
    
    /**
     * 处理机物料入库信息
     *
     * @param request 入库请求
     * @return 处理结果，true表示成功，false表示失败
     */
    boolean processInMaterial(MachineMaterialInboundVo request);

    IPage<InMachineListDto> pageHistoryList(QueryModel queryModel);
} 