package com.tgvs.wms.business.modules.storage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.business.modules.container.entity.BoxItem;
import com.tgvs.wms.business.modules.container.mapper.BoxItemMapper;
import com.tgvs.wms.business.modules.storage.entity.Shelf;
import com.tgvs.wms.business.modules.storage.mapper.ShelfMapper;
import com.tgvs.wms.business.modules.storage.vo.StockListDto;
import com.tgvs.wms.business.modules.task.dto.taskResultDto;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;
import com.tgvs.wms.common.util.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.storage.entity.Stock;
import com.tgvs.wms.business.modules.storage.mapper.StockMapper;
import com.tgvs.wms.business.modules.storage.service.IStockService;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class StockServiceImpl extends BaseServiceImpl<StockMapper, Stock> implements IStockService {
    @Autowired
    private StockMapper stockMapper;
    @Autowired
    private BoxItemMapper boxItemMapper;
    @Autowired
    private ShelfMapper shelfMapper;

    public Stock selectBycode(String code) {
        return this.stockMapper.selectBycode(code);
    }

    public IPage<StockListDto> pageStockList(QueryModel queryModel)
    {
        QueryWrapper<BoxItem> queryWrapper = QueryGenerator.initQueryWrapper(queryModel.getSearchParams());
        // 分页查询
        Page<BoxItem> page = new Page<>(queryModel.getPage(), queryModel.getLimit());
        IPage<StockListDto> pageDto = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        try{
            // 设置排序
            if (com.tgvs.wms.common.util.StringUtils.isNotEmpty(queryModel.getSortList())) {
                for (QueryModel.SortModel sortModel : queryModel.getSortList()) {
                    if (sortModel.getSortType() == 1) {
                        queryWrapper.orderByAsc(sortModel.getColumn());
                    } else if (sortModel.getSortType() == 2) {
                        queryWrapper.orderByDesc(sortModel.getColumn());
                    }
                }
            } else {
                // 默认按创建时间倒序
                queryWrapper.orderByDesc("update_time");
            }
            queryWrapper.eq("delete_flag",0);
            queryWrapper.eq("status",2);

            List<BoxItem> list = boxItemMapper.selectPage(page, queryWrapper).getRecords();
            List<StockListDto> dto = list.stream().map(item -> {
                StockListDto machinedto = new StockListDto();
                BeanUtils.copyProperties(item, machinedto);
                return machinedto;
            }).collect(Collectors.toList());
            for (StockListDto item : dto)
            {
                QueryWrapper<Shelf> query = new QueryWrapper<>();
                query.eq("box_no", item.getBoxNo());
                Shelf shelf = shelfMapper.selectOne(query.last("LIMIT 1"));
                if (shelf != null) {
                    item.setColumn(shelf.getColumn());
                    item.setRow(shelf.getRow());
                    item.setLevel(shelf.getLevel());
                    item.setCode(shelf.getCode());
                    item.setUpdateTime(shelf.getUpdateTime());
                } else {
                    log.error("找不到容器" + item.getBoxNo() + "的库位信息");
                }
            }

            pageDto.setRecords(dto);
            pageDto.setTotal((boxItemMapper.selectPage(page, queryWrapper).getTotal()));

        }catch (Exception ex){
            log.error(ex.toString());
        }
        return pageDto;
    }
}