package com.tgvs.wms.business.wmsservice.server;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tgvs.wms.business.enums.enumTaskStatus;
import com.tgvs.wms.business.httpservice.baseBean.agv.*;
import com.tgvs.wms.business.modules.commncation.entity.CommncationConfig;
import com.tgvs.wms.business.modules.storage.entity.Shelf;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import com.tgvs.wms.business.modules.task.service.IRosReportService;
import com.tgvs.wms.business.modules.task.service.ITaskReportQueueService;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.util.httpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

/**
 * （HK）AGV调度服务
 * 实现调度相关接口对接 (已更新为新协议)
 */

@Slf4j
@Service
public class AgvHkService {

    public static AgvHkService agvHkService;
    public static final String sysname = "HikRoboot"; // 可能用作配置标识


    @Autowired
    private IRosReportService rosReportService;
    
    @Autowired
    private ITaskReportQueueService taskReportQueueService;

    protected static String baseUrl = ""; // 从配置加载
    protected static String apiHost = ""; // 从 baseUrl 解析得到
    protected static boolean hikWorking = true;

    // New API Path Structure (assumed to be compatible or to be reviewed separately)
    private static final String API_TASK_PREFIX_PATH = "/rcs/rtas/api/robot/controller/task";

    // 新增预调度接口路径
    private static final String PRE_TASK_INTERFACE_NAME = "/pretask";

    // 新增外设执行通知接口路径
    private static final String API_EQUIPMENT_NOTIFY_PATH = "/rcs/rtas/spi/wcs/robot/eqpt/notify";


    private static final String SUBMIT_TASK_INTERFACE_NAME = "/submit";

    private static final String GROUP_TASK_INTERFACE_NAME = "/group";
    private static final String CONTINUE_TASK_INTERFACE_NAME = "/extend/continue";

    // HTTP Header constants for RCS-2000 V4.1
    //private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_X_lR_REQUEST_ID = "X-lr-request-id"; // Changed from X-lr-request-id
    private static final String HEADER_X_lR_VERSION = "X-lr-version";       // Changed from X-lr-version
    private static final String HEADER_X_lR_TRACE = "X-lr-trace-id";
    private static final String HEADER_X_lR_APPKEY = "X-lr-appkey";
    private static final String API_VERSION_V4_1 = "4.1"; // Changed from v1.0

    /** 拣货工位料架条码，存储当前拣货工位上料架的编码 */
    public static final String REDIS_LIFT_TRAY_CODE_KEY = "lift:tray_code";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // 重试配置常量
    private static final int MAX_RETRY_ATTEMPTS = 5;
    private static final long RETRY_DELAY_MS = 300L;
    private static final Set<String> SUCCESS_CODES = new HashSet<>(Arrays.asList("0", "200", "SUCCESS"));

    @PostConstruct
    public void init() {
        agvHkService = this; // 确保静态字段引用Spring管理的Bean实例
        // 从配置表中获取正确的URL、appKey和appSecret
        LambdaQueryWrapper<CommncationConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommncationConfig::getSysname, sysname);
        List<CommncationConfig> list = MainService.mainServiceutil.commncationConfigMapper.selectList(queryWrapper);
        // 实际项目中需要从配置表中获取正确的URL、appKey和appSecret
        for (CommncationConfig config : list) {
            if (sysname.equals(config.getSysname())) {
                baseUrl = config.getAddress() != null ? config.getAddress() : "";
                try {
                    if (!baseUrl.isEmpty()) {
                        URL url = new URL(baseUrl);
                        apiHost = url.getHost() + (url.getPort() > 0 ? ":" + url.getPort() : "");
                    }
                } catch (MalformedURLException e) {
                    log.error("初始化海康AGV调度服务URL解析失败: {}", e.getMessage());
                }
                break;
            }
        }

        // The check for appSecret might need adjustment if it's not used in V4.1 auth
        if (baseUrl.isEmpty()) {
            log.warn("海康AGV调度服务配置不完整（baseUrl, appKey, appSecret），请检查配置。appSecret的V4.1用途待确认。");
            hikWorking = false;
        } else {
            log.info("海康AGV调度服务初始化成功: baseUrl={}", baseUrl);
            hikWorking = true;
        }

        MainService.updatecommncationServer(sysname, hikWorking ? 1 : 0);
    }

    public void Start() {
        hikWorking = true;
        MainService.updatecommncationServer(sysname, 1);
    }

    public void Stop() {
        hikWorking = false;
        MainService.updatecommncationServer(sysname, 0);
    }

    /**
     * 提交AGV任务到海康机器人调度系统
     *
     * @param request 任务请求对象
     * @return 任务响应对象
     */
    public TaskSubmitResponse submitTask(TaskSubmitRequest request) {
        if (!hikWorking) {
            log.error("海康AGV调度服务未启用或配置不正确");
            TaskSubmitResponse response = new TaskSubmitResponse();
            response.setCode("Err_Exception");
            response.setMessage("海康AGV调度服务未启用或配置不正确");
            return response;
        }

        try {
            // 构建API URL
            String apiUrl = baseUrl + API_TASK_PREFIX_PATH + SUBMIT_TASK_INTERFACE_NAME;

            // 生成请求ID
            String requestId = generateUUID(); // Changed from appKey to unique random value for X-Ir-request-id

            // 请求JSON
            String requestJson = JSON.toJSONString(request);



            // 创建HTTP头
            Map<String, String> headers = new LinkedHashMap<>();


            // 构建Header for V4.1 - 只设置必填头部
          //  headers.put(HEADER_CONTENT_TYPE, CONTENT_TYPE_JSON);
            headers.put(HEADER_X_lR_REQUEST_ID, requestId); // Use new header name
            headers.put(HEADER_X_lR_VERSION, API_VERSION_V4_1);   // Use new header name and version

            //计算Content-Length
           // int contentLength = requestJson.getBytes(StandardCharsets.UTF_8).length;
           // headers.put("Content-Length", String.valueOf(contentLength));
            // 记录请求日志
            log.info("AGV调度请求地址 (V4.1): {}", apiUrl);
            log.info("AGV调度请求头: {}", JSON.toJSONString(headers));
            log.info("AGV调度请求体: {}", requestJson);
            // 发送请求

            String responseJson = httpUtils.httpsPost(apiUrl, headers, requestJson);

            // 记录响应日志
            log.info("AGV调度响应: {}", responseJson);

            // 解析响应
            if (responseJson != null && !responseJson.isEmpty()) {
                //如果响应码为200，则返回成功
                if (JSON.parseObject(responseJson, TaskSubmitResponse.class).getCode().equals("SUCCESS")) {
                    //更新任务状态
                    LambdaQueryWrapper<WmsBoxTaskList> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(WmsBoxTaskList::getTaskOrder, request.getRobotTaskCode());
                    queryWrapper.eq(WmsBoxTaskList::getTaskStatus, enumTaskStatus.ready.getValue());
                    WmsBoxTaskList wmsBoxTaskList = MainService.mainServiceutil.wmsBoxTaskListMapper.selectOne(queryWrapper);
                    if (null != wmsBoxTaskList) {
                        wmsBoxTaskList.setTaskStatus(enumTaskStatus.allot.getValue());
                    }
                    MainService.mainServiceutil.wmsBoxTaskListMapper.updateById(wmsBoxTaskList);
                }
            }

            return JSON.parseObject(responseJson, TaskSubmitResponse.class);
        } catch (Exception e) {
            log.error("提交AGV任务异常: {}", e.getMessage(), e);
            TaskSubmitResponse response = new TaskSubmitResponse();
            response.setCode("Err_Internal");
            response.setMessage("提交AGV任务异常: " + e.getMessage());
            return response;
        }
    }


    /**
     * 提交AGV任务到海康机器人调度系统
     *
     * @param request 任务请求对象
     * @return 任务响应对象
     */
    public TaskSubmitResponse groupTask(TaskGroupRequest request) {
        if (!hikWorking) {
            log.error("海康AGV调度服务未启用或配置不正确");
            TaskSubmitResponse response = new TaskSubmitResponse();
            response.setCode("Err_Exception");
            response.setMessage("海康AGV调度服务未启用或配置不正确");
            return response;
        }

        try {
            // 构建API URL
            String apiUrl = baseUrl + API_TASK_PREFIX_PATH + GROUP_TASK_INTERFACE_NAME;

            // 生成请求ID
            String requestId = generateUUID(); // Changed from appKey to unique random value for X-Ir-request-id

            // 请求JSON
            String requestJson = JSON.toJSONString(request);



            // 创建HTTP头
            Map<String, String> headers = new LinkedHashMap<>();


            // 构建Header for V4.1 - 只设置必填头部
            //  headers.put(HEADER_CONTENT_TYPE, CONTENT_TYPE_JSON);
            headers.put(HEADER_X_lR_REQUEST_ID, requestId); // Use new header name
            headers.put(HEADER_X_lR_VERSION, API_VERSION_V4_1);   // Use new header name and version
            headers.put(HEADER_X_lR_TRACE, generateUUID());   // Use new header name and version
            headers.put(HEADER_X_lR_APPKEY, generateUUID());   // Use new header name and version
            //计算Content-Length
            // int contentLength = requestJson.getBytes(StandardCharsets.UTF_8).length;
            // headers.put("Content-Length", String.valueOf(contentLength));
            // 记录请求日志
            log.info("AGV组任务下发调度请求地址: {}", apiUrl);
            log.info("AGV组任务下发调度请求头: {}", JSON.toJSONString(headers));
            log.info("AGV组任务下发调度请求体: {}", requestJson);
            // 发送请求

            String responseJson = httpUtils.httpsPost(apiUrl, headers, requestJson);

            // 记录响应日志
            log.info("AGV调度响应: {}", responseJson);

            // 解析响应
            if (responseJson != null && !responseJson.isEmpty()) {
                //如果响应码为200，则返回成功
                if (JSON.parseObject(responseJson, TaskSubmitResponse.class).getCode().equals("SUCCESS")) {

                }
            }

            return JSON.parseObject(responseJson, TaskSubmitResponse.class);
        } catch (Exception e) {
            log.error("提交AGV任务异常: {}", e.getMessage(), e);
            TaskSubmitResponse response = new TaskSubmitResponse();
            response.setCode("Err_Internal");
            response.setMessage("提交AGV任务异常: " + e.getMessage());
            return response;
        }
    }

    /**
     * 生成UUID (不带连字符)
     */
    private String generateUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 调用任务继续接口
     */
    public ContinueTaskResponse continueTask(ContinueTaskRequest request) {
        // 实现与submitTask类似的逻辑
        if (!hikWorking) {
            log.error("海康AGV调度服务未启用或配置不正确");
            ContinueTaskResponse response = new ContinueTaskResponse();
            response.setCode("Err_Exception");
            response.setMessage("海康AGV调度服务未启用或配置不正确");
            return response;
        }

        try {
            // 构建API URL
            String apiUrl = baseUrl + API_TASK_PREFIX_PATH + CONTINUE_TASK_INTERFACE_NAME;

            // 生成请求ID和追踪ID
            String requestId = generateUUID(); // For X-Ir-request-id

            log.info("ContinueTaskRequest: robotTaskCode={}, triggerType={}, triggerCode={}",
                    request.getRobotTaskCode(), request.getTriggerType(), request.getTriggerCode());
            // 请求JSON
            String requestJson = JSON.toJSONString(request);

            // 获取当前时间戳 - Old: for Authorization header

            // 创建HTTP头
            Map<String, String> headers = new LinkedHashMap<>();
            headers.put(HEADER_X_lR_REQUEST_ID, requestId); // Use new header name
            headers.put(HEADER_X_lR_VERSION, API_VERSION_V4_1);   // Use new header name and version

            // 记录请求日志
            log.info("AGV继续任务请求地址 (V4.1): {}", apiUrl);
            log.info("AGV继续任务请求头: {}", JSON.toJSONString(headers));
            log.info("AGV继续任务请求体: {}", requestJson);

            // 发送请求
            String responseJson = httpUtils.httpsPost( apiUrl, headers, requestJson);

            // 记录响应日志
            log.info("AGV继续任务响应: {}", responseJson);

            // 解析响应
            if (responseJson != null && !responseJson.isEmpty()) {
                return JSON.parseObject(responseJson, ContinueTaskResponse.class);
            } else {
                log.error("AGV继续任务响应为空");
                ContinueTaskResponse response = new ContinueTaskResponse();
                response.setCode("Err_Internal");
                response.setMessage("AGV继续任务响应为空");
                return response;
            }
        } catch (Exception e) {
            log.error("继续AGV任务异常: {}", e.getMessage(), e);
            ContinueTaskResponse response = new ContinueTaskResponse();
            response.setCode("Err_Internal");
            response.setMessage("继续AGV任务异常: " + e.getMessage());
            return response;
        }
    }

    /**
     * 处理外设执行通知接口 (WCS通知RCS)
     * 对应文档2.1.15节的外设执行通知接口
     *
     * @param request 外设执行通知请求对象
     * @return 外设执行通知响应对象
     */
    public EquipmentNotifyResponse handleEquipmentNotify(EquipmentNotifyRequest request){
            if (!hikWorking) {
                log.error("海康AGV调度服务未启用或配置不正确");
                EquipmentNotifyResponse response = new EquipmentNotifyResponse();
                response.setCode("Err_Exception");
                response.setMessage("海康AGV调度服务未启用或配置不正确");
                return response;
            }

            try {
                // 构建API URL
                String apiUrl = baseUrl + API_EQUIPMENT_NOTIFY_PATH;

                // 生成请求ID
                String requestId = generateUUID();

                // 请求JSON
                String requestJson = JSON.toJSONString(request);

                // 创建HTTP头
                Map<String, String> headers = new LinkedHashMap<>();

                // 构建Header for V4.1 - 只设置必填头部
                headers.put(HEADER_X_lR_REQUEST_ID, requestId);
                headers.put(HEADER_X_lR_VERSION, API_VERSION_V4_1);

                // 记录请求日志
                log.info("外设执行通知请求地址 (V4.1): {}", apiUrl);
                log.info("外设执行通知请求头: {}", JSON.toJSONString(headers));
                log.info("外设执行通知请求体: {}", requestJson);

                // 发送请求 - 使用POST方法
                String responseJson = httpUtils.httpsPost(apiUrl, headers, requestJson);

                // 记录响应日志
                log.info("外设执行通知响应: {}", responseJson);

                // 解析响应
                if (responseJson != null && !responseJson.isEmpty()) {
                    //如果响应码为200，则返回成功
                    if (JSON.parseObject(responseJson, EquipmentNotifyResponse.class).getCode().equals("200")||
                            JSON.parseObject(responseJson, EquipmentNotifyResponse.class).getCode().equals("SUCCESS")|| JSON.parseObject(responseJson, EquipmentNotifyResponse.class).getCode().equals("0")) {
                        return JSON.parseObject(responseJson, (Type) EquipmentNotifyResponse.class);
                    }
                    else {
                        log.error("外设执行通知响应失败: {}", responseJson);
                        EquipmentNotifyResponse response = new EquipmentNotifyResponse();
                        response.setCode("Err_Exception");
                        response.setMessage("外设执行通知响应失败: " + responseJson);
                        return response;
                    }
                }else{
                    log.error("外设执行通知响应为空");
                    EquipmentNotifyResponse response = new EquipmentNotifyResponse();
                    response.setCode("Err_Exception");
                    response.setMessage("外设执行通知响应为空");
                    return response;
                }
            }
            catch(Exception e) {
                log.error("处理外设执行通知异常: {}", e.getMessage(), e);
                EquipmentNotifyResponse response = new EquipmentNotifyResponse();
                response.setCode("Err_Exception");
                response.setMessage("处理外设执行通知异常: " + e.getMessage());
                return response;
            }
        }
    /**
 * 任务执行过程回馈接口 - 接收机器人系统回调通知
 * @param request 任务执行通知请求
 * @return 任务执行通知响应
 */
public TaskExecutionNoticeResponse taskExecutionNotice(TaskExecutionNoticeRequest request){
    try {
        log.info("收到机器人任务执行通知: {}", JSON.toJSONString(request));

        // 默认初始化响应对象
        TaskExecutionNoticeResponse response = new TaskExecutionNoticeResponse();
        response.setCode("SUCCESS");
        response.setMessage("成功");

        // 将请求持久化或发送到消息队列，用于后续的异步处理和可能的重试
        // 例如: recordTaskNoticeForAsyncProcessing(request);

        // 获取method值，判断任务执行阶段
        String method = request.getMethod();
        String deviceNo = null;
        String eqptCode = request.getEqptCode();
        String taskCode = request.getTaskCode();
        int deviceType = 0;
        // 首先检查最外层是否有method参数

        if (request.getExtra() != null && method==null) {
            // 如果最外层没有method，则从Extra.values中获取
            com.alibaba.fastjson.JSONObject extra = request.getExtra();
            if (extra.containsKey("values")) {
                com.alibaba.fastjson.JSONObject values = extra.getJSONObject("values");
                if (values.containsKey("eqptCode")) {
                    eqptCode = values.getString("eqptCode");
                }
                if (values.containsKey("taskCode")) {
                    taskCode = values.getString("taskCode");
                    request.setRobotTaskCode(taskCode);
                }
                if (values.containsKey("method")) {
                    method = values.getString("method");
                    if (values.containsKey("amrCode")) {
                        deviceNo = values.getString("amrCode");
                    }
                    if (values.containsKey("amrCategory")) {
                        if (values.getString("amrCategory").equals("CTU")){
                            deviceType = 1;
                        }
                        else if (values.getString("amrCategory").equals("AGV")){
                            deviceType = 2;
                        }
                    }
                }
            }
            if (taskCode!=null)
            {
                request.setRobotTaskCode(taskCode);
                request.setEqptCode(eqptCode);

            }else {
                taskCode = request.getRobotTaskCode();
                eqptCode=request.getEqptCode();
            }
        }

        if (method == null) {
            log.warn("任务执行通知缺少method值");
            response.setCode("Error");
            response.setMessage("缺少method参数");
            return response;
        }

        log.info("任务执行阶段: {}, 任务编号: {}", method, taskCode);
        LambdaQueryWrapper<WmsBoxTaskList> queryWrapper = new LambdaQueryWrapper<>();
        // 根据不同的method值处理任务
        switch (method) {
            case "start":
                // 机器人任务开始
                log.warn("机器人任务开始执行，任务编号: {}", request.getRobotTaskCode());
                queryWrapper.eq(WmsBoxTaskList::getTaskOrder, request.getRobotTaskCode());
                WmsBoxTaskList startWmsBoxTaskList = MainService.mainServiceutil.wmsBoxTaskListMapper.selectOne(queryWrapper);
                if (null != startWmsBoxTaskList) {
                    //记录到redis
                  MainService.mainServiceutil.stringRedisTemplate.opsForValue().set("order:"+request.getRobotTaskCode(), JSON.toJSONString(startWmsBoxTaskList));
                }
                break;
            case "APPLY_FROM_AGV"://出库放货申请发的方法名是APPLY_FROM_AGV

                // 异步处理任务暂停的业务逻辑
                log.warn("CTU执行继续出库，任务编号: {}", taskCode);
                //从redis中获取任务,如果redis中没有任务，则从数据库中获取
                WmsBoxTaskList  outWmsBoxTaskList = JSON.parseObject(MainService.mainServiceutil.stringRedisTemplate.opsForValue().get("order:"+taskCode), WmsBoxTaskList.class);
                if (null == outWmsBoxTaskList) {
                    queryWrapper.eq(WmsBoxTaskList::getTaskOrder,taskCode);
                    outWmsBoxTaskList = MainService.mainServiceutil.wmsBoxTaskListMapper.selectOne(queryWrapper);
                    if (null != outWmsBoxTaskList) {
                        MainService.mainServiceutil.stringRedisTemplate.opsForValue().set("order:"+taskCode, JSON.toJSONString(outWmsBoxTaskList));
                    }
                }
                log.warn("CTU执行继续出库任务信息:"+JSON.toJSONString(outWmsBoxTaskList));
                    // taskType任务类型：0采购入库，1一般入库，2生产退料回库，3领料出库，4调拨出库，5采购退货出库，6指定出库，7.指定入库，8.紧急出库。9.调拨入库，10.盘点出库
                if (null != outWmsBoxTaskList)
                {
                    Integer taskType = outWmsBoxTaskList.getTaskType();
                    EquipmentNotifyRequest equipmentNotifyRequest = new EquipmentNotifyRequest();
                    equipmentNotifyRequest.setTaskCode(taskCode);
                    equipmentNotifyRequest.setEqptCode(eqptCode);

                    String proceedFailureReason = "";
                    if (taskType==3||taskType==4||taskType==5||taskType==6||taskType==7||taskType==8||taskType==10)
                    {
                        equipmentNotifyRequest.setActionStatus("6");
                        String ctuPlaceStatusStr = MainService.mainServiceutil.stringRedisTemplate.opsForValue().get("ctu:place_box:status");
                        log.warn("CTU放货位状态信息:"+ctuPlaceStatusStr);
                        boolean canPlaceBoxAtCTU = "1".equals(ctuPlaceStatusStr);
                        if (canPlaceBoxAtCTU)
                        {
                            EquipmentNotifyResponse conResponse = continueTask(equipmentNotifyRequest); //尝试2次
                            if (conResponse.getCode().equals("SUCCESS")||
                                    conResponse.getCode().equals("200") || conResponse.getCode().equals("0")) {
                                outWmsBoxTaskList.setTaskStatus(enumTaskStatus.allow.getValue());
                                MainService.mainServiceutil.wmsBoxTaskListMapper.updateById(outWmsBoxTaskList);
                                return  response;
                            }else {
                                proceedFailureReason = "AGV continueTask失败: " + conResponse.getMessage();
                                log.error("继续任务失败（出库流程-AGV前往放箱），任务编号: {}, 原因: {}", request.getRobotTaskCode(), proceedFailureReason);
                            }
                        } else
                        {
                            proceedFailureReason = "CTU放箱口不允许放箱";
                            log.warn("CTU放箱口当前不允许放箱，无法继续出库任务 {}。请检查输送线状态。{}", taskCode,proceedFailureReason);
                            response.setCode("Error");
                            response.setMessage(proceedFailureReason);
                            return response;
                        }
                    }
                }
                break;
            case "APPLY_TO_AGV":
                // AGV请求出/入库中途的继续指令
                log.warn("任务应用到AGV (APPLY_TO_AGV)，任务编号: {}", request.getRobotTaskCode());
                queryWrapper.eq(WmsBoxTaskList::getTaskOrder, request.getRobotTaskCode());
                // 状态检查应该更灵活，可能任务已分配但等待AGV到达某个点
                //queryWrapper.eq(WmsBoxTaskList::getTaskStatus, enumTaskStatus.execute.getValue());
                WmsBoxTaskList wmsBoxTaskList2 = MainService.mainServiceutil.wmsBoxTaskListMapper.selectOne(queryWrapper);
               // taskType任务类型：0采购入库，1调拨入库，2生产退料回库，3领料出库，4调拨出库，5采购退货出库，6指定出库，7.指定入库，8.紧急出库。10.盘点出库
               if (null != wmsBoxTaskList2) {
                    // 任务状态检查
                    if (!Objects.equals(wmsBoxTaskList2.getTaskStatus(), enumTaskStatus.complete.getValue())) {
                        Integer taskType = wmsBoxTaskList2.getTaskType();
                        EquipmentNotifyRequest equipmentNotifyRequest = new EquipmentNotifyRequest();
                        equipmentNotifyRequest.setTaskCode(request.getRobotTaskCode());
                        equipmentNotifyRequest.setEqptCode(eqptCode);

                        boolean canProceed = false;
                        String proceedFailureReason = "";

                        if (taskType == 0 || taskType == 1 || taskType == 2 || taskType == 7) { // 入库任务
                            log.info("入库任务 ()，任务编号: {}", request.getRobotTaskCode());
                            equipmentNotifyRequest.setActionStatus("5");
                            String ctuTakeBoxCode = MainService.mainServiceutil.stringRedisTemplate.opsForValue().get("ctu:take_box:boxNo");
                            if (ctuTakeBoxCode != null && ctuTakeBoxCode.equals(wmsBoxTaskList2.getBoxNo())) {
                                if (MainService.mainServiceutil.plcInstructionService != null) {
                                    Result<?> setResult = MainService.mainServiceutil.plcInstructionService.setConvCtuTakeStatus(false);
                                    if (!setResult.isSuccess()) {
                                        log.error("调用 PlcInstructionService.setConvCtuTakeStatus 失败: {}，任务: {}", setResult.getMessage(), request.getRobotTaskCode());
                                        // 根据业务决定此失败是否阻止继续，如果PLC控制很重要，则这里应该标记失败
                                        proceedFailureReason = "PLC设置CTU取货口状态失败";
                                        // canProceed 保持 false
                                    } else {
                                        // PLC设置成功，尝试通知AGV
                                        EquipmentNotifyResponse conResponse = continueTask(equipmentNotifyRequest); //尝试2次
                                        if (conResponse.getCode().equals("SUCCESS")) {
                                            canProceed = true;
                                            // 异步更新任务状态
                                            // processAsyncUpdateTaskStatus(wmsBoxTaskList2, enumTaskStatus.allow);
                                            // MainService.mainServiceutil.wmsBoxTaskListMapper.updateById(wmsBoxTaskList2); // 暂时同步，后续可改为异步
                                            wmsBoxTaskList2.setTaskStatus(enumTaskStatus.allow.getValue()); // 更新内存对象状态，如果后续使用
                                            MainService.mainServiceutil.wmsBoxTaskListMapper.updateById(wmsBoxTaskList2);
                                            return response;
                                        } else {
                                            proceedFailureReason = "AGV continueTask失败: " + conResponse.getMessage();
                                            log.error("继续任务失败（入库流程-CTU取货），任务编号: {}, 原因: {}", request.getRobotTaskCode(), conResponse.getMessage());
                                        }
                                    }
                                } else {
                                    proceedFailureReason = "PlcInstructionService未注入";
                                    log.error("PlcInstructionService 未正确注入，无法设置CTU状态，任务: {}", request.getRobotTaskCode());
                                }
                            } else {
                                proceedFailureReason = "输送线取货口箱号不匹配";
                                log.error("输送线取货位值信号的箱号 ({}) 与任务 ({}) 的箱号 ({}) 不一致，任务编号: {}", ctuTakeBoxCode, wmsBoxTaskList2.getTaskOrder(), wmsBoxTaskList2.getBoxNo(), request.getRobotTaskCode());
                                MainService.insertMQ(String.valueOf(ctuTakeBoxCode), null, method + "_error:输送线取货位值信号的箱号跟任务的不一致");

                            }
                        }

                        response.setCode("Error"); // 或AGV协议中表示条件不满足的代码
                        response.setMessage("业务条件不满足或操作失败: " + proceedFailureReason);
                        // 进一步考虑：是否需要将此失败信息通过 MQ 发送出去，用于告警或后续处理
                        // MainService.insertMQ(request.getRobotTaskCode(), null, method + "_error:" + proceedFailureReason);
                    }else {
                        log.warn("任务 () 已完成，任务编号: {}", request.getRobotTaskCode());
                        response.setCode("SUCCESS");
                        response.setMessage("任务已完成");
                    }
                } else {
                     log.warn("任务 () 未找到或状态不正确，任务编号: {}", request.getRobotTaskCode());
                     response.setCode("Error");
                     response.setMessage("任务未找到或状态不正确");
                }
                break; 
            case "end":
                log.warn("机器人任务执行结束，任务编号: {}", request.getRobotTaskCode());
                // 异步处理任务结束的业务逻辑
                LambdaQueryWrapper <WmsBoxTaskList> queryWrapper2 = new LambdaQueryWrapper<>();
                queryWrapper2.eq(WmsBoxTaskList::getTaskOrder, request.getRobotTaskCode());
                 WmsBoxTaskList wmsBoxTaskListList = MainService.mainServiceutil.wmsBoxTaskListMapper.selectOne(queryWrapper2);
                MainService.mainServiceutil.stringRedisTemplate.delete("order:"+request.getRobotTaskCode());
                wmsBoxTaskListList.setTaskStatus(enumTaskStatus.complete.getValue());
                wmsBoxTaskListList.setDeviceType(deviceType);
                wmsBoxTaskListList.setDeviceNo(deviceNo);
                MainService.mainServiceutil.wmsBoxTaskListMapper.updateById(wmsBoxTaskListList);
                if(wmsBoxTaskListList.getBoxType()==2 && wmsBoxTaskListList.getTaskStatus()>4 && wmsBoxTaskListList.getDeleteFlag()<1
                        && (wmsBoxTaskListList.getTaskType()>3 && wmsBoxTaskListList.getTaskType()!=7)){
                    MainService.mainServiceutil.stringRedisTemplate.opsForValue().set(REDIS_LIFT_TRAY_CODE_KEY,wmsBoxTaskListList.getBoxNo());
                }

        // 直接记录任务到上报队列，无需Redis事件
        recordTaskToReportQueue(wmsBoxTaskListList);
        // ============================================
                taskEnd(wmsBoxTaskListList);
                break;
                
            default:
                log.warn("未知的任务执行阶段: {}", method);
                // 根据实际需求处理未知阶段

                break;
        }
        
        // 返回响应，告知机器人系统是否继续执行
        return response;
        
    } catch (Exception e) {
        log.error("处理任务执行通知异常: {}", e.getMessage(), e);
        TaskExecutionNoticeResponse response = new TaskExecutionNoticeResponse();
        response.setCode("Error");
        response.setMessage("处理任务执行通知异常: " + e.getMessage());
        return response;
    }
}


    /**
     * 把装箱入库结果推送到OA
     */
    private void taskCompletionReport(String taskCode)
    {
        try {
            LambdaQueryWrapper<WmsBoxTaskList> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WmsBoxTaskList::getTaskStatus, 5);
            queryWrapper.ge(WmsBoxTaskList::getPushStatus, 0);
            queryWrapper.eq(WmsBoxTaskList::getDeleteFlag, 0);
            queryWrapper.eq(WmsBoxTaskList::getMaterialType,1);
            queryWrapper.eq(WmsBoxTaskList::getTaskOrder,taskCode);
            queryWrapper.orderByAsc(WmsBoxTaskList::getCreateTime);
            WmsBoxTaskList taskInfo = MainService.mainServiceutil.wmsBoxTaskListMapper.selectOne(queryWrapper.last("LIMIT 1"));
            if(taskInfo != null)
            {
                try {
                    if (stringRedisTemplate != null && taskCode != null) {
                        boolean hasKey = stringRedisTemplate.hasKey(taskCode);
                        if (hasKey) {
                            String responseJson = stringRedisTemplate.opsForValue().get(taskCode);
                            log.info("任务 {} 的Redis返回值: {}", taskCode, responseJson);
                            if (responseJson != null && !responseJson.isEmpty()) {
                                // OARequest请求处理注释掉的代码省略.
                                boolean status=OAService.submitOA(responseJson);
                                if(status){
                                    stringRedisTemplate.delete(taskCode);
                                    taskInfo.setPushStatus(1);
                                    MainService.mainServiceutil.wmsBoxTaskListMapper.updateById(taskInfo);
                                }else{
                                    log.error("任务："+taskCode+"上报OA异常");
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("处理任务{}时出现异常", taskCode, e);
                }

            } else {
                log.info("没有待上报的完成任务"+taskCode);
            }
        } catch (Exception e) {
            log.error("任务完成报告处理异常", e);
        }
    }

    /**
     *任务结束后逻辑处理
     * @param wmsBoxTaskListList
     */
    private void taskEnd(WmsBoxTaskList wmsBoxTaskListList)
    {
        log.debug("任务结束，更新库位信息："+JSON.toJSONString(wmsBoxTaskListList));
        try{
            switch (wmsBoxTaskListList.getTaskType()){
                case 0:
                case 1:
                case 2:
                case 7:
                    inShelf(wmsBoxTaskListList);
                    break;
                case 3:
                case 4:
                case 5:
                case 6:
                case 8:
                case 10:
                    outShelf(wmsBoxTaskListList);
                default:
                    break;
            }
        }catch (Exception ex){
            log.error("更新库位失败："+JSON.toJSONString(wmsBoxTaskListList));
        }
    }

    /**
     * 更新出库任务库位
     * @param wmsBoxTaskListList
     */
    private void outShelf(WmsBoxTaskList wmsBoxTaskListList)
    {
        log.debug("出库任务结束，更新库位信息："+JSON.toJSONString(wmsBoxTaskListList));
        try{
            QueryWrapper<Shelf> shelfQueryWrapper=new QueryWrapper<>();
            shelfQueryWrapper.eq("code",wmsBoxTaskListList.getFromSite());
            Shelf shelf=MainService.mainServiceutil.shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
            if(shelf!=null) {
                shelf.setLocked(0);
                shelf.setState(0);
                shelf.setBoxNo("");
                MainService.mainServiceutil.shelfMapper.updateById(shelf);
            }else {
                log.error("找不到"+wmsBoxTaskListList.getFromSite()+"的库位信息");
            }
        }catch (Exception ex){
            log.error("更新出库任务库位信息异常："+JSON.toJSONString(wmsBoxTaskListList));
        }
    }

    /**
     * 更新入库任务库位
     * @param wmsBoxTaskListList
     */
    private void inShelf(WmsBoxTaskList wmsBoxTaskListList)
    {
        log.debug("入库任务结束，更新库位信息："+JSON.toJSONString(wmsBoxTaskListList));
        try{
            QueryWrapper<Shelf> shelfQueryWrapper=new QueryWrapper<>();
            shelfQueryWrapper.eq("code",wmsBoxTaskListList.getToSite());
            Shelf shelf=MainService.mainServiceutil.shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
            if(shelf!=null) {
                shelf.setLocked(0);
                shelf.setState(1);
                shelf.setBoxNo(wmsBoxTaskListList.getBoxNo());
                MainService.mainServiceutil.shelfMapper.updateById(shelf);
            }else {
                log.error("找不到"+wmsBoxTaskListList.getToSite()+"的库位信息");
            }
        }catch (Exception ex){
            log.error("更新出库任务库位信息异常："+JSON.toJSONString(wmsBoxTaskListList));
        }
    }

// 辅助方法：尝试调用 continueTask，带重试次数
private EquipmentNotifyResponse continueTask(EquipmentNotifyRequest request) {
    log.info("开始执行继续任务请求: taskCode={}, eqptCode={}, actionStatus={}", 
             request.getTaskCode(), request.getEqptCode(), request.getActionStatus());
    
    EquipmentNotifyResponse lastResponse = null;
    Exception lastException = null;
    
    for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
        try {
            log.debug("继续任务第 {}/{} 次尝试: taskCode={}", attempt, MAX_RETRY_ATTEMPTS, request.getTaskCode());
            
            // 调用外设执行通知接口
            EquipmentNotifyResponse response = handleEquipmentNotify(request);
            
            // 检查响应是否为空
            if (response == null) {
                log.warn("继续任务第 {}/{} 次尝试响应为空: taskCode={}", attempt, MAX_RETRY_ATTEMPTS, request.getTaskCode());
                lastResponse = createErrorResponse("EMPTY_RESPONSE", "外设执行通知响应为空");
                continue;
            }
            
            // 检查响应码是否表示成功
            if (isSuccessResponse(response)) {
                // 标准化成功响应码
                response.setCode("SUCCESS");
                log.info("继续任务执行成功: taskCode={}, attempt={}/{}, responseCode={}", 
                        request.getTaskCode(), attempt, MAX_RETRY_ATTEMPTS, response.getCode());
                return response;
            } else {
                // 业务逻辑失败
                log.warn("继续任务第 {}/{} 次尝试业务失败: taskCode={}, responseCode={}, message={}", 
                        attempt, MAX_RETRY_ATTEMPTS, request.getTaskCode(), response.getCode(), response.getMessage());
                
                // 对于业务逻辑错误，通常不需要重试，直接返回
                if (isBusinessLogicError(response.getCode())) {
                    log.info("检测到业务逻辑错误，不进行重试: taskCode={}, responseCode={}", 
                            request.getTaskCode(), response.getCode());
                    return response;
                }
                
                lastResponse = response;
            }
            
        } catch (Exception e) {
            lastException = e;
            log.error("继续任务第 {}/{} 次尝试发生异常: taskCode={}, error={}", 
                     attempt, MAX_RETRY_ATTEMPTS, request.getTaskCode(), e.getMessage(), e);
            
            // 创建异常响应对象
            lastResponse = createErrorResponse("INTERNAL_ERROR", "继续任务执行异常: " + e.getMessage());
        }
        
        // 如果不是最后一次尝试，等待后重试
        if (attempt < MAX_RETRY_ATTEMPTS) {
            log.debug("等待 {}ms 后进行第 {} 次重试: taskCode={}", RETRY_DELAY_MS, attempt + 1, request.getTaskCode());
            try {
                Thread.sleep(RETRY_DELAY_MS);
            } catch (InterruptedException ie) {
                log.warn("重试等待被中断: taskCode={}", request.getTaskCode());
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    // 所有重试都失败了
    log.error("继续任务所有重试均失败: taskCode={}, maxAttempts={}", request.getTaskCode(), MAX_RETRY_ATTEMPTS);
    
    // 返回最后一次的响应，如果没有响应则创建默认错误响应
    if (lastResponse != null) {
        return lastResponse;
    } else if (lastException != null) {
        return createErrorResponse("RETRY_EXHAUSTED", "所有重试均失败，最后异常: " + lastException.getMessage());
    } else {
        return createErrorResponse("UNKNOWN_ERROR", "继续任务失败，原因未知");
    }
}

/**
 * 检查响应是否表示成功
 * @param response 响应对象
 * @return 是否成功
 */
private boolean isSuccessResponse(EquipmentNotifyResponse response) {
    if (response == null || response.getCode() == null) {
        return false;
    }
    return SUCCESS_CODES.contains(response.getCode());
}

/**
 * 检查是否为业务逻辑错误（通常不需要重试的错误）
 * @param responseCode 响应码
 * @return 是否为业务逻辑错误
 */
private boolean isBusinessLogicError(String responseCode) {
    if (responseCode == null) {
        return false;
    }
    
    // 定义不需要重试的业务错误码
    Set<String> businessErrorCodes = new HashSet<>(Arrays.asList(
        "INVALID_TASK",      // 无效任务
        "TASK_NOT_FOUND",    // 任务不存在
        "INVALID_PARAMETER", // 参数无效
        "PERMISSION_DENIED", // 权限拒绝
        "BUSINESS_RULE_VIOLATION" // 业务规则违反
    ));
    
    return businessErrorCodes.contains(responseCode);
}

/**
 * 创建错误响应对象
 * @param errorCode 错误码
 * @param errorMessage 错误消息
 * @return 错误响应对象
 */
private EquipmentNotifyResponse createErrorResponse(String errorCode, String errorMessage) {
    EquipmentNotifyResponse response = new EquipmentNotifyResponse();
    response.setCode(errorCode);
    response.setMessage(errorMessage);
    return response;
}

/**
 * 预调度接口 - 将机器人预调度到指定站点
 * 
 * @return 预调度响应结果
 */
public PreTaskResponse preTask(PreTaskRequest request) {
    // 检查AGV服务状态
    if (!hikWorking) {
        log.error("海康AGV调度服务未启用或配置不正确");
        PreTaskResponse response = new PreTaskResponse();
        response.setCode("Err_Exception");
        response.setMessage("海康AGV调度服务未启用或配置不正确");
        return response;
    }


    try {
        // 构建API URL
        String apiUrl = baseUrl + API_TASK_PREFIX_PATH + PRE_TASK_INTERFACE_NAME;

        // 生成请求ID
        String requestId = generateUUID();

       
        // 请求JSON
        String requestJson = JSON.toJSONString(request);

        // 创建HTTP头
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put(HEADER_X_lR_REQUEST_ID, requestId);
        headers.put(HEADER_X_lR_VERSION, API_VERSION_V4_1);

        // 记录请求日志
        log.info("AGV预调度请求地址 (V4.1): {}", apiUrl);
        log.info("AGV预调度请求头: {}", JSON.toJSONString(headers));
        log.info("AGV预调度请求体: {}", requestJson);

        // 发送请求
        String responseJson = httpUtils.httpsPost(apiUrl, headers, requestJson);

        // 记录响应日志
        log.info("AGV预调度响应: {}", responseJson);

        // 解析响应
        if (responseJson != null && !responseJson.isEmpty()) {
            // 如果响应码为200，则返回成功
            if (JSON.parseObject(responseJson, PreTaskResponse.class).getCode().equals("SUCCESS")) {
                // 通知输送线释放
                MainService.mainServiceutil.plcInstructionService.requestConvLiftRelease(true);
            }
            return JSON.parseObject(responseJson, PreTaskResponse.class);
        } else {
            log.error("AGV预调度响应为空");
            PreTaskResponse response = new PreTaskResponse();
            response.setCode("Err_Internal");
            response.setMessage("AGV预调度响应为空");
            return response;
        }
    } catch (Exception e) {
        log.error("预调度AGV任务异常: {}", e.getMessage(), e);
        PreTaskResponse response = new PreTaskResponse();
        response.setCode("Err_Internal");
        response.setMessage("预调度AGV任务异常: " + e.getMessage());
        return response;
    }
}

/**
 * 记录完成的任务到上报队列
 * 只有采购入库（taskType=0）和领料出库（taskType=3）才需要上报
 * 
 * @param taskInfo 完成的任务信息
 */
private void recordTaskToReportQueue(WmsBoxTaskList taskInfo) {
    try {
        // 只处理特定类型的任务：0.采购入库，3.领料出库
        if (taskInfo.getTaskType() == null) {
            log.debug("任务类型为空，跳过上报队列记录: taskOrder={}", taskInfo.getTaskOrder());
            return;
        }
        
        String reportType;
        if (taskInfo.getTaskType() == 0) {
            // 0. 采购入库
            reportType = "INBOUND";
        } else if (taskInfo.getTaskType() == 3) {
            // 3. 领料出库  
            reportType = "OUTBOUND";
        } else {
            // 其他任务类型不需要上报
            log.debug("任务类型[{}]不需要上报，跳过记录: taskOrder={}, taskTypeDesc={}", 
                     taskInfo.getTaskType(), taskInfo.getTaskOrder(), getTaskTypeDesc(taskInfo.getTaskType()));
            return;
        }
        
        // 创建上报队列记录
        com.tgvs.wms.business.modules.task.entity.TaskReportQueue reportQueue = 
            new com.tgvs.wms.business.modules.task.entity.TaskReportQueue();
        
        // 设置基本信息
        reportQueue.setTaskOrder(taskInfo.getTaskOrder());
        reportQueue.setTaskType(taskInfo.getTaskType());
        reportQueue.setBoxNo(taskInfo.getBoxNo());
        reportQueue.setBoxType(taskInfo.getBoxType());
        reportQueue.setMaterialType(taskInfo.getMaterialType());
        
        // 设置上报信息
        reportQueue.setReportType(reportType);
        reportQueue.setReportStatus(0); // 待上报
        reportQueue.setPriority(5); // 普通优先级
        reportQueue.setRetryCount(0);
        reportQueue.setMaxRetryCount(3);
        
        // 设置时间信息
        Date now = new Date();
        reportQueue.setCreateTime(now);
        reportQueue.setUpdateTime(now);
        
        // 设置系统字段
        reportQueue.setVersion(1);
        reportQueue.setDeleteFlag(0);
        
        // 保存到数据库
        try {
            // 检查是否已存在相同任务的记录，避免重复插入
            LambdaQueryWrapper<com.tgvs.wms.business.modules.task.entity.TaskReportQueue> checkWrapper = 
                new LambdaQueryWrapper<>();
            checkWrapper.eq(com.tgvs.wms.business.modules.task.entity.TaskReportQueue::getTaskOrder, 
                           taskInfo.getTaskOrder())
                       .eq(com.tgvs.wms.business.modules.task.entity.TaskReportQueue::getDeleteFlag, 0);
            
            // 通过Service检查记录
            long existingCount = taskReportQueueService.count(checkWrapper);
            
            if (existingCount > 0) {
                log.debug("任务上报队列记录已存在，跳过插入: taskOrder={}", taskInfo.getTaskOrder());
                return;
            }
            
            // 插入新记录
            boolean insertResult = taskReportQueueService.save(reportQueue);
            
            if (insertResult) {
                log.info("成功记录任务到上报队列: 任务单号={}, 任务类型={}, 上报类型={}, 箱号={}", 
                        reportQueue.getTaskOrder(), getTaskTypeDesc(reportQueue.getTaskType()), 
                        reportQueue.getReportType(), reportQueue.getBoxNo());
            } else {
                log.error("记录任务到上报队列失败: taskOrder={}", taskInfo.getTaskOrder());
            }
            
        } catch (Exception e) {
            log.error("保存任务上报队列记录异常: 任务单号={}, 错误={}", 
                     taskInfo.getTaskOrder(), e.getMessage(), e);
        }
        
    } catch (Exception e) {
        log.error("记录任务到上报队列失败: 任务单号={}, 错误={}", 
                 taskInfo.getTaskOrder(), e.getMessage(), e);
    }
}

/**
 * 获取任务类型描述
 * @param taskType 任务类型编码
 * @return 任务类型描述
 */
private String getTaskTypeDesc(Integer taskType) {
    if (taskType == null) {
        return "未知";
    }
    
    switch (taskType) {
        case 0: return "采购入库";
        case 1: return "调拨入库";
        case 2: return "生产退料回库";
        case 3: return "领料出库";
        case 4: return "调拨出库";
        case 5: return "采购退货出库";
        case 6: return "指定出库";
        case 7: return "指定入库";
        case 8: return "紧急出库";
        case 10: return "盘点出库";
        default: return "未知类型(" + taskType + ")";
    }
}

}
