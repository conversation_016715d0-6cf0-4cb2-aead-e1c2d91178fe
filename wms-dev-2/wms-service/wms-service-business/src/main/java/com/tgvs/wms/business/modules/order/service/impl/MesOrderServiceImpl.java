package com.tgvs.wms.business.modules.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.order.entity.MesBox;
import com.tgvs.wms.business.modules.order.entity.MesOrder;
import com.tgvs.wms.business.modules.order.mapper.MesOrderMapper;
import com.tgvs.wms.business.modules.order.service.MesBoxService;
import com.tgvs.wms.business.modules.order.service.MesOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class MesOrderServiceImpl extends BaseServiceImpl<MesOrderMapper, MesOrder> implements MesOrderService {

    @Lazy
    @Autowired
    MesBoxService mesBoxService;
    @Override
    public boolean out(List<String> bookNoList,String siteNo) {
        List<MesBox> mesBoxList=new ArrayList<>();
        LambdaQueryWrapper<MesBox> boxWrapper =new LambdaQueryWrapper<>();
        for (String bookNo : bookNoList) {
            lambdaUpdate().le(MesOrder::getBookNo,bookNo)
                    .set(MesOrder::getStatus,1)
                    .set(BaseEntity::getUpdateTime,new Date())
                    .update();
            boxWrapper.like(MesBox::getBookNo,bookNo);
            List<MesBox> list = mesBoxService.list(boxWrapper);
            boxWrapper.clear();
            mesBoxList.addAll(list);
        }
        List<Map<String,String>> list=new ArrayList<>();
        int sortNo=0;
        for (MesBox mesBox : mesBoxList) {
            sortNo++;
            Map<String,String> map=new HashMap<>();
            map.put("BoxNo",mesBox.getBoxNo());
            map.put("SortNo", String.valueOf(sortNo));
            map.put("BoxType",mesBox.getBoxType());
            map.put("MainBoxNo","");
            list.add(map);
        }
        Map<String,Object> order=new HashMap<>();
        order.put("orderNo",getOrderNo());
        order.put("SiteNo",siteNo);
        order.put("Priority",orderNo);
        order.put("UserNo","admin");
        order.put("SubBoxList",list);
        System.err.println(JSON.toJSON(order));
        return true;
    }
    private int orderNo=1;
    private int getOrderNo(){
        if (orderNo<100){
            return orderNo++;
        }
        return orderNo=1;
    }
}
