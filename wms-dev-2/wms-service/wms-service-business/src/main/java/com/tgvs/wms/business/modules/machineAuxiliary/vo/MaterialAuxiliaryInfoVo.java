package com.tgvs.wms.business.modules.machineAuxiliary.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

//辅料基础信息Vo
@Data
@Schema(description = "辅料基础信息Vo")
public class MaterialAuxiliaryInfoVo implements Serializable {
    //辅料编码
    @NotBlank(message = "辅料编码不能为空")
    @Schema(description = "辅料编码", required = true)
    private String materialCode;
    //辅料名称
    @NotBlank(message = "辅料名称不能为空")
    @Schema(description = "辅料名称", required = true)
    private String materialName;
    //是否入智能仓
    @NotNull(message = "是否入智能仓不能为空")
    @Schema(description = "是否入智能仓", required = true)
    private Integer isStore;
    //辅料类型
    @Schema(description = "辅料类型（1.辅料；2.面料；3.成品）",example = "1")
    private Integer materialType;
    //操作类型（1.新增；2.修改）
    @Schema(description = "操作类型（1.新增；2.修改）",example = "1")
    private Integer operationType=1;
}
