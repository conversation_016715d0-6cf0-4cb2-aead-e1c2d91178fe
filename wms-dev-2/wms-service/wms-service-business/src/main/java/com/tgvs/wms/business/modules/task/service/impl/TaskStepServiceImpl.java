package com.tgvs.wms.business.modules.task.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.task.entity.TaskStep;
import com.tgvs.wms.business.modules.task.mapper.TaskStepMapper;
import com.tgvs.wms.business.modules.task.service.ITaskStepService;

@Service
public class TaskStepServiceImpl extends BaseServiceImpl<TaskStepMapper, TaskStep> implements ITaskStepService {
}
