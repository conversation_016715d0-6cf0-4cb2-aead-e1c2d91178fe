package com.tgvs.wms.business.enums;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumRouter {
    unknown(Integer.valueOf(0), "unknown", "未知"),
    conveyor(Integer.valueOf(1), "conveyor", "输送"),
    lift(Integer.valueOf(2), "lift", "换楼层"),
    binding(Integer.valueOf(3), "binding", "绑定"),
    bindingPiece(Integer.valueOf(4), "binding<PERSON><PERSON><PERSON>", "花片站"),
    bindingCross(Integer.valueOf(5), "bindingCross", "衣片辅料站"),
    bindingMatching(Integer.valueOf(6), "bindingMatching", "对包站"),
    pick(Integer.valueOf(7), "pick", "搬运");

    private Integer value;

    private String code;

    private String text;

    enumRouter(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumRouter getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumRouter val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumRouter toEnum(Integer Value) {
        for (enumRouter e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return unknown;
    }
}
