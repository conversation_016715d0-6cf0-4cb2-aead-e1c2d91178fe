package com.tgvs.wms.business.modules.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.order.entity.MesOrder;
import com.tgvs.wms.business.modules.order.service.MesOrderService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@Api(tags = {"工单"})
@RestController
@RequestMapping("mes/order")
public class MesOrderController extends BaseController<MesOrder, MesOrderService> {
    @ApiOperation(value = "工单-分页列表查询", notes = "工单-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<MesOrder> pageList = service.pageList(queryModel);
        Result<List<MesOrder>> result = Result.ok(pageList.getRecords());
        result.setTotal(pageList.getTotal());
        return result;
    }

    @AutoLog("工单-添加")
    @ApiOperation(value = "工单-添加", notes = "工单-添加")
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody MesOrder order) {
        this.service.save(order);
        return Result.OK("添加成功！");
    }

    @AutoLog("工单-编辑")
    @ApiOperation(value = "工单-编辑", notes = "工单-编辑")
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody MesOrder mesOrder) {
        this.service.updateById(mesOrder);
        return Result.OK("编辑成功!");
    }

    @AutoLog("工单-通过id删除")
    @ApiOperation(value = "工单-通过id删除", notes = "工单-通过id删除")
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.service.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("工单-批量删除")
    @ApiOperation(value = "工单-批量删除", notes = "工单-批量删除")
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.service.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("工单-通过id查询")
    @ApiOperation(value = "工单-通过id查询", notes = "工单-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        MesOrder mesOrder = (MesOrder) this.service.getById(id);
        if (mesOrder == null)
            return Result.error("未找到对应数据");
        return Result.OK(mesOrder);
    }

    @AutoLog("工单-通过订单编号出库")
    @ApiOperation(value = "工单-通过订单编号出库", notes = "工单-通过订单编号出库")
    @PostMapping({"/out"})
    public Result<?> out(@RequestBody List<String> bookNoList,String siteNo) {
        boolean out = this.service.out(bookNoList,siteNo);
        if (!out)
            return Result.error("未找到对应数据");
        return Result.OK("出库中");
    }

    @AutoLog("工单-出库完成通知接口")
    @ApiOperation(value = "工单-出库完成通知接口", notes = "工单-出库完成通知接口")
    @PostMapping({"/accomplish"})
    public Result<?> accomplish(@RequestBody List<String> bookNoList) {
        boolean update = this.service.lambdaUpdate().in(MesOrder::getBookNo, bookNoList)
                .set(MesOrder::getStatus, 2)
                .update();
        if (!update)
            return Result.error("未找到对应数据");
        return Result.OK("出库成功");
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, MesOrder mesOrder) {
        return exportXls(request, mesOrder, MesOrder.class, "工单");
    }

    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, MesOrder.class);
    }
}
