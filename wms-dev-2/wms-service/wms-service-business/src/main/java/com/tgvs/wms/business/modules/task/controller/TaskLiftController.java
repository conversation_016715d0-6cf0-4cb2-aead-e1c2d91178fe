package com.tgvs.wms.business.modules.task.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.enums.enumTaskStatus;
import com.tgvs.wms.business.modules.task.entity.TaskBox;
import com.tgvs.wms.business.modules.task.entity.TaskLift;
import com.tgvs.wms.business.modules.task.service.ITaskLiftService;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.business.wmsservice.server.LiftService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"提升机任务管理"})
@RestController
@RequestMapping({"/task/taskLift"})
@Slf4j
public class TaskLiftController extends BaseController<TaskLift, ITaskLiftService> {

    @Autowired
    private ITaskLiftService taskLiftService;

    @ApiOperation(value = "提升机任务管理-分页列表查询", notes = "提升机任务管理-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<TaskLift> pageList = taskLiftService.pageList(queryModel);
        if (pageList.getTotal() > 0L && pageList.getRecords().size() > 0)
            for (TaskLift lift : pageList.getRecords()) {
                Point point = MainService.getPoint(lift.getToSite());
                if (null != point)
                    lift.setOccupy(point.getOccupy());
            }
        Result result = Result.ok(pageList.getRecords());
        result.setTotal(pageList.getTotal());
        return result;
    }

    @AutoLog("提升机任务管理-添加")
    @ApiOperation(value = "提升机任务管理-添加", notes = "提升机任务管理-添加")
    @RequiresPermissions({"taskLift:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody TaskLift taskLift) {
        this.taskLiftService.save(taskLift);
        return Result.OK("添加成功！");
    }

    @AutoLog("提升机任务管理-编辑")
    @ApiOperation(value = "提升机任务管理-编辑", notes = "提升机任务管理-编辑")
    @RequiresPermissions({"taskLift:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody TaskLift taskLift) {
        this.taskLiftService.updateById(taskLift);
        return Result.OK("编辑成功!");
    }

    @AutoLog("提升机任务管理-通过id删除")
    @ApiOperation(value = "提升机任务管理-通过id删除", notes = "提升机任务管理-通过id删除")
    @RequiresPermissions({"taskLift:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestBody String id) {
        this.taskLiftService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("提升机任务管理-批量删除")
    @ApiOperation(value = "提升机任务管理-批量删除", notes = "提升机任务管理-批量删除")
    @RequiresPermissions({"taskLift:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestBody String ids) {
        this.taskLiftService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("提升机任务管理-通过id查询")
    @ApiOperation(value = "提升机任务管理-通过id查询", notes = "提升机任务管理-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestBody String id) {
        TaskLift taskLift = (TaskLift)this.taskLiftService.getById(id);
        if (taskLift == null)
            return Result.error("未找到对应数据");
        return Result.OK(taskLift);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, TaskLift taskLift) {
        return exportXls(request, taskLift, TaskLift.class, "提升机任务管理");
    }

    @RequiresPermissions({"taskLift:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, TaskLift.class);
    }

    @AutoLog(value = "提升机任务管理-手动完成作业", operateType = 4)
    @ApiOperation(value = "提升机任务管理-手动完成作业", notes = "提升机任务管理-手动完成作业")
    @RequiresPermissions({"taskLift:complete"})
    @PostMapping({"/complete"})
    public Result<?> complete(@RequestBody TaskLift taskLift) {
        TaskLift taskLiftold = (TaskLift)this.taskLiftService.getById(taskLift.getId());
        if (taskLiftold == null)
            return Result.error("未找到对应数据");
        if (taskLiftold.getState().equals(enumTaskStatus.execute.getValue())) {
            LiftService.handComplete(taskLiftold);
        } else {
            return Result.error("不是执行的作业，不能手动完成");
        }
        return Result.OK("手动完成作业成功!");
    }
}