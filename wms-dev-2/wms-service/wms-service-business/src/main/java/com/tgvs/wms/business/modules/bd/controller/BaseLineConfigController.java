package com.tgvs.wms.business.modules.bd.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.BaseLineConfig;
import com.tgvs.wms.business.modules.bd.service.IBaseLineConfigService;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"入库参考线设置"})
@RestController
@RequestMapping({"/bd/baseLineConfig"})
@Slf4j
public class BaseLineConfigController extends BaseController<BaseLineConfig, IBaseLineConfigService> {


    @Autowired
    private IBaseLineConfigService baseLineConfigService;

    @AutoLog("入库参考线设置-分页列表查询")
    @ApiOperation(value = "入库参考线设置-分页列表查询", notes = "入库参考线设置-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(BaseLineConfig baseLineConfig, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<BaseLineConfig> queryWrapper = QueryGenerator.initQueryWrapper(baseLineConfig, req.getParameterMap());
        Page<BaseLineConfig> page = new Page(pageNo.intValue(), pageSize.intValue());
        IPage<BaseLineConfig> pageList = this.baseLineConfigService.page((IPage)page, (Wrapper)queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog("入库参考线设置-添加")
    @ApiOperation(value = "入库参考线设置-添加", notes = "入库参考线设置-添加")
    @RequiresPermissions({"baseLineConfig:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody BaseLineConfig baseLineConfig) {
        this.baseLineConfigService.save(baseLineConfig);
        return Result.OK("添加成功！");
    }

    @AutoLog("入库参考线设置-编辑")
    @ApiOperation(value = "入库参考线设置-编辑", notes = "入库参考线设置-编辑")
    @RequiresPermissions({"baseLineConfig:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody BaseLineConfig baseLineConfig) {
        this.baseLineConfigService.updateById(baseLineConfig);
        try {
            MainService.inboundColumns = baseLineConfig.getValue();
        } catch (Exception exception) {}
        return Result.OK("编辑成功!");
    }

    @AutoLog("入库参考线设置-通过id删除")
    @ApiOperation(value = "入库参考线设置-通过id删除", notes = "入库参考线设置-通过id删除")
    @RequiresPermissions({"baseLineConfig:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.baseLineConfigService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("入库参考线设置-批量删除")
    @ApiOperation(value = "入库参考线设置-批量删除", notes = "入库参考线设置-批量删除")
    @RequiresPermissions({"baseLineConfig:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.baseLineConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("入库参考线设置-通过id查询")
    @ApiOperation(value = "入库参考线设置-通过id查询", notes = "入库参考线设置-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        BaseLineConfig baseLineConfig = (BaseLineConfig)this.baseLineConfigService.getById(id);
        if (baseLineConfig == null)
            return Result.error("未找到对应数据");
        return Result.OK(baseLineConfig);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, BaseLineConfig baseLineConfig) {
        return exportXls(request, baseLineConfig, BaseLineConfig.class, "入库参考线设置");
    }

    @RequiresPermissions({"baseLineConfig:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, BaseLineConfig.class);
    }
}
