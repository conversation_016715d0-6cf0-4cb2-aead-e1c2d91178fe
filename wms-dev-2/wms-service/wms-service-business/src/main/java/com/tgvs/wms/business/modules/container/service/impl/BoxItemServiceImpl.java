package com.tgvs.wms.business.modules.container.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.httpservice.baseBean.agv.OARequest;
import com.tgvs.wms.business.modules.container.Dto.materialOutBoundDetailDTO;
import com.tgvs.wms.business.modules.container.dto.*;
import com.tgvs.wms.business.modules.container.entity.BoxItem;
import com.tgvs.wms.business.modules.container.entity.Container;
import com.tgvs.wms.business.modules.container.entity.WmsMachineMaterialsBoxPrePacking;
import com.tgvs.wms.business.modules.container.mapper.BoxItemMapper;
import com.tgvs.wms.business.modules.container.mapper.ContainerMapper;
import com.tgvs.wms.business.modules.container.mapper.WmsMachineMaterialsBoxPrePackingMapper;
import com.tgvs.wms.business.modules.container.service.IBoxItemService;
import com.tgvs.wms.business.modules.machineMaterials.dto.InBoundDetailsListDto;
import com.tgvs.wms.business.modules.machineMaterials.dto.MachineMaterialInBoundDetailsDto;
import com.tgvs.wms.business.modules.machineMaterials.dto.MachineMaterialsBoxPrePackingDto;
import com.tgvs.wms.business.modules.machineMaterials.dto.PreDetailsDto;
import com.tgvs.wms.business.modules.machineMaterials.entity.*;
import com.tgvs.wms.business.modules.machineMaterials.mapper.*;
import com.tgvs.wms.business.modules.storage.entity.Shelf;
import com.tgvs.wms.business.modules.storage.entity.Stock;
import com.tgvs.wms.business.modules.storage.entity.WmsStation;
import com.tgvs.wms.business.modules.storage.mapper.ShelfMapper;
import com.tgvs.wms.business.modules.storage.mapper.StockMapper;
import com.tgvs.wms.business.modules.storage.mapper.WmsStationMapper;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import com.tgvs.wms.business.modules.task.mapper.WmsBoxTaskListMapper;
import com.tgvs.wms.business.modules.task.service.IWmsBoxTaskListService;
import com.tgvs.wms.business.wmsservice.server.OAService;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.util.DateUtils;
import com.tgvs.wms.common.util.ShiroUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BoxItemServiceImpl  extends BaseServiceImpl<BoxItemMapper, BoxItem> implements IBoxItemService {



    @Autowired
    private MaterialInfoMapper wmsMaterialInfoMapper;
    @Autowired
    private BoxItemMapper boxItemMapper;
    @Autowired
    private ContainerMapper containerMapper;
    @Autowired
    private WmsMachineMaterialsBoxPrePackingMapper wmsMachineMaterialsBoxPrePackingMapper;
    @Autowired
    private WmsMachineInListMapper wmsMachineInListMapper;

    @Autowired
    private WmsBoxTaskListMapper wmsBoxTaskListMapper;
    @Autowired
    private ShelfMapper shelfMapper;
    @Autowired
    private WmsStationMapper wmsStationMapper;

    @Autowired
    private StockMapper stockMapper;

    @Autowired
    private WmsMachineOutDetailListMapper wmsMachineOutDetailListMapper;
    @Autowired
    private WmsMachineOutDetailRecordMapper wmsMachineOutDetailRecordMapper;

    @Autowired
    private IWmsBoxTaskListService wmsBoxTaskListService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private WmsMachineOutboundMapper wmsMachineOutboundMapper;

    /** 拣货工位料架条码，存储当前拣货工位上料架的编码 */
    public static final String REDIS_LIFT_TRAY_CODE_KEY = "lift:tray_code";

    /** 料箱释放状态，控制输送线上料箱的释放 */
    public static final String REDIS_BOX_RELEASE_STATUS_KEY = "box:release:status";

    //    private Cache<String, String> cache;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inBound(List<MachineMaterialInboundDTO> model)
    {
        boolean result=false;
        try {
            //上报OA测试
//            OARequest request=new OARequest();
//            request.setActionName("采购入库");
//            request.setInputjson(JSON.toJSONString(model));
//            OAService.submitOA(request);

            for (MachineMaterialInboundDTO item : model)
            {
                //判断是否已经预装
                QueryWrapper<WmsMachineInList> query = new QueryWrapper<>();
                query.eq("id", item.getId());
                query.eq("status", 1);
                //机物料基础信息
                List<WmsMachineInList> wmsMachineInInfo = wmsMachineInListMapper.selectList(query);
                if (wmsMachineInInfo.isEmpty())
                {
                    //1.基础信息表查询对应的箱型以及容量
                    QueryWrapper<WmsMachineInfo> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("material_code", item.getMaterialCode());
                    //机物料基础信息
                    WmsMachineInfo existingMaterial = wmsMaterialInfoMapper.selectOne(queryWrapper.last("LIMIT 1"));

                    //判断机物料入库类型
                    if (existingMaterial!=null&&existingMaterial.getPriorityContainerType() <= 8)
                    {
                        //判断入库数量是否超过两个箱子容量，如果超过就装料架
//                        if (item.getInQuantity() / existingMaterial.getCapacity() <= 2)
//                        {
                        //判断是否有机物料预装明细
                        List<WmsMachineMaterialsBoxPrePacking> machineMaterialsBoxPrePackingList = wmsMachineMaterialsBoxPrePackingMapper.selectMaterialCode(1);
                        if (machineMaterialsBoxPrePackingList == null || machineMaterialsBoxPrePackingList.isEmpty()) {
                            //判断是否有相同机物料库存明细
                            result=IsBoxStockExist(item, existingMaterial);
                            if(!result){
                                log.error("预装失败:" + JSON.toJSONString(item));
                                result=false;
                            }
                        } else
                        {
                            //预装表
                            //获取相同机物料预装明细
                            List<WmsMachineMaterialsBoxPrePacking> machineMaterialsPrePackingList = machineMaterialsBoxPrePackingList.stream()
                                    .filter(WmsMachineMaterialsBoxPrePacking -> WmsMachineMaterialsBoxPrePacking != null &&
                                            WmsMachineMaterialsBoxPrePacking.getMaterialCode() != null &&
                                            item.getMaterialCode() != null &&
                                            WmsMachineMaterialsBoxPrePacking.getMaterialCode().equals(item.getMaterialCode()) &&
                                            WmsMachineMaterialsBoxPrePacking.getGridStatus() != null &&
                                            WmsMachineMaterialsBoxPrePacking.getGridStatus() == 2)
                                    .collect(Collectors.toList());
                            //是否有相同机物料预装明细
                            if (machineMaterialsPrePackingList.isEmpty()) {
                                //没有相同机物料预装明细,在判断是否有机物料库存
                                result=IsBoxStockExist(item, existingMaterial);
                                if(!result){
                                    log.error("预装失败:" + JSON.toJSONString(item));
                                    result=false;
                                }
                            } else
                            {
                                //求和
                                List<WmsMachineMaterialsBoxPrePacking> preList = machineMaterialsPrePackingList.stream()
                                        .collect(Collectors.toMap(WmsMachineMaterialsBoxPrePacking::getGridCode, a -> a, (o1, o2) -> {
                                            o1.setPendingQuantity(o1.getPendingQuantity() + o2.getPendingQuantity());
                                            return o1;
                                        })).values().stream().collect(Collectors.toList());

                                boolean preStatus = false;
                                //有相同机物料预装明细,是否放得下
                                for (WmsMachineMaterialsBoxPrePacking list : preList)
                                {
                                    //判断该料箱是否已经出库到线体上
//                                        WmsBoxTaskList  WmsBoxTaskInfo=wmsBoxTaskListMapper.selectOne();

                                    Container container = containerMapper.selectBoxNo(list.getBoxCode());
                                    int containerType = container.getBoxContainerType();//料箱类型
                                    int boxEmptyStatus = container.getBoxEmptyStatus();//已使用格数
                                    int singleGridCapacity = existingMaterial.getCapacity() / containerType;//单个格子容量

                                    //根据格号和入库单号查询
                                    QueryWrapper<WmsMachineMaterialsBoxPrePacking> queryPerWrapper = new QueryWrapper<>();
                                    queryPerWrapper.eq("grid_code", list.getGridCode());
                                    queryPerWrapper.eq("in_store_number", item.getInStoreNumber());
                                    WmsMachineMaterialsBoxPrePacking prePackingList = wmsMachineMaterialsBoxPrePackingMapper.selectOne(queryPerWrapper.last("LIMIT 1"));
                                    String inStoreNo;

                                    //判断当前格子是否放得下
                                    if (list.getPendingQuantity() + item.getInQuantity() <= singleGridCapacity) {
                                        int status = (list.getPendingQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
                                        if (prePackingList == null) {
                                            inStoreNo = item.getInStoreNumber();
                                            //当前格子放得下，直接预装,生成预装明细
                                            boolean ret = PreInOldBoxDetails(list, list.getGridCode(), item.getInQuantity(), status, inStoreNo);
                                            preStatus = ret == true ? true : false;
                                            if (!preStatus) {
                                                log.error("预装失败，余箱信息:" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
                                            } else {
                                                WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                wmsMachineInList.setStatus(1);
                                                wmsMachineInListMapper.updateById(wmsMachineInList);
                                            }
                                        } else {
                                            inStoreNo = list.getInStoreNumber();
                                            int number = prePackingList.getPendingQuantity() + item.getInQuantity();
                                            prePackingList.setPendingQuantity(number);
                                            prePackingList.setActualQuantity(number);
                                            prePackingList.setStatus(status);
                                            wmsMachineMaterialsBoxPrePackingMapper.updateById(prePackingList);
                                        }

                                    } else {
                                        //当前格子放不下，判断该料箱是否有其他格子可以预装
                                        int quantity = (containerType - boxEmptyStatus) * singleGridCapacity + (singleGridCapacity - list.getPendingQuantity());//其他空格+未满格子累计容量
                                        if (quantity > item.getInQuantity()) {
                                            //获取料箱空格子数据
                                            List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(list.getBoxCode());

                                            //其他空格放得下，计算需要空格数，并生成预装明细
                                            int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
                                            int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
                                            if (remainder + list.getPendingQuantity() <= singleGridCapacity) {
                                                int status = (list.getPendingQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
                                                //余数在未满格子放得下，未满格子生成预装明细
                                                //根据格号和入库单号查询
                                                if (prePackingList == null) {
                                                    inStoreNo = item.getInStoreNumber();
                                                } else {
                                                    inStoreNo = list.getInStoreNumber();
                                                    int number = prePackingList.getPendingQuantity() + remainder;
                                                    prePackingList.setPendingQuantity(number);
                                                    prePackingList.setActualQuantity(number);
                                                    prePackingList.setStatus(status);
                                                    prePackingList.setAssetClass(list.getAssetClass());
                                                    wmsMachineMaterialsBoxPrePackingMapper.updateById(prePackingList);
                                                }
                                                for (int i = 0; i < gridNum; i++) {
                                                    WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                    machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                    machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
                                                    machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i + 1).getGridId());
                                                    machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                    machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                                    machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                                    machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
                                                    machineMaterialsBoxPrePackingModel.setAssetcModel(list.getAssetcModel());
                                                    machineMaterialsBoxPrePackingModel.setBrand(list.getBrand());
                                                    //                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                                    machineMaterialsBoxPrePackingModel.setPendingQuantity(singleGridCapacity);
                                                    machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                                    machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                                    machineMaterialsBoxPrePackingModel.setStatus(1);
                                                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
                                                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                    container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                    container.setMaterialType(2);
                                                    containerMapper.updateById(container);
                                                    WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                    wmsMachineInList.setStatus(1);
                                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                                }
                                                preStatus = true;
                                            } else {
                                                //余数在未满格子放不下，重新分配一个新空格子给余数预装
                                                for (int i = 0; i < gridNum + 1; i++) {
                                                    if (i == 0) {
                                                        //先生成余数预装明细
                                                        boolean ret = PreInOldBoxDetails(list, boxList.get(i).getGridId(), remainder, 2, item.getInStoreNumber());
                                                        if (ret) {
                                                            container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                            container.setMaterialType(2);
                                                            containerMapper.updateById(container);
                                                            WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                            wmsMachineInList.setStatus(1);
                                                            wmsMachineInListMapper.updateById(wmsMachineInList);
                                                            preStatus = true;
                                                        } else {
                                                            log.error("生成余数预装明细失败;余箱信息：" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
                                                            preStatus = false;
                                                        }
                                                    } else {
                                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
                                                        machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
                                                        //                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(singleGridCapacity);
                                                        machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                                        machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
                                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                        container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                        container.setMaterialType(2);
                                                        containerMapper.updateById(container);
                                                    }
                                                }
                                                preStatus = true;
                                            }

                                        }
                                    }
                                }
                                if (!preStatus) {
                                    //当前预装料箱放不下,出新的对应空箱
                                    preStatus=OutNewBox(item, existingMaterial);//分配新空箱预装
                                    if(!preStatus){
                                        log.error("生成料箱预装明细失败：" + JSON.toJSONString(item));
                                        result= false;
                                    }
                                }
                            }
                        }

//                        }
//                        else
//                        {
//                            //容量大于两个料箱，装料架入库
//                             result = taryPreDetails(item, existingMaterial);
//                            if (!result) {
//                                log.error("生成料架预装明细失败：" + JSON.toJSONString(item));
//                                result= false;
//                            }
//                        }
                    }
                    else if(existingMaterial.getPriorityContainerType()==99){
                        log.info("入料架，入料架参数:"+JSON.toJSONString(item));
                        //优先箱型为料架，装料架
                        result = taryPreDetails(item, existingMaterial);
                        if (!result) {
                            log.error("生成料架预装明细失败：" + JSON.toJSONString(item));
                            result= false;
                        }
                    }else if (existingMaterial.getPriorityContainerType()==100){
                        log.info("入虚拟库位，入库参数:"+JSON.toJSONString(item));
                        String boxNo="XN"+ DateUtils.getDate();
                        BoxItem boxItem=new BoxItem();
                        boxItem.setBoxType(1);
                        boxItem.setBoxNo(boxNo);
                        boxItem.setMaterialCode(item.getMaterialCode());
                        boxItem.setMaterialName(item.getMaterialName());
                        boxItem.setMaterialQuantity(item.getInQuantity());
                        boxItem.setMaterialModel(item.getAssetModel());
                        boxItemMapper.insert(boxItem);
                        //入虚拟库位
                        Stock stock=new Stock();
                        stock.setBoxNo(boxNo);
                        stock.setBoxType(99);
                        stock.setLocked(1);
                        stockMapper.insert(stock);

                        OARequest request=new OARequest();
                        request.setActionName("采购入库");
                        request.setInputjson(JSON.toJSONString(model));
                        OAService.submitOA(JSON.toJSONString(request));
                    }
                }else
                {
                    log.error("入库单"+item.getInStoreNumber()+"已经预装；状态："+wmsMachineInInfo.get(0).getStatus());
                    result=false;
                }
                if(result)
                {
                    //预装完成，生成出库任务
                    result= OutBoxTask();
                }
            }

        }catch (Exception ex){
            log.error(ex.toString());
            result=false;
        }
        return result;
    }

    @Override
    public boolean insertBoxItem(BoxItem boxItem) {
        return boxItemMapper.insert(boxItem) > 0;
    }

    /**
     * 生成容器出库装箱任务
     */
    public boolean OutBoxTask(){
        boolean status=false;
        try{
            //预装完成，生成出库任务
            QueryWrapper<WmsMachineMaterialsBoxPrePacking> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", "1");
            List<WmsMachineMaterialsBoxPrePacking> machineMaterialsBoxPrePackingList = wmsMachineMaterialsBoxPrePackingMapper.selectList(queryWrapper);
            List<WmsMachineMaterialsBoxPrePacking> list = machineMaterialsBoxPrePackingList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(WmsMachineMaterialsBoxPrePacking::getBoxCode, p -> p, (p1, p2) -> p1), // 使用第一个遇到的元素作为值，如果有重复则忽略后面的元素
                            map -> new ArrayList<>(map.values()) // 将map的values转换回list
                    ));
            for(WmsMachineMaterialsBoxPrePacking preList:list)
            {
                QueryWrapper<WmsBoxTaskList> queryTaskWrapper = new QueryWrapper<>();
                queryTaskWrapper.eq("box_no", preList.getBoxCode());
                queryTaskWrapper.lt("task_status", 4);
                queryTaskWrapper.eq("delete_flag", 0);
                WmsBoxTaskList  WmsBoxTaskInfo=wmsBoxTaskListMapper.selectOne(queryTaskWrapper.last("LIMIT 1"));
                if(WmsBoxTaskInfo==null)
                {
                    //当前料箱没有任务时才会生成出库任务
                    WmsBoxTaskList wmsBoxTaskList = new WmsBoxTaskList();
                    QueryWrapper<WmsStation> queryMapper = new QueryWrapper<>();
                    queryMapper.eq("station_type", 2);
                    WmsStation stationInfo = wmsStationMapper.selectOne(queryMapper.last("LIMIT 1"));
                    if (stationInfo == null) {
                        log.error("出库站点为空，无法进行出库；" );
                        status = false;
                    } else
                    {
                        wmsBoxTaskList.setToSite(stationInfo.getStationCode());//出库口
                    }
                    QueryWrapper<Container> ContainerWrapper=new QueryWrapper<>();
                    ContainerWrapper.eq("material_type",2);
                    List<Container> containerList= containerMapper.selectList(ContainerWrapper);
                    String toSite="";
                    if(containerList.isEmpty()) {
                        //分配库位
                        QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
                        shelfQueryWrapper.eq("box_type", 1);
                        shelfQueryWrapper.eq("locked", 0);
                        shelfQueryWrapper.eq("state", 0);
                        Shelf shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
                        toSite=shelfInfo.getCode();
                    }else {
                        for(Container item:containerList)
                        {
                            QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
                            shelfQueryWrapper.eq("box_no", item.getBoxNo());
                            Shelf shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
                            if(shelfInfo!=null) {
                                Shelf shelf= SerEMptyStock(shelfInfo.getColumn(), shelfInfo.getRow());
                                if(shelf!=null){
                                    toSite=shelfInfo.getCode();
                                    break ;
                                }
                            }
                        }
                        if(toSite.isEmpty()){
                            //分配库位
                            QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
                            shelfQueryWrapper.eq("box_type", 1);
                            shelfQueryWrapper.eq("locked", 0);
                            shelfQueryWrapper.eq("state", 0);
                            Shelf shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
                            toSite=shelfInfo.getCode();
                        }
                    }
                    String date=(new Date()).toString();
                    String orderType=wmsBoxTaskListService.getTaskOrder();
                    wmsBoxTaskList.setTaskOrder(orderType);
                    wmsBoxTaskList.setBoxNo(preList.getBoxCode());
                    wmsBoxTaskList.setMaterialType(1);
                    wmsBoxTaskList.setBoxType(preList.getContainerType());
                    wmsBoxTaskList.setTaskStatus(0);
                    wmsBoxTaskList.setPushStatus(2);
                    wmsBoxTaskList.setTaskType(6);
                    wmsBoxTaskList.setToSite(toSite);
                    wmsBoxTaskList.setFromSite(stationInfo.getStationCode());
                    wmsBoxTaskList.setPriority(0);
                    wmsBoxTaskList.setCreateTime(DateUtils.getDate());
                    wmsBoxTaskList.setCreateBy(ShiroUtils.getLoginName());
                    wmsBoxTaskListMapper.insert(wmsBoxTaskList);
                    status=true;
                }else {
                    log.info("已存在容器："+preList.getBoxCode()+"的任务");
                }
            }

        }catch (Exception ex){
            log.error("生成料箱出库任务失败："+ex.toString());
            status=false;
        }
        return status;
    }

    public Shelf SerEMptyStock(int column,int row)
    {
        QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
        shelfQueryWrapper.eq("column", 1);
        shelfQueryWrapper.eq("row", 1);
        shelfQueryWrapper.eq("locked", 0);
        shelfQueryWrapper.eq("state", 0);
        return shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
    }

    /**
     * 绑定入库
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<ResultPreDTO> InBoundDetails(MachineMaterialInBoundDetailsDto model)
    {
        log.info("机物料绑定入库："+JSON.toJSONString(model));
//        boolean status=false;
        Result<ResultPreDTO> result=new Result<>();
        result.setSuccess(false);
        try
        {
            if(!model.getInBoundDetailsListDto().isEmpty())
            {
                //先检查是否有可用空库位
                WmsBoxTaskList wmsBoxTaskList = new WmsBoxTaskList();
                QueryWrapper<Shelf> query = new QueryWrapper<>();
                query.eq("locked", 0);
                query.eq("state", 0);
                if (model.getBoxNo().contains("F")) {
                    query.eq("box_type", 1);
                } else if (model.getBoxNo().contains("P")) {
                    query.eq("box_type", 2);
                } else {
                    result.setMessage("容器类型错误：" + JSON.toJSONString(model));
                    result.setCode(500);
                    throw new RuntimeException("绑定异常："+result.getMessage());
                }
                List<Shelf> shelfList = shelfMapper.selectList(query);
                List<Shelf> areaList = shelfList.stream()
                        .filter(Container -> Container.getArea().equals("B"))
                        .collect(Collectors.toList());
                if (areaList.isEmpty()) {
                    List<Shelf> areaListA = shelfList.stream()
                            .filter(Container -> Container.getArea().equals("A"))
                            .collect(Collectors.toList());
                    if (areaListA.isEmpty()) {
                        log.error("入库失败,没有可用空库位；" + JSON.toJSONString(model));
                        result.setMessage("入库失败,没有可用空库位；" + JSON.toJSONString(model));
                        result.setCode(500);
                        throw new RuntimeException("绑定异常："+result.getMessage());
                    } else {
                        wmsBoxTaskList.setToSite(areaListA.get(0).getCode());//目的库位
                        query.eq("code", areaListA.get(0).getCode());
                        Shelf shelf = shelfMapper.selectOne(query.last("LIMIT 1"));
                        shelf.setLocked(1);
                        shelfMapper.updateById(shelf);
//                    status=true;
                        result.setSuccess(true);
                    }
                } else {
                    wmsBoxTaskList.setToSite(areaList.get(0).getCode());//目的库位
                    query.eq("code", areaList.get(0).getCode());
                    Shelf shelf = shelfMapper.selectOne(query.last("LIMIT 1"));
                    shelf.setLocked(1);
                    shelfMapper.updateById(shelf);
//                status=true;
                    result.setSuccess(true);
                }
                if (result.isSuccess())
                {
                    WmsMachineInList wmsMachineInList = new WmsMachineInList();
                    for (InBoundDetailsListDto item : model.getInBoundDetailsListDto())
                    {
                        QueryWrapper<WmsMachineInList> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("in_store_number", item.getInStoreNumber());
                        queryWrapper.eq("object_id", item.getObjectId());
                        wmsMachineInList = wmsMachineInListMapper.selectOne(queryWrapper.last("LIMIT 1"));
                        if (wmsMachineInList != null) {
                            wmsBoxTaskList.setTaskType(wmsMachineInList.getTaskType() - 1);
                        } else {
                            log.error("找不到Object为" + item.getObjectId() + "的入库单：" + item.getInStoreNumber());
//                        return false;
                            result.setSuccess(false);
                            result.setMessage("找不到Object为" + item.getObjectId() + "的入库单：" + item.getInStoreNumber());
                            result.setCode(500);
                            throw new RuntimeException("绑定异常："+result.getMessage());
                        }
                        QueryWrapper<BoxItem> queryMapper = new QueryWrapper<>();
                        queryMapper.eq("box_no", model.getBoxNo());
//                        List<BoxItem> boxItemList = boxItemMapper.selectList(queryMapper);
                        if (model.getBoxNo().contains("F"))
                        {
                            queryMapper.eq("grid_id", item.getGridNo());
                            BoxItem boxItem = boxItemMapper.selectOne(queryMapper.last("LIMIT 1"));
                            wmsBoxTaskList.setFromSite("RGZQ1A01011");//料箱起点固定
                            wmsBoxTaskList.setBoxType(1);
                            if (boxItem!=null)
                            {
                                boxItem.setMaterialQuantity(boxItem.getMaterialQuantity() + item.getActualQuantity());
                                boxItem.setBoxNo(model.getBoxNo());
                                boxItem.setMaterialCode(item.getMaterialCode());
                                boxItem.setMaterialName(item.getMaterialName());
                                boxItem.setMaterialModel(item.getAssetModel());
                                boxItem.setCreateTime(DateUtils.getDate());
                                boxItem.setCreateBy(ShiroUtils.getLoginName());
                                boxItem.setStatus(2);
                                boxItemMapper.updateById(boxItem);
                            }else
                            {
                                log.error("请求装箱入库失败，找不到料箱" + model.getBoxNo()+"第"+item.getGridNo()+"格的信息");
                                result.setSuccess(false);
                                result.setMessage("请求装箱入库失败，找不到料箱" + model.getBoxNo()+"第"+item.getGridNo()+"格的信息");
                                result.setCode(500);
                                throw new RuntimeException("绑定异常："+result.getMessage());
                            }
                        } else if (model.getBoxNo().contains("P"))
                        {
                            BoxItem trayItem=new BoxItem();
                            List<BoxItem> boxItemList = boxItemMapper.selectList(queryMapper);
                            if(!boxItemList.isEmpty() && boxItemList!=null)
                            {
                                wmsBoxTaskList.setFromSite("0294000AA0299196");//料架起点固定
                                wmsBoxTaskList.setBoxType(2);
//                                trayItem = boxItemList.stream()
//                                        .filter(Container -> Container.getMaterialCode().equals(item.getMaterialCode()))
//                                        .findFirst().get();
                                for(BoxItem boxItem:boxItemList)
                                {
                                    if(!boxItem.getMaterialCode().isEmpty() && boxItem.getMaterialCode()==item.getMaterialCode())
                                    {
                                        trayItem.setMaterialQuantity(trayItem.getMaterialQuantity() + item.getActualQuantity());
                                        trayItem.setGridVolume(model.getVolume());
                                        boxItemMapper.updateById(trayItem);
                                        break;
                                    } else
                                    {
                                        trayItem.setMaterialQuantity(item.getActualQuantity());
                                        trayItem.setBoxNo(model.getBoxNo());
                                        trayItem.setMaterialCode(item.getMaterialCode());
                                        trayItem.setMaterialName(item.getMaterialName());
                                        trayItem.setMaterialModel(item.getAssetModel());
                                        trayItem.setCreateTime(DateUtils.getDate());
                                        trayItem.setCreateBy(ShiroUtils.getLoginName());
                                        trayItem.setStatus(2);
                                        trayItem.setBoxType(2);
                                        trayItem.setGridVolume(model.getVolume());
                                        boxItemMapper.insert(trayItem);
                                        break;
                                    }
                                }

                                String boxNo=stringRedisTemplate.opsForValue().get(REDIS_LIFT_TRAY_CODE_KEY);
                                if(!boxNo.isEmpty()) {
                                    QueryWrapper<WmsMachineMaterialsBoxPrePacking> queryPreWrapper = new QueryWrapper<>();
                                    queryPreWrapper.eq("status", 1);
                                    queryPreWrapper.eq("container_type", 2);
                                    queryPreWrapper.eq("box_code", boxNo);//测试数据，需要根据AGV上报的托盘号来跟新此处
                                    queryPreWrapper.orderByDesc("box_code").orderByAsc("grid_code");
                                    List<WmsMachineMaterialsBoxPrePacking> preDetails = wmsMachineMaterialsBoxPrePackingMapper.selectList(queryPreWrapper);
                                   if(!preDetails.isEmpty())
                                   {
                                       result.setCode(300);
                                   }else{
                                       stringRedisTemplate.opsForValue().set(REDIS_LIFT_TRAY_CODE_KEY, "");
                                   }
                                }
//                                if (trayItem != null)
//                                {
//                                    trayItem.setMaterialQuantity(trayItem.getMaterialQuantity() + item.getActualQuantity());
//                                    trayItem.setBoxNo(model.getBoxNo());
//                                    trayItem.setMaterialCode(item.getMaterialCode());
//                                    trayItem.setMaterialName(item.getMaterialName());
//                                    trayItem.setMaterialModel(item.getAssetModel());
//                                    trayItem.setCreateTime(DateUtils.getDate());
//                                    trayItem.setCreateBy(ShiroUtils.getLoginName());
//                                    trayItem.setStatus(2);
//                                    trayItem.setGridVolume(model.getVolume());
//                                    boxItemMapper.updateById(trayItem);
//                                }
//                                else {
//                                    trayItem.setMaterialQuantity(item.getActualQuantity());
//                                    trayItem.setBoxNo(model.getBoxNo());
//                                    trayItem.setMaterialCode(item.getMaterialCode());
//                                    trayItem.setMaterialName(item.getMaterialName());
//                                    trayItem.setMaterialModel(item.getAssetModel());
//                                    trayItem.setCreateTime(DateUtils.getDate());
//                                    trayItem.setCreateBy(ShiroUtils.getLoginName());
//                                    trayItem.setStatus(2);
//                                    trayItem.setGridVolume(model.getVolume());
//                                    boxItemMapper.insert(trayItem);
//                                }
                            }else{
                               log.error("找不到料架："+model.getBoxNo()+"的信息");
                                result.setMessage("找不到料架："+model.getBoxNo()+"的信息");
                                throw new RuntimeException("绑定异常："+result.getMessage());
                            }
                        } else {
                            log.error("请求入库失败：" + JSON.toJSONString(item));
//                        return false;
                            result.setSuccess(false);
                            result.setMessage("请求入库失败：" + JSON.toJSONString(item));
                            result.setCode(500);
                            throw new RuntimeException("绑定异常："+result.getMessage());
                        }
//                        BoxItem boxItem = boxItemMapper.selectOne(queryMapper.last("LIMIT 1"));
//                        if (boxItem != null)
//                        {
//                            boxItem.setBoxNo(model.getBoxNo());
////                        boxItem.setBoxType(model.getContainerType());
////                            boxItem.setGridStatus(item.getGridNo());
//                            boxItem.setMaterialCode(item.getMaterialCode());
//                            boxItem.setMaterialName(item.getMaterialName());
//                            if (model.getBoxNo().contains("P")) {
//                                boxItem.setGridVolume(model.getVolume());
//                            }
//                            boxItem.setMaterialModel(item.getAssetModel());
//                            boxItem.setCreateTime(DateUtils.getDate());
//                            boxItem.setCreateBy(ShiroUtils.getLoginName());
//                            boxItem.setStatus(2);
//                            boxItemMapper.updateById(boxItem);
                        WmsMachineMaterialsBoxPrePacking wmsMachineMaterialsBoxPrePacking = wmsMachineMaterialsBoxPrePackingMapper.selectById(item.getId());
                        wmsMachineMaterialsBoxPrePacking.setStatus(2);
                        wmsMachineMaterialsBoxPrePacking.setDeleteFlag(1);
                        wmsMachineMaterialsBoxPrePackingMapper.updateById(wmsMachineMaterialsBoxPrePacking);
                    }
                    //绑定成功
                    // 1.生成入库任务
                    String orderType = wmsBoxTaskListService.getTaskOrder();
                    wmsBoxTaskList.setTaskOrder(orderType);
                    wmsBoxTaskList.setMaterialType(1);
                    wmsBoxTaskList.setBoxNo(model.getBoxNo());
                    wmsBoxTaskList.setTaskStatus(0);
//                wmsBoxTaskList.setTaskType(0);
                    wmsBoxTaskList.setCreateTime(DateUtils.getDate());
                    wmsBoxTaskList.setCreateBy(ShiroUtils.getLoginName());
                    wmsBoxTaskListMapper.insert(wmsBoxTaskList);
                    // 2.如果是料箱，通知输送线释放
//                    if (model.getBoxNo().contains("F")) {
//                        stringRedisTemplate.opsForValue().set(REDIS_BOX_RELEASE_STATUS_KEY, "1");
//                    }
                    List<MachineMaterialInboundDTO> dtoList = new ArrayList<>();
                    for (InBoundDetailsListDto item : model.getInBoundDetailsListDto()) {
                        MachineMaterialInboundDTO dto = new MachineMaterialInboundDTO();
                        dto.setObjectId(item.getObjectId());
                        dto.setInQuantity(item.getActualQuantity());
                        dto.setMaterialCode(item.getMaterialCode());
                        dto.setMaterialName(item.getMaterialName());
                        dtoList.add(dto);
                    }
                    log.info("绑定入库写入redis结果：" + JSON.toJSONString(dtoList));
                    //待任务完成后结果推送OA
                    OARequest request=new OARequest();
                    request.setActionName("采购入库");
                    request.setInputjson(JSON.toJSONString(dtoList));
                    boolean status=OAService.submitOA(JSON.toJSONString(request));
                    stringRedisTemplate.opsForValue().set(orderType, JSON.toJSONString(request));

                }else{
                    log.error("入库失败,没有可用空库位；" + JSON.toJSONString(model));
                    result.setMessage("入库失败,没有可用空库位；" + JSON.toJSONString(model));
                    result.setCode(500);
                    throw new RuntimeException("绑定异常："+result.getMessage());
                }
            }
            else{
                result.setSuccess(false);
                result.setMessage("绑定入库失败，入库明细不能为空："+JSON.toJSONString(model));
                result.setCode(500);
                throw new RuntimeException("绑定异常："+result.getMessage());
            }

        }catch (Exception ex){
            log.error("绑定入库失败："+ex.toString()+"；"+JSON.toJSONString(model));
//            status=false;
            result.setSuccess(false);
            result.setMessage("绑定入库失败："+ex.toString()+"；"+JSON.toJSONString(model));
            result.setCode(500);
            throw new RuntimeException("绑定异常："+result.getMessage());
        }
        return result;
    }

    /**
     * 切换料架
     * @param model
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<ResultPreDTO> SwitchTray(MachineMaterialInBoundDetailsDto model)
    {
        Result<ResultPreDTO> result=new Result<>();
        result.setSuccess(false);
        try{
            if(model!=null)
            {
                    List<BoxItem> trayItemList = boxItemMapper.selectMaterialCodeTray(2);
                    if(!trayItemList.isEmpty())
                    {
                        for(InBoundDetailsListDto item:model.getInBoundDetailsListDto())
                        {
                            QueryWrapper<WmsMachineMaterialsBoxPrePacking> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("id", item.getId());
                            WmsMachineMaterialsBoxPrePacking info = wmsMachineMaterialsBoxPrePackingMapper.selectOne(queryWrapper.last("LIMIT 1"));
                            info.setBoxCode(trayItemList.get(0).getBoxNo());
                            wmsMachineMaterialsBoxPrePackingMapper.updateById(info);
                        }
                        //生成料架出库任务
                        WmsBoxTaskList wmsBoxTaskList=new WmsBoxTaskList();
                        QueryWrapper<Shelf> query = new QueryWrapper<>();
                        query.eq("box_no", trayItemList.get(0).getBoxNo());
                        query.eq("state", 1);
                        Shelf shelfInfo =shelfMapper.selectOne(query.last("LIMIT 1"));
                        if(shelfInfo!=null)
                        {
                            wmsBoxTaskList.setFromSite(shelfInfo.getCode());//料架起点固定
                            shelfInfo.setLocked(1);
                            shelfMapper.updateById(shelfInfo);
                        }
                        String orderType = wmsBoxTaskListService.getTaskOrder();
                        wmsBoxTaskList.setTaskOrder(orderType);
                        wmsBoxTaskList.setMaterialType(1);
                        wmsBoxTaskList.setToSite("0294000AA0299196");
                        wmsBoxTaskList.setBoxNo(trayItemList.get(0).getBoxNo());
                        wmsBoxTaskList.setTaskStatus(0);
                        wmsBoxTaskList.setTaskType(6);
                        wmsBoxTaskList.setBoxType(2);
                        wmsBoxTaskList.setCreateTime(DateUtils.getDate());
                        wmsBoxTaskList.setCreateBy(ShiroUtils.getLoginName());
                        wmsBoxTaskListMapper.insert(wmsBoxTaskList);
                        stringRedisTemplate.opsForValue().set(REDIS_LIFT_TRAY_CODE_KEY, "");
                    }else
                    {
                        log.error("找不到有容量的料架");
                        throw new RuntimeException("切换料架异常，找不到有容量的料架");
                    }
            }

        }catch (Exception ex)
        {
            log.error("切换料架异常："+ex.toString());
            throw new RuntimeException("切换料架异常："+ex.toString());
        }
        return result;
    }
    /**
     ***新料箱生成新空格预装明细
     * boxNo 料箱号
     * item 入库物料信息
     * specification 物料规格
     * gridCode 格子号
     * quantity 待入库数量(预装数量)
     * * gridstatus 格子状态
     */
    public boolean PreInNewBoxDetails(String boxNo,MachineMaterialInboundDTO item,String specification,int gridCode,int quantity,int gridstatus)
    {
        log.info("生成新空格预装明细:"+ JSON.toJSONString(item));
        try {
            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
            machineMaterialsBoxPrePackingModel.setBoxCode(boxNo);
            machineMaterialsBoxPrePackingModel.setGridCode(gridCode);
            machineMaterialsBoxPrePackingModel.setContainerType(1);
            machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
            machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
            machineMaterialsBoxPrePackingModel.setAssetClass(item.getAssetClass());
            machineMaterialsBoxPrePackingModel.setAssetcModel(item.getAssetModel());
            machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
//            machineMaterialsBoxPrePackingModel.setOriginalOuantity(quantity);
            machineMaterialsBoxPrePackingModel.setPendingQuantity(quantity);
            machineMaterialsBoxPrePackingModel.setActualQuantity(quantity);
            machineMaterialsBoxPrePackingModel.setGridStatus(gridstatus);
            machineMaterialsBoxPrePackingModel.setStatus(1);
            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
            machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
            return true;
        }catch (Exception ex)
        {
            log.error(ex.toString());
            return false;
        }
    }

    /**
     ***分配新空箱预装
     */
    public boolean OutNewBox(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        boolean status=false;
        log.info("分配新空箱预装:"+ JSON.toJSONString(item));
        try {
            List<Container> emptyBoxList=containerMapper.selectEmptyBoxList(existingMaterial.getPriorityContainerType());
            if(emptyBoxList.isEmpty())
            {
                log.info("没有找到空料箱，无法预装");
                status=false;
            }else
            {
                int inQuantity=item.getInQuantity();//待入库数量
                for(Container emptyBox:emptyBoxList)
                {
                    QueryWrapper<WmsMachineInfo> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("material_code", item.getMaterialCode());
                    //机物料基础信
                    WmsMachineInfo machineInfo = wmsMaterialInfoMapper.selectOne(queryWrapper.last("LIMIT 1"));

                    if(inQuantity<machineInfo.getCapacity())//判断一个箱子是否放得下
                    {
                        int singleGridCapacity=machineInfo.getCapacity()/emptyBox.getBoxContainerType();//单个格子容量
                        int gridNum= 0;//需要格子数量
                        int remainder=0;//获取余数
                        //分配新空箱预装,先判断需要几个格子
                        if(item.getInQuantity()>singleGridCapacity)
                        {
                            //入库数量大于单个容量
                            gridNum= inQuantity/singleGridCapacity;//需要格子数量
                            remainder=inQuantity%singleGridCapacity;//获取余数
                            //如果入库数量大于单个格子容量，先判断需要几个格子
                            int k=0;
                            if(remainder>0)
                            {
                                //预装空箱第一个格子,先装余数
                                k=2;
                                boolean result =PreInNewBoxDetails( emptyBox.getBoxNo(), item, machineInfo.getSpecification(), 1,remainder,2);
                                if(result)
                                {
                                    emptyBox.setBoxEmptyStatus(1);
                                    emptyBox.setMaterialType(2);
                                    containerMapper.updateById(emptyBox);
                                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                                    wmsMachineInList.setStatus(1);
                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                    status=true;
                                }
                            }
                            for(int i=0;i<gridNum;i++)
                            {
                                //预装空箱其他格子
                                boolean result =PreInNewBoxDetails( emptyBox.getBoxNo(), item, machineInfo.getSpecification(), i+k,singleGridCapacity,3);
                                if(result) {
                                    emptyBox.setBoxEmptyStatus(i + k);
                                    emptyBox.setMaterialType(2);
                                    containerMapper.updateById(emptyBox);
                                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                                    wmsMachineInList.setStatus(1);
                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                    status=true;
                                }
                            }
                            break;

                        }else
                        {
                            //入库数量小于单个格子容量
                            boolean result =PreInNewBoxDetails( emptyBox.getBoxNo(), item, machineInfo.getSpecification(), 1,item.getInQuantity(),2);
                            if(result)
                            {
                                emptyBox.setBoxEmptyStatus(1);
                                emptyBox.setMaterialType(2);
                                containerMapper.updateById(emptyBox);
                                WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                                wmsMachineInList.setStatus(1);
                                wmsMachineInListMapper.updateById(wmsMachineInList);
                                status=true;
                            }
                            break;
                        }
                    }else
                    {
                        //一个箱子放不下
                        int singleGridCapacity=machineInfo.getCapacity()/emptyBox.getBoxContainerType();//单个格子容量
                        for(int i=0;i<emptyBox.getBoxContainerType();i++)
                        {
                            boolean result =PreInNewBoxDetails( emptyBox.getBoxNo(), item, machineInfo.getSpecification(), i+1,singleGridCapacity,3);
                            if(result)
                            {
                                emptyBox.setBoxEmptyStatus(1);
                                emptyBox.setMaterialType(2);
                                containerMapper.updateById(emptyBox);
                                status=true;
                            }

                        }
                        inQuantity=item.getInQuantity()-emptyBox.getBoxContainerType();
                    }
                }

            }
            return status;
        }catch (Exception ex)
        {
            log.error(ex.toString());
            return false;
        }
    }


    /**
     ***余料箱生成新空格预装明细
     * list 入库物料信息
     * gridCode 格子号
     * inQuantity 待入库数量(预装数量)
     * * gridStatus 格子状态
     * inStoreNumber 入库单号
     */
    public boolean PreInOldBoxDetails(WmsMachineMaterialsBoxPrePacking list,int gridCode,int inQuantity,int gridStatus,String inStoreNumber)
    {
        boolean status=false;
        try{
            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
            machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
            machineMaterialsBoxPrePackingModel.setGridCode(gridCode);
            machineMaterialsBoxPrePackingModel.setContainerType(1);
            machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
            machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
            machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
            machineMaterialsBoxPrePackingModel.setAssetcModel(list.getAssetcModel());
            machineMaterialsBoxPrePackingModel.setBrand(list.getBrand());
            //                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
            machineMaterialsBoxPrePackingModel.setPendingQuantity(list.getInStoreNumber()==inStoreNumber?list.getPendingQuantity() +inQuantity:inQuantity);
            machineMaterialsBoxPrePackingModel.setActualQuantity(list.getInStoreNumber()==inStoreNumber?list.getPendingQuantity()+inQuantity:inQuantity);
            machineMaterialsBoxPrePackingModel.setGridStatus(gridStatus);
            machineMaterialsBoxPrePackingModel.setStatus(1);
            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
            machineMaterialsBoxPrePackingModel.setInStoreNumber(inStoreNumber);
            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
            status=true;
        }catch (Exception ex){
            log.error(ex.toString());
            status=false;
        }
        return status;
    }
    /**
     ***是否存在相同机物料库存
     */
    public boolean IsBoxStockExist(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        log.info("是否存在相同机物料库存:"+ JSON.toJSONString(item));
        boolean preStatus = false;
        try {

            //判断库里是否有相同机物料库存,如果有是否放得下
            List<BoxItem> boxItemList = boxItemMapper.selectMaterialCode(item.getMaterialCode(), 1);//获取库里相同机物料料箱库存
            if (!boxItemList.isEmpty())
            {
                //库里有相同机物料库存
                for (BoxItem list : boxItemList) {
                    Container container = containerMapper.selectBoxNo(list.getBoxNo());
                    int containerType = container.getBoxContainerType();//料箱类型
                    int boxEmptyStatus = container.getBoxEmptyStatus();//已使用格数
                    int singleGridCapacity = existingMaterial.getCapacity() / containerType;//单个格子容量
                    //判断当前格子是否放得下
                    if (list.getMaterialQuantity() + item.getInQuantity() <= singleGridCapacity) {
                        int status = (list.getMaterialQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
                        //当前格子放得下，直接预装,生成预装明细
                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                        machineMaterialsBoxPrePackingModel.setGridCode(list.getGridId());
                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
                        machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setGridStatus(status);
                        machineMaterialsBoxPrePackingModel.setStatus(1);
                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//旧的空格子
                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                        preStatus = true;
                    } else {
                        //当前格子放不下，判断是否有其他格子可以预装
                        int quantity = (containerType - boxEmptyStatus) * singleGridCapacity + (singleGridCapacity - list.getMaterialQuantity());//其他空格+未满格子累计容量
                        if (quantity > item.getInQuantity()) {
                            //获取料箱空格子数据
                            List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(list.getBoxNo());
                            //其他空格放得下，计算需要空格数，并生成预装明细
                            int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
                            int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
                            if (remainder + list.getMaterialQuantity() <= singleGridCapacity) {
                                int status = (list.getMaterialQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
                                //余数在未满格子放得下，未满格子生成预装明细
                                WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                                machineMaterialsBoxPrePackingModel.setGridCode(list.getGridId());
                                machineMaterialsBoxPrePackingModel.setContainerType(1);
                                machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                machineMaterialsBoxPrePackingModel.setActualQuantity(remainder);
                                machineMaterialsBoxPrePackingModel.setGridStatus(status);
                                machineMaterialsBoxPrePackingModel.setStatus(1);
                                machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                for (int i = gridNum; i < gridNum; i++) {
                                    machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                    machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                                    machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                    machineMaterialsBoxPrePackingModel.setContainerType(1);
                                    machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                    machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                    machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
//                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                    machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                    machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                    machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                    machineMaterialsBoxPrePackingModel.setStatus(1);
                                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                    container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                    container.setMaterialType(2);
                                    containerMapper.updateById(container);
                                }
                                preStatus = true;
                            } else {
                                //余数在未满格子放不下，重新分配一个新空格子给余数预装
                                for (int i = 0; i < gridNum + 1; i++) {
                                    if (i == 0) {
                                        //先生成余数预装明细
                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                                        machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
//                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                        machineMaterialsBoxPrePackingModel.setActualQuantity(remainder);//余数
                                        machineMaterialsBoxPrePackingModel.setGridStatus(2);
                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                        container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                        container.setMaterialType(2);
                                        containerMapper.updateById(container);
                                    } else {
                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                                        machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
//                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                        machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                        machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                        container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                        container.setMaterialType(2);
                                        containerMapper.updateById(container);
                                    }
                                }
                                preStatus = true;
                            }

                        }
                    }
                }
                if (!preStatus) {
                    //当前库存料箱放不下,出新的对应空箱
                    preStatus= OutNewBox(item, existingMaterial);//分配新空箱预装
                    if(!preStatus){
                        log.error("生成料箱预装明细失败：" + JSON.toJSONString(item));
                    }
                }
            } else
            {
                //没有相同机物料库存，直接出空箱
//            OutNewBox( item, existingMaterial);//分配新空箱预装
                //库里没有相同机物料库存,是否有同箱型未满的机物料库存
                List<Container> containerList = containerMapper.selectContainerType(2, 0, existingMaterial.getPriorityContainerType());
                if (containerList.isEmpty()) {
                    preStatus= OutNewBox(item, existingMaterial);//分配新空箱预装
                } else
                {
                    //存在未满的同箱型机物料库存料箱
                    for (Container container : containerList) {
                        int containerType = container.getBoxContainerType();//料箱类型
                        int boxEmptyStatus = container.getBoxEmptyStatus();//已使用格数
                        int singleGridCapacity = existingMaterial.getCapacity() / containerType;//单个格子容量
                        int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
                        int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
                        if (remainder > 0) {
                            gridNum = gridNum + 1;
                        }
                        if ((containerType - boxEmptyStatus) * singleGridCapacity > item.getInQuantity())//未满料箱是否放得下
                        {
                            List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(container.getBoxNo());
                            if (item.getInQuantity() > singleGridCapacity) {
                                //入库数量大于单个格子容量
                                for (int i = gridNum; i < gridNum; i++) {
                                    if (i == 0) {
                                        //预装余数
                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                        machineMaterialsBoxPrePackingModel.setBoxCode(container.getBoxNo());
                                        machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                        machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                                        machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                                        machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                                        machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                        machineMaterialsBoxPrePackingModel.setActualQuantity(remainder);
                                        machineMaterialsBoxPrePackingModel.setGridStatus(2);//未满格
                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                        container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                        container.setMaterialType(2);
                                        containerMapper.updateById(container);
                                    } else
                                    {
                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                        machineMaterialsBoxPrePackingModel.setBoxCode(container.getBoxNo());
                                        machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                        machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                                        machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                                        machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                                        machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                        machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                        machineMaterialsBoxPrePackingModel.setGridStatus(3);//满格
                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                        container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                        container.setMaterialType(2);
                                        containerMapper.updateById(container);
                                    }
                                    preStatus = true;//生成预装明细完成
                                }
                            }
                        }
                    }
                    if (!preStatus) {
                        preStatus= OutNewBox(item, existingMaterial);//分配新空箱预装
                        if(!preStatus){
                            log.error("生成料箱预装明细失败：" + JSON.toJSONString(item));
                        }
                    }

                }
            }
        }catch (Exception ex){
            log.error(ex.toString());
            preStatus= false;
        }
        return preStatus;
    }


    /**
     ***是否存在机物料未满料架库存
     */
    public boolean IsTaryStockExist(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        log.info("是否存在机物料未满料架库存:"+ JSON.toJSONString(item));
        boolean status=false;
        try {
            //获取库里相同机物料料架库存
            List<BoxItem> trayItemList = boxItemMapper.selectMaterialCode(item.getMaterialCode(), 2);
            //判断库里是否有相同机物料库存,如果有是否放得下
            if (!trayItemList.isEmpty())
            {
                for (BoxItem list : trayItemList)
                {
                    if (list.getGridVolume() < 100)
                    {
                        //确认料架放得下，使用余料架进行预装
                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                        machineMaterialsBoxPrePackingModel.setContainerType(2);
                        machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                        machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                        machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                        machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
                        machineMaterialsBoxPrePackingModel.setOriginalOuantity(machineMaterialsBoxPrePackingModel.getOriginalOuantity() + item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setGridStatus(2);
                        machineMaterialsBoxPrePackingModel.setStatus(1);
                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                        WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                        wmsMachineInList.setStatus(1);
                        wmsMachineInListMapper.updateById(wmsMachineInList);
                        status = true;
                        break;
                    }
                }
                if (!status) {
                    //余料架放不下，使用空料架进行预装
                    status= OutNewTary(item, existingMaterial);
                    if(!status){
                        log.error("分配新料架预装失败："+JSON.toJSONString(item));
                    }
                }

            } else {
                //没有相同机物料的料架库存，是否有其他的机物料未满料架库存
                List<BoxItem> trayList = boxItemMapper.selectTray(2);
                if (trayList.isEmpty()) {
                    //没有未满料架库存，使用空料架进行预装
                    status= OutNewTary(item, existingMaterial);
                    if(!status){
                        log.error("分配新料架预装失败："+JSON.toJSONString(item));
                    }
                } else {
                    //有未满料架库存，使用余料架进行预装
                    WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                    machineMaterialsBoxPrePackingModel.setBoxCode(trayList.get(0).getBoxNo());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                    machineMaterialsBoxPrePackingModel.setContainerType(2);
                    machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                    machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                    machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                    machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(machineMaterialsBoxPrePackingModel.getOriginalOuantity() + item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setGridStatus(2);
                    machineMaterialsBoxPrePackingModel.setStatus(1);
                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                    wmsMachineInList.setStatus(1);
                    wmsMachineInListMapper.updateById(wmsMachineInList);
                    status=true;
                }
            }
        }catch (Exception ex)
        {
            log.error(ex.toString());
            status=false;
        }
        return status;
    }

    /**
     ***分配新料架预装
     */
    public boolean OutNewTary(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        log.info("准备分配新料架预装:"+ JSON.toJSONString(item));
        boolean status=false;
        try{
            List<Container> trayList=containerMapper.selectTrayNo(2);
            if(trayList.isEmpty())
            {
                log.info("找不到空料架，无法出库");
                return false;
            }else
            {
                WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                machineMaterialsBoxPrePackingModel.setBoxCode(trayList.get(0).getBoxNo());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                machineMaterialsBoxPrePackingModel.setContainerType(2);
                machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
//            machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setGridStatus(2);
                machineMaterialsBoxPrePackingModel.setStatus(1);
                machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                wmsMachineInList.setStatus(1);
                wmsMachineInListMapper.updateById(wmsMachineInList);
                status= true;
            }
        }catch (Exception ex){
            log.error(ex.toString());
            status=false;
        }
        return status;
    }


    /**
     ***生成料架预装明细
     */
    public boolean  taryPreDetails(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        log.info("准备生成料架预装明细:"+ JSON.toJSONString(item));
        boolean status=false;
        try {
            //是否有料架预装明细
            List<WmsMachineMaterialsBoxPrePacking> machineMaterialsTrayPrePackingList = wmsMachineMaterialsBoxPrePackingMapper.selectMaterialCode(2);
            if (machineMaterialsTrayPrePackingList == null || machineMaterialsTrayPrePackingList.isEmpty())
            {
                //没有料架预装明细，判断是否有机物料库存料架
                //获取机物料库存，是否有相同机物料库存，
                // 如果没有相同机物料库存，是否有其他机物料库存
                // 都没有，出空料架
                status=IsTaryStockExist(item, existingMaterial);

            } else
            {
                //有料架预装明细
                //是否有相同机物料预装明细
                List<WmsMachineMaterialsBoxPrePacking> trayPrePackingList = machineMaterialsTrayPrePackingList.stream()
                        .filter(Container -> Container != null &&
                                Container.getMaterialCode() != null &&
                                item.getMaterialCode() != null &&
                                Container.getMaterialCode().equals(item.getMaterialCode()))
                        .collect(Collectors.toList());
                if (trayPrePackingList.isEmpty())
                {
                    //没有相同机物料预装明细
                    WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                    machineMaterialsBoxPrePackingModel.setBoxCode(machineMaterialsTrayPrePackingList.get(0).getBoxCode());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                    machineMaterialsBoxPrePackingModel.setContainerType(2);
                    machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                    machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                    machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                    machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
//                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                    machineMaterialsBoxPrePackingModel.setPendingQuantity(machineMaterialsTrayPrePackingList.get(0).getPendingQuantity() + item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setGridStatus(2);
                    machineMaterialsBoxPrePackingModel.setStatus(1);
                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                    wmsMachineInList.setStatus(1);
                    wmsMachineInListMapper.updateById(wmsMachineInList);
                    status = true;
                } else {
                    //有相同机物料预装明细
                    WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                    machineMaterialsBoxPrePackingModel.setBoxCode(trayPrePackingList.get(0).getBoxCode());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                    machineMaterialsBoxPrePackingModel.setContainerType(2);
                    machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                    machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                    machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                    machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
//                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                    machineMaterialsBoxPrePackingModel.setPendingQuantity(trayPrePackingList.get(0).getPendingQuantity() + item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setGridStatus(2);
                    machineMaterialsBoxPrePackingModel.setStatus(1);
                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setInStoreNumber(trayPrePackingList.get(0).getInStoreNumber()+','+item.getInStoreNumber());
                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                    wmsMachineInList.setStatus(1);
                    wmsMachineInListMapper.updateById(wmsMachineInList);
                    status = true;
                }
            }
        }catch (Exception ex){
            log.error(ex.toString());
            status=false;
        }
        return status;
    }


    /**
     ***生出库前先校验库存是否租足够
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<ResultPreDTO> OutCheckInventory(List<MachineMaterialOutBoundDTO> model) {
        List<VerifyOutBoundDTO> insufficientList = new ArrayList<>();
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try {
            //根据物料编码分组求和
            Map<String, Integer> machineMaterialOutBoundDTOList = model.stream()
                    .collect(Collectors.groupingBy(MachineMaterialOutBoundDTO::getMaterialCode, Collectors
                            .summingInt(MachineMaterialOutBoundDTO::getOutQuantity)));

            //分组求和
            List<MachineMaterialOutBoundDTO> machineMaterialOutBoundList = new ArrayList<>();
            machineMaterialOutBoundDTOList.forEach((k, v) -> {
                MachineMaterialOutBoundDTO machineMaterialOutBoundDTO = new MachineMaterialOutBoundDTO();
                machineMaterialOutBoundDTO.setOutQuantity(v);
                machineMaterialOutBoundDTO.setMaterialCode(k);
                machineMaterialOutBoundList.add(machineMaterialOutBoundDTO);
            });

            //校验库存是否足够
            for (MachineMaterialOutBoundDTO item : machineMaterialOutBoundList)
            {
                //先判断每种机物料库存总数是否足够
                materialOutBoundDetailDTO materialOutBoundDetail = boxItemMapper.checkInventory(item.getMaterialCode());
                if (materialOutBoundDetail == null)
                {
                    log.error("找不到机物料" + item.getMaterialName() + "的库存，是否继续出库");
                    VerifyOutBoundDTO verifyOutBoundDTO = new VerifyOutBoundDTO();
                    verifyOutBoundDTO.setMaterialName(item.getMaterialName());
                    verifyOutBoundDTO.setMaterialCode(item.getMaterialCode());
                    verifyOutBoundDTO.setOutQuantity(item.getOutQuantity());
                    verifyOutBoundDTO.setOutQuantity(0);
                    insufficientList.add(verifyOutBoundDTO);
//                    int number = 0;//托盘库存
//                    for (BoxItem boxItem : list) {
//                        if (boxItem.getBoxType().equals("2")) {
//                            int materialQuantity = boxItem.getMaterialQuantity();
//                            number += materialQuantity;
//                        }
//                    }
//                    if (number > 0)
//                    {
//                        //料架有库存，判断出库数量是否大于料架库存数量
//                        if (item.getOutQuantity() >= number)
//                        {
//                            //出库数量是否大于所有库存（料箱+料架）
//                            int numberTray = list.stream()
//                                    .mapToInt(BoxItem::getMaterialQuantity)
//                                    .sum();
//                            if (item.getOutQuantity() > numberTray)
//                            {
//                                log.error("机物料"+item.getMaterialName()+"的库存不足，是否继续出库");
//                                return status;//库存不够
//                            } else
//                            {
//                                status = OutBound(item, 1, number);
//                            }
//                        } else
//                        {
//                            //小于料架库存，直接出料架
//                            status = OutBound(item, 2, number);
//                        }
//                    } else
//                    {
//                        //料架没库存
//
//                    }
                } else
                {
                    if (materialOutBoundDetail.getMaterialQuantity() < item.getOutQuantity()) {
                        log.error("机物料" + item.getMaterialName() + "的库存不足，是否继续出库");
                        VerifyOutBoundDTO verifyOutBoundDTO = new VerifyOutBoundDTO();
                        verifyOutBoundDTO.setMaterialName(item.getMaterialName());
                        verifyOutBoundDTO.setMaterialCode(item.getMaterialCode());
                        verifyOutBoundDTO.setOutQuantity(item.getOutQuantity());
                        verifyOutBoundDTO.setOutQuantity(materialOutBoundDetail.getMaterialQuantity());
                        insufficientList.add(verifyOutBoundDTO);
                    }
                }
            }

            if(insufficientList.isEmpty())
            {
                //库存足够，直接出库
                result =OutBound(model,machineMaterialOutBoundList);
                if(result.isSuccess()){
                    log.info("出库成功"+JSON.toJSONString(model));
                }
            }
        } catch (Exception ex) {
            log.error(ex.toString());
            throw new RuntimeException("出库异常："+ex.getMessage(), ex);
        }
//        CheckInventoryResultDTO resultDTO = new CheckInventoryResultDTO(insufficientList);
        return result;
    }

    /** 出库拣货
     ***机物料出库,生成出库明细和出库任务
     * modelList 每条出库单数据
     * model 每种机物料需要出库总数
     */
    public Result<ResultPreDTO> OutBound(List<MachineMaterialOutBoundDTO> modelList,List<MachineMaterialOutBoundDTO> model)
    {
//        boolean status=false;
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try
        {
            for (MachineMaterialOutBoundDTO item : modelList)
            {
                Integer quantity = item.getOutQuantity();//待出库数量
                materialOutBoundDetailDTO materialOutBoundDetail = boxItemMapper.materialOutBound(item.getMaterialCode(), item.getOutQuantity());
                if(materialOutBoundDetail==null)
                {
                    //单个箱子机物料不满足出库数量
                    List<materialOutBoundDetailDTO> boxItemList=boxItemMapper.boxGridInventoryList(item.getMaterialCode());
                    for (materialOutBoundDetailDTO boxItem:boxItemList)
                    {
                        if(quantity>0)
                        {
                            if(quantity>=boxItem.getMaterialQuantity())
                            {
                                //生成出库明细
                                WmsMachineOutDetailRecord wmsMachineOutDetailRecord = new WmsMachineOutDetailRecord();
                                wmsMachineOutDetailRecord.setOutStoreNumber(item.getOutStoreNumber());
                                wmsMachineOutDetailRecord.setOutQuantity(boxItem.getMaterialQuantity());
                                wmsMachineOutDetailRecord.setBoxNo(boxItem.getBoxNo());
                                wmsMachineOutDetailRecord.setStatus(1);
                                wmsMachineOutDetailRecord.setObjectId(item.getObjectId());
                                if(boxItem.getBoxNo().contains("F"))
                                {
                                    wmsMachineOutDetailRecord.setGridNo(boxItem.getGridId());
                                    wmsMachineOutDetailRecord.setContainerType(1);
                                }else {
                                    wmsMachineOutDetailRecord.setContainerType(2);
                                }
                                wmsMachineOutDetailRecord.setCreateTime(DateUtils.getDate());
                                wmsMachineOutDetailRecord.setMaterialCode(item.getMaterialCode());
                                wmsMachineOutDetailRecord.setCreateBy(ShiroUtils.getLoginName());
                                wmsMachineOutDetailRecordMapper.insert(wmsMachineOutDetailRecord);
                                //更新库存数据，出库数量大于等于格子库存数量，删除格子库存
                                QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
                                queryWrapper.eq("box_no", boxItem.getBoxNo());
                                queryWrapper.eq("material_code", item.getMaterialCode());
                                if (boxItem.getBoxNo().contains("F")) {
                                    queryWrapper.eq("grid_id", boxItem.getGridId());
                                }
                                BoxItem boxItemInfo=boxItemMapper.selectOne(queryWrapper.last("LIMIT 1"));
                                if(boxItemInfo!=null)
                                {
                                    boxItemInfo.setMaterialQuantity(0);
                                    boxItemInfo.setGridStatus(0);
                                    boxItemInfo.setMaterialCode("");
                                    boxItemInfo.setMaterialName("");
                                    boxItemInfo.setMaterialModel("");
                                    boxItemInfo.setMaterialColor("");
                                    boxItemInfo.setStatus(0);
                                    boxItemInfo.setUpdateTime(DateUtils.getDate());
                                    boxItemMapper.updateById(boxItemInfo);
                                }
//                                boxItemMapper.delete(queryWrapper);
                                if(boxItem.getBoxNo().contains("F"))
                                {
                                    //如果容器为料箱，修改料箱使用格数
                                    QueryWrapper<Container> queryWrapperContainer = new QueryWrapper<>();
                                    queryWrapperContainer.eq("box_no", boxItem.getBoxNo());
                                    Container containerInfo = containerMapper.selectOne(queryWrapperContainer.last("LIMIT 1"));
                                    containerInfo.setBoxEmptyStatus(containerInfo.getBoxEmptyStatus() - 1);
                                    containerMapper.updateById(containerInfo);
                                }
                                quantity=quantity-boxItem.getMaterialQuantity();
//                                status=true;
                                result.setSuccess(true);
                            }else
                            {
                                //生成出库明细
                                WmsMachineOutDetailRecord wmsMachineOutDetailRecord = new WmsMachineOutDetailRecord();
                                wmsMachineOutDetailRecord.setOutStoreNumber(item.getOutStoreNumber());
                                wmsMachineOutDetailRecord.setOutQuantity(quantity);
                                wmsMachineOutDetailRecord.setBoxNo(boxItem.getBoxNo());
                                wmsMachineOutDetailRecord.setStatus(1);
                                wmsMachineOutDetailRecord.setObjectId(item.getObjectId());
                                if(boxItem.getBoxNo().contains("F"))
                                {
                                    wmsMachineOutDetailRecord.setGridNo(boxItem.getGridId());
                                    wmsMachineOutDetailRecord.setContainerType(1);
                                }else {
                                    wmsMachineOutDetailRecord.setContainerType(2);
                                }
                                wmsMachineOutDetailRecord.setCreateTime(DateUtils.getDate());
                                wmsMachineOutDetailRecord.setMaterialCode(item.getMaterialCode());
                                wmsMachineOutDetailRecord.setCreateBy(ShiroUtils.getLoginName());
                                wmsMachineOutDetailRecordMapper.insert(wmsMachineOutDetailRecord);
                                //出库数量小于格子库存数量，更新库存数据
                                QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
                                queryWrapper.eq("box_no", boxItem.getBoxNo());
                                queryWrapper.eq("material_code", boxItem.getMaterialCode());
                                if(boxItem.getBoxNo().contains("F")) {
                                    queryWrapper.eq("grid_id", boxItem.getGridId());
                                }
                                BoxItem boxItemInfo = boxItemMapper.selectOne(queryWrapper.last("LIMIT 1"));
                                Integer stockQuantity=boxItem.getMaterialQuantity()-quantity;
                                boxItemInfo.setMaterialQuantity(stockQuantity);
                                boxItemMapper.updateById(boxItemInfo);
                                quantity=boxItem.getMaterialQuantity()-quantity;
//                                status=true;
                                result.setSuccess(true);
                            }
                        }else {
                            break;
                        }
                    }
                }else
                {
                    //单个箱子机物料满足出库数量
                    QueryWrapper<BoxItem> query = new QueryWrapper<>();
                    query.eq("box_no", materialOutBoundDetail.getBoxNo());
                    query.eq("material_code", materialOutBoundDetail.getMaterialCode());
                    List<BoxItem> boxItemList = boxItemMapper.selectList(query);//单个料箱格子列表
                    for (BoxItem boxItem : boxItemList)
                    {
                        if(quantity>0)
                        {
                            if (quantity <= boxItem.getMaterialQuantity())
                            {
                                //生成出库明细
                                WmsMachineOutDetailRecord wmsMachineOutDetailRecord = new WmsMachineOutDetailRecord();
                                wmsMachineOutDetailRecord.setOutStoreNumber(item.getOutStoreNumber());
                                wmsMachineOutDetailRecord.setOutQuantity(quantity);
                                wmsMachineOutDetailRecord.setBoxNo(boxItem.getBoxNo());
                                wmsMachineOutDetailRecord.setStatus(1);
                                wmsMachineOutDetailRecord.setObjectId(item.getObjectId());
                                if(boxItem.getBoxNo().contains("F"))
                                {
                                    wmsMachineOutDetailRecord.setGridNo(boxItem.getGridId());
                                    wmsMachineOutDetailRecord.setContainerType(1);
                                }else {
                                    wmsMachineOutDetailRecord.setContainerType(2);
                                }
                                wmsMachineOutDetailRecord.setCreateTime(DateUtils.getDate());
                                wmsMachineOutDetailRecord.setMaterialCode(item.getMaterialCode());
                                wmsMachineOutDetailRecord.setCreateBy(ShiroUtils.getLoginName());
                                wmsMachineOutDetailRecordMapper.insert(wmsMachineOutDetailRecord);
                                //更新库存数据
                                QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
                                queryWrapper.eq("box_no", boxItem.getBoxNo());
                                queryWrapper.eq("material_code", boxItem.getMaterialCode());
                                if(boxItem.getBoxNo().contains("F")) {
                                    queryWrapper.eq("grid_id", boxItem.getGridId());
                                }
                                BoxItem boxItemInfo = boxItemMapper.selectOne(queryWrapper.last("LIMIT 1"));
                                boxItemInfo.setMaterialQuantity(boxItem.getMaterialQuantity()-quantity);
                                if(quantity==boxItem.getMaterialQuantity())
                                {
                                    boxItemInfo.setGridStatus(0);
                                    boxItemInfo.setMaterialCode("");
                                    boxItemInfo.setMaterialName("");
                                    boxItemInfo.setMaterialModel("");
                                    boxItemInfo.setMaterialColor("");
                                    boxItemInfo.setStatus(0);
                                }
                                boxItemMapper.updateById(boxItemInfo);
                                quantity=boxItem.getMaterialQuantity()-quantity;
    //                            status=true;
                                result.setSuccess(true);
                                break;
                            }
//                            else if (quantity == boxItem.getMaterialQuantity())
//                            {
//                                //生成出库明细
//                                WmsMachineOutDetailRecord wmsMachineOutDetailRecord = new WmsMachineOutDetailRecord();
//                                wmsMachineOutDetailRecord.setOutStoreNumber(item.getOutStoreNumber());
//                                wmsMachineOutDetailRecord.setOutQuantity(quantity);
//                                wmsMachineOutDetailRecord.setBoxNo(boxItem.getBoxNo());
//                                wmsMachineOutDetailRecord.setStatus(1);
//                                wmsMachineOutDetailRecord.setObjectId(item.getObjectId());
//                                if(boxItem.getBoxNo().contains("F"))
//                                {
//                                    wmsMachineOutDetailRecord.setGridNo(boxItem.getGridId());
//                                    wmsMachineOutDetailRecord.setContainerType(1);
//                                }else {
//                                    wmsMachineOutDetailRecord.setContainerType(2);
//                                }
//                                wmsMachineOutDetailRecord.setCreateTime(DateUtils.getDate());
//                                wmsMachineOutDetailRecord.setMaterialCode(item.getMaterialCode());
//                                wmsMachineOutDetailRecord.setCreateBy(ShiroUtils.getLoginName());
//                                wmsMachineOutDetailRecordMapper.insert(wmsMachineOutDetailRecord);
//                                //更新库存数据
//                                QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
//                                queryWrapper.eq("box_no", boxItem.getBoxNo());
//                                queryWrapper.eq("material_code", boxItem.getMaterialCode());
//                                if(boxItem.getBoxNo().contains("F")) {
//                                    queryWrapper.eq("grid_id", boxItem.getGridId());
//                                }
//                                BoxItem boxItemInfo=boxItemMapper.selectOne(queryWrapper.last("LIMIT 1"));
//                                if(boxItemInfo!=null){
//                                    boxItemInfo.setMaterialQuantity(0);
//                                    boxItemInfo.setGridStatus(0);
//                                    boxItemInfo.setMaterialCode("");
//                                    boxItemInfo.setMaterialName("");
//                                    boxItemInfo.setMaterialModel("");
//                                    boxItemInfo.setMaterialColor("");
//                                    boxItemInfo.setStatus(0);
//                                    boxItemInfo.setUpdateTime(DateUtils.getDate());
//                                    boxItemMapper.updateById(boxItemInfo);
//                                }
//                                if(boxItem.getBoxNo().contains("F")) {
//                                    //修改料箱使用格数
//                                    QueryWrapper<Container> queryWrapperContainer = new QueryWrapper<>();
//                                    queryWrapperContainer.eq("box_no", boxItem.getBoxNo());
//                                    Container containerInfo = containerMapper.selectOne(queryWrapperContainer.last("LIMIT 1"));
//                                    containerInfo.setBoxEmptyStatus(containerInfo.getBoxEmptyStatus() - 1);
//                                    containerMapper.updateById(containerInfo);
//                                }
//                                result.setSuccess(true);
//                                quantity=0;
//                                break;
//                            }
                            else
                            {
                                if (quantity > boxItem.getMaterialQuantity())
                                {
                                    //生成出库明细
                                    WmsMachineOutDetailRecord wmsMachineOutDetailRecord = new WmsMachineOutDetailRecord();
                                    wmsMachineOutDetailRecord.setOutStoreNumber(item.getOutStoreNumber());
                                    wmsMachineOutDetailRecord.setOutQuantity(boxItem.getMaterialQuantity());
                                    wmsMachineOutDetailRecord.setBoxNo(boxItem.getBoxNo());
                                    wmsMachineOutDetailRecord.setStatus(1);
                                    wmsMachineOutDetailRecord.setObjectId(item.getObjectId());
                                    if (boxItem.getBoxNo().contains("F")) {
                                        wmsMachineOutDetailRecord.setGridNo(boxItem.getGridId());
                                        wmsMachineOutDetailRecord.setContainerType(1);
                                    } else {
                                        wmsMachineOutDetailRecord.setContainerType(2);
                                    }
                                    wmsMachineOutDetailRecord.setCreateTime(DateUtils.getDate());
                                    wmsMachineOutDetailRecord.setMaterialCode(item.getMaterialCode());
                                    wmsMachineOutDetailRecord.setCreateBy(ShiroUtils.getLoginName());
                                    wmsMachineOutDetailRecordMapper.insert(wmsMachineOutDetailRecord);
                                    //更新库存数据
                                    QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
                                    queryWrapper.eq("box_no", boxItem.getBoxNo());
                                    queryWrapper.eq("material_code", boxItem.getMaterialCode());
                                    if (boxItem.getBoxNo().contains("F")) {
                                        queryWrapper.eq("grid_id", boxItem.getGridId());
                                    }
                                    BoxItem boxItemInfo = boxItemMapper.selectOne(queryWrapper.last("LIMIT 1"));
                                    if (boxItemInfo != null) {
                                        boxItemInfo.setMaterialQuantity(0);
                                        boxItemInfo.setGridStatus(0);
                                        boxItemInfo.setMaterialCode("");
                                        boxItemInfo.setMaterialName("");
                                        boxItemInfo.setMaterialModel("");
                                        boxItemInfo.setMaterialColor("");
                                        boxItemInfo.setStatus(0);
                                        boxItemInfo.setUpdateTime(DateUtils.getDate());
                                        boxItemMapper.updateById(boxItemInfo);
                                    }
                                    if (boxItem.getBoxNo().contains("F")) {
                                        //修改料箱使用格数
                                        QueryWrapper<Container> queryWrapperContainer = new QueryWrapper<>();
                                        queryWrapperContainer.eq("box_no", boxItem.getBoxNo());
                                        Container containerInfo = containerMapper.selectOne(queryWrapperContainer.last("LIMIT 1"));
                                        containerInfo.setBoxEmptyStatus(containerInfo.getBoxEmptyStatus() - 1);
                                        containerMapper.updateById(containerInfo);
                                    }
                                    quantity = quantity - boxItem.getMaterialQuantity();
                                    result.setSuccess(true);
                                }
                            }
                        }else{
                            break;
                        }
                    }
                }
                if(result.isSuccess())
                {
                    QueryWrapper<WmsMachineOutDetailList> queryWrapper=new QueryWrapper<>();
                    queryWrapper.eq("out_store_number",item.getOutStoreNumber());
                    queryWrapper.eq("object_id",item.getObjectId());
                    WmsMachineOutDetailList wmsMachineOutDetailList=wmsMachineOutDetailListMapper.selectOne(queryWrapper.last("LIMIT 1"));
                    wmsMachineOutDetailList.setStatus(2);
                    wmsMachineOutDetailListMapper.updateById(wmsMachineOutDetailList);
                    result=OutBoxPickingTask();
                }
            }
//            Integer boxType = 0;//容器类型
//            for(MachineMaterialOutBoundDTO modelItem:model)
//            {
//                materialOutBoundDetailDTO materialOutBoundDetail = boxItemMapper.materialOutBound(modelItem.getMaterialCode(), modelItem.getOutQuantity());
//                if (materialOutBoundDetail == null)
//                {
//                    Integer quantity=0;
//                    //库存够，单个容器库存不够
//                    List<materialOutBoundDetailDTO> materialOutBoundDetailList=boxItemMapper.materialOutBoundList(modelItem.getMaterialCode());
//                    for(materialOutBoundDetailDTO materialOutBoundDetailDTOItem:materialOutBoundDetailList)
//                    {
//                        for (MachineMaterialOutBoundDTO item : modelList)
//                        {
//                            if(item.getMaterialCode()==materialOutBoundDetailDTOItem.getMaterialCode())
//                            {
//
//                                if(materialOutBoundDetailDTOItem.getBoxNo().equals("F"))
//                                {
//                                    //料箱
//                                    boxType=1;
//                                    //生成出库明细
//                                    WmsMachineOutDetailList wmsMachineOutDetail = new WmsMachineOutDetailList();
//                                    wmsMachineOutDetail.setOutStoreNumber(item.getOutStoreNumber());
//                                    wmsMachineOutDetail.setOutQuantity(materialOutBoundDetailDTOItem.getMaterialQuantity());
//                                    wmsMachineOutDetail.setBoxNo(materialOutBoundDetail.getBoxNo());
//                                    wmsMachineOutDetail.setStatus(1);
//                                    wmsMachineOutDetail.setGridNo(materialOutBoundDetailDTOItem.getGridId());
//                                    wmsMachineOutDetail.setCreateTime(DateUtils.getDate());
//                                    wmsMachineOutDetail.setMaterialCode(modelItem.getMaterialCode());
//                                    wmsMachineOutDetail.setCreateBy(ShiroUtils.getLoginName());
//                                    wmsMachineOutDetailListMapper.insert(wmsMachineOutDetail);
//                                    quantity = quantity + materialOutBoundDetailDTOItem.getMaterialQuantity();
//                                    //更新库存数据
//                                    QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
//                                    queryWrapper.eq("box_no", boxItem.getBoxNo());
//                                    queryWrapper.eq("grid_id", boxItem.getGridId());
////                                    BoxItem boxItemInfo=boxItemMapper.selectOne(queryWrapper);
//                                    boxItemMapper.delete(queryWrapper);
//                                }else
//                                {
//                                    //料架
//                                    boxType=2;
//                                }
//                            }
//                        }
//                    }
//                } else
//                {
//                    //库存够，单个容器库存满足出库数量
//                    for (MachineMaterialOutBoundDTO item : modelList)
//                    {
//                        Integer quantity = 0;//待出库数量
//                        if (Objects.equals(item.getMaterialCode(), materialOutBoundDetail.getMaterialCode()))
//                        {
//                            if (materialOutBoundDetail.getBoxNo().equals("F")) {
//                                //料箱
//                                boxType = 1;
//                                quantity = item.getOutQuantity();
//                                List<materialOutBoundDetailDTO> boxList = boxItemMapper.materialBoxGridSum(materialOutBoundDetail.getBoxNo(), materialOutBoundDetail.getMaterialCode());
//                                for (materialOutBoundDetailDTO boxItem : boxList) {
//                                    if (quantity >= boxItem.getMaterialQuantity()) {
//                                        //生成出库明细
//                                        WmsMachineOutDetailList wmsMachineOutDetail = new WmsMachineOutDetailList();
//                                        wmsMachineOutDetail.setOutStoreNumber(item.getOutStoreNumber());
//                                        wmsMachineOutDetail.setOutQuantity(boxItem.getMaterialQuantity());
//                                        wmsMachineOutDetail.setBoxNo(materialOutBoundDetail.getBoxNo());
//                                        wmsMachineOutDetail.setStatus(1);
//                                        wmsMachineOutDetail.setGridNo(boxItem.getGridId());
//                                        wmsMachineOutDetail.setCreateTime(DateUtils.getDate());
//                                        wmsMachineOutDetail.setMaterialCode(modelItem.getMaterialCode());
//                                        wmsMachineOutDetail.setCreateBy(ShiroUtils.getLoginName());
//                                        wmsMachineOutDetailListMapper.insert(wmsMachineOutDetail);
//                                        quantity = quantity - boxItem.getMaterialQuantity();
//                                        //更新库存数据
//                                        QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
//                                        queryWrapper.eq("box_no", boxItem.getBoxNo());
//                                        queryWrapper.eq("grid_id", boxItem.getGridId());
////                                    BoxItem boxItemInfo=boxItemMapper.selectOne(queryWrapper);
//                                        boxItemMapper.delete(queryWrapper);
//                                    } else {
//                                        if (quantity > 0) {
//                                            //生成出库明细
//                                            WmsMachineOutDetailList wmsMachineOutDetail = new WmsMachineOutDetailList();
//                                            wmsMachineOutDetail.setOutStoreNumber(item.getOutStoreNumber());
//                                            wmsMachineOutDetail.setOutQuantity(quantity);
//                                            wmsMachineOutDetail.setBoxNo(materialOutBoundDetail.getBoxNo());
//                                            wmsMachineOutDetail.setStatus(1);
//                                            wmsMachineOutDetail.setGridNo(boxItem.getGridId());
//                                            wmsMachineOutDetail.setCreateTime(DateUtils.getDate());
//                                            wmsMachineOutDetail.setMaterialCode(modelItem.getMaterialCode());
//                                            wmsMachineOutDetail.setCreateBy(ShiroUtils.getLoginName());
//                                            wmsMachineOutDetailListMapper.insert(wmsMachineOutDetail);
//                                            quantity = quantity - boxItem.getMaterialQuantity();
//                                            //更新库存数据
//                                            QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
//                                            queryWrapper.eq("box_no", boxItem.getBoxNo());
//                                            queryWrapper.eq("grid_id", boxItem.getGridId());
//                                            BoxItem boxItemInfo = boxItemMapper.selectOne(queryWrapper);
//                                            boxItemInfo.setMaterialQuantity(boxItemInfo.getMaterialQuantity() - quantity);
//                                            boxItemMapper.updateById(boxItemInfo);
//                                        }
//                                    }
//                                }
//                            } else
//                            {
//                                //料架
//                                //生成出库明细
//                                boxType = 2;
//                                WmsMachineOutDetailList wmsMachineOutDetail = new WmsMachineOutDetailList();
//                                wmsMachineOutDetail.setOutStoreNumber(item.getOutStoreNumber());
//                                wmsMachineOutDetail.setOutQuantity(item.getOutQuantity());
//                                wmsMachineOutDetail.setBoxNo(materialOutBoundDetail.getBoxNo());
//                                wmsMachineOutDetail.setStatus(1);
//                                wmsMachineOutDetail.setCreateTime(DateUtils.getDate());
//                                wmsMachineOutDetail.setMaterialCode(modelItem.getMaterialCode());
//                                wmsMachineOutDetail.setCreateBy(ShiroUtils.getLoginName());
//                                wmsMachineOutDetailListMapper.insert(wmsMachineOutDetail);
//                                if (item.getOutQuantity() == materialOutBoundDetail.getMaterialQuantity()) {
//                                    //更新库存数据
//                                    QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
//                                    queryWrapper.eq("box_no", materialOutBoundDetail.getBoxNo());
//                                    queryWrapper.eq("material_code", materialOutBoundDetail.getMaterialCode());
////                                    BoxItem boxItemInfo=boxItemMapper.selectOne(queryWrapper);
//                                    boxItemMapper.delete(queryWrapper);
//                                } else {
//                                    //更新库存数据
//                                    QueryWrapper<BoxItem> queryWrapper = new QueryWrapper<>();
//                                    queryWrapper.eq("box_no", materialOutBoundDetail.getBoxNo());
//                                    queryWrapper.eq("material_code", materialOutBoundDetail.getMaterialCode());
//                                    BoxItem boxItemInfo = boxItemMapper.selectOne(queryWrapper);
//                                    boxItemInfo.setMaterialQuantity(boxItemInfo.getMaterialQuantity() - quantity);
//                                    boxItemMapper.updateById(boxItemInfo);
//                                }
//
//                            }
//                            //生成容器出库任务
//                            WmsBoxTaskList wmsBoxTaskList = new WmsBoxTaskList();
//                            QueryWrapper<WmsStation> queryMapper = new QueryWrapper<>();
//                            queryMapper.eq("station_type", 2);
//                            WmsStation stationInfo = wmsStationMapper.selectOne(queryMapper);
//                            if (stationInfo == null) {
//                                log.error("出库站点为空，无法进行出库；");
//                                status = false;
//                            } else {
//                                wmsBoxTaskList.setToSite(stationInfo.getStationCode());//出库口
//                            }
//
//                            wmsBoxTaskList.setBoxNo(materialOutBoundDetail.getBoxNo());
//                            wmsBoxTaskList.setBoxType(boxType);
//                            wmsBoxTaskList.setTaskStatus(0);
//                            wmsBoxTaskList.setTaskType(3);
//                            wmsBoxTaskList.setCreateTime(DateUtils.getDate());
//                            wmsBoxTaskList.setCreateBy(ShiroUtils.getLoginName());
//                            wmsBoxTaskListMapper.insert(wmsBoxTaskList);
//                        }
//                    }
//                }
//            }

        }catch (Exception ex)
        {
            log.error(ex.toString());
//            status=false;
            result.setSuccess(false);
            result.setMessage("出库拣货任务异常："+ex.toString());
            throw new RuntimeException("出库异常："+result.getMessage());
        }
        return result;
    }

    /**
     * 生成出库拣货任务
     */
    public Result<ResultPreDTO> OutBoxPickingTask()
    {
//        boolean status=false;
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try{
            //预装完成，生成出库任务
            QueryWrapper<WmsMachineOutDetailRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1);
            queryWrapper.eq("delete_flag", 0);
            List<WmsMachineOutDetailRecord> wmsMachineOutDetailRecordList = wmsMachineOutDetailRecordMapper.selectList(queryWrapper);

            List<WmsMachineOutDetailRecord> list = wmsMachineOutDetailRecordList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(WmsMachineOutDetailRecord::getBoxNo, p -> p, (p1, p2) -> p1), // 使用第一个遇到的元素作为值，如果有重复则忽略后面的元素
                            map -> new ArrayList<>(map.values()) // 将map的values转换回list
                    ));
            for(WmsMachineOutDetailRecord preList:list)
            {
                QueryWrapper<WmsBoxTaskList> queryTaskWrapper = new QueryWrapper<>();
                queryTaskWrapper.eq("box_no", preList.getBoxNo());
                queryTaskWrapper.lt("task_status", 5);
                queryTaskWrapper.eq("delete_flag", 0);
                WmsBoxTaskList  WmsBoxTaskInfo=wmsBoxTaskListMapper.selectOne(queryTaskWrapper.last("LIMIT 1"));
                if(WmsBoxTaskInfo==null)
                {
                    //当前料箱没有任务时才会生成出库任务
                    WmsBoxTaskList wmsBoxTaskList = new WmsBoxTaskList();
                    QueryWrapper<WmsMachineOutbound> query = new QueryWrapper<>();
                    query.eq("out_store_number", preList.getOutStoreNumber());
                    WmsMachineOutbound wmsMachineOutboundInfo=wmsMachineOutboundMapper.selectOne(query.last("LIMIT 1"));
                    if(wmsMachineOutboundInfo!=null){
                        wmsBoxTaskList.setTaskType(3);
                    }else{
                        wmsBoxTaskList.setTaskType(10);
                    }

                    Shelf shelfInfo=new Shelf();
                    QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
                    shelfQueryWrapper.eq("box_no", preList.getBoxNo());//料架
                    shelfQueryWrapper.eq("locked", 0);
                    shelfQueryWrapper.eq("state", 1);
                    shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
                    if(shelfInfo!=null) {
//                    String fromSite="";
//                    QueryWrapper<Container> ContainerWrapper=new QueryWrapper<>();
//                    ContainerWrapper.eq("material_type",2);
//                    List<Container> containerList= containerMapper.selectList(ContainerWrapper);
//                    if(preList.getContainerType()==1)
//                    {
//                        if (containerList.isEmpty()) {
//                            //分配库位
//                            QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
//                            shelfQueryWrapper.eq("box_type", 1);//料箱
//                            shelfQueryWrapper.eq("locked", 0);
//                            shelfQueryWrapper.eq("state", 0);
//                            shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
//                            fromSite = shelfInfo.getRobotCode();
//                        } else {
//                            for (Container item : containerList) {
//                                QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
//                                shelfQueryWrapper.eq("box_no", item.getBoxNo());
//                                shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
//                                if (shelfInfo != null) {
//                                    Shelf shelf = SerEMptyStock(shelfInfo.getColumn(), shelfInfo.getRow());
//                                    if (shelf != null) {
//                                        fromSite = shelfInfo.getRobotCode();
//                                        break;
//                                    }
//                                }
//                            }
//                            if (fromSite.isEmpty()) {
//                                //分配库位
//                                QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
//                                shelfQueryWrapper.eq("box_type", 1);
//                                shelfQueryWrapper.eq("locked", 0);
//                                shelfQueryWrapper.eq("state", 0);
//                                shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
//                                fromSite = shelfInfo.getRobotCode();
//                            }
//                        }
//                    } else if (preList.getContainerType() == 2)
//                    {
//                        //分配库位
//                        QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
//                        shelfQueryWrapper.eq("box_type", 2);//料架
//                        shelfQueryWrapper.eq("locked", 0);
//                        shelfQueryWrapper.eq("state", 0);
//                        shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
//                        fromSite = shelfInfo.getRobotCode();
//                    }
                        String orderType = wmsBoxTaskListService.getTaskOrder();
                        wmsBoxTaskList.setTaskOrder(orderType);
                        wmsBoxTaskList.setBoxNo(preList.getBoxNo());
                        wmsBoxTaskList.setBoxType(preList.getContainerType());
                        wmsBoxTaskList.setTaskStatus(0);
//                        wmsBoxTaskList.setTaskType(3);
//                    wmsBoxTaskList.setToSite(stationInfo.getStationCode());
                        if (preList.getContainerType() == 1) {
                            wmsBoxTaskList.setToSite("RGZQ3A01011");//料箱出库口
                        } else if (preList.getContainerType() == 2) {
                            wmsBoxTaskList.setToSite("0294000AA0299196");//料架出库口
                        }
                        wmsBoxTaskList.setFromSite(shelfInfo.getCode());
                        wmsBoxTaskList.setPriority(1);
                        wmsBoxTaskList.setPushStatus(2);
                        wmsBoxTaskList.setCreateTime(DateUtils.getDate());
                        wmsBoxTaskList.setCreateBy(ShiroUtils.getLoginName());
                        wmsBoxTaskListMapper.insert(wmsBoxTaskList);
                        shelfInfo.setLocked(1);
                        shelfMapper.updateById(shelfInfo);
//                        status=true;
                        result.setSuccess(true);
                    }else{
                        log.error("生成出库拣货任务失败：找不到库位信息");
                        result.setSuccess(false);
                        result.setCode(500);
                        result.setMessage("生成出库拣货任务失败：找不到库位信息:"+JSON.toJSONString(preList));
                        return result;
                    }
                }else {
                    log.info("已存在容器："+preList.getBoxNo()+"的任务");
                }
            }

        }catch (Exception ex){
            log.error("生成料箱出库拣货任务失败："+ex.toString());
//            status=false;
            result.setSuccess(false);
            result.setCode(500);
            result.setMessage("生成出库拣货任务失败：找不到库位信息:"+JSON.toJSONString(ex.toString()));
        }
        return result;
    }


    /**
     * 拣货完成入库
     * @param model
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<ResultPreDTO> OutBoundDetails(MachineMaterialOutBoundRecordDTO model){
        Result<ResultPreDTO> result=new Result<>();
        result.setSuccess(false);
        try{
            WmsBoxTaskList wmsBoxTaskList=new WmsBoxTaskList();
            WmsMachineOutDetailRecord wmsMachineOutDetailRecord=new WmsMachineOutDetailRecord();
            for(MachineMaterialOutBoundRecordListDTO item:model.getMachineMaterialOutBoundRecordListDTOList())
            {
                //1.解绑，更新拣货明细状态
                QueryWrapper<WmsMachineOutDetailRecord> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("out_store_number", item.getOutStoreNumber());
//                queryWrapper.eq("object_id", item.getObjectId());
                queryWrapper.eq("delete_flag", 0);
                queryWrapper.eq("box_no", model.getBoxNo());
                if(model.getBoxNo().contains("F")) {
                    queryWrapper.eq("grid_no", item.getGridNo());
                }
                wmsMachineOutDetailRecord = wmsMachineOutDetailRecordMapper.selectOne(queryWrapper.last("LIMIT 1"));
                if (wmsMachineOutDetailRecord != null) {
                    wmsMachineOutDetailRecord.setStatus(3);
                    wmsMachineOutDetailRecordMapper.updateById(wmsMachineOutDetailRecord);
                } else {
                    result.setMessage("找不到出库单" + item.getOutStoreNumber() + "的拣货记录");
                    result.setCode(500);
                    throw new RuntimeException("解绑异常："+result.getMessage());
                }

                //2.检查容器状态，生成容器入库任务
                QueryWrapper<BoxItem> queryBoxWrapper = new QueryWrapper<>();
                queryBoxWrapper.eq("box_no", model.getBoxNo());
                Integer gridNo = 0;
                if (model.getBoxNo().contains("F"))
                {
                    List<BoxItem> boxItemList = boxItemMapper.selectList(queryBoxWrapper);//料箱
                    for (BoxItem list : boxItemList)
                    {
                        if (list.getGridId().equals(item.getGridNo()))
                        {
                            if(list.getMaterialQuantity()>item.getInQuantity())
                            {
                                list.setMaterialQuantity(list.getMaterialQuantity() - item.getInQuantity());
                                boxItemMapper.updateById(list);
                            }else
                            {
                                //更新料箱明细信息
                                list.setMaterialQuantity(0);
                                list.setStatus(0);
                                list.setMaterialName("");
                                list.setMaterialCode("");
                                boxItemMapper.updateById(list);
                                //更新料箱信息
                                QueryWrapper<Container> containerQueryWrapper = new QueryWrapper<>();
                                containerQueryWrapper.eq("box_no", model.getBoxNo());
                                Container containerInfo = containerMapper.selectOne(containerQueryWrapper.last("LIMIT 1"));
                                int boxStatus=containerInfo.getBoxEmptyStatus()-1;
//                                containerInfo.setBoxEmptyStatus(gridStatus);
                                if(boxStatus>0){
                                    containerInfo.setBoxEmptyStatus(boxStatus);
                                }else
                                {
                                    containerInfo.setBoxEmpty(0);
                                    containerInfo.setMaterialType(0);
                                }
                                containerMapper.updateById(containerInfo);
                            }
                        }
                    }
                } else if (model.getBoxNo().contains("P"))
                {
                    List<BoxItem> boxItemList = boxItemMapper.selectList(queryBoxWrapper);//料架
                    if (!boxItemList.isEmpty())
                    {
                        for(BoxItem boxItemInfo:boxItemList)
                        {
                            boxItemInfo.setGridVolume(model.getVolume());
                            if(boxItemInfo.getMaterialCode().equals(item.getMaterialCode()))
                            {
                                if(item.getInQuantity()<boxItemInfo.getMaterialQuantity())
                                {
                                    boxItemInfo.setMaterialQuantity(boxItemInfo.getMaterialQuantity()-item.getInQuantity());
                                    boxItemMapper.updateById(boxItemInfo);
                                }else
                                {
                                    boxItemMapper.deleteById(boxItemInfo);
                                }
                            }
                        }
                    } else
                    {
                        log.error("找不到料架：" + model.getBoxNo() + "的信息");
                        result.setSuccess(false);
                        result.setCode(500);
                        result.setMessage("找不到料架：" + model.getBoxNo() + "的信息");
                        throw new RuntimeException("解绑异常："+result.getMessage());
                    }
                } else {
                    log.error("找不到该容器信息：" + model.getBoxNo());
                    result.setCode(500);
                    result.setMessage("找不到该容器信息：" + model.getBoxNo());
                    result.setSuccess(false);
                    throw new RuntimeException("解绑异常："+result.getMessage());
                }
            }
            //解绑完成，判断料架是否为空
            if (model.getBoxNo().contains("P"))
            {
                QueryWrapper<BoxItem> queryBoxWrapper = new QueryWrapper<>();
                queryBoxWrapper.eq("box_no", model.getBoxNo());
                queryBoxWrapper.eq("grid_volume", 0);
                BoxItem boxItem=boxItemMapper.selectOne(queryBoxWrapper.last("LIMIT 1"));
                if(boxItem!=null)
                {
                    QueryWrapper<Container> queryContainerWrapper = new QueryWrapper<>();
                    queryContainerWrapper.eq("box_no", model.getBoxNo());
                    Container container=containerMapper.selectOne(queryContainerWrapper.last("LIMIT 1"));
                    container.setMaterialType(0);
                    containerMapper.updateById(container);
                }

            }

            //生成入库任务,先检查是否有空库位
            QueryWrapper<Shelf> query = new QueryWrapper<>();
            query.eq("locked", 0);
            query.eq("state", 0);
            if (model.getBoxNo().contains("F")) {
                query.eq("box_type", 1);
                wmsBoxTaskList.setFromSite("RGZQ1A01011");//料箱起点固定
                wmsBoxTaskList.setBoxType(1);
            } else if (model.getBoxNo().contains("P")) {
                query.eq("box_type", 2);
                wmsBoxTaskList.setFromSite("0294000AA0299196");//料架起点固定
                wmsBoxTaskList.setBoxType(2);
            }

            List<Shelf> shelfList =shelfMapper.selectList(query);
            List<Shelf> areaList= shelfList.stream()
                    .filter(Container -> Container.getArea().equals("B"))
                    .collect(Collectors.toList());
            if(areaList.isEmpty())
            {
                List<Shelf> areaListA= shelfList.stream()
                        .filter(Container -> Container.getArea().equals("A"))
                        .collect(Collectors.toList());
                if (areaListA.isEmpty())
                {
                    log.error("入库失败,没有可用空库位；"+JSON.toJSONString(model));
                    result.setSuccess(false);
                    result.setMessage("入库失败,没有可用空库位；"+JSON.toJSONString(model));
                    throw new RuntimeException("解绑异常："+result.getMessage());
                }else
                {
                    wmsBoxTaskList.setToSite(areaListA.get(0).getCode());//目的库位
                    query.eq("code", areaListA.get(0).getCode());
                    Shelf shelf=shelfMapper.selectOne(query.last("LIMIT 1"));
                    shelf.setLocked(1);
                    shelfMapper.updateById(shelf);
                }
            }else
            {
                wmsBoxTaskList.setToSite(areaList.get(0).getCode());//目的库位
                query.eq("code", areaList.get(0).getCode());
                Shelf shelf=shelfMapper.selectOne(query.last("LIMIT 1"));
                shelf.setLocked(1);
                shelfMapper.updateById(shelf);
            }
            String orderType = wmsBoxTaskListService.getTaskOrder();
            wmsBoxTaskList.setTaskOrder(orderType);
            wmsBoxTaskList.setMaterialType(1);
            wmsBoxTaskList.setBoxNo(model.getBoxNo());
            wmsBoxTaskList.setTaskStatus(0);
            wmsBoxTaskList.setTaskType(7);
            wmsBoxTaskList.setCreateTime(DateUtils.getDate());
            wmsBoxTaskList.setCreateBy(ShiroUtils.getLoginName());
//                wmsBoxTaskListMapper.insert(wmsBoxTaskList);
            //3.上报OA
            OARequest request=new OARequest();
            request.setActionName("领料出库");
            request.setInputjson(JSON.toJSONString(model.getMachineMaterialOutBoundRecordListDTOList()));
            boolean status=OAService.submitOA(JSON.toJSONString(request));
            if(status){
                wmsMachineOutDetailRecord.setPushStatus(1);
                wmsMachineOutDetailRecordMapper.updateById(wmsMachineOutDetailRecord);
                wmsBoxTaskList.setPushStatus(1);
            }else {
                log.error("出库拣货上报OA失败："+JSON.toJSONString(request));
                stringRedisTemplate.opsForValue().set(orderType,JSON.toJSONString(request));
            }
            wmsBoxTaskListMapper.insert(wmsBoxTaskList);
//                if(model.getBoxNo().contains("F")){
//                    //判断是否为料箱，如多是料箱通知输送线释放
//                    stringRedisTemplate.opsForValue().set(REDIS_BOX_RELEASE_STATUS_KEY,"1");
//                }
//            }
        }catch (Exception ex){
            log.error("拣货解绑异常："+ex.toString());
            result.setSuccess(false);
            result.setCode(500);
            throw new RuntimeException("解绑异常："+result.getMessage());
        }
        return result;
    }

    /**
     * 测试--------------------------------------------------------------------------------------------------------------------------
     * @param model
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<ResultPreDTO> inBound1(List<MachineMaterialInboundDTO> model)
    {
        log.info("机物料开始预装："+JSON.toJSONString(model));
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try {
            for (MachineMaterialInboundDTO item : model)
            {

                /**
                 * 待入库数量
                 */
                int inQuantity=item.getInQuantity();
                //判断是否已经预装
                QueryWrapper<WmsMachineInList> query = new QueryWrapper<>();
                query.eq("id", item.getId());
                query.eq("status", 1);
                //机物料基础信息
                WmsMachineInList wmsMachineInInfo = wmsMachineInListMapper.selectOne(query.last("LIMIT 1"));
                if (wmsMachineInInfo==null)
                {
                    //1.基础信息表查询对应的箱型以及容量
                    QueryWrapper<WmsMachineInfo> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("material_code", item.getMaterialCode());
                    //机物料基础信息
                    WmsMachineInfo existingMaterial = wmsMaterialInfoMapper.selectOne(queryWrapper.last("LIMIT 1"));

                    //判断机物料入库类型
                    if (existingMaterial!=null&&existingMaterial.getPriorityContainerType() <= 8)
                    {
                        //判断入库数量是否超过两个箱子容量，如果超过就装料架
//                        if (item.getInQuantity() / existingMaterial.getCapacity() <= 2)
//                        {
                        //判断是否有机物料预装明细
                        List<WmsMachineMaterialsBoxPrePacking> machineMaterialsBoxPrePackingList = wmsMachineMaterialsBoxPrePackingMapper.selectMaterialCode(1);
                        if (machineMaterialsBoxPrePackingList == null || machineMaterialsBoxPrePackingList.isEmpty())
                        {
                            //判断是否有相同机物料库存明细
                            result=IsBoxStockExist1(item, existingMaterial);
                            if(!result.isSuccess()){
                                log.error("预装失败:" + JSON.toJSONString(item));
                               throw new RuntimeException("预装失败:" + JSON.toJSONString(item));
                            }
                        } else
                        {
                            //预装表
                            //获取相同机物料预装明细
                            List<WmsMachineMaterialsBoxPrePacking> machineMaterialsPrePackingList = machineMaterialsBoxPrePackingList.stream()
                                    .filter(WmsMachineMaterialsBoxPrePacking -> WmsMachineMaterialsBoxPrePacking != null &&
                                            WmsMachineMaterialsBoxPrePacking.getMaterialCode() != null &&
                                            item.getMaterialCode() != null &&
//                                            WmsMachineMaterialsBoxPrePacking.getMaterialCode().equals(item.getMaterialCode()) &&
                                            WmsMachineMaterialsBoxPrePacking.getGridStatus() != null &&
                                            WmsMachineMaterialsBoxPrePacking.getGridStatus() == 2)
                                    .collect(Collectors.toList());
                            //是否有相同机物料预装明细
                            if (machineMaterialsPrePackingList.isEmpty())
                            {
                                //没有相同机物料预装明细,在判断是否有机物料库存
                                result=IsBoxStockExist1(item, existingMaterial);
                                if(!result.isSuccess()){
                                    log.error("预装失败:" + JSON.toJSONString(item));
//                                    result.setSuccess(false);
                                    result.setMessage("预装失败:" + JSON.toJSONString(item));
                                    throw new RuntimeException("预装异常："+result.getMessage());
                                }
                            } else
                            {
                                log.info("机物料预装明细数据："+JSON.toJSONString(machineMaterialsPrePackingList));
                                log.info("没有相同机物料预装明细："+JSON.toJSONString(item));
                                //求和
                                List<WmsMachineMaterialsBoxPrePacking> preList = machineMaterialsPrePackingList.stream()
                                        .collect(Collectors.toMap(WmsMachineMaterialsBoxPrePacking::getBoxCode, a -> a, (o1, o2) -> {
                                            o1.setPendingQuantity(o1.getPendingQuantity() + o2.getPendingQuantity());
                                            return o1;
                                        })).values().stream().collect(Collectors.toList());

//                                boolean preStatus = false;
                                for (WmsMachineMaterialsBoxPrePacking list : preList)
                                {
                                    //有相同机物料预装明细,是否放得下
//                                        WmsBoxTaskList  WmsBoxTaskInfo=wmsBoxTaskListMapper.selectOne();
                                    Container container = containerMapper.selectBoxNo(list.getBoxCode());
                                    int containerType = container.getBoxContainerType();//料箱类型
                                    int boxEmptyStatus = container.getBoxEmptyStatus();//已使用格数
                                    int singleGridCapacity = (int) Math.ceil((double) existingMaterial.getCapacity() / containerType);//单个格子容量
                                    String inStoreNo;
                                    if(item.getMaterialCode().equals(list.getMaterialCode()))
                                    {
                                        //根据格号和入库单号查询
                                        QueryWrapper<WmsMachineMaterialsBoxPrePacking> queryPerWrapper = new QueryWrapper<>();
                                        queryPerWrapper.eq("grid_code", list.getGridCode());
                                        queryPerWrapper.eq("in_store_number", item.getInStoreNumber());
                                        WmsMachineMaterialsBoxPrePacking prePackingList = wmsMachineMaterialsBoxPrePackingMapper.selectOne(queryPerWrapper.last("LIMIT 1"));

                                        //判断当前格子是否放得下
                                        if (list.getPendingQuantity() + item.getInQuantity() <= singleGridCapacity)
                                        {
                                            int gridStatus = (list.getPendingQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
                                            if (prePackingList == null) {
                                                inStoreNo = item.getInStoreNumber();
                                                //当前格子放得下，直接预装,生成预装明细
                                                result = PreInOldBoxDetails1(list, list.getGridCode(), item.getInQuantity(), gridStatus, inStoreNo);
                                                if (!result.isSuccess()) {
                                                    log.error("预装失败，余箱信息:" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
//                                                result.setSuccess(false);
                                                result.setMessage("预装失败，余箱信息:" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
                                                    throw new RuntimeException("预装异常："+result.getMessage());
                                                } else {
                                                    inQuantity = inQuantity - item.getInQuantity();
                                                    WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                    wmsMachineInList.setStatus(1);
                                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                                }
                                            } else {
                                                inStoreNo = list.getInStoreNumber();
                                                int number = prePackingList.getPendingQuantity() + item.getInQuantity();
                                                prePackingList.setPendingQuantity(number);
                                                prePackingList.setActualQuantity(number);
                                                prePackingList.setStatus(gridStatus);
                                                prePackingList.setObjectId(item.getObjectId());
                                                wmsMachineMaterialsBoxPrePackingMapper.updateById(prePackingList);
                                                log.info("当前格子放得下，直接预装,生成预装明细");
                                                log.info("原容器信息：" + JSON.toJSONString(prePackingList));
                                                log.info("往原容器预装信息：" + JSON.toJSONString(item));
                                            }

                                        } else
                                        {
                                            //当前格子放不下，判断该料箱是否有其他格子可以预装
                                            int quantity = (containerType - boxEmptyStatus) * singleGridCapacity + (singleGridCapacity - list.getPendingQuantity());//其他空格+未满格子累计容量
                                            if (quantity > item.getInQuantity()) {
                                                //获取料箱空格子数据
//                                            List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(list.getBoxCode());
                                                Container containerInfo = containerMapper.selectBoxNo(list.getBoxCode());

                                                //其他空格放得下，计算需要空格数，并生成预装明细
                                                int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
                                                int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
                                                if (remainder + list.getPendingQuantity() <= singleGridCapacity)
                                                {
                                                    int gridStatus = (list.getPendingQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
                                                    //余数在未满格子放得下，未满格子生成预装明细
                                                    //根据格号和入库单号查询
                                                    if (prePackingList == null) {
                                                        inStoreNo = item.getInStoreNumber();
                                                        result = PreInOldBoxDetails1(list, list.getGridCode(), remainder, gridStatus, inStoreNo);
                                                        if (!result.isSuccess()) {
                                                            log.error("预装失败，余箱信息:" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
                                                             result.setMessage("预装失败，余箱信息:" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
                                                            throw new RuntimeException("预装异常："+result.getMessage());
                                                        } else {
                                                            inQuantity=inQuantity-remainder;
                                                            WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                            wmsMachineInList.setStatus(1);
                                                            wmsMachineInListMapper.updateById(wmsMachineInList);
                                                        }

                                                    } else
                                                    {
                                                        inStoreNo = list.getInStoreNumber();
                                                        int number = prePackingList.getPendingQuantity() + remainder;
                                                        prePackingList.setPendingQuantity(number);
                                                        prePackingList.setActualQuantity(number);
                                                        prePackingList.setStatus(gridStatus);
                                                        prePackingList.setAssetClass(list.getAssetClass());
                                                        wmsMachineMaterialsBoxPrePackingMapper.updateById(prePackingList);
                                                        inQuantity=inQuantity-remainder;
                                                    }
                                                    for (int i = 0; i < gridNum; i++) {
                                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                        machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
                                                        machineMaterialsBoxPrePackingModel.setGridCode(containerInfo.getBoxEmptyStatus() + 1 + i);
                                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
                                                        machineMaterialsBoxPrePackingModel.setAssetcModel(list.getAssetcModel());
                                                        machineMaterialsBoxPrePackingModel.setBrand(list.getBrand());
                                                        //                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(singleGridCapacity);
                                                        machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                                        machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
                                                        machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                        WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                        wmsMachineInList.setStatus(1);
                                                        wmsMachineInListMapper.updateById(wmsMachineInList);
                                                        QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                        boxItemQueryWrapper.eq("box_no",list.getBoxCode());
                                                        boxItemQueryWrapper.eq("grid_id",containerInfo.getBoxEmptyStatus() + 1 + i);
                                                        BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                        boxItemInfo.setStatus(1);
                                                        boxItemMapper.updateById(boxItemInfo);
                                                        inQuantity=inQuantity-singleGridCapacity;
                                                    }
                                                    container.setBoxEmptyStatus(container.getBoxEmptyStatus() + gridNum);
                                                    container.setMaterialType(2);
                                                    containerMapper.updateById(container);
                                                    result.setSuccess(true);
                                                } else
                                                {
                                                    //余数在未满格子放不下，重新分配一个新空格子给余数预装
                                                    for (int i = 0; i < gridNum + 1; i++)
                                                    {
                                                        if (i == 0) {
                                                            //先生成余数预装明细
                                                            result = PreInOldBoxDetails1(list, containerInfo.getBoxEmptyStatus() + 1 + i, remainder, 2, item.getInStoreNumber());
                                                            if (result.isSuccess()) {
                                                                inQuantity=inQuantity-remainder;
                                                                container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                                container.setMaterialType(2);
                                                                containerMapper.updateById(container);
                                                                WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                                wmsMachineInList.setStatus(1);
                                                                wmsMachineInListMapper.updateById(wmsMachineInList);
                                                                QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                                boxItemQueryWrapper.eq("box_no",list.getBoxCode());
                                                                boxItemQueryWrapper.eq("grid_id",containerInfo.getBoxEmptyStatus() + 1 + i);
                                                                BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                                boxItemInfo.setStatus(1);
                                                                boxItemMapper.updateById(boxItemInfo);
                                                                result.setSuccess(true);
                                                            } else {
                                                                log.error("生成余数预装明细失败;余箱信息：" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
//                                                            preStatus = false;
//                                                            result.setSuccess(false);
                                                               result.setMessage("生成余数预装明细失败;余箱信息：" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
                                                                throw new RuntimeException("预装异常："+result.getMessage());
                                                            }
                                                        } else
                                                        {
                                                            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                            machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
                                                            machineMaterialsBoxPrePackingModel.setGridCode(containerInfo.getBoxEmptyStatus() + 1 + i);
                                                            machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                            machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                                            machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                                            machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
                                                            //                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                                            machineMaterialsBoxPrePackingModel.setPendingQuantity(singleGridCapacity);
                                                            machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                                            machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                                            machineMaterialsBoxPrePackingModel.setStatus(1);
                                                            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                            machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
                                                            machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                                            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                            container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                            container.setMaterialType(2);
                                                            containerMapper.updateById(container);
                                                            QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                            boxItemQueryWrapper.eq("box_no",list.getBoxCode());
                                                            boxItemQueryWrapper.eq("grid_id",containerInfo.getBoxEmptyStatus() + 1 + i);
                                                            BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                            boxItemInfo.setStatus(1);
                                                            boxItemMapper.updateById(boxItemInfo);
                                                            inQuantity=inQuantity-singleGridCapacity;
                                                        }
                                                    }
                                                    result.setSuccess(true);
                                                }

                                            }
                                        }
                                    }
                                    else
                                    {
                                        //机物料不同，但是优先箱型相同，也可以预装
                                        //1.基础信息表查询对应的箱型以及容量
                                        if(inQuantity>0)
                                        {
                                            QueryWrapper<WmsMachineInfo> queryWmsMachineInfoWrapper = new QueryWrapper<>();
                                            queryWmsMachineInfoWrapper.eq("material_code", list.getMaterialCode());
                                            //预装机物料基础信息
                                            WmsMachineInfo wmsMachineInfo = wmsMaterialInfoMapper.selectOne(queryWmsMachineInfoWrapper.last("LIMIT 1"));
                                            if (wmsMachineInfo.getPriorityContainerType().equals(existingMaterial.getPriorityContainerType()))
                                            {
                                                //机物料不同，优先箱型相同，判断该料箱是否有其他格子可以预装
                                                int quantity = (containerType - boxEmptyStatus) * singleGridCapacity;//其他空格+未满格子累计容量
                                                if (quantity > item.getInQuantity())
                                                {
                                                    List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(list.getBoxCode());
                                                    if (!boxList.isEmpty())
                                                    {
                                                        //入库数量小于单格容量
                                                        for (BoxItem boxItem : boxList)
                                                        {
                                                            QueryWrapper<WmsMachineMaterialsBoxPrePacking> prePackingQueryWrapper = new QueryWrapper<>();
                                                            prePackingQueryWrapper.eq("box_code", boxItem.getBoxNo());
                                                            prePackingQueryWrapper.eq("grid_code", boxItem.getGridId());
                                                            prePackingQueryWrapper.eq("delete_flag", 0);
                                                            WmsMachineMaterialsBoxPrePacking boxPrePacking = wmsMachineMaterialsBoxPrePackingMapper.selectOne(prePackingQueryWrapper.last("LIMIT 1"));
                                                            if (boxPrePacking != null)
                                                            {
                                                                if (inQuantity <= singleGridCapacity)
                                                                {
                                                                    WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                                    machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                                    machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
                                                                    machineMaterialsBoxPrePackingModel.setGridCode(boxItem.getGridId());
                                                                    machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                                    machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                                                                    machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                                                                    machineMaterialsBoxPrePackingModel.setAssetClass(item.getAssetClass());
                                                                    machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
                                                                    machineMaterialsBoxPrePackingModel.setPendingQuantity(inQuantity);
                                                                    machineMaterialsBoxPrePackingModel.setActualQuantity(inQuantity);
                                                                    machineMaterialsBoxPrePackingModel.setGridStatus(2);
                                                                    machineMaterialsBoxPrePackingModel.setStatus(1);
                                                                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
                                                                    machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                                                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                                    WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                                    wmsMachineInList.setStatus(1);
                                                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                                                    container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                                    container.setMaterialType(2);
                                                                    containerMapper.updateById(container);
                                                                    QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                                    boxItemQueryWrapper.eq("box_no",list.getBoxCode());
                                                                    boxItemQueryWrapper.eq("grid_id",boxItem.getGridId());
                                                                    BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                                    boxItemInfo.setStatus(1);
                                                                    boxItemMapper.updateById(boxItemInfo);
                                                                    result.setSuccess(true);
                                                                    inQuantity = 0;
                                                                    break;
                                                                }
                                                                else
                                                                {
                                                                    //入库数量大于单格容量
                                                                    int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
                                                                    int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
                                                                    if (remainder <= singleGridCapacity)
                                                                    {
                                                                        WmsMachineMaterialsBoxPrePacking prePackingList = new WmsMachineMaterialsBoxPrePacking();
                                                                        int gridStatus = item.getInQuantity() >= singleGridCapacity ? 3 : 2;//判断格子状态

                                                                        prePackingList.setBoxCode(list.getBoxCode());
                                                                        prePackingList.setPendingQuantity(remainder);
                                                                        prePackingList.setActualQuantity(remainder);
                                                                        prePackingList.setStatus(gridStatus);
                                                                        prePackingList.setAssetClass(item.getAssetClass());
                                                                        prePackingList.setStatus(1);
                                                                        prePackingList.setOperator(ShiroUtils.getLoginName());
                                                                        prePackingList.setCreateTime(DateUtils.getDate());
                                                                        prePackingList.setUpdateTime(DateUtils.getDate());
                                                                        prePackingList.setInStoreNumber(item.getInStoreNumber());//新的空格子
                                                                        prePackingList.setObjectId(item.getObjectId());
                                                                        wmsMachineMaterialsBoxPrePackingMapper.updateById(prePackingList);
                                                                        inQuantity = inQuantity - remainder;

                                                                        for (int i = 0; i < gridNum; i++)
                                                                        {
                                                                            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                                            machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                                            machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
                                                                            machineMaterialsBoxPrePackingModel.setGridCode(boxItem.getGridId());
                                                                            machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                                            machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                                                            machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                                                            machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
                                                                            machineMaterialsBoxPrePackingModel.setAssetcModel(list.getAssetcModel());
                                                                            machineMaterialsBoxPrePackingModel.setBrand(list.getBrand());
                                                                            machineMaterialsBoxPrePackingModel.setPendingQuantity(singleGridCapacity);
                                                                            machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                                                            machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                                                            machineMaterialsBoxPrePackingModel.setStatus(1);
                                                                            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                                            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                                            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                                            machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
                                                                            machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                                                            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                                            WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                                            wmsMachineInList.setStatus(1);
                                                                            wmsMachineInListMapper.updateById(wmsMachineInList);
                                                                            QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                                            boxItemQueryWrapper.eq("box_no",list.getBoxCode());
                                                                            boxItemQueryWrapper.eq("grid_id",boxItem.getGridId());
                                                                            BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                                            boxItemInfo.setStatus(1);
                                                                            boxItemMapper.updateById(boxItemInfo);
                                                                            inQuantity = inQuantity - singleGridCapacity;
                                                                        }
                                                                        container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                                        container.setMaterialType(2);
                                                                        containerMapper.updateById(container);
                                                                        result.setSuccess(true);
                                                                        break;
                                                                    }
                                                                    else
                                                                    {
                                                                        //余数在未满格子放不下，重新分配一个新空格子给余数预装
                                                                        for (int i = 0; i < gridNum + 1; i++)
                                                                        {
                                                                            if (i == 0) {
                                                                                //先生成余数预装明细
                                                                                result = PreInOldBoxDetails1(list, boxItem.getGridId(), remainder, 2, item.getInStoreNumber());
                                                                                if (result.isSuccess()) {
                                                                                    container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                                                    container.setMaterialType(2);
                                                                                    containerMapper.updateById(container);
                                                                                    WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                                                                    wmsMachineInList.setStatus(1);
                                                                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                                                                    QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                                                    boxItemQueryWrapper.eq("box_no",list.getBoxCode());
                                                                                    boxItemQueryWrapper.eq("grid_id",boxItem.getGridId());
                                                                                    BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                                                    boxItemInfo.setStatus(1);
                                                                                    boxItemMapper.updateById(boxItemInfo);
                                                                                    inQuantity = inQuantity - remainder;
                                                                                    result.setSuccess(true);
                                                                                } else {
                                                                                    log.error("生成余数预装明细失败;余箱信息：" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
                                                                                    result.setSuccess(false);
                                                                                    result.setMessage("生成余数预装明细失败;余箱信息：" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
//
                                                                                    throw new RuntimeException("预装异常："+result.getMessage());
                                                                                }
                                                                            } else {
                                                                                WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                                                machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
                                                                                machineMaterialsBoxPrePackingModel.setGridCode(boxItem.getGridId());
                                                                                machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                                                machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                                                                machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                                                                machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
                                                                                //                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                                                                machineMaterialsBoxPrePackingModel.setPendingQuantity(singleGridCapacity);
                                                                                machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                                                                machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                                                                machineMaterialsBoxPrePackingModel.setStatus(1);
                                                                                machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                                                machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                                                machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                                                machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
                                                                                machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                                                                wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                                                container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                                                                container.setMaterialType(2);
                                                                                containerMapper.updateById(container);
                                                                                QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                                                boxItemQueryWrapper.eq("box_no",list.getBoxCode());
                                                                                boxItemQueryWrapper.eq("grid_id",boxItem.getGridId());
                                                                                BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                                                boxItemInfo.setStatus(1);
                                                                                boxItemMapper.updateById(boxItemInfo);
                                                                                inQuantity = inQuantity - singleGridCapacity;
                                                                            }
                                                                        }
                                                                        result.setSuccess(true);
                                                                        break;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            break;
                                        }
                                    }
                                }
                                if (inQuantity>0) {
                                    //当前预装料箱放不下,出新的对应空箱
                                    result=OutNewBox1(item, existingMaterial);//分配新空箱预装
                                    if(!result.isSuccess()){
                                        log.error("生成料箱预装明细失败：" + JSON.toJSONString(item));
//                                        result.setSuccess(false);
                                        result.setMessage("生成料箱预装明细失败：" + JSON.toJSONString(item));
                                        result.setCode(500);
                                        throw new RuntimeException("预装异常："+result.getMessage());
                                    }
                                }
                            }
                        }

                    }
                    else if(existingMaterial.getPriorityContainerType()==99)
                    {
                        log.info("入料架，入料架参数:"+JSON.toJSONString(item));
                        //优先箱型为料架，装料架
                        result = taryPreDetails1(item, existingMaterial);
                        if (!result.isSuccess()) {
                            log.error("生成料架预装明细失败：" + JSON.toJSONString(item));
//                            result.setSuccess(false);
                            result.setMessage("生成料箱预装明细失败：" + JSON.toJSONString(item));
                            throw new RuntimeException("预装异常："+result.getMessage());
                        }
                    }else if (existingMaterial.getPriorityContainerType()==100)
                    {
                        log.info("入虚拟库位，入库参数:"+JSON.toJSONString(item));
                        String boxNo="XN"+ DateUtils.getDate();
                        BoxItem boxItem=new BoxItem();
                        boxItem.setBoxType(1);
                        boxItem.setBoxNo(boxNo);
                        boxItem.setMaterialCode(item.getMaterialCode());
                        boxItem.setMaterialName(item.getMaterialName());
                        boxItem.setMaterialQuantity(item.getInQuantity());
                        boxItem.setMaterialModel(item.getAssetModel());
                        boxItemMapper.insert(boxItem);
                        //入虚拟库位
                        QueryWrapper<Shelf> shelfQueryWrapper=new QueryWrapper<>();
                        shelfQueryWrapper.eq("box_type",3);
                        Shelf shelfInfo=shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
                        shelfInfo.setBoxNo(boxNo);
                        shelfMapper.updateById(shelfInfo);
                        OARequest request=new OARequest();
                        request.setActionName("采购入库");
                        request.setInputjson(JSON.toJSONString(model));
                        OAService.submitOA(JSON.toJSONString(request));
                    }
                }else
                {
                    log.error("入库单"+item.getInStoreNumber()+"已经预装；状态："+wmsMachineInInfo.getStatus());
                    result.setSuccess(false);
                    result.setMessage("入库单"+item.getInStoreNumber()+"已经预装；状态："+wmsMachineInInfo.getStatus());
                    result.setCode(500);
                    throw new RuntimeException("预装异常："+result.getMessage());
                }
                WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                wmsMachineInList.setStatus(1);
                wmsMachineInListMapper.updateById(wmsMachineInList);
            }
            if(result.isSuccess())
            {
                //预装完成，生成出库任务
                result= OutBoxTask1();
                if(!result.isSuccess())
                {
                    throw new RuntimeException("生成出库任务异常："+result.getMessage());
                }
            }

        }catch (Exception ex){
            log.error(ex.toString());
            result.setSuccess(false);
            result.setMessage(ex.toString());
            result.setCode(500);
            throw new RuntimeException("预装异常："+ex.getMessage(), ex);
        }
        return result;
    }


//    public Result<ResultPreDTO> box(int containerType)
//    {
//        log.info("判断该料箱是否有其他格子可以预装:"+ JSON.toJSONString(item));
//        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
//        result.setSuccess(false);
//        try {
//            //当前格子放不下，判断该料箱是否有其他格子可以预装
//            int quantity = (containerType - boxEmptyStatus) * singleGridCapacity + (singleGridCapacity - list.getPendingQuantity());//其他空格+未满格子累计容量
//            if (quantity > item.getInQuantity()) {
//                //获取料箱空格子数据
////                                            List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(list.getBoxCode());
//                Container containerInfo = containerMapper.selectBoxNo(list.getBoxCode());
//
//                //其他空格放得下，计算需要空格数，并生成预装明细
//                int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
//                int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
//                if (remainder + list.getPendingQuantity() <= singleGridCapacity) {
//                    int gridStatus = (list.getPendingQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
//                    //余数在未满格子放得下，未满格子生成预装明细
//                    //根据格号和入库单号查询
//                    if (prePackingList == null) {
//                        inStoreNo = item.getInStoreNumber();
//                        result = PreInOldBoxDetails1(list, list.getGridCode(), remainder, gridStatus, inStoreNo);
//                        if (!result.isSuccess()) {
//                            log.error("预装失败，余箱信息:" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
////                                                result.setSuccess(false);
////                                                result.setMessage("预装失败，余箱信息:" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
//                            result.setCode(500);
//                            return result;
//                        } else {
//                            WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
//                            wmsMachineInList.setStatus(1);
//                            wmsMachineInListMapper.updateById(wmsMachineInList);
//                        }
//
//                    } else {
//                        inStoreNo = list.getInStoreNumber();
//                        int number = prePackingList.getPendingQuantity() + remainder;
//                        prePackingList.setPendingQuantity(number);
//                        prePackingList.setActualQuantity(number);
//                        prePackingList.setStatus(gridStatus);
//                        prePackingList.setAssetClass(list.getAssetClass());
//                        wmsMachineMaterialsBoxPrePackingMapper.updateById(prePackingList);
//                    }
//                    for (int i = 0; i < gridNum; i++) {
//                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
//                        machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
//                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
//                        machineMaterialsBoxPrePackingModel.setGridCode(containerInfo.getBoxEmptyStatus() + 1 + i);
//                        machineMaterialsBoxPrePackingModel.setContainerType(1);
//                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
//                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
//                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
//                        machineMaterialsBoxPrePackingModel.setAssetcModel(list.getAssetcModel());
//                        machineMaterialsBoxPrePackingModel.setBrand(list.getBrand());
//                        //                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
//                        machineMaterialsBoxPrePackingModel.setPendingQuantity(singleGridCapacity);
//                        machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
//                        machineMaterialsBoxPrePackingModel.setGridStatus(3);
//                        machineMaterialsBoxPrePackingModel.setStatus(1);
//                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
//                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
//                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
//                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
//                        machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
//                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
//                        WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
//                        wmsMachineInList.setStatus(1);
//                        wmsMachineInListMapper.updateById(wmsMachineInList);
//                    }
//                    container.setBoxEmptyStatus(container.getBoxEmptyStatus() + gridNum);
//                    container.setMaterialType(2);
//                    containerMapper.updateById(container);
////                                                preStatus = true;
//                    result.setSuccess(true);
//                } else {
//                    //余数在未满格子放不下，重新分配一个新空格子给余数预装
//                    for (int i = 0; i < gridNum + 1; i++) {
//                        if (i == 0) {
//                            //先生成余数预装明细
//                            result = PreInOldBoxDetails1(list, containerInfo.getBoxEmptyStatus() + 1 + i, remainder, 2, item.getInStoreNumber());
//                            if (result.isSuccess()) {
//                                container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
//                                container.setMaterialType(2);
//                                containerMapper.updateById(container);
//                                WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
//                                wmsMachineInList.setStatus(1);
//                                wmsMachineInListMapper.updateById(wmsMachineInList);
//                                result.setSuccess(true);
//                            } else {
//                                log.error("生成余数预装明细失败;余箱信息：" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
////                                                            preStatus = false;
////                                                            result.setSuccess(false);
////                                                            result.setMessage("生成余数预装明细失败;余箱信息：" + JSON.toJSONString(list) + "；入库信息：" + JSON.toJSONString(item));
//                                result.setCode(500);
//                                return result;
//                            }
//                        } else {
//                            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
//                            machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
//                            machineMaterialsBoxPrePackingModel.setGridCode(containerInfo.getBoxEmptyStatus() + 1 + i);
//                            machineMaterialsBoxPrePackingModel.setContainerType(1);
//                            machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
//                            machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
//                            machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
//                            //                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
//                            machineMaterialsBoxPrePackingModel.setPendingQuantity(singleGridCapacity);
//                            machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
//                            machineMaterialsBoxPrePackingModel.setGridStatus(3);
//                            machineMaterialsBoxPrePackingModel.setStatus(1);
//                            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
//                            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
//                            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
//                            machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
//                            machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
//                            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
//                            container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
//                            container.setMaterialType(2);
//                            containerMapper.updateById(container);
//                        }
//                    }
////                                                preStatus = true;
//                    result.setSuccess(true);
//                }
//
//            }
//        }catch (Exception ex){
//            log.error(ex.toString());
////            preStatus= false;
//            result.setSuccess(false);
//            result.setMessage("生成料箱预装明细失败异常：" + JSON.toJSONString(item));
//            return result;
//        }
//        return result;
//    }
    /**
     * 测试--------------------------------------------------------------------------------------------------------------------------
     * @param
     * @return
     */
    public Result<ResultPreDTO> IsBoxStockExist1(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        log.info("是否存在相同机物料库存:"+ JSON.toJSONString(item));
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
//        boolean preStatus = false;
        try {

            //判断库里是否有相同机物料库存,如果有是否放得下
            List<BoxItem> boxItemList = boxItemMapper.selectMaterialCodeStock(item.getMaterialCode(), 1);//获取库里相同机物料料箱库存
            if (!boxItemList.isEmpty())
            {
                //库里有相同机物料库存
                for (BoxItem list : boxItemList)
                {
                    QueryWrapper<WmsMachineMaterialsBoxPrePacking> queryWrapper=new QueryWrapper<>();
                    queryWrapper.eq("box_code",list.getBoxNo());
                    queryWrapper.eq("container_type",1);
                    queryWrapper.eq("status",1);
                    queryWrapper.lt("grid_status",3);
                    List<WmsMachineMaterialsBoxPrePacking> boxPreList=wmsMachineMaterialsBoxPrePackingMapper.selectList(queryWrapper);
                    for(WmsMachineMaterialsBoxPrePacking boxPreGrid:boxPreList){
                        if(list.getGridId()==boxPreGrid.getGridCode()){
                            list.setMaterialQuantity(list.getMaterialQuantity()-boxPreGrid.getActualQuantity());//扣减预装数量
                        }
                    }
                    Container container = containerMapper.selectBoxNo(list.getBoxNo());
                    int containerType = container.getBoxContainerType();//料箱类型
                    int boxEmptyStatus = container.getBoxEmptyStatus();//已使用格数
                    int singleGridCapacity = existingMaterial.getCapacity() / containerType;//单个格子容量
                    //判断当前格子是否放得下
                    if (list.getMaterialQuantity() + item.getInQuantity() <= singleGridCapacity)
                    {
                        int status = (list.getMaterialQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
                        //当前格子放得下，直接预装,生成预装明细
                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                        machineMaterialsBoxPrePackingModel.setGridCode(list.getGridId());
                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
                        machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setGridStatus(status);
                        machineMaterialsBoxPrePackingModel.setStatus(1);
                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//旧的空格子
                        machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
//                        preStatus = true;
                        result.setSuccess(true);
                    } else
                    {
                        //当前格子放不下，判断是否有其他格子可以预装
                        int quantity = (containerType - boxEmptyStatus) * singleGridCapacity + (singleGridCapacity - list.getMaterialQuantity());//其他空格+未满格子累计容量
                        if (quantity > item.getInQuantity())
                        {
                            //获取料箱空格子数据
                            List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(list.getBoxNo());
                            //其他空格放得下，计算需要空格数，并生成预装明细
                            int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
                            int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
                            if (remainder + list.getMaterialQuantity() <= singleGridCapacity)
                            {
                                int status = (list.getMaterialQuantity() + item.getInQuantity()) == singleGridCapacity ? 3 : 2;//判断格子状态
                                //余数在未满格子放得下，未满格子生成预装明细
                                WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                                machineMaterialsBoxPrePackingModel.setGridCode(list.getGridId());
                                machineMaterialsBoxPrePackingModel.setContainerType(1);
                                machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                machineMaterialsBoxPrePackingModel.setActualQuantity(remainder);
                                machineMaterialsBoxPrePackingModel.setGridStatus(status);
                                machineMaterialsBoxPrePackingModel.setStatus(1);
                                machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                for (int i = gridNum; i < gridNum; i++) {
                                    machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                    machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                                    machineMaterialsBoxPrePackingModel.setGridCode(container.getBoxEmptyStatus()+i+1);
                                    machineMaterialsBoxPrePackingModel.setContainerType(1);
                                    machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                    machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                    machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
//                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                    machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                    machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                    machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                    machineMaterialsBoxPrePackingModel.setStatus(1);
                                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                    machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                }
                                container.setBoxEmptyStatus(container.getBoxEmptyStatus() + gridNum);
                                container.setMaterialType(2);
                                containerMapper.updateById(container);
//                                preStatus = true;
                                result.setSuccess(true);
                            } else {
                                //余数在未满格子放不下，重新分配一个新空格子给余数预装
                                for (int i = 0; i < gridNum + 1; i++)
                                {
                                    if (i == 0) {
                                        //先生成余数预装明细
                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                                        machineMaterialsBoxPrePackingModel.setGridCode(container.getBoxEmptyStatus()+i+1);
                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
//                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                        machineMaterialsBoxPrePackingModel.setActualQuantity(remainder);//余数
                                        machineMaterialsBoxPrePackingModel.setGridStatus(2);
                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                        machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                    } else {
                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
                                        machineMaterialsBoxPrePackingModel.setGridCode(container.getBoxEmptyStatus()+i+1);
                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                        machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
                                        machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
                                        machineMaterialsBoxPrePackingModel.setAssetClass(list.getMaterialModel());
//                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(list.getMaterialQuantity());
                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                        machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                        machineMaterialsBoxPrePackingModel.setGridStatus(3);
                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                        machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                    }
                                }
                                container.setBoxEmptyStatus(container.getBoxEmptyStatus() + gridNum+1);
                                container.setMaterialType(2);
                                containerMapper.updateById(container);
//                                preStatus = true;
                                result.setSuccess(true);
                            }

                        }else
                        {
                            //当前料箱装不下，是否有其他未满同类型料箱
                            //库里没有相同机物料库存,是否有同箱型未满的机物料库存
                            List<Container> containerList = containerMapper.selectContainerType(2, 0, existingMaterial.getPriorityContainerType());
                            if (containerList.isEmpty()) {
                                result= OutNewBox1(item, existingMaterial);//分配新空箱预装
                                if(!result.isSuccess()){
                                    throw new RuntimeException("预装异常："+result.getMessage());
                                }
                            } else
                            {
                                int inQuantity=item.getInQuantity();
                                //存在未满的同箱型机物料库存料箱
                                for (Container container1 : containerList)
                                {
                                    if(inQuantity>0)
                                    {
                                         containerType = container1.getBoxContainerType();//料箱类型
                                         boxEmptyStatus = container1.getBoxEmptyStatus();//已使用格数
                                         singleGridCapacity = existingMaterial.getCapacity() / containerType;//单个格子容量
                                        int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
                                        int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
                                        if (remainder > 0) {
                                            gridNum = gridNum + 1;
                                        }
                                        if ((containerType - boxEmptyStatus) * singleGridCapacity >= item.getInQuantity())//未满料箱是否放得下
                                        {
                                            List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(container1.getBoxNo());
                                            if (item.getInQuantity() >= singleGridCapacity)
                                            {
                                                //入库数量大于单个格子容量
                                                for (int i = 0; i < gridNum; i++)
                                                {
                                                    if (i == 0 && remainder>0)
                                                    {
                                                        //预装余数
                                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                        machineMaterialsBoxPrePackingModel.setBoxCode(container1.getBoxNo());
                                                        machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                        machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                                                        machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                                                        machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                                                        machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                                        machineMaterialsBoxPrePackingModel.setActualQuantity(remainder);
                                                        machineMaterialsBoxPrePackingModel.setGridStatus(2);//未满格
                                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                                        machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                        container1.setBoxEmptyStatus(container1.getBoxEmptyStatus() + 1);
                                                        container1.setMaterialType(2);
                                                        containerMapper.updateById(container1);
                                                        inQuantity = inQuantity - remainder;
                                                    }
                                                    else
                                                    {
                                                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                                        machineMaterialsBoxPrePackingModel.setBoxCode(container1.getBoxNo());
                                                        machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                                        machineMaterialsBoxPrePackingModel.setContainerType(1);
                                                        machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                                                        machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                                                        machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                                                        machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                                                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                                        machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                                        machineMaterialsBoxPrePackingModel.setGridStatus(3);//满格
                                                        machineMaterialsBoxPrePackingModel.setStatus(1);
                                                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                                        machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                                        container1.setBoxEmptyStatus(container1.getBoxEmptyStatus() + 1);
                                                        container1.setMaterialType(2);
                                                        containerMapper.updateById(container1);
                                                        inQuantity = inQuantity - singleGridCapacity;
                                                    }
                                                    QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                    boxItemQueryWrapper.eq("box_no",boxList.get(i).getBoxNo());
                                                    boxItemQueryWrapper.eq("grid_id",boxList.get(i).getGridId());
                                                    BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                    boxItemInfo.setStatus(1);
                                                    boxItemMapper.updateById(boxItemInfo);
                                                    result.setSuccess(true);
                                                }
                                            }
                                        }
                                    }else{
                                        break;
                                    }

                                }
                                if (!result.isSuccess()) {
                                    result= OutNewBox1(item, existingMaterial);//分配新空箱预装
                                    if(!result.isSuccess()){
                                        log.error("生成料箱预装明细失败：" + JSON.toJSONString(item));
                                        throw new RuntimeException("预装异常："+result.getMessage());
                                    }
                                }

                            }
                        }
                    }
                }
                if (!result.isSuccess()) {
                    //当前库存料箱放不下,出新的对应空箱
                    result= OutNewBox1(item, existingMaterial);//分配新空箱预装
                    if(!result.isSuccess()){
                        log.error("生成料箱预装明细失败：" + JSON.toJSONString(item));

                        throw new RuntimeException("预装异常："+result.getMessage());
                    }
                }else {
                    WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                    wmsMachineInList.setStatus(1);
                    wmsMachineInListMapper.updateById(wmsMachineInList);
                }
            } else
            {
                //没有相同机物料库存，直接出空箱
                //库里没有相同机物料库存,是否有同箱型未满的机物料库存
                List<Container> containerList = containerMapper.selectContainerType(2, 0, existingMaterial.getPriorityContainerType());
                if (containerList.isEmpty()) {
                    result= OutNewBox1(item, existingMaterial);//分配新空箱预装
                    if(!result.isSuccess()){
                        throw new RuntimeException("预装异常："+result.getMessage());
                    }
                } else
                {
                    int inQuantity=item.getInQuantity();
                    //存在未满的同箱型机物料库存料箱
                    for (Container container : containerList)
                    {
                        if(inQuantity>0)
                        {
                            int containerType = container.getBoxContainerType();//料箱类型
                            int boxEmptyStatus = container.getBoxEmptyStatus();//已使用格数
                            int singleGridCapacity = existingMaterial.getCapacity() / containerType;//单个格子容量
                            int gridNum = item.getInQuantity() / singleGridCapacity;//需要格子数量
                            int remainder = item.getInQuantity() % singleGridCapacity;//获取余数
                            if (remainder > 0) {
                                gridNum = gridNum + 1;
                            }
                            if ((containerType - boxEmptyStatus) * singleGridCapacity >= item.getInQuantity())//未满料箱是否放得下
                            {
                                List<BoxItem> boxList = boxItemMapper.selectEmptyboxNo(container.getBoxNo());
                                if (item.getInQuantity() >= singleGridCapacity)
                                {
                                    //入库数量大于单个格子容量
                                    for (int i = 0; i < gridNum; i++)
                                    {
                                        if (i == 0 && remainder>0)
                                        {
                                            //预装余数
                                            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                            machineMaterialsBoxPrePackingModel.setBoxCode(container.getBoxNo());
                                            machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                            machineMaterialsBoxPrePackingModel.setContainerType(1);
                                            machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                                            machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                                            machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                                            machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                                            machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                            machineMaterialsBoxPrePackingModel.setActualQuantity(remainder);
                                            machineMaterialsBoxPrePackingModel.setGridStatus(2);//未满格
                                            machineMaterialsBoxPrePackingModel.setStatus(1);
                                            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                            machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                            machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                            container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                            container.setMaterialType(2);
                                            containerMapper.updateById(container);
                                            inQuantity = inQuantity - remainder;
                                        } else
                                        {
                                            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                                            machineMaterialsBoxPrePackingModel.setBoxCode(container.getBoxNo());
                                            machineMaterialsBoxPrePackingModel.setGridCode(boxList.get(i).getGridId());
                                            machineMaterialsBoxPrePackingModel.setContainerType(1);
                                            machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                                            machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                                            machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                                            machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                                            machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                                            machineMaterialsBoxPrePackingModel.setActualQuantity(singleGridCapacity);
                                            machineMaterialsBoxPrePackingModel.setGridStatus(3);//满格
                                            machineMaterialsBoxPrePackingModel.setStatus(1);
                                            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                                            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                                            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                                            machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                                            machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                                            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                                            container.setBoxEmptyStatus(container.getBoxEmptyStatus() + 1);
                                            container.setMaterialType(2);
                                            containerMapper.updateById(container);
                                            inQuantity = inQuantity - singleGridCapacity;
                                        }
                                                    QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                                    boxItemQueryWrapper.eq("box_no",boxList.get(i).getBoxNo());
                                                    boxItemQueryWrapper.eq("grid_id",boxList.get(i).getGridId());
                                                    BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                                    boxItemInfo.setStatus(1);
                                                    boxItemMapper.updateById(boxItemInfo);
                                                     result.setSuccess(true);
                                    }
                                }
                            }
                        }else{
                            break;
                        }

                    }
                    if (!result.isSuccess()) {
                        result= OutNewBox1(item, existingMaterial);//分配新空箱预装
                        if(!result.isSuccess()){
                            log.error("生成料箱预装明细失败：" + JSON.toJSONString(item));
                            throw new RuntimeException("预装异常："+result.getMessage());
                        }
                    }

                }
            }
        }catch (Exception ex){
            log.error(ex.toString());
//            preStatus= false;
            result.setSuccess(false);
            result.setMessage("生成料箱预装明细失败异常：" + JSON.toJSONString(item));
            throw new RuntimeException("预装异常："+result.getMessage());
        }
        return result;
    }
    /**
     * 测试--------------------------------------------------------------------------------------------------------------------------
     * @param
     * @return
     */
    public Result<ResultPreDTO> OutNewBox1(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
//        boolean status=false;
        log.info("分配新空箱预装:"+ JSON.toJSONString(item));
        try {
            List<Container> emptyBoxList=containerMapper.selectEmptyBoxList(existingMaterial.getPriorityContainerType());
            if(emptyBoxList.isEmpty())
            {
                log.info("没有找到空料箱，无法预装");
//                status=false;
                result.setSuccess(false);
                result.setMessage("没有找到空料箱，无法预装，请联系仓管员");
                throw new RuntimeException("预装异常："+result.getMessage());
            }else
            {
                int inQuantity=item.getInQuantity();//待入库数量
                for(Container emptyBox:emptyBoxList)
                {
                    QueryWrapper<WmsMachineInfo> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("material_code", item.getMaterialCode());
                    //机物料基础信
                    WmsMachineInfo machineInfo = wmsMaterialInfoMapper.selectOne(queryWrapper.last("LIMIT 1"));

                    if(inQuantity<machineInfo.getCapacity())//判断一个箱子是否放得下
                    {
                        int singleGridCapacity=(int) Math.ceil((double) machineInfo.getCapacity()/emptyBox.getBoxContainerType());//单个格子容量
                        int gridNum= 0;//需要格子数量
                        int remainder=0;//获取余数
                        //分配新空箱预装,先判断需要几个格子
                        if(inQuantity>singleGridCapacity)
                        {
                            //入库数量大于单个容量
                            gridNum= inQuantity/singleGridCapacity;//需要格子数量
                            remainder=inQuantity%singleGridCapacity;//获取余数
                            //如果入库数量大于单个格子容量，先判断需要几个格子
                            int k=0;
                            if(remainder>0)
                            {
                                //预装空箱第一个格子,先装余数
                                k=2;
                                result =PreInNewBoxDetails1( emptyBox.getBoxNo(), item, machineInfo.getSpecification(), 1,remainder,2);
                                if(result.isSuccess())
                                {
                                    emptyBox.setBoxEmptyStatus(1);
                                    emptyBox.setMaterialType(2);
                                    containerMapper.updateById(emptyBox);
                                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                                    wmsMachineInList.setStatus(1);
                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                    QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                    boxItemQueryWrapper.eq("box_no",emptyBox.getBoxNo());
                                    boxItemQueryWrapper.eq("grid_id",1);
                                    BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                    boxItemInfo.setStatus(1);
                                    boxItemMapper.updateById(boxItemInfo);
                                    inQuantity=inQuantity-remainder;
                                }
                            }
                            for(int i=0;i<gridNum;i++)
                            {
                                //预装空箱其他格子
                                result =PreInNewBoxDetails1( emptyBox.getBoxNo(), item, machineInfo.getSpecification(), i+k,singleGridCapacity,3);
                                if(result.isSuccess()) {
                                    emptyBox.setBoxEmptyStatus(i + k);
                                    emptyBox.setMaterialType(2);
                                    containerMapper.updateById(emptyBox);
                                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                                    wmsMachineInList.setStatus(1);
                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                    QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                    boxItemQueryWrapper.eq("box_no",emptyBox.getBoxNo());
                                    boxItemQueryWrapper.eq("grid_id",i+k);
                                    BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                    boxItemInfo.setStatus(1);
                                    boxItemMapper.updateById(boxItemInfo);
                                    inQuantity=inQuantity-singleGridCapacity;
                                }else{
                                    return result;
                                }
                            }
                            break;

                        }else
                        {
                            //入库数量小于单个格子容量
                            result =PreInNewBoxDetails1( emptyBox.getBoxNo(), item, machineInfo.getSpecification(), 1,inQuantity,2);
                            if(result.isSuccess())
                            {
                                emptyBox.setBoxEmptyStatus(1);
                                emptyBox.setMaterialType(2);
                                containerMapper.updateById(emptyBox);
                                WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                                wmsMachineInList.setStatus(1);
                                wmsMachineInListMapper.updateById(wmsMachineInList);
                                QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                boxItemQueryWrapper.eq("box_no",emptyBox.getBoxNo());
                                boxItemQueryWrapper.eq("grid_id",1);
                                BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                boxItemInfo.setStatus(1);
                                boxItemMapper.updateById(boxItemInfo);
                                inQuantity=0;
                            }else
                            {
                                throw new RuntimeException("预装异常："+result.getMessage());
                            }
                            break;
                        }
                    }else
                    {
                        if(inQuantity>0)
                        {
                            //一个箱子放不下
                            int singleGridCapacity=(int) Math.ceil((double) machineInfo.getCapacity() / emptyBox.getBoxContainerType());//单个格子容量
                            for (int i = 0; i < emptyBox.getBoxContainerType(); i++)
                            {
                                result = PreInNewBoxDetails1(emptyBox.getBoxNo(), item, machineInfo.getSpecification(), i + 1, singleGridCapacity, 3);
                                if (result.isSuccess()) {
                                    emptyBox.setBoxEmptyStatus(i + 1);
                                    emptyBox.setMaterialType(2);
                                    containerMapper.updateById(emptyBox);
                                    WmsMachineInList wmsMachineInList = wmsMachineInListMapper.selectById(item.getId());
                                    wmsMachineInList.setStatus(1);
                                    wmsMachineInListMapper.updateById(wmsMachineInList);
                                    QueryWrapper<BoxItem> boxItemQueryWrapper=new QueryWrapper<>();
                                    boxItemQueryWrapper.eq("box_no",emptyBox.getBoxNo());
                                    boxItemQueryWrapper.eq("grid_id",i+1);
                                    BoxItem boxItemInfo=boxItemMapper.selectOne(boxItemQueryWrapper.last("LIMIT 1"));
                                    boxItemInfo.setStatus(1);
                                    boxItemMapper.updateById(boxItemInfo);
                                } else {
                                    return result;
                                }
                                inQuantity = inQuantity - singleGridCapacity;
                            }
                        }else{
                            break;
                        }
                    }
                }
                if(inQuantity>0){
                    //可用空箱已经用完,还没入完
                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                    wmsMachineInList.setInQuantity(inQuantity);
                    wmsMachineInList.setStatus(0);
                    wmsMachineInListMapper.updateById(wmsMachineInList);
                }
            }
            return result;
        }catch (Exception ex)
        {
            log.error(ex.toString());
            result.setSuccess(false);
            result.setMessage("分配新空箱预装预装异常："+toString()+"；参数信息:"+ JSON.toJSONString(item));
            throw new RuntimeException("预装异常："+result.getMessage());
        }
    }
    /**
     * 测试--------------------------------------------------------------------------------------------------------------------------
     * @param
     * @return
     */
    public Result<ResultPreDTO> PreInNewBoxDetails1(String boxNo,MachineMaterialInboundDTO item,String specification,int gridCode,int quantity,int gridstatus)
    {
        log.info("生成新空格预装明细:"+ JSON.toJSONString(item));
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try {
            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
            machineMaterialsBoxPrePackingModel.setBoxCode(boxNo);
            machineMaterialsBoxPrePackingModel.setGridCode(gridCode);
            machineMaterialsBoxPrePackingModel.setContainerType(1);
            machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
            machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
            machineMaterialsBoxPrePackingModel.setAssetClass(item.getAssetClass());
            machineMaterialsBoxPrePackingModel.setAssetcModel(item.getAssetModel());
            machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
//            machineMaterialsBoxPrePackingModel.setOriginalOuantity(quantity);
            machineMaterialsBoxPrePackingModel.setPendingQuantity(quantity);
            machineMaterialsBoxPrePackingModel.setActualQuantity(quantity);
            machineMaterialsBoxPrePackingModel.setGridStatus(gridstatus);
            machineMaterialsBoxPrePackingModel.setStatus(1);
            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
            machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());//新的空格子
            machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
//            return true;
            result.setSuccess(true);
        }catch (Exception ex)
        {
            log.error(ex.toString());
//            return false;
            result.setSuccess(true);
            result.setMessage("生成新空格预装明细异常:"+ JSON.toJSONString(item)+ex.toString());
            throw new RuntimeException("预装异常："+result.getMessage());
        }
        return result;
    }

    /**
     ***余料箱生成新空格预装明细
     * list 入库物料信息
     * gridCode 格子号
     * inQuantity 待入库数量(预装数量)
     * * gridStatus 格子状态
     * inStoreNumber 入库单号
     */
    public Result<ResultPreDTO> PreInOldBoxDetails1(WmsMachineMaterialsBoxPrePacking list,int gridCode,int inQuantity,int gridStatus,String inStoreNumber)
    {
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
//        boolean status=false;
        try{
            WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
            machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxCode());
            machineMaterialsBoxPrePackingModel.setGridCode(gridCode);
            machineMaterialsBoxPrePackingModel.setContainerType(1);
            machineMaterialsBoxPrePackingModel.setMaterialCode(list.getMaterialCode());
            machineMaterialsBoxPrePackingModel.setMaterialName(list.getMaterialName());
            machineMaterialsBoxPrePackingModel.setAssetClass(list.getAssetClass());
            machineMaterialsBoxPrePackingModel.setAssetcModel(list.getAssetcModel());
            machineMaterialsBoxPrePackingModel.setBrand(list.getBrand());
            //                                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
            machineMaterialsBoxPrePackingModel.setPendingQuantity(list.getInStoreNumber()==inStoreNumber?list.getPendingQuantity() +inQuantity:inQuantity);
            machineMaterialsBoxPrePackingModel.setActualQuantity(list.getInStoreNumber()==inStoreNumber?list.getPendingQuantity()+inQuantity:inQuantity);
            machineMaterialsBoxPrePackingModel.setGridStatus(gridStatus);
            machineMaterialsBoxPrePackingModel.setStatus(1);
            machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
            machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
            machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
            machineMaterialsBoxPrePackingModel.setInStoreNumber(inStoreNumber);
            machineMaterialsBoxPrePackingModel.setObjectId(list.getObjectId());
            wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
            result.setSuccess(true);
//            status=true;
        }catch (Exception ex){
            log.error(ex.toString());
            result.setSuccess(false);
            result.setMessage("余料箱生成新空格预装明细异常："+ex.toString());
            throw new RuntimeException("预装异常："+result.getMessage());
        }
        return result;
    }


    /**
     ***生成料架预装明细
     */
    public Result<ResultPreDTO>  taryPreDetails1(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        log.info("准备生成料架预装明细:"+ JSON.toJSONString(item));
//        boolean status=false;
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try {
            //是否有料架预装明细
            List<WmsMachineMaterialsBoxPrePacking> machineMaterialsTrayPrePackingList = wmsMachineMaterialsBoxPrePackingMapper.selectMaterialCode(2);
            if (machineMaterialsTrayPrePackingList == null || machineMaterialsTrayPrePackingList.isEmpty())
            {
                //没有料架预装明细，判断是否有机物料库存料架
                //获取机物料库存，是否有相同机物料库存，
                // 如果没有相同机物料库存，是否有其他机物料库存
                // 都没有，出空料架
                result=IsTaryStockExist1(item, existingMaterial);
                if(!result.isSuccess()){
                    throw new RuntimeException("预装异常："+result.getMessage());
                }
            } else
            {
                //有料架预装明细
                //是否有相同机物料预装明细
                List<WmsMachineMaterialsBoxPrePacking> trayPrePackingList = machineMaterialsTrayPrePackingList.stream()
                        .filter(Container -> Container != null &&
                                Container.getMaterialCode() != null &&
                                item.getMaterialCode() != null &&
                                Container.getMaterialCode().equals(item.getMaterialCode()))
                        .collect(Collectors.toList());
                if (trayPrePackingList.isEmpty())
                {
                    //没有相同机物料预装明细
                    WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                    machineMaterialsBoxPrePackingModel.setBoxCode(machineMaterialsTrayPrePackingList.get(0).getBoxCode());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                    machineMaterialsBoxPrePackingModel.setContainerType(2);
                    machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                    machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                    machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                    machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
//                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                    machineMaterialsBoxPrePackingModel.setPendingQuantity(machineMaterialsTrayPrePackingList.get(0).getPendingQuantity() + item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
//                    machineMaterialsBoxPrePackingModel.setGridstatus(gridstatus);
                    machineMaterialsBoxPrePackingModel.setStatus(1);
                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                    machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                    wmsMachineInList.setStatus(1);
                    wmsMachineInListMapper.updateById(wmsMachineInList);
//                    status = true;
                    result.setSuccess(true);
                } else {
                    //有相同机物料预装明细
                    WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                    machineMaterialsBoxPrePackingModel.setBoxCode(trayPrePackingList.get(0).getBoxCode());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                    machineMaterialsBoxPrePackingModel.setContainerType(2);
                    machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                    machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                    machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                    machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
//                                machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                    machineMaterialsBoxPrePackingModel.setPendingQuantity(trayPrePackingList.get(0).getPendingQuantity() + item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
//                    machineMaterialsBoxPrePackingModel.setGridstatus(gridstatus);
                    machineMaterialsBoxPrePackingModel.setStatus(1);
                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setInStoreNumber(trayPrePackingList.get(0).getInStoreNumber()+','+item.getInStoreNumber());
                    machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                    wmsMachineInList.setStatus(1);
                    wmsMachineInListMapper.updateById(wmsMachineInList);
//                    status = true;
                    result.setSuccess(true);
                }
            }
        }catch (Exception ex){
            log.error(ex.toString());
//            status=false;
            result.setSuccess(false);
            throw new RuntimeException("预装异常："+result.getMessage());
        }
        return result;
    }


    /**
     ***是否存在机物料未满料架库存
     */
    public  Result<ResultPreDTO>  IsTaryStockExist1(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        log.info("是否存在机物料未满料架库存:"+ JSON.toJSONString(item));
//        boolean status=false;
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try {
            //获取库里相同机物料料架库存
            List<BoxItem> trayItemList = boxItemMapper.selectMaterialCode(item.getMaterialCode(), 2);
            //判断库里是否有相同机物料库存,如果有是否放得下
            if (!trayItemList.isEmpty()) {
                for (BoxItem list : trayItemList)
                {
                    if (list.getGridVolume() < 100)
                    {
                        //确认料架放得下，使用余料架进行预装
                        WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                        machineMaterialsBoxPrePackingModel.setBoxCode(list.getBoxNo());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                        machineMaterialsBoxPrePackingModel.setContainerType(2);
                        machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                        machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                        machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                        machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
                        machineMaterialsBoxPrePackingModel.setOriginalOuantity(machineMaterialsBoxPrePackingModel.getOriginalOuantity() + item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                        machineMaterialsBoxPrePackingModel.setGridStatus(2);
                        machineMaterialsBoxPrePackingModel.setStatus(1);
                        machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                        machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                        machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                        machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                        machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                        wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                        WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                        wmsMachineInList.setStatus(1);
                        wmsMachineInListMapper.updateById(wmsMachineInList);
//                        status = true;
                        result.setSuccess(true);
                        break;
                    }
                }
                if (!result.isSuccess()) {
                    //余料架放不下，使用空料架进行预装
                    result= OutNewTary1(item, existingMaterial);
                    if(!result.isSuccess()){
                        log.error("分配新料架预装失败："+JSON.toJSONString(item));
                        result.setMessage("分配新料架预装失败："+JSON.toJSONString(item));
                        throw new RuntimeException("预装异常："+result.getMessage());
                    }
                }

            } else {
                //没有相同机物料的料架库存，是否有其他的机物料未满料架库存
                List<BoxItem> trayList = boxItemMapper.selectTray(2);
                if (trayList.isEmpty()) {
                    //没有未满料架库存，使用空料架进行预装
                    result= OutNewTary1(item, existingMaterial);
                    if(!result.isSuccess()){
                        log.error("分配新料架预装失败："+JSON.toJSONString(item));
                        result.setMessage("分配新料架预装失败："+JSON.toJSONString(item));
                        throw new RuntimeException("预装异常："+result.getMessage());
                    }
                } else
                {
                    //有未满料架库存，使用余料架进行预装
                    WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                    machineMaterialsBoxPrePackingModel.setBoxCode(trayList.get(0).getBoxNo());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                    machineMaterialsBoxPrePackingModel.setContainerType(2);
                    machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                    machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                    machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                    machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
                    machineMaterialsBoxPrePackingModel.setOriginalOuantity(item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                    machineMaterialsBoxPrePackingModel.setGridStatus(2);
                    machineMaterialsBoxPrePackingModel.setStatus(1);
                    machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                    machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                    machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                    machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                    wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                    WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                    wmsMachineInList.setStatus(1);
                    wmsMachineInListMapper.updateById(wmsMachineInList);
//                    status=true;
                    result.setSuccess(true);
                }
            }
        }catch (Exception ex)
        {
            log.error(ex.toString());
//            status=false;
            result.setSuccess(false);
            throw new RuntimeException("预装异常："+result.getMessage());
        }
        return result;
    }


    public Result<ResultPreDTO> OutNewTary1(MachineMaterialInboundDTO item,WmsMachineInfo existingMaterial)
    {
        log.info("准备分配新料架预装:"+ JSON.toJSONString(item));
//        boolean status=false;
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try{
            QueryWrapper<Shelf> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("box_type",2);
            queryWrapper.eq("locked",0);
            List<Shelf> trayList=shelfMapper.selectList(queryWrapper);
            if(trayList.isEmpty())
            {
                log.info("找不到空料架，无法出库");
                result.setSuccess(false);
                result.setMessage("找不到空料架，无法出库");
                throw new RuntimeException("预装异常："+result.getMessage());
            }else
            {
                WmsMachineMaterialsBoxPrePacking machineMaterialsBoxPrePackingModel = new WmsMachineMaterialsBoxPrePacking();
                machineMaterialsBoxPrePackingModel.setBoxCode(trayList.get(0).getBoxNo());
//                    machineMaterialsBoxPrePackingModel.setGridCode(gridCode);料架不用格子号
                machineMaterialsBoxPrePackingModel.setContainerType(2);
                machineMaterialsBoxPrePackingModel.setMaterialCode(item.getMaterialCode());
                machineMaterialsBoxPrePackingModel.setMaterialName(item.getMaterialName());
                machineMaterialsBoxPrePackingModel.setAssetClass(existingMaterial.getSpecification());
                machineMaterialsBoxPrePackingModel.setBrand(item.getBrand());
//            machineMaterialsBoxPrePackingModel.setOriginalOuantity(0);
                machineMaterialsBoxPrePackingModel.setPendingQuantity(item.getInQuantity());
                machineMaterialsBoxPrePackingModel.setActualQuantity(item.getInQuantity());
                machineMaterialsBoxPrePackingModel.setGridStatus(2);
                machineMaterialsBoxPrePackingModel.setStatus(1);
                machineMaterialsBoxPrePackingModel.setOperator(ShiroUtils.getLoginName());
                machineMaterialsBoxPrePackingModel.setCreateTime(DateUtils.getDate());
                machineMaterialsBoxPrePackingModel.setUpdateTime(DateUtils.getDate());
                machineMaterialsBoxPrePackingModel.setInStoreNumber(item.getInStoreNumber());
                machineMaterialsBoxPrePackingModel.setObjectId(item.getObjectId());
                wmsMachineMaterialsBoxPrePackingMapper.insert(machineMaterialsBoxPrePackingModel);
                WmsMachineInList wmsMachineInList=wmsMachineInListMapper.selectById(item.getId());
                wmsMachineInList.setStatus(1);
                wmsMachineInListMapper.updateById(wmsMachineInList);
//                status= true;
                result.setSuccess(true);
            }
        }catch (Exception ex){
            log.error(ex.toString());
//            status=false;
            result.setSuccess(false);
            result.setMessage(ex.toString());
            throw new RuntimeException("预装异常："+result.getMessage());
        }
        return result;
    }

    /**
     * 生成料箱出库任务
     */
    public Result<ResultPreDTO> OutBoxTask1()
    {
//        boolean status=false;
        Result<ResultPreDTO> result=new Result<ResultPreDTO>();
        result.setSuccess(false);
        try{
            //预装完成，生成出库任务
            QueryWrapper<WmsMachineMaterialsBoxPrePacking> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1);
            queryWrapper.eq("task_status", 0);
            queryWrapper.eq("delete_flag", 0);
            List<WmsMachineMaterialsBoxPrePacking> machineMaterialsBoxPrePackingList = wmsMachineMaterialsBoxPrePackingMapper.selectList(queryWrapper);
            List<WmsMachineMaterialsBoxPrePacking> list = machineMaterialsBoxPrePackingList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(WmsMachineMaterialsBoxPrePacking::getBoxCode, p -> p, (p1, p2) -> p1), // 使用第一个遇到的元素作为值，如果有重复则忽略后面的元素
                            map -> new ArrayList<>(map.values()) // 将map的values转换回list
                    ));
            for(WmsMachineMaterialsBoxPrePacking preList:list)
            {
                QueryWrapper<WmsBoxTaskList> queryTaskWrapper = new QueryWrapper<>();
                queryTaskWrapper.eq("box_no", preList.getBoxCode());
                queryTaskWrapper.lt("task_status", 5);
                queryTaskWrapper.eq("delete_flag", 0);
                WmsBoxTaskList  WmsBoxTaskInfo=wmsBoxTaskListMapper.selectOne(queryTaskWrapper.last("LIMIT 1"));
                if(WmsBoxTaskInfo==null)
                {
                    //当前料箱没有任务时才会生成出库任务
                    WmsBoxTaskList wmsBoxTaskList = new WmsBoxTaskList();
//                    QueryWrapper<WmsStation> queryMapper = new QueryWrapper<>();
//                    queryMapper.eq("station_type", 2);
//                    WmsStation stationInfo = wmsStationMapper.selectOne(queryMapper.last("LIMIT 1"));
//                    if (stationInfo == null) {
//                        log.error("出库站点为空，无法进行出库；" );
////                        status = false;
//                        result.setSuccess(false);
//                        result.setMessage("出库站点为空，无法进行出库；" );
//                        return result;
//                    } else
//                    {
////                        wmsBoxTaskList.setToSite(stationInfo.getStationCode());//出库口
//                        wmsBoxTaskList.setToSite("RGZQ3A01011");//出库口
//                    }
                    Shelf shelfInfo=new Shelf();
                    QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
                    shelfQueryWrapper.eq("box_no", preList.getBoxCode());//料架
                    shelfQueryWrapper.eq("locked", 0);
                    shelfQueryWrapper.eq("state", 1);
                    shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
                    if(shelfInfo!=null)
                    {
//                    String fromSite="";
//                    QueryWrapper<Container> ContainerWrapper=new QueryWrapper<>();
//                    ContainerWrapper.eq("material_type",2);
//                    List<Container> containerList= containerMapper.selectList(ContainerWrapper);
//                    if(preList.getContainerType()==1)
//                    {
//                        if (containerList.isEmpty()) {
//                            //分配库位
//                            QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
//                            shelfQueryWrapper.eq("box_type", 1);//料箱
//                            shelfQueryWrapper.eq("locked", 0);
//                            shelfQueryWrapper.eq("state", 0);
//                            shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
//                            fromSite = shelfInfo.getRobotCode();
//                        } else {
//                            for (Container item : containerList) {
//                                QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
//                                shelfQueryWrapper.eq("box_no", item.getBoxNo());
//                                shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
//                                if (shelfInfo != null) {
//                                    Shelf shelf = SerEMptyStock(shelfInfo.getColumn(), shelfInfo.getRow());
//                                    if (shelf != null) {
//                                        fromSite = shelfInfo.getRobotCode();
//                                        break;
//                                    }
//                                }
//                            }
//                            if (fromSite.isEmpty()) {
//                                //分配库位
//                                QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
//                                shelfQueryWrapper.eq("box_type", 1);
//                                shelfQueryWrapper.eq("locked", 0);
//                                shelfQueryWrapper.eq("state", 0);
//                                shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
//                                fromSite = shelfInfo.getRobotCode();
//                            }
//                        }
//                    } else if (preList.getContainerType() == 2)
//                    {
//                        //分配库位
//                        QueryWrapper<Shelf> shelfQueryWrapper = new QueryWrapper<>();
//                        shelfQueryWrapper.eq("box_type", 2);//料架
//                        shelfQueryWrapper.eq("locked", 0);
//                        shelfQueryWrapper.eq("state", 0);
//                        shelfInfo = shelfMapper.selectOne(shelfQueryWrapper.last("LIMIT 1"));
//                        fromSite = shelfInfo.getRobotCode();
//                    }
                        String orderType = wmsBoxTaskListService.getTaskOrder();
                        wmsBoxTaskList.setTaskOrder(orderType);
                        wmsBoxTaskList.setBoxNo(preList.getBoxCode());
                        wmsBoxTaskList.setBoxType(preList.getContainerType());
                        wmsBoxTaskList.setTaskStatus(0);
                        wmsBoxTaskList.setTaskType(6);
//                    wmsBoxTaskList.setToSite(stationInfo.getStationCode());
                        if (preList.getContainerType() == 1) {
                            wmsBoxTaskList.setToSite("RGZQ3A01011");//料箱出库口
                        } else if (preList.getContainerType() == 2) {
                            wmsBoxTaskList.setToSite("0294000AA0299196");//料架出库口
                        }
                        wmsBoxTaskList.setFromSite(shelfInfo.getCode());
                        wmsBoxTaskList.setPriority(1);
                        wmsBoxTaskList.setPushStatus(2);
                        wmsBoxTaskList.setCreateTime(DateUtils.getDate());
                        wmsBoxTaskList.setCreateBy(ShiroUtils.getLoginName());
                        wmsBoxTaskListMapper.insert(wmsBoxTaskList);
                        shelfInfo.setLocked(1);
                        shelfMapper.updateById(shelfInfo);
                        result.setSuccess(true);
                    }else{
                        log.error("生成出库任务失败：找不到"+preList.getBoxCode()+"库位信息");
                        result.setSuccess(false);
                        result.setMessage("生成出库任务失败：找不到"+preList.getBoxCode()+"库位信息");
                        throw new RuntimeException("预装异常："+result.getMessage());
                    }
                }else {
                    log.info("已存在容器："+preList.getBoxCode()+"的任务");
                    result.setSuccess(true);
                }
            }

        }catch (Exception ex){
            log.error("生成出库任务失败："+ex.toString());
//            status=false;
            result.setSuccess(false);
            result.setMessage("生成出库任务失败："+ex.toString());
            throw new RuntimeException("预装异常："+result.getMessage());
        }
        return result;
    }


}





