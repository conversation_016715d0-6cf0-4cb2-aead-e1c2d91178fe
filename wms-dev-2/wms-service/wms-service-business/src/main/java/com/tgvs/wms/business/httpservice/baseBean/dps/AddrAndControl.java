package com.tgvs.wms.business.httpservice.baseBean.dps;


import java.util.Arrays;

import com.alibaba.fastjson.annotation.JSONField;

public class AddrAndControl {
    @JSONField(name = "Addresses")
    private String[] Addresses;

    @JSONField(name = "ControllerID")
    private int ControllerID;

    public void setAddresses(String[] Addresses) {
        this.Addresses = Addresses;
    }

    public void setControllerID(int ControllerID) {
        this.ControllerID = ControllerID;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof AddrAndControl))
            return false;
        AddrAndControl other = (AddrAndControl)o;
        return !other.canEqual(this) ? false : (!Arrays.deepEquals((Object[])getAddresses(), (Object[])other.getAddresses()) ? false : (!(getControllerID() != other.getControllerID())));
    }

    protected boolean canEqual(Object other) {
        return other instanceof AddrAndControl;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        result = result * 59 + Arrays.deepHashCode((Object[])getAddresses());
        return result * 59 + getControllerID();
    }

    public String toString() {
        return "AddrAndControl(Addresses=" + Arrays.deepToString((Object[])getAddresses()) + ", ControllerID=" + getControllerID() + ")";
    }

    public String[] getAddresses() {
        return this.Addresses;
    }

    public int getControllerID() {
        return this.ControllerID;
    }
}
