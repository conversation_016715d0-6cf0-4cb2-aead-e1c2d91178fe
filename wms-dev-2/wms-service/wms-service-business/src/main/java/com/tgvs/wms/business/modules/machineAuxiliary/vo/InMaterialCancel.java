package com.tgvs.wms.business.modules.machineAuxiliary.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(description = "辅料收料入库单信息撤销Dto")
public class InMaterialCancel implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "入库单号", required = true)
    @NotBlank(message = "入库单号不能为空")
    private String inStoreNumber;

    @Schema(description = "入库类型（1.采购入库，2.一般入库，3.产线退料回库;)", required = true)
    @NotNull(message = "入库类型不能为空")
    private Integer taskType;

}
