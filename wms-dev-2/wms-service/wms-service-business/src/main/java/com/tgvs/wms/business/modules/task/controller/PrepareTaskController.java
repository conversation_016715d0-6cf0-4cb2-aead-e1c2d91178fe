package com.tgvs.wms.business.modules.task.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.enums.enumPointType;
import com.tgvs.wms.business.enums.enumPrepareStatus;
import com.tgvs.wms.business.modules.middleware.entity.MesPubuPlanSub;
import com.tgvs.wms.business.modules.task.entity.PrepareTask;
import com.tgvs.wms.business.modules.task.entity.PrepareTaskSub;
import com.tgvs.wms.business.modules.task.entity.TaskBox;
import com.tgvs.wms.business.modules.task.service.IPrepareTaskService;
import com.tgvs.wms.business.modules.task.service.IPrepareTaskSubService;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.business.wmsservice.server.AgvService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = {"备货任务"})
@RestController
@RequestMapping({"/task/prepareTask"})
@Slf4j
public class PrepareTaskController extends BaseController<PrepareTask, IPrepareTaskService> {

    @Autowired
    private IPrepareTaskService prepareTaskService;

    @Autowired
    private IPrepareTaskSubService prepareTaskSubService;

    @ApiOperation(value = "备货任务-分页列表查询", notes = "备货任务-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(@RequestBody(required = false) QueryModel queryModel) {
        IPage<PrepareTask> list = prepareTaskService.pageList(queryModel);
        Result result = Result.ok(list.getRecords());
        result.setTotal(list.getTotal());
        return result;
    }

    @AutoLog("备货任务-添加")
    @ApiOperation(value = "备货任务-添加", notes = "备货任务-添加")
    @RequiresPermissions({"prepareTask:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody PrepareTask prepareTask) {
        this.prepareTaskService.save(prepareTask);
        return Result.OK("添加成功！");
    }

    @AutoLog("备货任务-编辑")
    @ApiOperation(value = "备货任务-编辑", notes = "备货任务-编辑")
    @RequiresPermissions({"prepareTask:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody PrepareTask prepareTask) {
        PrepareTask prepareTaskOld = (PrepareTask)this.prepareTaskService.getById(prepareTask.getId());
        if (prepareTaskOld == null)
            return Result.error("未找到对应数据");
        if (prepareTask.getState().equals(enumPrepareStatus.error.getValue()))
            return Result.error("拉布单不能修改异常状态！");
        if (prepareTaskOld.getState().equals(enumPrepareStatus.error.getValue()))
            return Result.error("该拉布单异常，不能被修改！可通过删除后，自动读取");
        if (null != prepareTask.getLatheNo() && !prepareTask.getLatheNo().equals(prepareTaskOld.getLatheNo())) {
            if (prepareTaskOld.getState().equals(enumPrepareStatus.execute.getValue()))
                return Result.error("拉布单已经执行，不能切换组别！");
            LambdaQueryWrapper<Point> queryWrapperLathe = new LambdaQueryWrapper();
            queryWrapperLathe.eq(Point::getArea, prepareTask.getArea());
            queryWrapperLathe.eq(Point::getPointNo, prepareTask.getLatheNo());
            Integer iLathe = MainService.mainServiceutil.pointMapper.selectCount((Wrapper)queryWrapperLathe);
            if (iLathe.intValue() == 0)
                return Result.error("指定区域不存在该组别，请指定正确的区域和组别！");
            Point point = MainService.getPoint(prepareTask.getLatheNo());
            if (null == point)
                return Result.error("指定铺床不存在，请指定正确的铺床信息！");
            if (!point.getPointType().equals(enumPointType.cutting.getValue()))
                return Result.error("指定铺床错误，请指定正确的铺床信息！");
            LambdaQueryWrapper<PrepareTaskSub> queryWrapperCloth = new LambdaQueryWrapper();
            queryWrapperCloth.eq(PrepareTaskSub::getBillNo, prepareTask.getBillNo());
            List<PrepareTaskSub> listcloth = MainService.mainServiceutil.prepareTaskSubMapper.selectList((Wrapper)queryWrapperCloth);
            if (null != listcloth && listcloth.size() > 0)
                for (PrepareTaskSub p : listcloth) {
                    if (!prepareTask.getPlanTime().equals(prepareTaskOld.getPlanTime()))
                        p.setPlanTime(prepareTask.getPlanTime());
                    p.setLatheNo(prepareTask.getLatheNo());
                    MainService.mainServiceutil.prepareTaskSubMapper.updateById(p);
                }
            if (null != point)
                prepareTask.setArea(point.getArea());
            prepareTask.setDepLatheno(prepareTaskOld.getLatheNo());
        }
        if (prepareTask.getState().equals(enumPrepareStatus.execute.getValue()) &&
                null == prepareTask.getStartTime())
            prepareTask.setStartTime(new Date());
        this.prepareTaskService.updateById(prepareTask);
        return Result.OK("编辑成功!");
    }

    @AutoLog("备货任务-通过id删除")
    @ApiOperation(value = "备货任务-通过id删除", notes = "备货任务-通过id删除")
    @RequiresPermissions({"prepareTask:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        PrepareTask prepareTask = (PrepareTask)this.prepareTaskService.getById(id);
        if (prepareTask == null)
            return Result.error("未找到对应数据");
        this.prepareTaskService.removeById(id);
        this.prepareTaskSubService.remove((Wrapper)(new QueryWrapper()).eq("bill_no", prepareTask.getBillNo()));
        return Result.OK("删除成功!");
    }

    @AutoLog("备货任务-批量删除")
    @ApiOperation(value = "备货任务-批量删除", notes = "备货任务-批量删除")
    @RequiresPermissions({"prepareTask:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.prepareTaskService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("备货任务-通过id查询")
    @ApiOperation(value = "备货任务-通过id查询", notes = "备货任务-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        PrepareTask prepareTask = (PrepareTask)this.prepareTaskService.getById(id);
        if (prepareTask == null)
            return Result.error("未找到对应数据");
        return Result.OK(prepareTask);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, PrepareTask prepareTask) {
        return exportXls(request, prepareTask, PrepareTask.class, "备货任务");
    }

    @RequiresPermissions({"prepareTask:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, PrepareTask.class);
    }

    @AutoLog(value = "备货任务-手动启动备货", operateType = 3)
    @ApiOperation(value = "备货任务-手动启动备货", notes = "备货任务-手动启动备货")
    @RequiresPermissions({"prepareTask:start"})
    @PostMapping({"/start"})
    public Result<?> start(@RequestBody PrepareTask prepareTask) {
        PrepareTask prepareTaskold = (PrepareTask)this.prepareTaskService.getById(prepareTask.getId());
        if (prepareTaskold == null)
            return Result.error("未找到对应数据");
        try {
            if (prepareTaskold.getState().equals(enumPrepareStatus.error.getValue()))
                return Result.error("拉布单异常，不能被执行，请核对MES系统提供的数据是否正确！");
            LambdaQueryWrapper<PrepareTaskSub> queryWapper = new LambdaQueryWrapper();
            queryWapper.eq(PrepareTaskSub::getBillNo, prepareTaskold.getBillNo());
            queryWapper.eq(PrepareTaskSub::getState, enumPrepareStatus.error.getValue());
            List<PrepareTaskSub> list = this.prepareTaskSubService.list((Wrapper)queryWapper);
            if (null != list && list.size() > 0)
                for (PrepareTaskSub sub : list) {
                    sub.setState(enumPrepareStatus.create.getValue());
                    this.prepareTaskSubService.updateById(sub);
                }
            prepareTaskold.setState(enumPrepareStatus.execute.getValue());
            if (null == prepareTaskold.getStartTime())
                prepareTaskold.setStartTime(new Date());
            this.prepareTaskService.updateById(prepareTaskold);
            AgvService.syncLatheBilltoAgv(prepareTaskold);
        } catch (Exception e) {
            log.error("开始备货异常", e);
            return Result.error(e.getMessage());
        }
        return Result.OK("手动启动备货成功!");
    }

    @AutoLog(value = "备货任务-手动暂停备货", operateType = 3)
    @ApiOperation(value = "备货任务-手动暂停备货", notes = "备货任务-手动暂停备货")
    @RequiresPermissions({"prepareTask:stop"})
    @PostMapping({"/stop"})
    public Result<?> stop(@RequestBody PrepareTask prepareTask) {
        PrepareTask prepareTaskold = (PrepareTask)this.prepareTaskService.getById(prepareTask.getId());
        if (prepareTaskold == null)
            return Result.error("未找到对应数据");
        try {
            if (prepareTaskold.getState().equals(enumPrepareStatus.error.getValue()))
                return Result.error("拉布单异常，不能操作，请核对MES系统提供的数据是否正确！");
            prepareTaskold.setState(enumPrepareStatus.cancel.getValue());
            this.prepareTaskService.updateById(prepareTaskold);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.OK("手动暂停备货成功!");
    }

    @AutoLog(value = "备货任务-手动完成铺床计划", operateType = 3)
    @ApiOperation(value = "备货任务-手动完成铺床计划", notes = "备货任务-手动完成铺床计划")
    @RequiresPermissions({"prepareTask:complete"})
    @PostMapping({"/complete"})
    public Result<?> complete(@RequestBody PrepareTask prepareTask) {
        PrepareTask prepareTaskold = (PrepareTask)this.prepareTaskService.getById(prepareTask.getId());
        if (prepareTaskold == null)
            return Result.error("未找到对应数据");
        try {
            LambdaQueryWrapper<PrepareTaskSub> queryWrapperCloth = new LambdaQueryWrapper();
            queryWrapperCloth.eq(PrepareTaskSub::getBillNo, prepareTaskold.getBillNo());
            List<PrepareTaskSub> listcloth = MainService.mainServiceutil.prepareTaskSubMapper.selectList((Wrapper)queryWrapperCloth);
            if (null != listcloth && listcloth.size() > 0)
                for (PrepareTaskSub p : listcloth) {
                    AgvService.insertprepareTaskSubHistory(p);
                    MainService.mainServiceutil.prepareTaskSubMapper.deleteById(p.getId());
                }
            AgvService.insertprepareTaskHistory(prepareTaskold);
            this.prepareTaskService.removeById(prepareTaskold.getId());
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.OK("手动完成铺床计划成功!");
    }

    @AutoLog(value = "备货任务-手动更新铺床计划", operateType = 3)
    @ApiOperation(value = "备货任务-手动更新铺床计划", notes = "备货任务-手动更新铺床计划")
    @RequiresPermissions({"prepareTask:update"})
    @PostMapping({"/update"})
    public Result<?> update(@RequestBody PrepareTask prepareTask) {
        PrepareTask prepareTaskold = (PrepareTask)this.prepareTaskService.getById(prepareTask.getId());
        if (prepareTaskold == null)
            return Result.error("未找到对应数据");
        try {
            if (prepareTaskold.getState().equals(enumPrepareStatus.error.getValue()))
                return Result.error("拉布单异常，系统会自动更新处理！");
            LambdaQueryWrapper<MesPubuPlanSub> queryWrapperCloth = new LambdaQueryWrapper();
            queryWrapperCloth.eq(MesPubuPlanSub::getBillNo, prepareTaskold.getBillNo());
            List<MesPubuPlanSub> listcloth = MainService.mainServiceutil.mesPubuPlanSubService.list((Wrapper)queryWrapperCloth);
            if (null != listcloth && listcloth.size() > 0) {
                for (MesPubuPlanSub p : listcloth) {
                    LambdaQueryWrapper<PrepareTaskSub> updateWrapper = new LambdaQueryWrapper();
                    updateWrapper.eq(PrepareTaskSub::getBillNo, p.getBillNo());
                    updateWrapper.eq(PrepareTaskSub::getClothNo, p.getClothNo());
                    if (p.getCompleted().intValue() == 3) {
                        List<PrepareTaskSub> list = this.prepareTaskSubService.list((Wrapper)updateWrapper);
                        if (null != list && list.size() > 0) {
                            ((PrepareTaskSub)list.get(0)).setCompleted(p.getCompleted());
                            ((PrepareTaskSub)list.get(0)).setIssongbu(p.getIsSongbu());
                            AgvService.insertprepareTaskSubHistory(list.get(0));
                            this.prepareTaskSubService.removeById(((PrepareTaskSub)list.get(0)).getId());
                            prepareTaskold.setCompletedCount(Integer.valueOf(prepareTaskold.getCompletedCount().intValue() + 1));
                        }
                        continue;
                    }
                    List<PrepareTaskSub> listclothsub = this.prepareTaskSubService.list((Wrapper)updateWrapper);
                    if (null != listclothsub && listclothsub.size() > 0) {
                        ((PrepareTaskSub)listclothsub.get(0)).setCompleted(p.getCompleted());
                        ((PrepareTaskSub)listclothsub.get(0)).setIssongbu(p.getIsSongbu());
                        this.prepareTaskSubService.updateById(listclothsub.get(0));
                    }
                }
                if (!prepareTaskold.getClothCount().equals(prepareTask.getCompletedCount()))
                    this.prepareTaskService.updateById(prepareTaskold);
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.OK("手动更新铺床计划成功!");
    }
}
