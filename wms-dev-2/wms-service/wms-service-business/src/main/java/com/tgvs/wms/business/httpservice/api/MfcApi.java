package com.tgvs.wms.business.httpservice.api;


import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.tgvs.wms.business.httpservice.baseBean.warehouse.AdjustLocation;
import com.tgvs.wms.business.httpservice.baseBean.warehouse.AdjustOrder;
import com.tgvs.wms.business.httpservice.baseBean.warehouse.AdjustResult;
import com.tgvs.wms.business.httpservice.baseBean.warehouse.ResultMFCTask;
import com.tgvs.wms.business.httpservice.baseBean.warehouse.ResultResponse;
import com.tgvs.wms.business.util.Logger;
import com.tgvs.wms.common.annotation.AutoLog;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping({"/api/mfc"})
public class MfcApi {

    @AutoLog("WCS反馈任务执行结果")
    @ApiOperation(value = "搬运信息", notes = "搬运信息")
    @PostMapping({"/updateMovement"})
    public ResultResponse updateMovement(@RequestBody ResultMFCTask resultMFCTask) {
        Logger.logFile(JSON.toJSONString(resultMFCTask));
        ResultResponse result = new ResultResponse();
        result.setId(resultMFCTask.getId());
        result.setMessageName(resultMFCTask.getMessageName());
        result.setWmsId(resultMFCTask.getWmsId());
        result.setResult(Integer.valueOf(0));
        return result;
    }

    @AutoLog("WCS反馈任务执行结果")
    @ApiOperation(value = "搬运信息", notes = "搬运信息")
    @PostMapping({"/taskAdjust"})
    public AdjustResult taskAdjust(@RequestBody AdjustOrder adjustOrder) {
        Logger.logFile(JSON.toJSONString(adjustOrder));
        AdjustResult result = new AdjustResult();
        result.setId(adjustOrder.getId().intValue());
        result.setMessageName(adjustOrder.getMessageName());
        result.setWmsId(adjustOrder.getWmsId());
        AdjustLocation location = new AdjustLocation();
        location.setBoxId(adjustOrder.getBoxId());
        location.setLevel(((Integer)adjustOrder.getAllowlevel().get(0)).intValue());
        location.setLocation(301012);
        location.setOutbound(0);
        result.setResult(location);
        return result;
    }
}
