package com.tgvs.wms.business.modules.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.order.entity.MesBox;
import com.tgvs.wms.business.modules.order.entity.MesOrder;
import com.tgvs.wms.business.modules.order.mapper.MesBoxMapper;
import com.tgvs.wms.business.modules.order.service.MesBoxService;
import com.tgvs.wms.business.modules.order.service.MesOrderService;
import com.tgvs.wms.common.util.http.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class MesBoxServiceImpl extends BaseServiceImpl<MesBoxMapper, MesBox> implements MesBoxService {

    @Autowired
    private MesOrderService mesOrderService;

    @Override
    public boolean out(List<String> boxNoList, String siteNo) {
        List<MesBox> list = lambdaQuery().in(MesBox::getBoxNo, boxNoList).list();
        List<Map<String, String>> boxList = new ArrayList<>();
        int i = 0;
        for (MesBox mesBox : list) {
            Map<String, String> map = new HashMap<>();
            map.put("SiteNo", siteNo);
            map.put("BoxNo", mesBox.getBoxNo());
            map.put("Priority", String.valueOf(i++));
            map.put("UserNo", "admin");
            boxList.add(map);
        }
        HttpUtils.sendPost("",JSON.toJSONString(boxList));

        System.err.println(JSON.toJSON(boxList));
        return true;
    }

    @Override
    public void orderAdd(String bookNo) {
        String[] bookNos = bookNo.split("\\|", -1);
        for (String no : bookNos) {
            MesOrder mesOrder = mesOrderService.lambdaQuery().eq(MesOrder::getBookNo, no).one();
            if (mesOrder == null) {
                mesOrder = new MesOrder();
                mesOrder.setBookNo(no);
                mesOrder.setStatus(3);
                mesOrder.setCreateTime(new Date());
                mesOrderService.save(mesOrder);
            }
        }
    }
}
