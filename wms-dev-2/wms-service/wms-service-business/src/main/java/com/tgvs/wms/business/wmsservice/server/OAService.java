package com.tgvs.wms.business.wmsservice.server;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.business.httpservice.baseBean.agv.OARequest;
import com.tgvs.wms.business.httpservice.baseBean.agv.OAResult;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import com.tgvs.wms.business.wmsservice.MainService;
import com.tgvs.wms.common.util.httpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OAService
{
    private static final String EngineCodeValue = "phyumukypya6282lc6t2zxt57";
    private static final String EngineSecretValue = "dhcQx/k3YwFU4xBcnrhiFqRcaQp4KKNnrW+e1drv3pXtYX3ohH8NpQ==";
    private static final String apiUrl = "https://www.h3yun.com/OpenApi/Invoke";

    /**
     * 调用OA接口
     * @param request
     * @return
     */
    public static boolean submitOA(String request)
    {
        boolean status=false;
        try{
            // 请求JSON
//            String requestJson = JSON.toJSONString(request);
            // 创建HTTP头
            Map<String, String> headers = new LinkedHashMap<>();
            // 构建Header
            headers.put("EngineCode", EngineCodeValue);
            headers.put("EngineSecret", EngineSecretValue);
            log.info("请求OA接口参数: {}", request);
            // 发送请求
            String responseJson = httpUtils.httpsPost( apiUrl, headers, request);
            if (responseJson != null && !responseJson.isEmpty()) {
                OAResult result = JSON.parseObject(responseJson, OAResult.class);
                if (result.isSuccessful()) {
                    log.info("请求OA接口响应成功: {}", responseJson);
                    status = true;

                } else {
                    log.error("请求OA接口出错：" + request);
                    status = false;
                }
            }else {
                log.error("请求OA接口出错：" + request);
                status = false;
            }
        }catch (Exception ex)
        {
            log.error("请求OA接口出错："+ex.toString());
            status=false;
        }
        return status;
    }


}
