package com.tgvs.wms.business.modules.machineAuxiliary.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tgvs.wms.base.service.BaseService;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryOutDetailsDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryOutListDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.StockVerificationResultDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.RemainInboundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.BreakInboundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.QueryAuxiliaryOutTaskDto;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutBound;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutMaterialCancel;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutMaterialVo;
import com.tgvs.wms.common.core.domain.QueryModel;

import java.util.List;

/**
 * 物料出库服务接口
 */
public interface IAuxiliaryOutboundService extends BaseService<WmsAuxiliaryOutBound> {

    /**
     * 处理辅料出库单信息
     *
     * @param request 出库请求
     * @return 处理结果，true表示成功，false表示失败
     */
    boolean processOutMaterial(OutMaterialVo request);

    /**
     * 取消辅料出库单信息
     *
     * @param vo 出库取消请求
     * @return 处理结果，true表示成功，false表示失败
     */
    boolean processOutMaterialCancel(OutMaterialCancel vo);

    /**
     * 查询辅料出库单信息
     * 
     * @param queryModel 查询条件
     * @return 辅料出库单信息
     */
    IPage<AuxiliaryOutListDto> queryPageList(QueryModel queryModel);

    /**
     * 验证辅料出库单信息
     * 
     * @param outBoundId 出库单ID列表
     * @param materialType 物料类型（用于过滤materialProperty，可以为null）
     * @return 验证结果,包含库存不足和有库存的物料信息
     */
    StockVerificationResultDto verifyStock(List<String> outBoundId, Integer materialType);

    /**
     * 确认库存验证结果
     * 
     * @param outBoundId   出库单ID列表
     * @param materialType
     * @return 确认结果
     */
    AuxiliaryOutDetailsDto confirmStock(List<String> outBoundId, Integer materialType);

    /**
     * 确认实际出库 - 从预占用状态转为实际出库
     * 
     * @param outBoundId 出库单ID列表
     * @return 操作结果
     */
    boolean confirmActualOutbound(List<String> outBoundId);

    /**
     * 取消预出库 - 删除预占用记录
     * 
     * @param outBoundId 出库单ID列表
     * @return 操作结果
     */
    boolean cancelPreOutbound(List<String> outBoundId);

    /**
     * 处理剩余物料入库 - 调整实际出库数量，剩余物料重新入库
     * 
     * @param request 剩余物料入库请求
     * @return 操作结果
     */
    boolean processRemainingMaterialInbound(RemainInboundDto request);

    /**
     * 托盘拆零入库 - 将托盘中剩余物料转换为料箱入库
     * 
     * @param request 托盘拆零入库请求
     * @return 操作结果
     */
    boolean processPalletBreakInbound(BreakInboundDto request);

    /**
     * 查询辅料出库任务
     * 支持任务状态过滤，只返回输送线上的料箱数据
     * 
     * @param queryModel 查询请求
     * @return 出库任务信息
     */
    QueryAuxiliaryOutTaskDto queryOutTask(QueryModel queryModel);
}