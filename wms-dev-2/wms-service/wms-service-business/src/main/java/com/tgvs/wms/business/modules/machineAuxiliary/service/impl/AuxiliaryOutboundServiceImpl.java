package com.tgvs.wms.business.modules.machineAuxiliary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.container.entity.BoxItem;
import com.tgvs.wms.business.modules.container.entity.Container;
import com.tgvs.wms.business.modules.container.service.IBoxItemService;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.*;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryDetail;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInfo;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutBound;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox;
import com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryOutboundMapper;
import com.tgvs.wms.business.modules.machineAuxiliary.service.*;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutContractNoListVo;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutMaterialCancel;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutMaterialItemVo;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutMaterialVo;
import com.tgvs.wms.business.modules.storage.entity.Shelf;
import com.tgvs.wms.business.modules.storage.service.IShelfService;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import com.tgvs.wms.business.modules.task.service.IWmsBoxTaskListService;
import com.tgvs.wms.common.annotation.Log;
import com.tgvs.wms.common.core.domain.QueryModel;
import com.tgvs.wms.common.enums.BusinessType;
import com.tgvs.wms.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.function.Function;

/**
 * 物料出库服务实现类
 */
@Slf4j
@Service
public class AuxiliaryOutboundServiceImpl extends BaseServiceImpl<WmsAuxiliaryOutboundMapper, WmsAuxiliaryOutBound>
        implements IAuxiliaryOutboundService {

    @Autowired
    private IAuxiliaryOutPreBoxService preBoxService;

    @Autowired
    private IAuxiliaryInfoService auxiliaryInfoService;

    @Autowired
    private IBoxItemService boxItemService;

    @Autowired
    private IAuxiliaryDetailService auxiliaryDetailService;

    @Autowired
    private IWmsBoxTaskListService wmsBoxTaskListService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private IShelfService shelfService;

    @Autowired
    private ContainerSelector containerSelector;

    @Autowired
    private IAuxiliaryInboundService auxiliaryInboundService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "辅料出库", businessType = BusinessType.INSERT)
    public boolean processOutMaterial(OutMaterialVo request) {
        log.info("开始处理辅料出库单信息: {}", request.getOutStoreNumber());

        // 验证请求参数
        if (StringUtils.isBlank(request.getOutStoreNumber())) {
            throw new IllegalArgumentException("出库单号不能为空");
        }

        if (request.getTaskType() == null) {
            throw new IllegalArgumentException("出库类型不能为空");
        }

        if (request.getContractNoList() == null || request.getContractNoList().isEmpty()) {
            throw new IllegalArgumentException("合约号列表不能为空");
        }
        try {
            // 先处理出库单数据
            // 1. 检查是否已经存在相同的出库单号
            WmsAuxiliaryOutBound existingOutbound = this.getOne(new LambdaQueryWrapper<WmsAuxiliaryOutBound>()
                    .eq(WmsAuxiliaryOutBound::getOutStoreNumber, request.getOutStoreNumber())
                    .ne(WmsAuxiliaryOutBound::getStatus, 3), false); // 3表示取消状态
            if (existingOutbound != null) {
                log.warn("辅料领料通知单，单号 {} 已存在", request.getOutStoreNumber());
                String errorMessage = String.format("辅料领料通知单，单号: %s,已存在", request.getOutStoreNumber());
                throw new IllegalArgumentException(errorMessage);
            }
            // 1. 创建并保存出库单头信息
            // 转换出库单类型接口入参0领料通知 1采购退货 需要转换成3领料出库，5采购退货出库
            if (request.getTaskType() == 0) {
                request.setTaskType(3); // 0领料通知转换为3领料出库
            } else if (request.getTaskType() == 1) {
                request.setTaskType(5); // 1采购退货转换为5采购退货出库
            }
            WmsAuxiliaryOutBound outboundHeader = new WmsAuxiliaryOutBound();
            outboundHeader.setId(UUID.randomUUID().toString());
            outboundHeader.setOutStoreNumber(request.getOutStoreNumber());
            outboundHeader.setOutType(request.getTaskType());
            outboundHeader.setStatus(0); // 初始状态：待处理
            outboundHeader.setCreateTime(new Date());
            boolean headerSaved = this.save(outboundHeader);
            if (!headerSaved) {
                log.error("保存辅料出库单头信息失败: {}", request.getOutStoreNumber());
                // 可以选择抛出异常或返回 false
                throw new RuntimeException("保存辅料出库信息失败");
            }
            log.info("辅料出库单头信息保存成功: {}", request.getOutStoreNumber());

            // 处理出库单明细数据，保存到WmsAuxiliaryDetail 表
            // 解析入参，准备保存到明细表
            List<WmsAuxiliaryDetail> detailList = new ArrayList<>();

            // 验证请求参数
            if (request.getContractNoList() == null || request.getContractNoList().isEmpty()) {
                log.error("出库数据合约号列表为空: {}", request.getOutStoreNumber());
                throw new IllegalArgumentException("出库数据合约号列表不能为空");
            }

            // 遍历合约号列表
            for (OutContractNoListVo contractItem : request.getContractNoList()) {
                // 验证每个合约号的物料列表
                if (contractItem.getOutMaterialItemList() == null || contractItem.getOutMaterialItemList().isEmpty()) {
                    log.warn("合约号 {} 的物料列表为空，跳过处理", contractItem.getContractNo());
                    String errorMessage = String.format("保存失败，合约号 %s 的物料列表为空", contractItem.getContractNo());
                    throw new IllegalArgumentException(errorMessage);
                }
                // 验证合约号
                if (StringUtils.isEmpty(contractItem.getContractNo())) {
                    log.warn("合约号为空，跳过处理: {}", contractItem);
                    String errorMessage = "保存失败，合约号为空";
                    throw new IllegalArgumentException(errorMessage);
                }
                // 遍历物料列表
                for (OutMaterialItemVo materialItem : contractItem.getOutMaterialItemList()) {
                    // 验证款号
                    if (StringUtils.isEmpty(materialItem.getItemNo())) {
                        log.warn("合约号 {} 的款号为空，跳过处理", contractItem.getContractNo());
                        String errorMessage = String.format("保存失败，合约号 %s 的款号不能为空", contractItem.getContractNo());
                        throw new IllegalArgumentException(errorMessage);
                    }
                    // 验证物料项
                    if (StringUtils.isEmpty(materialItem.getMaterialCode())
                            || StringUtils.isEmpty(materialItem.getMaterialName())) {
                        log.warn("合约号 {} 存在无效物料项，跳过: {}", contractItem.getContractNo(), materialItem);
                        String errorMessage = String.format("保存失败，合约号 %s 存在无效物料项,物料编码，物料名称不能为空",
                                contractItem.getContractNo());
                        throw new IllegalArgumentException(errorMessage);
                    }
                    // 验证物料数量
                    if (materialItem.getOutQuantity() == null || materialItem.getOutQuantity() <= 0) {
                        log.warn("合约号 {} 的物料编码 {} 数量无效，跳过处理", contractItem.getContractNo(),
                                materialItem.getMaterialCode());
                        String errorMessage = String.format("保存失败，合约号 %s 的物料编码 %s 数量无效", contractItem.getContractNo(),
                                materialItem.getMaterialCode());
                        throw new IllegalArgumentException(errorMessage);
                    }
                    WmsAuxiliaryDetail detailRecord = new WmsAuxiliaryDetail();
                    detailRecord.setId(UUID.randomUUID().toString());
                    detailRecord.setRefNumber(outboundHeader.getOutStoreNumber());
                    // 设置操作操作类型为出库
                    detailRecord.setOperationType(1);
                    // 从 contractItem 获取合约信息
                    detailRecord.setContractNo(contractItem.getContractNo());
                    // 从 materialItem 获取物料信息
                    detailRecord.setItemNo(materialItem.getItemNo());
                    detailRecord.setReqListId(materialItem.getReqListID());
                    detailRecord.setMaterialCode(materialItem.getMaterialCode());
                    detailRecord.setMaterialName(materialItem.getMaterialName());
                    detailRecord.setMaterialColor(materialItem.getMaterialColor());
                    detailRecord.setMaterialColorCode(materialItem.getMaterialColorCode());
                    detailRecord.setMaterialModel(materialItem.getMaterialModel());
                    detailRecord.setQuantity(materialItem.getOutQuantity());
                    detailRecord.setMaterialUnit(materialItem.getMaterialUnit());
                    detailRecord.setCreateTime(new Date());
                    detailList.add(detailRecord);
                    log.debug("准备添加出库明细记录: 出库单号={}, 合约号={}, 物料编码={}, 数量={}",
                            request.getOutStoreNumber(), contractItem.getContractNo(),
                            materialItem.getMaterialCode(), materialItem.getOutQuantity());
                }

                // 检查是否有记录需要保存
                if (detailList.isEmpty()) {
                    log.warn("没有有效的入库明细记录需要保存: {}", request.getOutStoreNumber());
                    return false;
                }

                log.info("准备保存 {} 条入库明细记录: {}", detailList.size(), request.getOutStoreNumber());
                // 批量保存入库明细数据
                auxiliaryDetailService.saveBatch(detailList);
                log.info("辅料出库单明细记录保存成功: 出库单号={}, 合约号={}, 物料数量={}",
                        request.getOutStoreNumber(), contractItem.getContractNo(), detailList.size());
                return true;
            }

            // 检查是否有记录需要保存
            log.warn("没有有效的出库明细记录需要保存: {}", request.getOutStoreNumber());
            return false;

        } catch (Exception e) {
            log.error("处理出库单明细数据失败: {}", request.getOutStoreNumber(), e);
            throw new IllegalArgumentException("处理辅料领料通知单失败:" + e.getMessage());
        }
    }

    /**
     * 辅料出库单信息撤销
     * 
     * @param vo 出库单信息撤销对象
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "辅料出库取消", businessType = BusinessType.UPDATE)
    public boolean processOutMaterialCancel(OutMaterialCancel vo) {
        log.info("开始处理辅料出库单取消: {}", vo.getOutStoreNumber());

        try {
            // 1. 验证请求参数
            if (StringUtils.isBlank(vo.getOutStoreNumber())) {
                throw new IllegalArgumentException("出库单号不能为空");
            }

            if (vo.getTaskType() == null) {
                throw new IllegalArgumentException("出库类型不能为空");
            }
            // 转换出库单类型接口入参0领料通知 1采购退货 需要转换成3领料出库，5采购退货出库
            if (vo.getTaskType() == 0) {
                vo.setTaskType(3); // 0领料通知转换为3领料出库
            } else if (vo.getTaskType() == 1) {
                vo.setTaskType(5); // 1采购退货转换为5采购退货出库
            }
            // 2. 根据出库单号查询出库单信息（可能有多个结果）
            List<WmsAuxiliaryOutBound> outboundList = this.list(new LambdaQueryWrapper<WmsAuxiliaryOutBound>()
                    .eq(WmsAuxiliaryOutBound::getOutStoreNumber, vo.getOutStoreNumber())
                    .eq(WmsAuxiliaryOutBound::getOutType, vo.getTaskType()));

            if (outboundList == null || outboundList.isEmpty()) {
                log.warn("出库单不存在: {}", vo.getOutStoreNumber());
                throw new IllegalArgumentException("出库单不存在: " + vo.getOutStoreNumber());
            }

            // 如果有多个记录，记录警告日志
            if (outboundList.size() > 1) {
                log.warn("发现多个出库单记录: 出库单号={}, 出库类型={}, 记录数量={}",
                        vo.getOutStoreNumber(), vo.getTaskType(), outboundList.size());
            }

            // 3. 检查所有出库单的状态，只有状态为0（待处理）的才可以取消
            List<WmsAuxiliaryOutBound> validOutbounds = new ArrayList<>();
            for (WmsAuxiliaryOutBound outbound : outboundList) {
                if (outbound.getStatus() == null) {
                    log.warn("出库单状态异常: 出库单号={}, ID={}", vo.getOutStoreNumber(), outbound.getId());
                    throw new IllegalArgumentException("出库单状态异常");
                }

                if (outbound.getStatus() == 0) {
                    validOutbounds.add(outbound);
                } else {
                    log.warn("出库单状态不允许取消: 出库单号={}, ID={}, 当前状态={}",
                            vo.getOutStoreNumber(), outbound.getId(), outbound.getStatus());
                }
            }

            // 如果没有可以取消的出库单
            if (validOutbounds.isEmpty()) {
                throw new IllegalArgumentException("未查到有效的领料出库单无法撤销");
            }

            // 4. 批量更新出库单状态为取消
            for (WmsAuxiliaryOutBound outbound : validOutbounds) {
                outbound.setStatus(3); // 3表示已取消
                outbound.setDeleteFlag(1); // 设置删除标志
                outbound.setUpdateTime(new Date());
            }

            boolean updateResult = this.updateBatchById(validOutbounds);

            if (!updateResult) {
                log.error("更新出库单状态失败: {}", vo.getOutStoreNumber());
                throw new RuntimeException("更新出库单状态失败");
            }

            // 5. 更新明细表状态，设置删除标志
            WmsAuxiliaryDetail detail = new WmsAuxiliaryDetail();
            detail.setDeleteFlag(1);
            auxiliaryDetailService.update(detail, new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                    .eq(WmsAuxiliaryDetail::getRefNumber, vo.getOutStoreNumber())
                    .eq(WmsAuxiliaryDetail::getOperationType, 1));

            log.info("辅料出库单取消成功: 出库单号={}, 处理记录数={}", vo.getOutStoreNumber(), validOutbounds.size());
            return true;

        } catch (Exception e) {
            log.error("处理辅料出库单取消失败: {}", vo.getOutStoreNumber(), e);
            throw new RuntimeException("处理辅料出库单取消失败: " + e.getMessage(), e);
        }
    }

    /**
     * 辅料出库查询出库单
     * 
     * @param queryModel 查询条件模型
     * @return 分页结果
     */
    @Override
    public IPage<AuxiliaryOutListDto> queryPageList(QueryModel queryModel) {
        log.info("辅料出库单查询开始，参数: {}", queryModel);

        // 参数验证
        if (queryModel == null) {
            log.warn("查询参数为空，返回空结果");
            Page<AuxiliaryOutListDto> emptyPage = new Page<>(1, 10, 0);
            emptyPage.setRecords(new ArrayList<>());
            return emptyPage;
        }

        // 设置默认分页参数
        if (queryModel.getPage() == null || queryModel.getPage() < 1) {
            queryModel.setPage(1);
        }
        if (queryModel.getLimit() == null || queryModel.getLimit() < 1) {
            queryModel.setLimit(10);
        }

        // 确保 searchParams 不为空
        if (queryModel.getSearchParams() == null) {
            queryModel.setSearchParams(new HashMap<>());
        }

        // 处理排序参数
        if (queryModel.getSortList() == null || queryModel.getSortList().isEmpty()) {
            queryModel.setSortList(new ArrayList<>());
            // 默认按创建时间倒序排序
            QueryModel.SortModel defaultSort = new QueryModel.SortModel();
            defaultSort.setColumn("createTime");
            defaultSort.setSortType(2); // 倒序
            queryModel.getSortList().add(defaultSort);
            log.debug("设置默认排序参数：按创建时间倒序");
        }

        try {
            // 创建分页对象
            Page<AuxiliaryOutListDto> page = new Page<>(queryModel.getPage(), queryModel.getLimit());

            // 直接调用 Mapper 的 selectPage 方法，返回组装好的 DTO
            IPage<AuxiliaryOutListDto> resultPage = this.baseMapper.selectPage(page, queryModel);
            //此处需要根据materialType来判断是款式属性还是自身属性
            if(queryModel.getSearchParams().get("materialType") != null && queryModel.getSearchParams().get("materialType").equals("1")){
                //过滤出所有的合约号，然后查询box_item表，获取所有合约号对应的款号等物料信息
                List<String> contractNos = resultPage.getRecords().stream()
                        .map(AuxiliaryOutListDto::getContractNo)
                        .distinct()
                        .collect(Collectors.toList());
                
                if (!contractNos.isEmpty()) {
                    // 查询款式属性的BoxItem数据
                    List<BoxItem> boxItems = boxItemService.list(new LambdaQueryWrapper<BoxItem>()
                            .in(BoxItem::getContractNo, contractNos)
                            .eq(BoxItem::getMaterialProperty, 1) // 款式属性
                            .isNotNull(BoxItem::getContractNoPo) // PO号不为空
                            .eq(BoxItem::getDeleteFlag, 0)); // 未删除
                    
                    if (!boxItems.isEmpty()) {
                        // 按合约号、款号、物料编码、物料颜色分组BoxItem
                        Map<String, List<BoxItem>> boxItemMap = boxItems.stream()
                                .collect(Collectors.groupingBy(item -> 
                                    String.format("%s_%s_%s_%s_%s",
                                        StringUtils.defaultString(item.getContractNo(), ""),
                                        StringUtils.defaultString(item.getItemNo(), ""),
                                        StringUtils.defaultString(item.getMaterialCode(), ""),
                                        StringUtils.defaultString(item.getMaterialColor(), ""),
                                            StringUtils.defaultString(item.getMaterialModel(),""))));
                        
                        // 创建新的记录列表
                        List<AuxiliaryOutListDto> expandedRecords = new ArrayList<>();
                        
                        // 遍历原始记录
                        for (AuxiliaryOutListDto originalDto : resultPage.getRecords()) {
                            // 生成匹配键
                            String matchKey = String.format("%s_%s_%s_%s_%s",
                                StringUtils.defaultString(originalDto.getContractNo(), ""),
                                StringUtils.defaultString(originalDto.getItemNo(), ""),
                                StringUtils.defaultString(originalDto.getMaterialCode(), ""),
                                StringUtils.defaultString(originalDto.getMaterialColor(), ""),
                                    StringUtils.defaultString(originalDto.getMaterialModel()));
                            
                            // 查找匹配的BoxItem
                            List<BoxItem> matchedBoxItems = boxItemMap.get(matchKey);
                            
                            if (matchedBoxItems != null && !matchedBoxItems.isEmpty()) {
                                // 获取所有不同的PO号
                                Set<String> uniquePoNumbers = matchedBoxItems.stream()
                                        .map(BoxItem::getContractNoPo)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toSet());
                                
                                if (!uniquePoNumbers.isEmpty()) {
                                    // 为每个PO号创建一条记录
                                    for (String poNumber : uniquePoNumbers) {
                                        AuxiliaryOutListDto expandedDto = createExpandedDto(originalDto, poNumber);
                                        expandedRecords.add(expandedDto);
                                        log.debug("拆分记录: 原始记录ID={}, PO号={}, 物料编码={},物料颜色={}，物料规格={}",
                                            originalDto.getId(), poNumber, originalDto.getMaterialCode(),originalDto.getMaterialColor(), originalDto.getMaterialModel());
                                    }
                                } else {
                                    // 没有PO号，保留原记录
                                    expandedRecords.add(originalDto);
                                }
                            } else {
                                // 没有匹配的BoxItem，保留原记录
                                expandedRecords.add(originalDto);
                            }
                        }
                        
                        // 更新结果
                        resultPage.setRecords(expandedRecords);
                        resultPage.setTotal(expandedRecords.size());
                        
                        log.info("款式属性物料PO号拆分完成: 原始记录数={}, 拆分后记录数={}", 
                            resultPage.getRecords().size(), expandedRecords.size());
                    }
                }
            }
            // 为返回的 DTO 设置 operationType 为 1（出库）
            if (resultPage.getRecords() != null && !resultPage.getRecords().isEmpty()) {
                resultPage.getRecords().forEach(dto -> {
                    dto.setOperationType(1); // 1表示出库
                    dto.setRefNumber(dto.getOutStoreNumber()); // 设置关联单号为出库单号
                });
            }
            
            log.info("辅料出库单查询完成，返回 {} 条记录", resultPage.getTotal());
            return resultPage;

        } catch (Exception e) {
            log.error("查询辅料出库单异常", e);
            // 返回空结果
            Page<AuxiliaryOutListDto> emptyPage = new Page<>(queryModel.getPage(), queryModel.getLimit(), 0);
            emptyPage.setRecords(new ArrayList<>());
            return emptyPage;
        }
    }



    /**
     * 辅料出库校验库存结果
     * 
     * @param detailId 出库单明细ID列表
     * @param materialType 物料类型（用于过滤materialProperty，可以为null表示不过滤）
     * @return 库存校验结果
     */
    @Override
    public StockVerificationResultDto verifyStock(List<String> detailId, Integer materialType) {
        log.info("开始库存校验,出库单明细ID数量: {}, materialType: {}", detailId.size(), materialType);

        // 1. 根据明细ID查询物料明细
        List<WmsAuxiliaryDetail> auxiliaryDetails = auxiliaryDetailService.list(
                new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                        .in(WmsAuxiliaryDetail::getId, detailId)
                        .eq(WmsAuxiliaryDetail::getOperationType, 1));

        if (auxiliaryDetails == null || auxiliaryDetails.isEmpty()) {
            log.warn("没有找到出库明细记录");
            return new StockVerificationResultDto(new ArrayList<>(), new ArrayList<>());
        }

        // 2. 获取所有相关物料编码，查询预占用数量
        List<String> materialCodes = auxiliaryDetails.stream()
                .map(WmsAuxiliaryDetail::getMaterialCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 筛选明细中的出库单号
        List<String> outBoundNo = auxiliaryDetails.stream()
                .map(WmsAuxiliaryDetail::getRefNumber)
                .distinct()
                .collect(Collectors.toList());
        
        // 查询预占用记录，分别查询其他出库单和当前出库单的预占用
        Map<String, Integer> reservedQuantityByMaterialKeyMap = new HashMap<>(); // 其他出库单的预占用
        Map<String, Integer> currentReservedQuantityByMaterialKeyMap = new HashMap<>(); // 当前出库单的预占用
        Map<String, Integer> totalReservedQuantityByMaterialKeyMap = new HashMap<>(); // 总预占用（用于显示）
        List<WmsAuxiliaryOutPreBox> currentReservations = new ArrayList<>(); // 当前出库单的预占用记录列表
        
        if (!materialCodes.isEmpty()) {
            // 查询除当前出库单之外的所有其他出库单的预占用记录
            List<WmsAuxiliaryOutPreBox> otherReservations = preBoxService.list(
                new LambdaQueryWrapper<WmsAuxiliaryOutPreBox>()
                    .in(WmsAuxiliaryOutPreBox::getMaterialCode, materialCodes)
                    .eq(WmsAuxiliaryOutPreBox::getReservationStatus, WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED)
                    .notIn(CollectionUtils.isNotEmpty(outBoundNo), WmsAuxiliaryOutPreBox::getOutboundNo, outBoundNo)
            );
            
            // 查询当前出库单的预占用记录
            currentReservations = preBoxService.list(
                new LambdaQueryWrapper<WmsAuxiliaryOutPreBox>()
                    .in(WmsAuxiliaryOutPreBox::getMaterialCode, materialCodes)
                    .eq(WmsAuxiliaryOutPreBox::getReservationStatus, WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED)
                    .in(CollectionUtils.isNotEmpty(outBoundNo), WmsAuxiliaryOutPreBox::getOutboundNo, outBoundNo)
            );
            
            // 按物料键分组计算其他出库单的预占用数量
            for (WmsAuxiliaryOutPreBox reservation : otherReservations) {
                String materialKey = generateMaterialKeyFromReservation(reservation);
                int quantity = reservation.getReservedQuantity() != null ? 
                    reservation.getReservedQuantity().intValue() : 0;
                reservedQuantityByMaterialKeyMap.merge(materialKey, quantity, Integer::sum);
                totalReservedQuantityByMaterialKeyMap.merge(materialKey, quantity, Integer::sum);
            }
            
            // 按物料键分组计算当前出库单的预占用数量
            for (WmsAuxiliaryOutPreBox reservation : currentReservations) {
                String materialKey = generateMaterialKeyFromReservation(reservation);
                int quantity = reservation.getReservedQuantity() != null ? 
                    reservation.getReservedQuantity().intValue() : 0;
                currentReservedQuantityByMaterialKeyMap.merge(materialKey, quantity, Integer::sum);
                totalReservedQuantityByMaterialKeyMap.merge(materialKey, quantity, Integer::sum);
            }
            
            // 记录预占用查询结果，便于调试和问题排查
            log.info("库存验证 - 当前出库单明细数量: {}, 涉及物料种类: {}, 其他出库单预占用记录数: {}, 当前出库单预占用记录数: {}",
                    detailId.size(), materialCodes.size(), otherReservations.size(), currentReservations.size());
            
            if (!totalReservedQuantityByMaterialKeyMap.isEmpty()) {
                int totalOtherReserved = reservedQuantityByMaterialKeyMap.values().stream().mapToInt(Integer::intValue).sum();
                int totalCurrentReserved = currentReservedQuantityByMaterialKeyMap.values().stream().mapToInt(Integer::intValue).sum();
                log.info("预占用统计 - 其他出库单预占用总量: {}, 当前出库单预占用总量: {}, 总预占用量: {}", 
                        totalOtherReserved, totalCurrentReserved, totalOtherReserved + totalCurrentReserved);
                log.debug("按物料键分组的预占用详情 - 其他出库单: {}", reservedQuantityByMaterialKeyMap);
                log.debug("按物料键分组的预占用详情 - 当前出库单: {}", currentReservedQuantityByMaterialKeyMap);
            } else {
                log.info("当前没有任何出库单对相关物料进行预占用");
            }
        }

        // 3. 构建查询条件查询库存
        LambdaQueryWrapper<BoxItem> queryWrapper = new LambdaQueryWrapper<>();

        // 3.1 添加materialProperty过滤条件（与confirmStock保持一致）
        if (materialType != null) {
            queryWrapper.eq(BoxItem::getMaterialProperty, materialType);
            String materialTypeDesc = materialType == 1 ? "款式属性" : (materialType == 0 ? "自身属性" : "其他");
            log.info("库存验证添加materialProperty过滤: {} ({})", materialType, materialTypeDesc);
        }

        queryWrapper.and(mainWrapper -> {
            boolean firstCondition = true;
            for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
                if (!firstCondition) {
                    mainWrapper.or();
                }
                mainWrapper.nested(w -> {
                    // 基础匹配条件：同合约、同款、同物料（必须满足）
                    w.eq(BoxItem::getMaterialCode, detail.getMaterialCode())
                            .eq(BoxItem::getContractNo, detail.getContractNo());

                    // 处理款号(ItemNo) - 必须匹配
                    if (StringUtils.isNotBlank(detail.getItemNo())) {
                        w.eq(BoxItem::getItemNo, detail.getItemNo());
                    } else {
                        w.and(itemCond -> itemCond.isNull(BoxItem::getItemNo).or().eq(BoxItem::getItemNo, ""));
                    }

                    // 根据物料类型应用不同的匹配策略
                    if (materialType != null && materialType == 1) {
                        // 款式属性物料：严格匹配PO、颜色、款式
                        applyStyleAttributeMatching(w, detail);
                    } else if (materialType != null && materialType == 0) {
                        // 自身属性物料：灵活匹配，有值则匹配，无值可忽略
                        applySelfAttributeMatching(w, detail);
                    } else {
                        // 未指定物料类型：使用默认匹配策略
                        applyDefaultMatching(w, detail);
                    }
                });
                firstCondition = false;
            }
        });

        List<BoxItem> boxItems = boxItemService.list(queryWrapper);
        log.info("库存验证根据materialProperty={}查询到库存数量: {}", materialType, boxItems.size());
        
        // 3.2 如果未找到库存且指定了materialType，尝试不限制materialProperty的查询
        if (CollectionUtils.isEmpty(boxItems) && materialType != null) {
            log.warn("库存验证按materialProperty={}未找到库存，尝试不限制materialProperty的查询", materialType);
            
            LambdaQueryWrapper<BoxItem> fallbackQueryWrapper = new LambdaQueryWrapper<>();
            fallbackQueryWrapper.and(mainWrapper -> {
                boolean firstCondition = true;
                for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
                    if (!firstCondition) {
                        mainWrapper.or();
                    }
                    mainWrapper.nested(w -> {
                        // 基础匹配条件：同合约、同款、同物料（必须满足）
                        w.eq(BoxItem::getMaterialCode, detail.getMaterialCode())
                                .eq(BoxItem::getContractNo, detail.getContractNo());

                        // 处理款号(ItemNo) - 必须匹配
                        if (StringUtils.isNotBlank(detail.getItemNo())) {
                            w.eq(BoxItem::getItemNo, detail.getItemNo());
                        } else {
                            w.and(itemCond -> itemCond.isNull(BoxItem::getItemNo).or().eq(BoxItem::getItemNo, ""));
                        }

                        // fallback查询使用默认匹配策略（不区分物料类型）
                        applyDefaultMatching(w, detail);
                    });
                    firstCondition = false;
                }
            });
            
            boxItems = boxItemService.list(fallbackQueryWrapper);
            log.info("库存验证不限制materialProperty查询到库存数量: {}", boxItems.size());
            
            if (!CollectionUtils.isEmpty(boxItems)) {
                // 打印找到的库存项的materialProperty值
                Map<Integer, Long> propertyDistribution = boxItems.stream()
                        .collect(Collectors.groupingBy(
                                item -> item.getMaterialProperty() != null ? item.getMaterialProperty() : -1,
                                Collectors.counting()));
                log.info("库存验证找到的库存materialProperty分布: {}", propertyDistribution);
            }
        }

        // 4. 首先进行聚合验证：计算总的出库数量和可用库存量进行比对
        log.info("开始聚合验证物料总出库量与总可用库存量");
        
        // 添加详细的调试信息
        log.info("库存查询结果: 共查询到 {} 条库存记录", boxItems.size());
        if (!boxItems.isEmpty()) {
            log.debug("库存记录详情:");
            for (BoxItem item : boxItems) {
                log.debug("  库存项: 物料编码={}, 合约号={}, 款号={}, 型号={}, 颜色={}, 数量={}, 料箱号={}, 类型={}",
                    item.getMaterialCode(), item.getContractNo(), item.getItemNo(), 
                    item.getMaterialModel(), item.getMaterialColor(), item.getMaterialQuantity(),
                    item.getBoxNo(), item.getBoxType());
            }
        }
        
        List<VerifyOutResultDto> insufficientList = new ArrayList<>();
        List<AvailableStockDto> availableList = new ArrayList<>();

        Map<String, Integer> aggregatedOutboundMap = new HashMap<>(); // 物料键 -> 总出库量
        Map<String, Integer> aggregatedStockMap = new HashMap<>(); // 物料键 -> 总库存量

        // 计算每种物料的总出库数量
        for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
            String materialKey = generateMaterialKeyFromDetail(detail);
            int quantity = detail.getQuantity() != null ? detail.getQuantity() : 0;
            aggregatedOutboundMap.merge(materialKey, quantity, Integer::sum);
            log.debug("出库明细: 物料键={}, 数量={}, 明细ID={}, 出库单号={}", 
                    materialKey, quantity, detail.getId(), detail.getRefNumber());
        }
        log.info("出库汇总: {} (共{}个物料键)", aggregatedOutboundMap, aggregatedOutboundMap.size());
        
        // 添加详细的出库明细分析
        for (Map.Entry<String, Integer> entry : aggregatedOutboundMap.entrySet()) {
            log.info("物料键[{}]总出库量: {}", entry.getKey(), entry.getValue());
        }

        // 计算每种物料的总库存数量和可用库存数量
        for (BoxItem item : boxItems) {
            String materialKey = generateMaterialKey(item);
            int quantity = item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
            aggregatedStockMap.merge(materialKey, quantity, Integer::sum);
            log.debug("库存明细: 物料键={}, 数量={}, 料箱号={}, 容器类型={}, 物料属性={}", 
                    materialKey, quantity, item.getBoxNo(), item.getBoxType(), item.getMaterialProperty());
        }
        log.info("库存汇总: {} (共{}个物料键)", aggregatedStockMap, aggregatedStockMap.size());
        
        // 添加详细的库存分析
        for (Map.Entry<String, Integer> entry : aggregatedStockMap.entrySet()) {
            log.info("物料键[{}]总库存量: {}", entry.getKey(), entry.getValue());
        }

        Map<String, WmsAuxiliaryDetail> materialKeyToDetailMap = new HashMap<>();

        // 构建物料键到明细的映射，用于聚合验证
        for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
            String materialKey = generateMaterialKeyFromDetail(detail);
            if (!materialKeyToDetailMap.containsKey(materialKey)) {
                materialKeyToDetailMap.put(materialKey, detail); // 用第一个遇到的明细作为代表
            }
        }

        // 创建物料键到库存的映射，用于计算箱库存和架库存
        Map<String, List<BoxItem>> stockMap = new HashMap<>();
        for (BoxItem item : boxItems) {
            String materialKey = generateMaterialKey(item);
            stockMap.computeIfAbsent(materialKey, k -> new ArrayList<>()).add(item);
        }

        // 验证物料键的一致性（用于调试）
        Set<String> outboundKeys = aggregatedOutboundMap.keySet();
        Set<String> stockKeys = stockMap.keySet();
        Set<String> unmatchedOutboundKeys = outboundKeys.stream()
                .filter(key -> !stockKeys.contains(key))
                .collect(Collectors.toSet());
        Set<String> unmatchedStockKeys = stockKeys.stream()
                .filter(key -> !outboundKeys.contains(key))
                .collect(Collectors.toSet());
        
        if (!unmatchedOutboundKeys.isEmpty()) {
            log.warn("出库物料键在库存中未找到匹配: {}", unmatchedOutboundKeys);
            // 分析不匹配的原因
            for (String unmatchedKey : unmatchedOutboundKeys) {
                log.warn("未匹配的出库物料键详情: {}", unmatchedKey);
                // 查找相似的物料键
                for (String stockKey : stockKeys) {
                    if (stockKey.startsWith(unmatchedKey.split("_")[0])) {
                        log.warn("  发现相似库存物料键: {}", stockKey);
                    }
                }
            }
        }
        if (!unmatchedStockKeys.isEmpty()) {
            log.debug("库存物料键在出库中未找到匹配（可能正常）: {}", unmatchedStockKeys);
        }
        
        log.info("物料键一致性检查: 出库物料种类={}, 库存物料种类={}, 出库未匹配={}, 库存未匹配={}", 
                outboundKeys.size(), stockKeys.size(), unmatchedOutboundKeys.size(), unmatchedStockKeys.size());
                
        // 深度分析匹配情况
        if (log.isDebugEnabled()) {
            log.debug("=== 出库物料键详细列表 ===");
            for (String key : outboundKeys) {
                log.debug("出库物料键: {}, 数量: {}", key, aggregatedOutboundMap.get(key));
            }
            log.debug("=== 库存物料键详细列表 ===");
            for (String key : stockKeys) {
                log.debug("库存物料键: {}, 数量: {}", key, aggregatedStockMap.get(key));
            }
        }

        // 聚合验证是否存在库存不足
        boolean aggregatedInsufficient = false;
        int processedInsufficientCount = 0; // 记录实际处理的不足记录数

        for (Map.Entry<String, Integer> entry : aggregatedOutboundMap.entrySet()) {
            String materialKey = entry.getKey();
            int totalOutbound = entry.getValue();
            int totalStock = aggregatedStockMap.getOrDefault(materialKey, 0)+totalOutbound;

            // 获取预占用数量 - 按物料键精确匹配
            int otherReservedQuantity = reservedQuantityByMaterialKeyMap.getOrDefault(materialKey, 0);
            int currentReservedQuantity = currentReservedQuantityByMaterialKeyMap.getOrDefault(materialKey, 0);
            int totalReservedQuantity = totalReservedQuantityByMaterialKeyMap.getOrDefault(materialKey, 0);

            // 计算可用库存 = 总库存 - 其他出库单的预占用（当前出库单的预占用不扣除，因为是自己的）
            int availableStock = totalStock - otherReservedQuantity;

            // 记录验证详情
            log.debug("聚合验证物料键 [{}]: 出库量={}, 总库存剩余={}, 其他预占用={}, 当前预出库数量={}, 当前预出库数量={}, 可用库存={}",
                    materialKey, totalOutbound, totalStock, otherReservedQuantity, currentReservedQuantity, 
                    totalReservedQuantity, availableStock);

            if (totalOutbound > availableStock) {
                // 先检查是否能找到对应的详情记录
                WmsAuxiliaryDetail detailRecord = materialKeyToDetailMap.get(materialKey);
                if (detailRecord == null) {
                    log.warn("聚合验证发现库存不足，但找不到物料键 [{}] 对应的详情记录，跳过该物料", materialKey);
                    
                    // 检查是否存在物料键不匹配的问题
                    String materialCode = materialKey.split("_")[0]; // 假设物料编码是第一部分
                    boolean foundSimilar = materialKeyToDetailMap.keySet().stream()
                            .anyMatch(key -> key.startsWith(materialCode + "_"));
                    
                    if (foundSimilar) {
                        log.warn("发现相似物料键，可能存在物料键生成不一致的问题，物料编码: {}", materialCode);
                    }
                    continue;
                }

                // 只有成功创建不足记录才标记为不足
                try {
                    // 重用stockMap中的库存项，避免重复计算
                    List<BoxItem> items = stockMap.getOrDefault(materialKey, Collections.emptyList());
                    
                    // 计算各类型库存并分析容器类型分布
                    int boxStock = 0;
                    int rackStock = 0;
                    Integer materialProperty = null;
                    Set<Integer> boxTypes = new HashSet<>(); // 收集所有容器类型
                    
                    if (!items.isEmpty()) {
                        // 获取物料属性（所有库存项应该具有相同的物料属性）
                        materialProperty = items.get(0).getMaterialProperty();
                        
                        for (BoxItem item : items) {
                            if (item.getBoxType() != null) {
                                boxTypes.add(item.getBoxType());
                                if (item.getBoxType() == 1) {
                                    boxStock += item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                                } else if (item.getBoxType() == 2) {
                                    rackStock += item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                                }
                            }
                        }
                    }
                    
                    // 对于聚合验证，boxType 设置策略：
                    Integer aggregatedBoxType = null;
                    String containerTypeDesc = "未知";
                    if (boxTypes.size() == 1) {
                        // 只有一种容器类型
                        aggregatedBoxType = boxTypes.iterator().next();
                        containerTypeDesc = aggregatedBoxType == 1 ? "料箱" : (aggregatedBoxType == 2 ? "托盘" : "其他");
                    } else if (boxTypes.size() > 1) {
                        // 混合容器类型，设置为特殊值表示混合情况
                        aggregatedBoxType = 0; // 0表示混合类型
                        containerTypeDesc = "混合(" + boxTypes.stream().map(t -> t == 1 ? "箱" : "盘").collect(Collectors.joining(",")) + ")";
                    }

                    // 创建聚合的库存不足结果
                    VerifyOutResultDto aggregatedInsufficientResult = new VerifyOutResultDto(
                            null, // 聚合验证不关联特定明细ID
                            0,
                            "聚合验证",
                            detailRecord.getMaterialCode(),
                            detailRecord.getMaterialName(),
                            detailRecord.getMaterialModel(),
                            detailRecord.getMaterialColor(),
                            detailRecord.getContractNo(),
                            detailRecord.getItemNo(),
                            totalOutbound,
                            materialProperty,
                            aggregatedBoxType, // 使用修正后的容器类型
                            boxStock,
                            rackStock,
                            VerifyOutResultDto.VerificationStatus.INSUFFICIENT,
                            String.format(
                                    "【聚合验证】物料 [%s] 总出库数量大于可用库存, 总出库量: %d, 总库存: %d, 其他预占用: %d, 可用库存: %d (箱: %d, 架: %d), 差额: %d, 容器类型: %s",
                                    detailRecord.getMaterialCode(), 
                                    totalOutbound, totalStock, otherReservedQuantity,
                                    availableStock, boxStock, rackStock,
                                    totalOutbound - availableStock, containerTypeDesc));

                    insufficientList.add(aggregatedInsufficientResult);
                    processedInsufficientCount++;
                    
                    log.warn("聚合验证: 物料 [{}] 总出库量 ({}) 大于可用库存 ({}), 预占用: {}, 差额: {}, 容器类型: {}",
                            detailRecord.getMaterialCode(), totalOutbound, availableStock, totalReservedQuantity,
                            totalOutbound - availableStock, containerTypeDesc);
                            
                } catch (Exception e) {
                    log.error("创建聚合库存不足记录时发生异常，物料键: {}, 错误: {}", materialKey, e.getMessage());
                    // 即使创建记录失败，也继续处理其他物料
                    continue;
                }
            } else {
                log.debug("聚合验证通过: 物料键 [{}], 可用库存({}) >= 出库量({})", 
                        materialKey, availableStock, totalOutbound);
            }
        }

        // 只有在实际创建了不足记录时才设置为不足
        aggregatedInsufficient = processedInsufficientCount > 0;

        // 记录聚合验证结果，但无论如何都要继续进行单个验证
        if (aggregatedInsufficient) {
            log.warn("聚合验证发现 {} 个物料总库存不足，但仍需进行单个明细验证以获取完整结果", processedInsufficientCount);
        } else {
            log.info("聚合验证通过：所有物料总库存充足，继续进行单个明细项验证");
        }

        // 5. 无论聚合验证结果如何，都要进行单个明细项的验证
        // 原因：聚合验证通过不代表每个物料的库存分布都满足要求
        log.info("开始进行单个明细项验证（聚合验证：{}）", aggregatedInsufficient ? "不足" : "通过");

        // 检测是否存在聚合出库情况
        boolean hasAggregatedOutbound = false;
        List<String> aggregatedOutboundMaterials = new ArrayList<>();

        // 对每个明细进行检查
        for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
            String materialKey = generateMaterialKeyFromDetail(detail);
            List<BoxItem> relevantStockItems = stockMap.getOrDefault(materialKey, Collections.emptyList());

            // 计算库存数量
            int boxStock = 0;
            int rackStock = 0;
            Integer materialProperty = null;
            Integer boxType = null;

            if (!relevantStockItems.isEmpty()) {
                // 获取物料属性
                materialProperty = relevantStockItems.get(0).getMaterialProperty();
                
                // 分析容器类型分布
                Set<Integer> itemBoxTypes = relevantStockItems.stream()
                        .map(BoxItem::getBoxType)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                
                // 设置容器类型
                if (itemBoxTypes.size() == 1) {
                    boxType = itemBoxTypes.iterator().next();
                } else if (itemBoxTypes.size() > 1) {
                    boxType = 0; // 混合类型
                } else {
                    boxType = null; // 无有效容器类型
                }

                // 计算箱库存和架库存
                for (BoxItem item : relevantStockItems) {
                    if (item.getBoxType() != null && item.getBoxType() == 1) {
                        boxStock += item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                    } else if (item.getBoxType() != null && item.getBoxType() == 2) {
                        rackStock += item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                    }
                }
            }

            int totalStock = boxStock + rackStock;
            // 获取预占用数量并计算可用库存 - 使用物料键精确匹配
            int otherReservedQuantity = reservedQuantityByMaterialKeyMap.getOrDefault(materialKey, 0);
            int currentReservedQuantity = currentReservedQuantityByMaterialKeyMap.getOrDefault(materialKey, 0);
            int totalReservedQuantity = totalReservedQuantityByMaterialKeyMap.getOrDefault(materialKey, 0);
            int availableStock = totalStock - otherReservedQuantity;
            int requiredQuantity = detail.getQuantity() != null ? detail.getQuantity() : 0;

            // 检测是否需要聚合出库（从多个容器中出库）
            boolean needsAggregatedOutbound = false;
            if (availableStock >= requiredQuantity && relevantStockItems.size() > 1) {
                // 检查是否有任何单个容器能满足全部需求（考虑当前物料的预占用情况）
                boolean canSingleContainerSatisfy = false;
                
                for (BoxItem item : relevantStockItems) {
                    int itemStock = item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                    
                    // 计算该容器中这个物料的可用库存（扣除其他出库单的预占用）
                    // 注意：这里简化处理，实际应该按容器+物料维度计算预占用
                    if (itemStock >= requiredQuantity) {
                        canSingleContainerSatisfy = true;
                        break;
                    }
                }
                
                if (!canSingleContainerSatisfy) {
                    needsAggregatedOutbound = true;
                    hasAggregatedOutbound = true;
                    aggregatedOutboundMaterials.add(detail.getMaterialCode());
                    
                    // 记录详细的容器库存信息 - 按容器号分组统计
                    StringBuilder containerInfo = new StringBuilder();
                    Map<String, Integer> containerInfoMap = new HashMap<>();
                    
                    for (BoxItem item : relevantStockItems) {
                        String boxNo = item.getBoxNo();
                        int itemStock = item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                        containerInfoMap.merge(boxNo, itemStock, Integer::sum);
                    }
                    
                    for (Map.Entry<String, Integer> entry : containerInfoMap.entrySet()) {
                        containerInfo.append(String.format("%s(%d)", entry.getKey(), entry.getValue())).append(",");
                    }
                    
                    log.info("检测到聚合出库需求: 物料 [{}] 需求数量 {}, 需要从 {} 个容器中组合出库, 容器库存: {}", 
                            detail.getMaterialCode(), requiredQuantity, relevantStockItems.size(), 
                            containerInfo.toString());
                }
            }

            // 确定容器类型描述
            String containerTypeDesc = "未知";
            if (boxType != null) {
                if (boxType == 0) {
                    containerTypeDesc = "混合";
                } else if (boxType == 1) {
                    containerTypeDesc = "料箱";
                } else if (boxType == 2) {
                    containerTypeDesc = "托盘";
                } else {
                    containerTypeDesc = "其他";
                }
            }

            // 检查库存情况：总库存不足 OR 需要聚合出库
            if (availableStock < requiredQuantity || needsAggregatedOutbound) {
                // 构建容器分布详情信息 - 基于预占用表数据显示当前出库单涉及的容器分布
                StringBuilder containerDetails = new StringBuilder();
                
                // 查询当前出库单对该物料的预占用记录
                String currentMaterialKey = generateMaterialKeyFromDetail(detail);
                List<WmsAuxiliaryOutPreBox> materialCurrentReservations = new ArrayList<>();
                
                // 从当前出库单的预占用记录中筛选匹配的物料
                for (WmsAuxiliaryOutPreBox reservation : currentReservations) {
                    String reservationMaterialKey = generateMaterialKeyFromReservation(reservation);
                    if (currentMaterialKey.equals(reservationMaterialKey)) {
                        materialCurrentReservations.add(reservation);
                    }
                }
                
                if (!materialCurrentReservations.isEmpty()) {
                    containerDetails.append("预占用分布: ");
                    
                    // 按容器号分组统计预占用数量
                    Map<String, Integer> containerReservationMap = new HashMap<>();
                    Map<String, Integer> containerTypeMap = new HashMap<>();
                    
                    for (WmsAuxiliaryOutPreBox reservation : materialCurrentReservations) {
                        String boxCode = reservation.getBoxCode();
                        int reservedQty = reservation.getReservedQuantity() != null ? 
                                reservation.getReservedQuantity().intValue() : 0;
                        Integer containerType = reservation.getContainerType();
                        
                        // 累计同一容器的预占用数量
                        containerReservationMap.merge(boxCode, reservedQty, Integer::sum);
                        
                        // 记录容器类型
                        if (containerType != null) {
                            containerTypeMap.put(boxCode, containerType);
                        }
                    }
                    
                    // 生成容器分布信息
                    for (Map.Entry<String, Integer> entry : containerReservationMap.entrySet()) {
                        String boxCode = entry.getKey();
                        Integer reservedQty = entry.getValue();
                        Integer containerType = containerTypeMap.get(boxCode);
                        String typeDesc = (containerType != null && containerType == 2) ? "托盘" : "料箱";
                        
                        containerDetails.append(String.format("%s:%s(%d个), ", 
                                boxCode, typeDesc, reservedQty));
                    }
                    
                    // 移除最后的逗号和空格
                    if (containerDetails.length() > 0) {
                        containerDetails.setLength(containerDetails.length() - 2);
                    }
                    
                    // 添加调试日志
                    log.debug("物料[{}]预占用容器分布: {}", detail.getMaterialCode(), containerReservationMap);
                } else {
                    // 如果没有预占用记录，则显示库存分布作为参考
                    containerDetails.append("库存分布: ");
                    
                    // 按容器号分组统计库存，只显示有库存的容器
                    Map<String, Integer> containerStockMap = new HashMap<>();
                    Map<String, Integer> containerTypeMap = new HashMap<>();
                    
                    for (BoxItem item : relevantStockItems) {
                        String boxNo = item.getBoxNo();
                        int itemStock = item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                        Integer itemBoxType = item.getBoxType();
                        
                        // 只记录有库存的容器
                        if (itemStock > 0) {
                            // 累计同一容器的库存
                            containerStockMap.merge(boxNo, itemStock, Integer::sum);
                            
                            // 记录容器类型
                            if (itemBoxType != null) {
                                containerTypeMap.put(boxNo, itemBoxType);
                            }
                        }
                    }
                    
                    // 生成容器分布信息
                    for (Map.Entry<String, Integer> entry : containerStockMap.entrySet()) {
                        String boxNo = entry.getKey();
                        Integer stock = entry.getValue();
                        Integer containerBoxType = containerTypeMap.get(boxNo);
                        String itemTypeDesc = (containerBoxType != null && containerBoxType == 2) ? "托盘" : "料箱";
                        
                        containerDetails.append(String.format("%s:%s(%d个), ", 
                                boxNo, itemTypeDesc, stock));
                    }
                    
                    // 移除最后的逗号和空格
                    if (containerDetails.length() > 0) {
                        containerDetails.setLength(containerDetails.length() - 2);
                    }
                    
                    log.debug("物料[{}]库存容器分布(无预占用): {}", detail.getMaterialCode(), containerStockMap);
                }

                // 确定状态和消息
                VerifyOutResultDto.VerificationStatus status;
                String message;
                
                if (availableStock < requiredQuantity) {
                    // 真正的库存不足 - 修正显示逻辑，统一使用其他预占用数量以保持一致性
                    status = VerifyOutResultDto.VerificationStatus.INSUFFICIENT;
                    message = String.format("【库存不足】物料 [%s] 总库存不足, 总库存: %d, 其他预占用: %d, 可用库存: %d (箱: %d, 架: %d), 需求数量: %d, 差额: %d, 容器类型: %s. %s",
                            detail.getMaterialCode(), totalStock, otherReservedQuantity, availableStock, boxStock,
                            rackStock, requiredQuantity, requiredQuantity - availableStock, containerTypeDesc,
                            containerDetails.toString());
                } else {
                    // 库存充足但需要聚合出库 - 使用INSUFFICIENT状态表示需要特殊处理
                    status = VerifyOutResultDto.VerificationStatus.INSUFFICIENT;
                    message = String.format("【聚合出库】物料 [%s] 需要从多个容器组合出库, 总库存: %d, 其他预占用: %d, 可用库存: %d (箱: %d, 架: %d), 需求数量: %d, 容器类型: %s. %s",
                            detail.getMaterialCode(), totalStock, otherReservedQuantity, availableStock, boxStock,
                            rackStock, requiredQuantity, containerTypeDesc,
                            containerDetails.toString());
                }

                VerifyOutResultDto insufficientResult = new VerifyOutResultDto(
                        detail.getId(), // 使用明细ID
                        0, // 单个明细验证不需要出库类型
                        detail.getRefNumber(), // 使用关联单号（出库单号）
                        detail.getMaterialCode(),
                        detail.getMaterialName(),
                        detail.getMaterialModel(),
                        detail.getMaterialColor(),
                        detail.getContractNo(),
                        detail.getItemNo(),
                        requiredQuantity,
                        materialProperty,
                        boxType,
                        boxStock,
                        rackStock,
                        status,
                        message);
                insufficientList.add(insufficientResult);
                
                log.warn("需要特殊处理: 明细ID [{}], 物料 [{}] - 状态: {}, 可用库存: {}, 需求量: {}, 容器数量: {}",
                        detail.getId(), detail.getMaterialCode(), 
                        availableStock < requiredQuantity ? "库存不足" : "聚合出库",
                        availableStock, requiredQuantity, relevantStockItems.size());
            } else {
                // 库存充足且单容器可满足，添加到可用库存列表
                AvailableStockDto availableStockDto = new AvailableStockDto(
                        detail.getId(),
                        detail.getRefNumber(),
                        detail.getMaterialCode(),
                        detail.getMaterialName(),
                        detail.getMaterialModel(),
                        detail.getMaterialColor(),
                        detail.getContractNo(),
                        detail.getItemNo(),
                        requiredQuantity,
                        materialProperty,
                        boxType,
                        (long) boxStock,
                        (long) rackStock
                );
                        
                availableList.add(availableStockDto);
                
                log.info("库存正常: 明细ID [{}], 物料 [{}] - 可用库存 ({}), 需求量: {}, 容器类型: {}, 单容器满足",
                        detail.getId(), detail.getMaterialCode(), availableStock, requiredQuantity, containerTypeDesc);
            }
        }

        // 创建验证结果
        StockVerificationResultDto result = new StockVerificationResultDto(insufficientList, availableList);

        // 设置验证结果状态和消息
        if (!insufficientList.isEmpty()) {
            result.setNeedManualConfirm(true);
            
            // 区分聚合验证失败和单个验证失败的情况
            if (aggregatedInsufficient) {
                result.setLimitOutbound(true);
                result.setMessage(String.format("发现 %d 个物料总库存不足，%d 个物料单独库存不足，建议重新选择出库单或调整出库数量", 
                        processedInsufficientCount, insufficientList.size()));
            } else {
                result.setLimitOutbound(false);
                result.setMessage(String.format("虽然总库存充足，但有 %d 个物料的库存分布不足，是否继续出库？", 
                        insufficientList.size()));
            }
            
            log.warn("库存验证完成 - 聚合验证：{}, 不足物料数：{}，详情：{}", 
                    aggregatedInsufficient ? "失败" : "通过", 
                    insufficientList.size(),
                    insufficientList.stream()
                            .map(item -> String.format("%s(需求:%d)", item.getMaterialCode(), item.getRequiredQuantity()))
                            .collect(Collectors.joining(", ")));
        } else if (hasAggregatedOutbound) {
            // 虽然库存充足，但存在聚合出库情况，需要人工确认
            result.setNeedManualConfirm(true);
            result.setLimitOutbound(false);
            result.setMessage(String.format("库存充足，但有 %d 个物料需要聚合出库（从多个容器组合），请确认是否继续？物料：%s", 
                    aggregatedOutboundMaterials.size(), 
                    String.join("、", aggregatedOutboundMaterials)));
            
            log.info("库存验证完成 - 库存充足但存在聚合出库，需要人工确认: {}", aggregatedOutboundMaterials);
        } else {
            result.setNeedManualConfirm(false);
            result.setLimitOutbound(false);
            result.setMessage("所有物料库存验证通过，可以正常出库");
            log.info("库存验证完成 - 所有物料库存充足，可以正常出库");
        }

        // 记录验证统计信息
        log.info("库存验证结果统计 - 总明细数：{}，聚合验证：{}，单个验证不足：{}，聚合出库物料：{}，需要人工确认：{}", 
                auxiliaryDetails.size(), 
                aggregatedInsufficient ? "失败" : "通过", 
                insufficientList.size(),
                aggregatedOutboundMaterials.size(),
                result.isNeedManualConfirm());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuxiliaryOutDetailsDto confirmStock(List<String> detailId, Integer materialType) {
        try {
            long startTime = System.currentTimeMillis();
            log.info("开始确认库存，出库单明细ID数量: {}, 物料类型: {}", detailId.size(), materialType);

            // 1. 根据明细ID查询物料明细
            List<WmsAuxiliaryDetail> auxiliaryDetails = auxiliaryDetailService.list(
                    new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                            .in(WmsAuxiliaryDetail::getId, detailId)
                            .eq(WmsAuxiliaryDetail::getOperationType, 1));

            if (CollectionUtils.isEmpty(auxiliaryDetails)) {
                log.warn("未找到有效的辅料出库明细");
                throw new RuntimeException("未找到有效的辅料出库明细");
            }

            // 2. 获取所有相关的出库单号，查询出库单信息
            List<String> outBoundNumbers = auxiliaryDetails.stream()
                    .map(WmsAuxiliaryDetail::getRefNumber)
                    .distinct()
                    .collect(Collectors.toList());

            List<WmsAuxiliaryOutBound> outboundList = list(new LambdaQueryWrapper<WmsAuxiliaryOutBound>()
                    .in(WmsAuxiliaryOutBound::getOutStoreNumber, outBoundNumbers));

            if (CollectionUtils.isEmpty(outboundList)) {
                log.warn("没有有效的出库单需要确认");
                throw new RuntimeException("没有有效的出库单需要确认");
            }

            // 2.1 检查出库单是否已经创建了任务（防止重复创建）
            List<String> outBoundIds = outboundList.stream()
                    .map(WmsAuxiliaryOutBound::getId)
                    .collect(Collectors.toList());

            // 检查预占用记录
            List<WmsAuxiliaryOutPreBox> existingReservations = preBoxService.getByOutboundIds(outBoundIds);
            
            // 检查任务表中是否已存在相关任务
            List<String> outBoundNos = outboundList.stream()
                    .map(WmsAuxiliaryOutBound::getOutStoreNumber)
                    .collect(Collectors.toList());
            
            // 查询相关物料的料箱编号
            Set<String> relatedBoxNos = new HashSet<>();
            for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
                // 根据明细查询相关的料箱编号（简化查询）
                List<BoxItem> relatedBoxItems = boxItemService.list(new LambdaQueryWrapper<BoxItem>()
                        .eq(BoxItem::getMaterialCode, detail.getMaterialCode())
                        .eq(BoxItem::getContractNo, detail.getContractNo())
                                .eq(BoxItem::getItemNo,detail.getItemNo())
                        .select(BoxItem::getBoxNo));
                
                relatedBoxNos.addAll(relatedBoxItems.stream()
                        .map(BoxItem::getBoxNo)
                        .collect(Collectors.toSet()));
            }
            
            // 检查这些料箱是否已有未完成的出库任务
            if (!relatedBoxNos.isEmpty()) {
                List<WmsBoxTaskList> existingTasks = wmsBoxTaskListService.list(new LambdaQueryWrapper<WmsBoxTaskList>()
                        .in(WmsBoxTaskList::getBoxNo, relatedBoxNos)
                        .in(WmsBoxTaskList::getTaskStatus, Arrays.asList(0, 1, 2, 3)) // 未完成状态
                        .eq(WmsBoxTaskList::getDeleteFlag,0)
                        .orderByDesc(WmsBoxTaskList::getCreateTime));
                
                if (!existingTasks.isEmpty()) {
                    log.warn("检测到料箱已有未完成的出库任务，料箱数量: {}, 任务数量: {}", 
                            relatedBoxNos.size(), existingTasks.size());
                    
                    // 如果有预占用记录，返回现有任务信息
                    if (!CollectionUtils.isEmpty(existingReservations)) {
                        log.info("发现已存在的预占用记录，返回现有任务信息");
                        return buildExistingOutTaskResult(existingReservations);
                    } else {
                        // 没有预占用记录但有任务，可能是数据不一致，记录警告
                        log.warn("发现料箱有未完成任务但无预占用记录，可能存在数据不一致");
                        throw new RuntimeException("检测到料箱已有未完成的出库任务，请先完成或取消现有任务后再重试");
                    }
                }
            }
            
            if (!CollectionUtils.isEmpty(existingReservations)) {
                log.info("检测到已存在的出库任务，出库单数量: {}, 已有预占用记录数量: {}", 
                        outboundList.size(), existingReservations.size());
                
                // 如果已经有预占用记录，说明任务已创建，直接返回现有任务信息
                return buildExistingOutTaskResult(existingReservations);
            }

            // 3. 构建库存查询条件
            LambdaQueryWrapper<BoxItem> queryWrapper = new LambdaQueryWrapper<>();
            
            // 3.1 首先记录查询条件用于调试
            log.info("开始构建库存查询条件，materialType: {}, 明细数量: {}", materialType, auxiliaryDetails.size());
            for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
                log.debug("出库明细: 物料编码={}, 合约号={}, 款号={}, 型号={}, 颜色={}", 
                    detail.getMaterialCode(), detail.getContractNo(), detail.getItemNo(), 
                    detail.getMaterialModel(), detail.getMaterialColor());
            }
            
            // 3.2 优先按materialProperty过滤（如果不为空）
            if (materialType != null) {
                queryWrapper.eq(BoxItem::getMaterialProperty, materialType);
            }

            // 4. 复杂的库存匹配条件 - 使用与verifyStock一致的匹配策略
            queryWrapper.and(wrapper -> {
                boolean needsOr = false;
                for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
                    if (needsOr) {
                        wrapper.or();
                    }

                    wrapper.nested(w -> {
                        // 基础匹配条件：同合约、同款、同物料（必须满足）
                        w.eq(BoxItem::getMaterialCode, detail.getMaterialCode())
                                .eq(BoxItem::getContractNo, detail.getContractNo());

                        // 处理款号(ItemNo) - 必须匹配
                        if (StringUtils.isNotBlank(detail.getItemNo())) {
                            w.eq(BoxItem::getItemNo, detail.getItemNo());
                        } else {
                            w.and(itemCond -> itemCond.isNull(BoxItem::getItemNo).or().eq(BoxItem::getItemNo, ""));
                        }

                        // 根据物料类型应用不同的匹配策略
                        if (materialType != null && materialType == 1) {
                            // 款式属性物料：严格匹配PO、颜色、款式
                            applyStyleAttributeMatching(w, detail);
                        } else if (materialType != null && materialType == 0) {
                            // 自身属性物料：灵活匹配，有值则匹配，无值可忽略
                            applySelfAttributeMatching(w, detail);
                        } else {
                            // 未指定物料类型：使用默认匹配策略
                            applyDefaultMatching(w, detail);
                        }
                    });
                    needsOr = true;
                }
            });

            // 5. 查询库存
            List<BoxItem> boxItems = boxItemService.list(queryWrapper);
            log.info("根据materialProperty={}查询到库存数量: {}", materialType, boxItems.size());
            
            // 5.1 如果未找到库存，尝试不限制materialProperty的查询
            if (CollectionUtils.isEmpty(boxItems) && materialType != null) {
                log.warn("按materialProperty={}未找到库存，尝试不限制materialProperty的查询", materialType);
                
                LambdaQueryWrapper<BoxItem> fallbackQueryWrapper = new LambdaQueryWrapper<>();
                fallbackQueryWrapper.and(wrapper -> {
                    boolean needsOr = false;
                    for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
                        if (needsOr) {
                            wrapper.or();
                        }

                        wrapper.nested(w -> {
                            // 基础匹配条件：同合约、同款、同物料（必须满足）
                            w.eq(BoxItem::getMaterialCode, detail.getMaterialCode())
                                    .eq(BoxItem::getContractNo, detail.getContractNo());

                            // 处理款号(ItemNo) - 必须匹配
                            if (StringUtils.isNotBlank(detail.getItemNo())) {
                                w.eq(BoxItem::getItemNo, detail.getItemNo());
                            } else {
                                w.and(itemCond -> itemCond.isNull(BoxItem::getItemNo).or().eq(BoxItem::getItemNo, ""));
                            }

                            // fallback查询使用默认匹配策略（不区分物料类型）
                            applyDefaultMatching(w, detail);
                        });
                        needsOr = true;
                    }
                });
                
                boxItems = boxItemService.list(fallbackQueryWrapper);
                log.info("不限制materialProperty查询到库存数量: {}", boxItems.size());
                
                if (!CollectionUtils.isEmpty(boxItems)) {
                    // 打印找到的库存项的materialProperty值
                    Map<Integer, Long> propertyDistribution = boxItems.stream()
                            .collect(Collectors.groupingBy(
                                    item -> item.getMaterialProperty() != null ? item.getMaterialProperty() : -1,
                                    Collectors.counting()));
                    log.info("找到的库存materialProperty分布: {}", propertyDistribution);
                }
            }
            
            // 5.2 如果仍未找到库存
            if (CollectionUtils.isEmpty(boxItems)) {
                // 查询是否存在相关物料的任何库存（用于更详细的调试信息）
                Set<String> materialCodes = auxiliaryDetails.stream()
                        .map(WmsAuxiliaryDetail::getMaterialCode)
                        .collect(Collectors.toSet());
                
                if (!materialCodes.isEmpty()) {
                    List<BoxItem> anyRelatedStock = boxItemService.list(
                            new LambdaQueryWrapper<BoxItem>()
                                    .in(BoxItem::getMaterialCode, materialCodes));
                    
                    if (!anyRelatedStock.isEmpty()) {
                        log.warn("数据库中存在相关物料的库存，但不匹配查询条件。库存总数: {}", anyRelatedStock.size());
                        // 输出前几条库存记录用于对比
                        anyRelatedStock.stream().limit(5).forEach(item -> 
                            log.warn("现有库存示例: 物料编码={}, 合约号={}, 款号={}, 型号={}, 颜色={}, materialProperty={}", 
                                item.getMaterialCode(), item.getContractNo(), item.getItemNo(), 
                                item.getMaterialModel(), item.getMaterialColor(), item.getMaterialProperty()));
                    } else {
                        log.warn("数据库中完全没有相关物料编码的库存: {}", materialCodes);
                    }
                }
                
                log.error("未找到匹配的库存项，请检查：1.物料属性是否正确(materialProperty) 2.物料基础信息是否匹配 3.库存是否存在");
                throw new RuntimeException("未找到匹配的库存项，materialProperty=" + materialType + "，请检查物料属性设置");
            }

            // 6. 处理出库任务并构建返回结果
            AuxiliaryOutDetailsDto resultDto = new AuxiliaryOutDetailsDto();
            List<BoxOutItemDto> boxOutItems = new ArrayList<>(); // 料箱出库项
            List<BoxOutItemDto> rackOutItems = new ArrayList<>(); // 托盘出库项

            // 按物料键分组库存
            Map<String, List<BoxItem>> stockByMaterialKey = new HashMap<>();
            for (BoxItem item : boxItems) {
                String materialKey = generateMaterialKey(item);
                stockByMaterialKey.computeIfAbsent(materialKey, k -> new ArrayList<>()).add(item);
            }

            // 创建出库单号到出库单对象的映射
            Map<String, WmsAuxiliaryOutBound> outboundMap = outboundList.stream()
                    .collect(Collectors.toMap(WmsAuxiliaryOutBound::getOutStoreNumber, 
                            outbound -> outbound, (existing, replacement) -> existing));

            // 处理每个明细项
            for (WmsAuxiliaryDetail detail : auxiliaryDetails) {
                // 获取对应的出库单信息
                WmsAuxiliaryOutBound outbound = outboundMap.get(detail.getRefNumber());
                if (outbound == null) {
                    log.warn("未找到明细 [{}] 对应的出库单: {}", detail.getId(), detail.getRefNumber());
                    continue;
                }

                String materialKey = generateMaterialKeyFromDetail(detail);
                List<BoxItem> matchedItems = stockByMaterialKey.get(materialKey);

                if (CollectionUtils.isEmpty(matchedItems)) {
                    log.warn("未找到物料 [{}] 的匹配库存，物料键: {}", detail.getMaterialCode(), materialKey);
                    
                    // 提供更详细的调试信息
                    log.debug("当前可用的物料键: {}", stockByMaterialKey.keySet());
                    
                    // 查找可能的相似物料键
                    String materialCode = detail.getMaterialCode();
                    String contractNo = detail.getContractNo();
                    List<String> similarKeys = stockByMaterialKey.keySet().stream()
                            .filter(key -> key.startsWith(materialCode + "_" + contractNo))
                            .collect(Collectors.toList());
                    
                    if (!similarKeys.isEmpty()) {
                        log.warn("找到相似的物料键（可能是款号、型号或颜色不匹配）: {}", similarKeys);
                    }
                    
                    continue;
                }

                // 按容器类型分组库存项
                Map<Integer, List<BoxItem>> stockByBoxType = matchedItems.stream()
                        .collect(Collectors.groupingBy(item -> item.getBoxType() != null ? item.getBoxType() : 0));

                // 获取料箱和托盘库存
                List<BoxItem> boxTypeItems = stockByBoxType.getOrDefault(1, Collections.emptyList()); // 料箱库存
                List<BoxItem> rackTypeItems = stockByBoxType.getOrDefault(2, Collections.emptyList()); // 托盘库存

                int requiredQuantity = detail.getQuantity() != null ? detail.getQuantity() : 0;
                int remainingQuantity = requiredQuantity;
                int boxOutQuantity = 0; // 料箱出库数量
                int rackOutQuantity = 0; // 托盘出库数量

                // 计算料箱和托盘库存总量
                int totalBoxStock = boxTypeItems.stream()
                        .mapToInt(item -> item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0)
                        .sum();
                int totalRackStock = rackTypeItems.stream()
                        .mapToInt(item -> item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0)
                        .sum();
                int totalStock = totalBoxStock + totalRackStock;

                // 判断是否需要组合出库以及是否允许修改出库数量
                boolean needsCombinedOutbound = requiredQuantity > totalBoxStock
                        && requiredQuantity > totalRackStock;
                boolean canModifyOutboundQuantity = needsCombinedOutbound && requiredQuantity > totalStock;

                if (needsCombinedOutbound) {
                    if (canModifyOutboundQuantity) {
                        log.info("物料 [{}] 需要组合出库且总库存不足，允许修改出库数量 - 需求数量: {}, 料箱库存: {}, 托盘库存: {}, 总库存: {}, 缺少数量: {}",
                                detail.getMaterialCode(), requiredQuantity, totalBoxStock, totalRackStock,
                                totalStock, requiredQuantity - totalStock);
                    } else {
                        log.info("物料 [{}] 需要组合出库 - 需求数量: {}, 料箱库存: {}, 托盘库存: {}, 总库存: {}",
                                detail.getMaterialCode(), requiredQuantity, totalBoxStock, totalRackStock,
                                totalStock);
                    }
                }

                // 先从料箱出库
                if (!boxTypeItems.isEmpty() && remainingQuantity > 0) {
                    // 按库存数量降序排序，优先使用库存数量大的箱子
                    boxTypeItems.sort((a, b) -> Integer.compare(
                            b.getMaterialQuantity() != null ? b.getMaterialQuantity() : 0,
                            a.getMaterialQuantity() != null ? a.getMaterialQuantity() : 0));

                    // 创建料箱出库任务DTO
                    BoxOutItemDto boxOutItemDto = new BoxOutItemDto();
                    boxOutItemDto.setBoxType(1); // 料箱类型
                    boxOutItemDto.setTaskType(outbound.getOutType()); // 出库任务类型
                    List<BoxItemDto> boxItemDtoList = new ArrayList<>();
                    // 获取出库目的位置
                    String toSite = stringRedisTemplate.opsForValue().get("outbound");
                    String fromSite = "";
                    for (BoxItem item : boxTypeItems) {
                        // 根据料箱查找库位信息
                        Shelf shelf = shelfService.selectByBoxNo(item.getBoxNo());
                        if (shelf == null) {
                           throw new IllegalArgumentException(item.getBoxNo()+"未找到库位信息");
                        }
                        fromSite = shelf.getCode();
                        if (remainingQuantity <= 0)
                            break;

                        int itemQuantity = item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                        if (itemQuantity <= 0)
                            continue;

                        // 计算此箱可出库数量
                        int outQuantity = Math.min(remainingQuantity, itemQuantity);

                        // 创建箱内物料DTO
                        BoxItemDto boxItemDto = new BoxItemDto();
                        boxItemDto.setContractNo(item.getContractNo());
                        boxItemDto.setItemNo(item.getItemNo());
                        boxItemDto.setMaterialCode(item.getMaterialCode());
                        boxItemDto.setMaterialName(item.getMaterialName());
                        boxItemDto.setMaterialModel(item.getMaterialModel());
                        boxItemDto.setMaterialColor(item.getMaterialColor());
                        boxItemDto.setInQuantity(outQuantity);

                        boxItemDtoList.add(boxItemDto);

                        // 创建出库任务
                        WmsBoxTaskList boxTask = new WmsBoxTaskList();
                        boxTask.setId(item.getId());
                        boxTask.setBoxNo(item.getBoxNo());
                        boxTask.setBoxType(1); // 料箱
                        boxTask.setTaskType(outbound.getOutType());
                        boxTask.setTaskStatus(0); // 初始状态：创建
                        boxTask.setPriority(1); // 默认优先级
                        boxTask.setFromSite(fromSite); // 起始位置通常由WMS系统自动确定
                        boxTask.setToSite(toSite); // 默认出库口
                        boxTask.setCreateTime(new Date());

                        // 保存出库任务
                        WmsBoxTaskList createdTask = wmsBoxTaskListService.createTask(boxTask);
                        
                        // 设置任务号到BoxOutItemDto
                        if (createdTask != null && createdTask.getTaskOrder() != null) {
                            boxOutItemDto.setTaskOrder(createdTask.getTaskOrder());
                        }

                        // 创建预占用记录（预占用服务内部已包含库存验证）
                        WmsAuxiliaryOutPreBox reservation = preBoxService.createReservation(
                                outbound.getId(),
                                outbound.getOutStoreNumber(),
                                item.getId(),
                                outQuantity,
                                item.getMaterialCode(),
                                item.getContractNo(),
                                item.getItemNo(),
                                item.getMaterialColor(),
                                item.getMaterialModel(),
                                item.getBoxNo(),
                                item.getBoxType(),
                                outbound.getOutType(),
                                String.valueOf(item.getGridId()));

                        if (reservation == null) {
                            log.error("创建预占用记录失败: 出库单ID={}, 库存项ID={}, 数量={}",
                                    outbound.getId(), item.getId(), outQuantity);
                            throw new RuntimeException("创建预占用记录失败");
                        }

                        // 修正：改为逻辑扣减库存（通过预占用状态控制），而非物理扣减
                        // 实际库存扣减将在确认出库时进行
                        // item.setMaterialQuantity(itemQuantity - outQuantity);
                        // boxItemService.updateById(item);

                        log.info("预出库成功: 出库单={}, 物料={}, 数量={}, 剩余库存={}",
                                outbound.getOutStoreNumber(), item.getMaterialCode(), outQuantity,
                                item.getMaterialQuantity());

                        // 更新计数器
                        remainingQuantity -= outQuantity;
                        boxOutQuantity += outQuantity;
                    }

                    if (!boxItemDtoList.isEmpty()) {
                        boxOutItemDto.setBoxItemList(boxItemDtoList);
                        boxOutItemDto.setOutQuantityList(boxOutQuantity);
                        boxOutItems.add(boxOutItemDto);
                    }
                }

                // 如果还有剩余数量，从托盘出库
                if (!rackTypeItems.isEmpty() && remainingQuantity > 0) {
                    // 按库存数量降序排序，优先使用库存数量大的托盘
                    rackTypeItems.sort((a, b) -> Integer.compare(
                            b.getMaterialQuantity() != null ? b.getMaterialQuantity() : 0,
                            a.getMaterialQuantity() != null ? a.getMaterialQuantity() : 0));

                    // 创建托盘出库任务DTO
                    BoxOutItemDto rackOutItemDto = new BoxOutItemDto();
                    rackOutItemDto.setBoxType(2); // 托盘类型
                    rackOutItemDto.setTaskType(outbound.getOutType()); // 出库任务类型
                    List<BoxItemDto> rackItemDtoList = new ArrayList<>();
                    // 获取出库目的位置
                    String toSite = stringRedisTemplate.opsForValue().get("agvCar");
                    String fromSite = "";
                    for (BoxItem item : rackTypeItems) {
                        // 根据托盘查找库位信息
                        Shelf shelf = shelfService.selectByBoxNo(item.getBoxNo());
                        if (shelf == null) {
                           throw new IllegalArgumentException(item.getBoxNo()+"未找到库位信息");
                        }
                        fromSite = shelf.getCode();
                        if (remainingQuantity <= 0)
                            break;

                        int itemQuantity = item.getMaterialQuantity() != null ? item.getMaterialQuantity() : 0;
                        if (itemQuantity <= 0)
                            continue;

                        // 计算此托盘可出库数量
                        int outQuantity = Math.min(remainingQuantity, itemQuantity);

                        // 创建托盘内物料DTO
                        BoxItemDto rackItemDto = new BoxItemDto();
                        rackItemDto.setContractNo(item.getContractNo());
                        rackItemDto.setItemNo(item.getItemNo());
                        rackItemDto.setMaterialCode(item.getMaterialCode());
                        rackItemDto.setMaterialName(item.getMaterialName());
                        rackItemDto.setMaterialModel(item.getMaterialModel());
                        rackItemDto.setMaterialColor(item.getMaterialColor());
                        rackItemDto.setInQuantity(outQuantity);

                        rackItemDtoList.add(rackItemDto);

                        // 创建出库任务
                        WmsBoxTaskList rackTask = new WmsBoxTaskList();
                        rackTask.setId(UUID.randomUUID().toString());
                        rackTask.setBoxNo(item.getBoxNo());
                        rackTask.setBoxType(2); // 托盘
                        rackTask.setTaskType(outbound.getOutType());
                        rackTask.setTaskStatus(0); // 初始状态：创建
                        rackTask.setPriority(1); // 默认优先级
                        rackTask.setFromSite(fromSite); // 起始位置通常由WMS系统自动确定
                        rackTask.setToSite(toSite); // 默认出库口
                        rackTask.setCreateTime(new Date());

                        // 保存出库任务
                        WmsBoxTaskList createdRackTask = wmsBoxTaskListService.createTask(rackTask);
                        
                        // 设置任务号到RackOutItemDto
                        if (createdRackTask != null && createdRackTask.getTaskOrder() != null) {
                            rackOutItemDto.setTaskOrder(createdRackTask.getTaskOrder());
                        }

                        // 创建预占用记录（预占用服务内部已包含库存验证）
                        WmsAuxiliaryOutPreBox rackReservation = preBoxService.createReservation(
                                outbound.getId(),
                                outbound.getOutStoreNumber(),
                                item.getId(),
                                outQuantity,
                                item.getMaterialCode(),
                                item.getContractNo(),
                                item.getItemNo(),
                                item.getMaterialColor(),
                                item.getMaterialModel(),
                                item.getBoxNo(),
                                item.getBoxType(),
                                outbound.getOutType(), String.valueOf(item.getGridId()));

                        if (rackReservation == null) {
                            log.error("创建预占用记录失败: 出库单ID={}, 库存项ID={}, 数量={}",
                                    outbound.getId(), item.getId(), outQuantity);
                            throw new RuntimeException("创建预占用记录失败");
                        }

                        // 修正：改为逻辑扣减库存（通过预占用状态控制），而非物理扣减
                        // 实际库存扣减将在确认出库时进行
                        // item.setMaterialQuantity(itemQuantity - outQuantity);
                        // boxItemService.updateById(item);

                        log.info("预出库成功: 出库单={}, 物料={}, 数量={}, 剩余库存={}",
                                outbound.getOutStoreNumber(), item.getMaterialCode(), outQuantity,
                                item.getMaterialQuantity());

                        // 更新计数器
                        remainingQuantity -= outQuantity;
                        rackOutQuantity += outQuantity;
                    }

                    if (!rackItemDtoList.isEmpty()) {
                        rackOutItemDto.setBoxItemList(rackItemDtoList);
                        rackOutItemDto.setOutQuantityList(rackOutQuantity);
                        rackOutItems.add(rackOutItemDto);
                    }
                }

                // 更新出库明细的实际出库数量
                int actualOutQuantity = requiredQuantity - remainingQuantity;
                if (actualOutQuantity > 0) {
                    // 只有组合出库且库存不足的情况才修改出库数量
                    if (canModifyOutboundQuantity) {
                        log.info("组合出库且库存不足，修改物料 [{}] 的出库数量: {} -> {}",
                                detail.getMaterialCode(), requiredQuantity, actualOutQuantity);
                        detail.setQuantity(actualOutQuantity);
                        auxiliaryDetailService.updateById(detail);
                    } else if (actualOutQuantity < requiredQuantity) {
                        // 如果出库不足但不允许修改出库数量，记录警告日志
                        log.warn("物料 [{}] 实际出库数量 ({}) 小于需求数量 ({}), 但不满足修改条件 (非组合出库或组合库存足够)",
                                detail.getMaterialCode(), actualOutQuantity, requiredQuantity);
                    }
                }
            }

            // 7. 更新所有相关出库单状态为已预出库
            for (WmsAuxiliaryOutBound outbound : outboundList) {
                outbound.setStatus(1); // 1表示已预出库
                outbound.setUpdateTime(new Date());
            }
            updateBatchById(outboundList);

            // 8. 构建返回的出库详情
            resultDto.setBoxOutItemDtoList(boxOutItems);
            resultDto.setRackOutItemDtoList(rackOutItems);

            log.info("出库任务创建完成，共创建料箱出库任务 {} 个，托盘出库任务 {} 个，耗时: {}ms",
                    boxOutItems.size(), rackOutItems.size(), System.currentTimeMillis() - startTime);

            return resultDto;
        } catch (Exception e) {
            log.error("确认库存过程中发生异常", e);
            throw new RuntimeException("确认库存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建拆分后的DTO记录
     * 
     * @param originalDto 原始DTO
     * @param poNumber PO号
     * @return 拆分后的DTO
     */
    private AuxiliaryOutListDto createExpandedDto(AuxiliaryOutListDto originalDto, String poNumber) {
        AuxiliaryOutListDto expandedDto = new AuxiliaryOutListDto();
        BeanUtils.copyProperties(originalDto, expandedDto);
        // 复制原始DTO的所有字段
        expandedDto.setId(originalDto.getId()); // 生成唯一ID
        expandedDto.setOutStoreNumber(originalDto.getOutStoreNumber());
        expandedDto.setOutType(originalDto.getOutType());
        expandedDto.setStatus(originalDto.getStatus());
        expandedDto.setDeleteFlag(originalDto.getDeleteFlag());
        expandedDto.setRemarks(originalDto.getRemarks());
        expandedDto.setCreateBy(originalDto.getCreateBy());
        expandedDto.setCreateTime(originalDto.getCreateTime());
        expandedDto.setUpdateBy(originalDto.getUpdateBy());
        expandedDto.setUpdateTime(originalDto.getUpdateTime());
        
        // 复制明细字段
        expandedDto.setDetailId(originalDto.getDetailId());
        expandedDto.setRefNumber(originalDto.getRefNumber());
        expandedDto.setOperationType(originalDto.getOperationType());
        expandedDto.setContractNo(originalDto.getContractNo());
        expandedDto.setItemNo(originalDto.getItemNo());
        expandedDto.setReqListId(originalDto.getReqListId());
        expandedDto.setMaterialCode(originalDto.getMaterialCode());
        expandedDto.setMaterialName(originalDto.getMaterialName());
        expandedDto.setMaterialColor(originalDto.getMaterialColor());
        expandedDto.setMaterialColorCode(originalDto.getMaterialColorCode());
        expandedDto.setMaterialModel(originalDto.getMaterialModel());
        expandedDto.setQuantity(originalDto.getQuantity());
        expandedDto.setMaterialUnit(originalDto.getMaterialUnit());
        expandedDto.setPriority(originalDto.getPriority());
        expandedDto.setMaterialType(originalDto.getMaterialType());
        
        // 在备注中添加PO号信息，方便前端展示
        String newPoNumber = StringUtils.isNotBlank(originalDto.getPoNumber()) ? originalDto.getPoNumber() : poNumber;
        expandedDto.setPoNumber(newPoNumber);
        
        return expandedDto;
    }

    /**
     * 生成物料唯一标识键
     * 格式: 物料编码_合约号_款号_型号_颜色
     * 
     * @param item 库存项
     * @return 物料唯一标识键
     */
    private String generateMaterialKey(BoxItem item) {
        return String.format("%s_%s_%s_%s_%s",
                item.getMaterialCode(),
                item.getContractNo(),
                StringUtils.defaultString(item.getItemNo(), ""),
                StringUtils.defaultString(item.getMaterialModel(), ""),
                StringUtils.defaultString(item.getMaterialColor(), ""));
    }

    /**
     * 从物料明细生成物料唯一标识键
     * 格式: 物料编码_合约号_款号_型号_颜色
     * 
     * @param detail 物料明细
     * @return 物料唯一标识键
     */
    private String generateMaterialKeyFromDetail(WmsAuxiliaryDetail detail) {
        return String.format("%s_%s_%s_%s_%s",
                detail.getMaterialCode(),
                detail.getContractNo(),
                StringUtils.defaultString(detail.getItemNo(), ""),
                StringUtils.defaultString(detail.getMaterialModel(), ""),
                StringUtils.defaultString(detail.getMaterialColor(), ""));
    }

    /**
     * 确认实际出库 - 从预占用状态转为实际出库
     * 
     * @param outBoundId 出库单ID列表
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmActualOutbound(List<String> outBoundId) {
        log.info("开始确认实际出库，出库单ID数量: {}", outBoundId.size());

        try {
            // 1. 查询出库单列表
            List<WmsAuxiliaryOutBound> outboundList = list(new LambdaQueryWrapper<WmsAuxiliaryOutBound>()
                    .in(WmsAuxiliaryOutBound::getId, outBoundId));

            if (CollectionUtils.isEmpty(outboundList)) {
                log.warn("没有有效的出库单需要确认");
                throw new RuntimeException("没有有效的出库单需要确认");
            }

            // 2. 确认预占用记录状态（库存已在预出库时扣减）
            boolean confirmResult = preBoxService.updateStatusByOutboundIds(
                    outBoundId,
                    WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED,
                    WmsAuxiliaryOutPreBox.ReservationStatus.CONFIRMED);

            if (!confirmResult) {
                log.error("确认预占用记录失败");
                throw new RuntimeException("确认预占用记录失败");
            }

            // 3. 更新出库单状态为已完成出库
            for (WmsAuxiliaryOutBound outbound : outboundList) {
                outbound.setStatus(2); // 2表示已完成出库
                outbound.setUpdateTime(new Date());
            }
            boolean updateResult = this.updateBatchById(outboundList);

            if (!updateResult) {
                log.error("更新出库单状态失败");
                throw new RuntimeException("更新出库单状态失败");
            }

            log.info("确认实际出库成功，共处理出库单 {} 个", outboundList.size());
            return true;

        } catch (Exception e) {
            log.error("确认实际出库过程中发生异常", e);
            throw new RuntimeException("确认实际出库失败: " + e.getMessage(), e);
        }
    }

    /**
     * 取消预出库 - 删除预占用记录
     * 
     * @param outBoundId 出库单ID列表
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelPreOutbound(List<String> outBoundId) {
        log.info("开始取消预出库，出库单ID数量: {}", outBoundId.size());

        try {
            // 1. 查询出库单列表
            List<WmsAuxiliaryOutBound> outboundList = list(new LambdaQueryWrapper<WmsAuxiliaryOutBound>()
                    .in(WmsAuxiliaryOutBound::getId, outBoundId));

            if (CollectionUtils.isEmpty(outboundList)) {
                log.warn("没有有效的出库单需要取消");
                throw new RuntimeException("没有有效的出库单需要取消");
            }

            // 2. 检查出库单状态，只有预出库状态(1)的才可以取消
            List<WmsAuxiliaryOutBound> validOutbounds = outboundList.stream()
                    .filter(outbound -> outbound.getStatus() != null && outbound.getStatus() == 1)
                    .collect(Collectors.toList());

            if (validOutbounds.isEmpty()) {
                throw new RuntimeException("没有可取消的预出库单，只有预出库状态的出库单才能取消");
            }

            // 3. 查询预占用记录用于库存回滚
            List<WmsAuxiliaryOutPreBox> reservations = preBoxService.getByOutboundIds(outBoundId);
            if (CollectionUtils.isEmpty(reservations)) {
                log.warn("未找到预占用记录");
            } else {
                // 按库存项ID分组，准备回滚库存
                Map<String, List<WmsAuxiliaryOutPreBox>> reservationsByBoxItem = reservations.stream()
                        .filter(r -> r.getReservationStatus() == WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED)
                        .collect(Collectors.groupingBy(WmsAuxiliaryOutPreBox::getBoxItemId));

                // 逐个库存项进行库存回滚
                for (Map.Entry<String, List<WmsAuxiliaryOutPreBox>> entry : reservationsByBoxItem.entrySet()) {
                    String boxItemId = entry.getKey();
                    List<WmsAuxiliaryOutPreBox> itemReservations = entry.getValue();

                    // 计算该库存项总的预占用数量
                    int totalReservedQuantity = itemReservations.stream()
                            .mapToInt(r -> r.getReservedQuantity().intValue())
                            .sum();

                    // 查询当前库存项并回滚库存
                    BoxItem boxItem = boxItemService.getById(boxItemId);
                    if (boxItem == null) {
                        log.error("库存项不存在: {}", boxItemId);
                        throw new RuntimeException("库存项不存在: " + boxItemId);
                    }

                    int currentStock = boxItem.getMaterialQuantity() != null ? boxItem.getMaterialQuantity() : 0;

                    // 回滚库存：加回之前扣减的数量
                    boxItem.setMaterialQuantity(currentStock + totalReservedQuantity);
                    boxItemService.updateById(boxItem);

                    log.info("库存回滚成功: 库存项ID={}, 原库存={}, 回滚数量={}, 回滚后库存={}",
                            boxItemId, currentStock, totalReservedQuantity, boxItem.getMaterialQuantity());
                }
            }

            // 4. 取消预占用记录
            boolean cancelResult = preBoxService.cancelReservations(outBoundId);

            if (!cancelResult) {
                log.error("取消预占用记录失败");
                throw new RuntimeException("取消预占用记录失败");
            }

            // 5. 更新出库单状态为待处理
            for (WmsAuxiliaryOutBound outbound : validOutbounds) {
                outbound.setStatus(0); // 0表示待处理状态
                outbound.setUpdateTime(new Date());
            }
            boolean updateResult = this.updateBatchById(validOutbounds);

            if (!updateResult) {
                log.error("更新出库单状态失败");
                throw new RuntimeException("更新出库单状态失败");
            }

            log.info("取消预出库成功，共处理出库单 {} 个，回滚库存项 {} 个",
                    validOutbounds.size(),
                    reservations.stream().collect(Collectors.groupingBy(WmsAuxiliaryOutPreBox::getBoxItemId)).size());
            return true;

        } catch (Exception e) {
            log.error("取消预出库过程中发生异常", e);
            throw new RuntimeException("取消预出库失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理剩余物料入库 - 调整实际出库数量，剩余物料重新入库
     *
     * @param request 剩余物料入库请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processRemainingMaterialInbound(RemainInboundDto request) {
        log.info("开始处理剩余物料入库，出库单ID数量: {}, 操作人员: {}", 
                request.getOutBoundId().size(), request.getOperator());

        try {
            // 1. 参数验证
            validateRemainingInboundRequest(request);
            
            // 2. 查询出库单列表
            List<WmsAuxiliaryOutBound> outboundList = list(new LambdaQueryWrapper<WmsAuxiliaryOutBound>()
                    .in(WmsAuxiliaryOutBound::getId, request.getOutBoundId()));

            if (CollectionUtils.isEmpty(outboundList)) {
                log.warn("没有有效的出库单需要处理");
                throw new IllegalArgumentException("没有有效的出库单需要处理");
            }

            // 3. 验证出库单状态
            validateOutboundStatus(outboundList);

            // 4. 查询预占用记录
            List<WmsAuxiliaryOutPreBox> reservations = preBoxService.getByOutboundIds(request.getOutBoundId());
            if (CollectionUtils.isEmpty(reservations)) {
                log.warn("未找到预占用记录");
                throw new IllegalArgumentException("未找到预占用记录");
            }

            // 5. 验证预占用记录状态
            validateReservationStatus(reservations);

            // 6. 验证库存一致性 - 新增验证
            validateStockConsistency(reservations, request.getDetailQuantities());

            // 7. 基于明细ID处理实际出库和剩余物料回库
            RemainingInboundResult result = processRemainingMaterialsByDetailId(request, reservations);

            // 8. 更新相关明细表状态
            updateDetailStatusAfterProcessing(request.getOutBoundId(), reservations, result);

            // 9. 验证出库单完整性并更新状态 - 修改验证逻辑
            validateAndUpdateOutboundStatus(outboundList, request.getOperator());

            // 10. 记录操作历史
            recordRemainingInboundHistory(request, result);

            log.info("剩余物料入库成功，共处理出库单 {} 个，处理明细 {} 个，回库容器 {} 个，创建入库任务 {} 个", 
                    outboundList.size(), result.getProcessedDetails(), result.getProcessedContainers(), result.getCreatedTasks());
            return true;

        } catch (IllegalArgumentException e) {
            log.error("剩余物料入库参数错误: {}", e.getMessage());
            throw new RuntimeException("剩余物料入库失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("剩余物料入库处理异常", e);
            throw new RuntimeException("剩余物料入库失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证库存一致性 - 确保当前库存状态与预占用记录一致
     */
    private void validateStockConsistency(List<WmsAuxiliaryOutPreBox> reservations, 
                                         Map<String, Integer> actualOutQuantities) {
        log.info("开始验证库存一致性，预占用记录数量: {}", reservations.size());
        
        for (WmsAuxiliaryOutPreBox reservation : reservations) {
            String boxItemId = reservation.getBoxItemId();
            
            // 查询当前真实库存
            BoxItem currentBoxItem = boxItemService.getById(boxItemId);
            if (currentBoxItem == null) {
                log.error("库存项不存在: {}", boxItemId);
                throw new IllegalArgumentException("库存项不存在: " + boxItemId);
            }
            
            int currentStock = currentBoxItem.getMaterialQuantity() != null ? 
                              currentBoxItem.getMaterialQuantity() : 0;
            int reservedQuantity = reservation.getReservedQuantity().intValue();
            
            // 获取此明细的实际出库数量
            Integer actualOutQuantity = actualOutQuantities.get(boxItemId);
            if (actualOutQuantity == null) {
                actualOutQuantity = 0; // 默认为0，表示未出库
            }
            
            // 验证实际出库数量不能超过预占用数量
            if (actualOutQuantity > reservedQuantity) {
                log.error("实际出库数量超过预占用数量: BoxItem={}, 预占用={}, 实际出库={}", 
                         boxItemId, reservedQuantity, actualOutQuantity);
                throw new IllegalArgumentException(
                    String.format("实际出库数量(%d)不能超过预占用数量(%d)", actualOutQuantity, reservedQuantity));
            }
            
            // 验证当前库存是否足够回滚剩余数量
            int remainingQuantity = reservedQuantity - actualOutQuantity;
            if (remainingQuantity > 0) {
                // 检查库存回滚后是否会超出合理范围
                int expectedStockAfterRollback = currentStock + remainingQuantity;
                if (expectedStockAfterRollback < 0) {
                    log.error("库存回滚后数量异常: BoxItem={}, 当前库存={}, 回滚数量={}, 预期结果={}", 
                             boxItemId, currentStock, remainingQuantity, expectedStockAfterRollback);
                    throw new IllegalArgumentException(
                        String.format("库存状态异常，无法正确回滚: BoxItem=%s", boxItemId));
                }
            }
            
            log.debug("库存一致性验证通过: BoxItem={}, 当前库存={}, 预占用={}, 实际出库={}, 剩余={}", 
                     boxItemId, currentStock, reservedQuantity, actualOutQuantity, remainingQuantity);
        }
        
        log.info("库存一致性验证完成");
    }

    /**
     * 验证出库单完整性并更新状态
     */
    private void validateAndUpdateOutboundStatus(List<WmsAuxiliaryOutBound> outboundList, String operator) {
        log.info("开始验证出库单完整性并更新状态");
        
        for (WmsAuxiliaryOutBound outbound : outboundList) {
            String outboundId = outbound.getId();
            
            // 1. 查询该出库单下所有预占用记录
            List<WmsAuxiliaryOutPreBox> allReservations = preBoxService.list(
                new LambdaQueryWrapper<WmsAuxiliaryOutPreBox>()
                    .eq(WmsAuxiliaryOutPreBox::getOutboundId, outboundId)
                    .ne(WmsAuxiliaryOutPreBox::getStatus, "已取消")
            );
            
            if (CollectionUtils.isEmpty(allReservations)) {
                log.warn("出库单 {} 下没有找到有效的预占用记录", outboundId);
                continue;
            }
            
            // 2. 检查是否所有预占用记录都已处理完成
            boolean allReservationsProcessed = true;
            int totalReservations = allReservations.size();
            int processedReservations = 0;

            for (WmsAuxiliaryOutPreBox reservation : allReservations) {
                String status = reservation.getStatus();
                Integer reservationStatus = reservation.getReservationStatus();

                // 检查预占用记录是否已确认出库（状态为已出库且确认状态为CONFIRMED）
                if ("已出库".equals(status) &&
                    reservationStatus != null &&
                    reservationStatus.equals(WmsAuxiliaryOutPreBox.ReservationStatus.CONFIRMED)) {
                    processedReservations++;
                    log.debug("预占用记录 {} 已确认出库", reservation.getId());
                } else if ("已完成".equals(status)) {
                    processedReservations++;
                    log.debug("预占用记录 {} 已完成", reservation.getId());
                } else {
                    // 检查是否通过 remainingMaterialInbound 处理过
                    BigDecimal actualQuantity = reservation.getActualQuantity();
                    if (actualQuantity != null &&
                        reservationStatus != null &&
                        reservationStatus.equals(WmsAuxiliaryOutPreBox.ReservationStatus.CONFIRMED)) {
                        // 已通过剩余物料入库处理，认为已完成
                        processedReservations++;
                        log.debug("预占用记录 {} 已通过剩余物料入库处理", reservation.getId());
                    } else {
                        log.warn("出库单 {} 下存在未完成的预占用记录: {}, 状态: {}, 确认状态: {}, 实际出库数量: {}",
                                outboundId, reservation.getId(), status, reservationStatus, actualQuantity);
                        allReservationsProcessed = false;
                    }
                }
            }
            
            // 3. 检查该出库单是否还有正在进行的关键任务
            boolean hasActiveTasks = checkActiveTasksForOutbound(outboundId, allReservations);

            log.info("出库单 {} 完整性检查: 总预占用记录数={}, 已处理数={}, 预占用全部完成={}, 是否有活跃任务={}",
                    outboundId, totalReservations, processedReservations, allReservationsProcessed, hasActiveTasks);

            // 4. 判断出库单完成状态
            if (allReservationsProcessed) {
                // 所有预占用记录已处理完成
                if (!hasActiveTasks) {
                    // 无活跃任务，完全完成
                    outbound.setStatus(2); // 2表示已完成出库（含剩余物料处理）
                    outbound.setUpdateTime(new Date());
                    outbound.setUpdateBy(operator);

                    log.info("出库单 {} 所有预占用记录已处理完成且无活跃任务，更新状态为已完成", outboundId);
                } else {
                    // 有活跃任务，但预占用已完成，仍设为已完成（任务完成不影响出库单状态）
                    outbound.setStatus(2); // 2表示已完成出库
                    outbound.setUpdateTime(new Date());
                    outbound.setUpdateBy(operator);

                    log.info("出库单 {} 所有预占用记录已处理完成，虽有活跃任务但仍更新状态为已完成", outboundId);
                }
            } else {
                // 仍有未处理的预占用记录
                outbound.setStatus(1); // 1表示处理中，仍有未处理项
                outbound.setUpdateTime(new Date());
                outbound.setUpdateBy(operator);

                log.warn("出库单 {} 仍有未处理的预占用记录，状态设置为处理中", outboundId);
            }
        }
        
        // 批量更新出库单状态
        boolean updateResult = this.updateBatchById(outboundList);
        if (!updateResult) {
            log.error("更新出库单状态失败");
            throw new RuntimeException("更新出库单状态失败");
        }
        
        log.info("出库单状态更新完成");
    }
    
    /**
     * 检查出库单是否还有正在进行的关键任务
     * 注意：剩余物料入库任务和空容器入库任务不应阻止出库单完成
     */
    private boolean checkActiveTasksForOutbound(String outboundId, List<WmsAuxiliaryOutPreBox> reservations) {
        try {
            // 获取该出库单下所有相关的容器编码
            Set<String> containerNos = reservations.stream()
                    .map(WmsAuxiliaryOutPreBox::getBoxCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (containerNos.isEmpty()) {
                log.debug("出库单 {} 没有相关容器，无活跃任务", outboundId);
                return false;
            }

            // 查询这些容器是否有正在进行的关键任务
            // 排除入库相关任务，因为这些任务是出库完成后的后续处理
            List<WmsBoxTaskList> activeTasks = wmsBoxTaskListService.list(
                new LambdaQueryWrapper<WmsBoxTaskList>()
                    .in(WmsBoxTaskList::getBoxNo, containerNos)
                    .in(WmsBoxTaskList::getTaskStatus, Arrays.asList(0, 1, 2,3)) // 0创建，1已下发，2执行搬运
                    .notIn(WmsBoxTaskList::getTaskType, Arrays.asList(7)) // 排除指定入库任务（7）和空容器入库任务（8）
            );

            if (!activeTasks.isEmpty()) {
                log.info("出库单 {} 下发现 {} 个关键活跃任务，容器: {}",
                        outboundId, activeTasks.size(),
                        activeTasks.stream().map(WmsBoxTaskList::getBoxNo).collect(Collectors.toSet()));

                // 记录具体的活跃任务信息
                for (WmsBoxTaskList task : activeTasks) {
                    log.debug("关键活跃任务详情: 任务ID={}, 容器={}, 任务类型={}, 状态={}",
                            task.getId(), task.getBoxNo(), task.getTaskType(), task.getTaskStatus());
                }

                return true;
            }

            log.debug("出库单 {} 无关键活跃任务", outboundId);
            return false;

        } catch (Exception e) {
            log.error("检查出库单 {} 活跃任务时发生异常", outboundId, e);
            // 发生异常时保守处理，但不应阻止正常的出库单完成
            log.warn("由于异常，跳过活跃任务检查，允许出库单状态更新");
            return false;
        }
    }

    /**
     * 基于明细ID处理剩余物料入库
     */
    private RemainingInboundResult processRemainingMaterialsByDetailId(RemainInboundDto request, 
                                                                      List<WmsAuxiliaryOutPreBox> reservations) {
        RemainingInboundResult result = new RemainingInboundResult();
        
        // 将预占用记录按BoxItemId索引
        Map<String, WmsAuxiliaryOutPreBox> reservationMap = reservations.stream()
                .collect(Collectors.toMap(WmsAuxiliaryOutPreBox::getBoxItemId, Function.identity()));
        
        // 记录需要创建入库任务的容器
        Set<String> containersNeedInboundTask = new HashSet<>();

        // 遍历前端传递的明细数量映射
        for (Map.Entry<String, Integer> entry : request.getDetailQuantities().entrySet()) {
            String boxItemId = entry.getKey(); // 这里是BoxItem的ID
            Integer actualOutQuantity = entry.getValue();
            
            // 查找对应的预占用记录
            WmsAuxiliaryOutPreBox reservation = reservationMap.get(boxItemId);
            if (reservation == null) {
                log.warn("BoxItem ID [{}] 未找到对应的预占用记录，跳过处理", boxItemId);
                continue;
            }

            try {
                // 处理单个明细的出库确认和剩余物料回库
                boolean hasRemainingMaterial = processDetailRemainingInbound(reservation, actualOutQuantity, result);
                
                // 所有处理过的容器都需要创建入库任务（无论是否有剩余物料）
                // 有剩余物料的容器：剩余物料入库
                // 无剩余物料的容器：空容器入库
                containersNeedInboundTask.add(reservation.getBoxCode());
                
                if (hasRemainingMaterial) {
                    log.info("容器 [{}] 有剩余物料，将创建剩余物料入库任务", reservation.getBoxCode());
                } else {
                    log.info("容器 [{}] 无剩余物料，将创建空容器入库任务", reservation.getBoxCode());
                }
                
                result.incrementProcessedDetails();
                
            } catch (Exception e) {
                log.error("处理BoxItem ID [{}] 失败", boxItemId, e);
                throw new RuntimeException("处理BoxItem ID [" + boxItemId + "] 失败: " + e.getMessage(), e);
            }
        }

        // 为有剩余物料的容器创建入库任务
        createInboundTasksForContainers(containersNeedInboundTask, result);

        return result;
    }

    /**
     * 处理单个明细的出库确认和剩余物料回库
     */
    private boolean processDetailRemainingInbound(WmsAuxiliaryOutPreBox reservation, 
                                                 Integer actualOutQuantity, 
                                                 RemainingInboundResult result) {
        String preBoxId = reservation.getId();
        String boxItemId = reservation.getBoxItemId();
        String containerNo = reservation.getBoxCode();
        String materialCode = reservation.getMaterialCode();
        
        // 获取预占用数量
        int reservedQuantity = reservation.getReservedQuantity().intValue();
        
        // 计算剩余数量
        int remainingQuantity = reservedQuantity - actualOutQuantity;
        
        log.info("处理预占用记录 [{}] (BoxItem: {}) - 容器: {}, 物料: {}, 预占用: {}, 实际出库: {}, 剩余: {}", 
                preBoxId, boxItemId, containerNo, materialCode, reservedQuantity, actualOutQuantity, remainingQuantity);

        try {
            // 1. 再次验证当前库存状态（二次验证，确保数据一致性）
            BoxItem currentBoxItem = boxItemService.getById(boxItemId);
            if (currentBoxItem == null) {
                log.error("库存项不存在: {}", boxItemId);
                throw new RuntimeException("库存项不存在: " + boxItemId);
            }
            
            int currentStock = currentBoxItem.getMaterialQuantity() != null ? 
                              currentBoxItem.getMaterialQuantity() : 0;
            
            // 验证库存回滚的合理性
            if (remainingQuantity > 0) {
                int expectedStockAfterRollback = currentStock + remainingQuantity;
                if (expectedStockAfterRollback < 0) {
                    log.error("库存回滚后数量异常: BoxItem={}, 当前库存={}, 回滚数量={}, 预期结果={}", 
                             boxItemId, currentStock, remainingQuantity, expectedStockAfterRollback);
                    throw new RuntimeException("库存状态异常，无法正确回滚: BoxItem=" + boxItemId);
                }
            }
            
            // 2. 先扣减实际出库数量（这是关键的修正）
            if (actualOutQuantity > 0) {
                boolean outboundStockUpdateSuccess = updateStockWithOutboundQuantity(
                        reservation.getBoxItemId(), actualOutQuantity);

                if (!outboundStockUpdateSuccess) {
                    log.error("预占用记录 [{}] (BoxItem: {}) 实际出库库存扣减失败", preBoxId, boxItemId);
                    throw new RuntimeException("预占用记录 [" + preBoxId + "] 实际出库库存扣减失败");
                }

                log.info("预占用记录 [{}] (BoxItem: {}) 实际出库库存扣减成功: 容器={}, 物料={}, 出库数量={}",
                        preBoxId, boxItemId, containerNo, materialCode, actualOutQuantity);
            }

            // 3. 更新预占用记录的实际出库数量和状态
            reservation.setActualQuantity(new BigDecimal(actualOutQuantity));
            reservation.setReservedQuantity(new BigDecimal(actualOutQuantity)); // 预占用数量调整为实际出库数量
            reservation.setStatus("已出库");
            reservation.setReservationStatus(WmsAuxiliaryOutPreBox.ReservationStatus.CONFIRMED); // 使用常量设置状态
            reservation.setConfirmTime(java.time.LocalDateTime.now()); // 设置确认时间
            reservation.setUpdateTime(new Date());
            preBoxService.updateById(reservation);

            // 4. 如果有剩余数量，进行库存回滚
            if (remainingQuantity > 0) {
                boolean stockRollbackSuccess = updateStockWithRemainingQuantity(
                        reservation.getBoxItemId(), remainingQuantity);

                if (!stockRollbackSuccess) {
                    log.error("预占用记录 [{}] (BoxItem: {}) 剩余物料库存回滚失败", preBoxId, boxItemId);
                    throw new RuntimeException("预占用记录 [" + preBoxId + "] 剩余物料库存回滚失败");
                }

                // 5. 验证最终库存结果
                BoxItem finalBoxItem = boxItemService.getById(boxItemId);
                if (finalBoxItem != null) {
                    int finalStock = finalBoxItem.getMaterialQuantity() != null ?
                                    finalBoxItem.getMaterialQuantity() : 0;
                    // 最终库存 = 原库存 - 实际出库数量 + 剩余回滚数量 = 原库存 - 实际出库数量 + (预占用数量 - 实际出库数量) = 原库存 - 预占用数量 + 剩余数量
                    int expectedFinalStock = currentStock - actualOutQuantity + remainingQuantity;

                    if (finalStock != expectedFinalStock) {
                        log.error("最终库存验证失败: BoxItem={}, 原库存={}, 实际出库={}, 剩余回滚={}, 预期最终库存={}, 实际最终库存={}",
                                 boxItemId, currentStock, actualOutQuantity, remainingQuantity, expectedFinalStock, finalStock);
                        throw new RuntimeException("最终库存验证失败: BoxItem=" + boxItemId);
                    }
                }

                result.addProcessedMaterial(materialCode, remainingQuantity);
                log.info("预占用记录 [{}] (BoxItem: {}) 处理完成: 容器={}, 物料={}, 原库存={}, 实际出库={}, 剩余回滚={}, 最终库存={}",
                        preBoxId, boxItemId, containerNo, materialCode, currentStock, actualOutQuantity, remainingQuantity, currentStock - actualOutQuantity + remainingQuantity);

                return true; // 有剩余物料
            } else {
                // 6. 验证完全出库的最终库存结果
                BoxItem finalBoxItem = boxItemService.getById(boxItemId);
                if (finalBoxItem != null) {
                    int finalStock = finalBoxItem.getMaterialQuantity() != null ?
                                    finalBoxItem.getMaterialQuantity() : 0;
                    int expectedFinalStock = currentStock - actualOutQuantity;

                    if (finalStock != expectedFinalStock) {
                        log.error("完全出库库存验证失败: BoxItem={}, 原库存={}, 实际出库={}, 预期最终库存={}, 实际最终库存={}",
                                 boxItemId, currentStock, actualOutQuantity, expectedFinalStock, finalStock);
                        throw new RuntimeException("完全出库库存验证失败: BoxItem=" + boxItemId);
                    }
                }

                log.info("预占用记录 [{}] (BoxItem: {}) 完全出库处理完成: 容器={}, 物料={}, 原库存={}, 出库数量={}, 最终库存={}",
                        preBoxId, boxItemId, containerNo, materialCode, currentStock, actualOutQuantity, currentStock - actualOutQuantity);

                return false; // 无剩余物料
            }
            
        } catch (Exception e) {
            log.error("处理预占用记录 [{}] (BoxItem: {}) 的库存操作失败", preBoxId, boxItemId, e);
            throw e;
        }
    }

    /**
     * 为处理过的容器创建入库任务（包含剩余物料容器和空容器）
     */
    private void createInboundTasksForContainers(Set<String> containerNos, RemainingInboundResult result) {
        for (String containerNo : containerNos) {
            try {
                // 查询容器信息以确定容器类型
                BoxItem boxItem = boxItemService.getOne(new LambdaQueryWrapper<BoxItem>()
                        .eq(BoxItem::getBoxNo, containerNo)
                        .last("LIMIT 1"));
                
                if (boxItem != null) {
                    // 检查容器是否还有库存，以确定入库任务类型
                    int currentStock = boxItem.getMaterialQuantity() != null ? boxItem.getMaterialQuantity() : 0;
                    int taskType=7;
                    String taskDescription;
                    if (currentStock > 0) {
                        taskDescription = "剩余物料入库";
                    } else {
                        taskDescription = "空容器入库";
                    }
                    
                    createInboundTask(containerNo, boxItem.getBoxType(), taskType);
                    result.incrementCreatedTasks();
                    result.incrementProcessedContainers();
                    
                    log.info("为容器 [{}] 创建{}任务成功，当前库存: {}", containerNo, taskDescription, currentStock);
                } else {
                    log.warn("容器 [{}] 未找到对应的库存信息，跳过入库任务创建", containerNo);
                }
                
            } catch (Exception e) {
                log.error("为容器 [{}] 创建入库任务失败", containerNo, e);
                // 不抛异常，避免影响其他容器的处理
            }
        }
    }

    /**
     * 更新相关明细表状态
     */
    private void updateDetailStatusAfterProcessing(List<String> outBoundIds, 
                                                   List<WmsAuxiliaryOutPreBox> reservations, 
                                                   RemainingInboundResult result) {
        log.info("开始更新明细表状态，出库单数量: {}", outBoundIds.size());
        
        try {
            // 获取所有出库单相关的明细记录
            List<WmsAuxiliaryDetail> detailsToUpdate = new ArrayList<>();
            
            for (String outBoundId : outBoundIds) {
                // 查询出库单信息
                WmsAuxiliaryOutBound outbound = this.getById(outBoundId);
                if (outbound == null) {
                    log.warn("出库单不存在: {}", outBoundId);
                    continue;
                }
                
                // 查询该出库单对应的明细记录
                List<WmsAuxiliaryDetail> details = auxiliaryDetailService.list(
                    new LambdaQueryWrapper<WmsAuxiliaryDetail>()
                        .eq(WmsAuxiliaryDetail::getRefNumber, outbound.getOutStoreNumber())
                        .eq(WmsAuxiliaryDetail::getOperationType, 1) // 1表示出库
                );
                
                if (!CollectionUtils.isEmpty(details)) {
                    for (WmsAuxiliaryDetail detail : details) {
                        // 检查该明细是否已经处理过
                        boolean isProcessed = reservations.stream()
                                .anyMatch(r -> matchDetailWithReservation(detail, r));
                        
                        if (isProcessed) {
                            // 更新明细状态为已处理
                            detail.setPreboxStatus(1); // 1表示已预装/已处理
                            detail.setPreboxId(reservations.get(0).getId());
                            detail.setUpdateTime(new Date());
                            detail.setUpdateBy("系统自动更新");
                            detailsToUpdate.add(detail);
                            
                            log.debug("标记明细记录为已处理: 出库单={}, 物料={}, 合约={}", 
                                    detail.getRefNumber(), detail.getMaterialCode(), detail.getContractNo());
                        }
                    }
                }
            }
            
            // 批量更新明细记录状态
            if (!detailsToUpdate.isEmpty()) {
                boolean updateSuccess = auxiliaryDetailService.updateBatchById(detailsToUpdate);
                if (updateSuccess) {
                    log.info("明细表状态更新成功，更新记录数: {}", detailsToUpdate.size());
                } else {
                    log.error("明细表状态更新失败");
                    throw new RuntimeException("明细表状态更新失败");
                }
            } else {
                log.info("没有需要更新状态的明细记录");
            }
            
        } catch (Exception e) {
            log.error("更新明细表状态时发生异常", e);
            throw new RuntimeException("更新明细表状态失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查明细记录与预占用记录是否匹配
     */
    private boolean matchDetailWithReservation(WmsAuxiliaryDetail detail, WmsAuxiliaryOutPreBox reservation) {
        // 通过物料编码、合约号、款号等关键字段进行匹配
        return Objects.equals(detail.getMaterialCode(), reservation.getMaterialCode()) 
                && Objects.equals(detail.getContractNo(), reservation.getContractNo())
                && Objects.equals(detail.getItemNo(), reservation.getStyleNo())
                && Objects.equals(detail.getMaterialColor(), reservation.getMaterialColor());
    }

    /**
     * 验证剩余物料入库请求参数
     */
    private void validateRemainingInboundRequest(RemainInboundDto request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (CollectionUtils.isEmpty(request.getOutBoundId())) {
            throw new IllegalArgumentException("出库单ID列表不能为空");
        }
        if (request.getDetailQuantities() == null || request.getDetailQuantities().isEmpty()) {
            throw new IllegalArgumentException("BoxItem数量信息不能为空");
        }
        if (StringUtils.isBlank(request.getOperator())) {
            throw new IllegalArgumentException("操作人员不能为空");
        }

        // 验证BoxItem实际出库数量值
        for (Map.Entry<String, Integer> entry : request.getDetailQuantities().entrySet()) {
            if (entry.getValue() == null || entry.getValue() < 0) {
                throw new IllegalArgumentException("BoxItem [" + entry.getKey() + "] 的实际出库数量无效: " + entry.getValue());
            }
        }
    }

    /**
     * 验证出库单状态
     */
    private void validateOutboundStatus(List<WmsAuxiliaryOutBound> outboundList) {
        for (WmsAuxiliaryOutBound outbound : outboundList) {
            if (outbound.getStatus() == null) {
                throw new IllegalArgumentException("出库单 [" + outbound.getOutStoreNumber() + "] 状态异常");
            }
            if (outbound.getStatus() != 1) { // 1表示已预出库
                throw new IllegalArgumentException("出库单 [" + outbound.getOutStoreNumber() + 
                    "] 状态不正确，当前状态: " + outbound.getStatus() + "，只有预出库状态才能进行剩余物料入库");
            }
        }
    }

    /**
     * 验证预占用记录状态
     */
    private void validateReservationStatus(List<WmsAuxiliaryOutPreBox> reservations) {
        for (WmsAuxiliaryOutPreBox reservation : reservations) {
            if (reservation.getReservationStatus() != WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED) {
                throw new IllegalArgumentException("预占用记录 [" + reservation.getId() + 
                    "] 状态不正确，当前状态: " + reservation.getReservationStatus());
            }
        }
    }

    /**
     * 验证实际出库数量的合理性
     */
    private void validateActualOutQuantities(Map<String, Integer> actualOutQuantities, 
                                           List<WmsAuxiliaryOutPreBox> reservations) {
        // 按物料键分组预占用记录
        Map<String, List<WmsAuxiliaryOutPreBox>> reservationsByMaterialKey = reservations.stream()
                .collect(Collectors.groupingBy(this::generateMaterialKeyFromReservation));

        for (Map.Entry<String, Integer> entry : actualOutQuantities.entrySet()) {
            String materialKey = entry.getKey();
            Integer actualQuantity = entry.getValue();
            
            List<WmsAuxiliaryOutPreBox> materialReservations = reservationsByMaterialKey.get(materialKey);
            if (CollectionUtils.isEmpty(materialReservations)) {
                throw new IllegalArgumentException("物料 [" + materialKey + "] 未找到对应的预占用记录");
            }

            // 计算总预占用数量
            int totalReservedQuantity = materialReservations.stream()
                    .mapToInt(r -> r.getReservedQuantity().intValue())
                    .sum();

            if (actualQuantity > totalReservedQuantity) {
                throw new IllegalArgumentException("物料 [" + materialKey + 
                    "] 的实际出库数量 (" + actualQuantity + ") 不能大于预占用数量 (" + totalReservedQuantity + ")");
            }
        }
    }

    /**
     * 按容器处理剩余物料入库
     */
    private RemainingInboundResult processRemainingMaterialsByContainer(RemainInboundDto request, 
                                                                       List<WmsAuxiliaryOutPreBox> reservations) {
        RemainingInboundResult result = new RemainingInboundResult();
        
        // 按物料键分组预占用记录
        Map<String, List<WmsAuxiliaryOutPreBox>> reservationsByMaterialKey = reservations.stream()
                .collect(Collectors.groupingBy(this::generateMaterialKeyFromReservation));

        for (Map.Entry<String, Integer> entry : request.getActualOutQuantities().entrySet()) {
            String materialKey = entry.getKey();
            Integer actualOutQuantity = entry.getValue();

            List<WmsAuxiliaryOutPreBox> materialReservations = reservationsByMaterialKey.get(materialKey);
            if (CollectionUtils.isEmpty(materialReservations)) {
                log.warn("物料 [{}] 未找到对应的预占用记录，跳过处理", materialKey);
                continue;
            }

            // 按容器分组处理，避免跨容器的数量混乱
            Map<String, List<WmsAuxiliaryOutPreBox>> reservationsByContainer = materialReservations.stream()
                    .collect(Collectors.groupingBy(WmsAuxiliaryOutPreBox::getBoxCode));

            // 分配实际出库数量到各个容器
            int remainingActualQuantity = actualOutQuantity;
            
            for (Map.Entry<String, List<WmsAuxiliaryOutPreBox>> containerEntry : reservationsByContainer.entrySet()) {
                String containerNo = containerEntry.getKey();
                List<WmsAuxiliaryOutPreBox> containerReservations = containerEntry.getValue();
                
                if (remainingActualQuantity <= 0) {
                    // 剩余数量已分配完，该容器的预占用全部回滚
                    processContainerRemainingInbound(containerNo, containerReservations, 0, result);
                } else {
                    // 计算该容器的预占用总量
                    int containerReservedQuantity = containerReservations.stream()
                            .mapToInt(r -> r.getReservedQuantity().intValue())
                            .sum();
                    
                    // 分配给该容器的实际出库数量
                    int containerActualQuantity = Math.min(remainingActualQuantity, containerReservedQuantity);
                    
                    processContainerRemainingInbound(containerNo, containerReservations, containerActualQuantity, result);
                    
                    remainingActualQuantity -= containerActualQuantity;
                }
            }
        }

        return result;
    }

    /**
     * 处理单个容器的剩余物料入库
     */
    private void processContainerRemainingInbound(String containerNo, 
                                                 List<WmsAuxiliaryOutPreBox> containerReservations,
                                                 int containerActualQuantity,
                                                 RemainingInboundResult result) {
        log.info("处理容器 [{}] 的剩余物料入库，预占用记录数: {}, 实际出库数量: {}", 
                containerNo, containerReservations.size(), containerActualQuantity);

        // 计算容器总预占用数量
        int totalReservedQuantity = containerReservations.stream()
                .mapToInt(r -> r.getReservedQuantity().intValue())
                .sum();

        int remainingQuantity = totalReservedQuantity - containerActualQuantity;
        
        if (remainingQuantity <= 0) {
            log.info("容器 [{}] 无剩余物料需要入库", containerNo);
            return;
        }

        // 选择第一个预占用记录作为代表（同一容器的同一物料应该在同一个库存项中）
        WmsAuxiliaryOutPreBox representativeReservation = containerReservations.get(0);

        try {
            // 回滚剩余数量到库存（使用乐观锁）
            boolean stockUpdateSuccess = updateStockWithRemainingQuantity(
                    representativeReservation.getBoxItemId(), remainingQuantity);
            
            if (!stockUpdateSuccess) {
                log.error("容器 [{}] 库存回滚失败，可能存在并发冲突", containerNo);
                throw new RuntimeException("容器 [" + containerNo + "] 库存回滚失败，请稍后重试");
            }

            // 创建入库任务
            BoxItem boxItem = boxItemService.getById(representativeReservation.getBoxItemId());
            if (boxItem != null) {
                createInboundTask(boxItem.getBoxNo(), boxItem.getBoxType(), 7); // 指定入库
                result.incrementCreatedTasks();
            }

            // 更新预占用记录的实际出库数量
            updateReservationActualQuantity(containerReservations, containerActualQuantity);

            result.incrementProcessedContainers();
            result.addProcessedMaterial(representativeReservation.getMaterialCode(), remainingQuantity);

            log.info("容器 [{}] 剩余物料入库成功: 物料={}, 剩余数量={}", 
                    containerNo, representativeReservation.getMaterialCode(), remainingQuantity);

        } catch (Exception e) {
            log.error("处理容器 [{}] 剩余物料入库失败", containerNo, e);
            throw new RuntimeException("处理容器 [" + containerNo + "] 剩余物料入库失败: " + e.getMessage(), e);
        }
    }

    /**
     * 扣减实际出库数量的库存
     */
    private boolean updateStockWithOutboundQuantity(String boxItemId, int outboundQuantity) {
        if (outboundQuantity <= 0) {
            log.warn("出库数量无效: {}", outboundQuantity);
            return false;
        }

        for (int attempt = 0; attempt < 3; attempt++) { // 最多重试3次
            try {
                // 使用SELECT FOR UPDATE锁定记录，避免并发问题
                BoxItem boxItem = boxItemService.getOne(new LambdaQueryWrapper<BoxItem>()
                        .eq(BoxItem::getId, boxItemId)
                        .last("FOR UPDATE"));

                if (boxItem == null) {
                    log.error("库存项不存在: {}", boxItemId);
                    return false;
                }

                int currentStock = boxItem.getMaterialQuantity() != null ? boxItem.getMaterialQuantity() : 0;
                int newStock = currentStock - outboundQuantity; // 扣减出库数量

                // 验证库存扣减后不能为负数
                if (newStock < 0) {
                    log.error("库存扣减后数量为负数: BoxItem={}, 当前库存={}, 出库数量={}, 结果={}",
                             boxItemId, currentStock, outboundQuantity, newStock);
                    throw new RuntimeException("库存不足，无法完成出库操作");
                }

                boxItem.setMaterialQuantity(newStock);
                boxItem.setUpdateTime(new Date());

                boolean updateSuccess = boxItemService.updateById(boxItem);
                if (updateSuccess) {
                    // 验证更新后的结果
                    BoxItem verifyItem = boxItemService.getById(boxItemId);
                    if (verifyItem != null && verifyItem.getMaterialQuantity().equals(newStock)) {
                        log.debug("库存扣减成功: 库存项ID={}, 原库存={}, 出库数量={}, 新库存={}",
                                boxItemId, currentStock, outboundQuantity, newStock);
                        return true;
                    } else {
                        log.warn("库存扣减验证失败，重试第 {} 次", attempt + 1);
                    }
                } else {
                    log.warn("库存扣减失败，重试第 {} 次", attempt + 1);
                }

                Thread.sleep(100); // 短暂等待后重试

            } catch (Exception e) {
                log.warn("库存扣减异常，重试第 {} 次: {}", attempt + 1, e.getMessage());
                if (attempt == 2) { // 最后一次重试失败
                    throw new RuntimeException("库存扣减失败: " + e.getMessage(), e);
                }
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("操作被中断", ie);
                }
            }
        }

        log.error("库存扣减重试3次均失败: BoxItem={}", boxItemId);
        return false;
    }

    /**
     * 使用乐观锁更新库存（回滚剩余数量）
     */
    private boolean updateStockWithRemainingQuantity(String boxItemId, int remainingQuantity) {
        if (remainingQuantity <= 0) {
            log.warn("剩余数量无效: {}", remainingQuantity);
            return false;
        }
        
        for (int attempt = 0; attempt < 3; attempt++) { // 最多重试3次
            try {
                // 使用SELECT FOR UPDATE锁定记录，避免并发问题
                BoxItem boxItem = boxItemService.getOne(new LambdaQueryWrapper<BoxItem>()
                        .eq(BoxItem::getId, boxItemId)
                        .last("FOR UPDATE"));
                
                if (boxItem == null) {
                    log.error("库存项不存在: {}", boxItemId);
                    return false;
                }

                int currentStock = boxItem.getMaterialQuantity() != null ? boxItem.getMaterialQuantity() : 0;
                int newStock = currentStock + remainingQuantity;
                
                // 额外的业务规则验证
                if (newStock < 0) {
                    log.error("库存回滚后数量为负数: BoxItem={}, 当前库存={}, 回滚数量={}, 结果={}", 
                             boxItemId, currentStock, remainingQuantity, newStock);
                    throw new RuntimeException("库存回滚后数量不能为负数");
                }
                
                // 记录原始版本号或更新时间，用于乐观锁验证
                Date originalUpdateTime = boxItem.getUpdateTime();
                
                boxItem.setMaterialQuantity(newStock);
                boxItem.setUpdateTime(new Date());
                
                boolean updateSuccess = boxItemService.updateById(boxItem);
                if (updateSuccess) {
                    // 验证更新后的结果
                    BoxItem verifyItem = boxItemService.getById(boxItemId);
                    if (verifyItem != null && verifyItem.getMaterialQuantity().equals(newStock)) {
                        log.debug("库存回滚成功: 库存项ID={}, 原库存={}, 回滚数量={}, 新库存={}", 
                                boxItemId, currentStock, remainingQuantity, newStock);
                        return true;
                    } else {
                        log.warn("库存更新验证失败，重试第 {} 次", attempt + 1);
                    }
                } else {
                    log.warn("库存更新失败，重试第 {} 次", attempt + 1);
                }
                
                Thread.sleep(100); // 短暂等待后重试
                
            } catch (Exception e) {
                log.warn("库存更新异常，重试第 {} 次: {}", attempt + 1, e.getMessage());
                if (attempt == 2) { // 最后一次重试失败
                    throw new RuntimeException("库存更新失败: " + e.getMessage(), e);
                }
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("操作被中断", ie);
                }
            }
        }
        
        log.error("库存更新重试3次均失败: BoxItem={}", boxItemId);
        return false;
    }

    /**
     * 更新预占用记录的实际出库数量
     */
    private void updateReservationActualQuantity(List<WmsAuxiliaryOutPreBox> reservations, int totalActualQuantity) {
        int remainingActualQuantity = totalActualQuantity;
        
        for (WmsAuxiliaryOutPreBox reservation : reservations) {
            int reservedQuantity = reservation.getReservedQuantity().intValue();
            int actualQuantityForThisReservation = Math.min(remainingActualQuantity, reservedQuantity);
            
            reservation.setReservedQuantity(new BigDecimal(actualQuantityForThisReservation));
            reservation.setUpdateTime(new Date());
            preBoxService.updateById(reservation);
            
            remainingActualQuantity -= actualQuantityForThisReservation;
            
            if (remainingActualQuantity <= 0) {
                break;
            }
        }
    }



    /**
     * 记录剩余物料入库历史
     */
    private void recordRemainingInboundHistory(RemainInboundDto request, RemainingInboundResult result) {
        try {
            // 这里可以记录到操作日志表或者库存变动历史表
            log.info("剩余物料入库操作记录: 操作人员={}, 处理容器数={}, 创建任务数={}, 处理物料={}",
                    request.getOperator(), result.getProcessedContainers(), 
                    result.getCreatedTasks(), result.getProcessedMaterials());
            
            // TODO: 实现具体的历史记录逻辑
            // 可以调用专门的历史记录服务
            
        } catch (Exception e) {
            log.error("记录剩余物料入库历史失败", e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 剩余物料入库处理结果
     */
    private static class RemainingInboundResult {
        private int processedContainers = 0;
        private int createdTasks = 0;
        private int processedDetails = 0;
        private Map<String, Integer> processedMaterials = new HashMap<>();

        public void incrementProcessedContainers() {
            this.processedContainers++;
        }

        public void incrementCreatedTasks() {
            this.createdTasks++;
        }

        public void incrementProcessedDetails() {
            this.processedDetails++;
        }

        public void addProcessedMaterial(String materialCode, int quantity) {
            this.processedMaterials.merge(materialCode, quantity, Integer::sum);
        }

        public int getProcessedContainers() {
            return processedContainers;
        }

        public int getCreatedTasks() {
            return createdTasks;
        }

        public int getProcessedDetails() {
            return processedDetails;
        }

        public Map<String, Integer> getProcessedMaterials() {
            return processedMaterials;
        }
    }

    /**
     * 托盘拆零入库 - 将托盘中剩余物料转换为料箱入库
     *
     * @param request 托盘拆零入库请求
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processPalletBreakInbound(BreakInboundDto request) {
        log.info("开始处理托盘拆零入库，出库单ID数量: {}", request.getOutBoundId().size());

        try {
            // 1. 查询出库单列表
            List<WmsAuxiliaryOutBound> outboundList = list(new LambdaQueryWrapper<WmsAuxiliaryOutBound>()
                    .in(WmsAuxiliaryOutBound::getId, request.getOutBoundId()));

            if (CollectionUtils.isEmpty(outboundList)) {
                log.warn("没有有效的出库单需要处理");
                throw new RuntimeException("没有有效的出库单需要处理");
            }

            // 2. 查询托盘预占用记录（只处理托盘类型）
            List<WmsAuxiliaryOutPreBox> palletReservations = preBoxService.getByOutboundIds(request.getOutBoundId())
                    .stream()
                    .filter(r -> r.getContainerType() != null && r.getContainerType() == 2) // 2表示托盘
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(palletReservations)) {
                log.warn("未找到托盘预占用记录");
                throw new RuntimeException("未找到托盘预占用记录");
            }

            // 3. 处理托盘拆零入库
            for (Map.Entry<String, Integer> entry : request.getBreakQuantities().entrySet()) {
                String materialKey = entry.getKey(); // 托盘物料编码_合约号_款号
                Integer breakQuantity = entry.getValue();

                // 查找对应的托盘预占用记录
                List<WmsAuxiliaryOutPreBox> materialReservations = palletReservations.stream()
                        .filter(r -> generateMaterialKeyFromReservation(r).equals(materialKey))
                        .collect(Collectors.toList());

                if (!materialReservations.isEmpty()) {
                    WmsAuxiliaryOutPreBox palletReservation = materialReservations.get(0);

                    // 从托盘扣减拆零数量
                    BoxItem palletItem = boxItemService.getById(palletReservation.getBoxItemId());
                    if (palletItem == null) {
                        log.error("托盘不存在: {}", palletReservation.getBoxItemId());
                        continue;
                    }

                    int currentPalletStock = palletItem.getMaterialQuantity() != null ? palletItem.getMaterialQuantity()
                            : 0;
                    if (currentPalletStock < breakQuantity) {
                        log.error("托盘库存不足，无法拆零: 当前库存={}, 拆零数量={}", currentPalletStock, breakQuantity);
                        continue;
                    }

                    // 扣减托盘库存
                    palletItem.setMaterialQuantity(currentPalletStock - breakQuantity);
                    boxItemService.updateById(palletItem);

                    // 使用ContainerSelector分配目标料箱（参照辅料入库逻辑）
                    String targetBoxNo = allocateTargetBox(request, palletReservation, breakQuantity);

                    // 查询或创建目标料箱库存项
                    BoxItem targetBoxItem = boxItemService.getOne(new LambdaQueryWrapper<BoxItem>()
                            .eq(BoxItem::getBoxNo, targetBoxNo)
                            .eq(BoxItem::getMaterialCode, palletReservation.getMaterialCode())
                            .eq(BoxItem::getContractNo, palletReservation.getContractNo())
                            .eq(BoxItem::getItemNo, palletReservation.getStyleNo())
                            .eq(BoxItem::getBoxType, 1), false); // 1表示料箱

                    if (targetBoxItem == null) {
                        // 创建新的料箱库存项
                        targetBoxItem = new BoxItem();
                        targetBoxItem.setId(UUID.randomUUID().toString());
                        targetBoxItem.setBoxNo(targetBoxNo);
                        targetBoxItem.setBoxType(1); // 料箱
                        targetBoxItem.setMaterialCode(palletReservation.getMaterialCode());
                        targetBoxItem.setContractNo(palletReservation.getContractNo());
                        targetBoxItem.setItemNo(palletReservation.getStyleNo());
                        targetBoxItem.setMaterialColor(palletReservation.getMaterialColor());
                        targetBoxItem.setMaterialModel(palletReservation.getMaterialSpec());
                        targetBoxItem.setMaterialQuantity(breakQuantity);
                        targetBoxItem.setMaterialProperty(palletItem.getMaterialProperty());
                        targetBoxItem.setCreateTime(new Date());
                        boxItemService.save(targetBoxItem);
                    } else {
                        // 更新现有料箱库存
                        int currentBoxStock = targetBoxItem.getMaterialQuantity() != null
                                ? targetBoxItem.getMaterialQuantity()
                                : 0;
                        targetBoxItem.setMaterialQuantity(currentBoxStock + breakQuantity);
                        boxItemService.updateById(targetBoxItem);
                    }

                    log.info("托盘拆零入库成功: 托盘={}, 料箱={}, 拆零数量={}, 托盘剩余={}, 料箱库存={}",
                            palletItem.getBoxNo(), targetBoxNo, breakQuantity,
                            palletItem.getMaterialQuantity(), targetBoxItem.getMaterialQuantity());

                    // 创建入库任务
                    createInboundTask(targetBoxNo, 1, 7); // 料箱类型，指定入库
                }
            }

            // 4. 更新出库单状态
            for (WmsAuxiliaryOutBound outbound : outboundList) {
                outbound.setStatus(2); // 2表示已完成出库（含拆零处理）
                outbound.setUpdateTime(new Date());
            }
            boolean updateResult = this.updateBatchById(outboundList);

            if (!updateResult) {
                log.error("更新出库单状态失败");
                throw new RuntimeException("更新出库单状态失败");
            }

            log.info("托盘拆零入库成功，共处理出库单 {} 个", outboundList.size());
            return true;

        } catch (Exception e) {
            log.error("托盘拆零入库过程中发生异常", e);
            throw new RuntimeException("托盘拆零入库失败: " + e.getMessage(), e);
        }
    }

    /**
     * 分配目标料箱 - 参照辅料入库的料箱分配逻辑（只支持自动分配）
     * 
     * @param request           拆零入库请求
     * @param palletReservation 托盘预占用记录
     * @param breakQuantity     拆零数量
     * @return 分配的料箱号
     */
    private String allocateTargetBox(BreakInboundDto request, WmsAuxiliaryOutPreBox palletReservation,
            Integer breakQuantity) {
        try {
            // 只支持自动分配料箱：使用ContainerSelector进行智能分配
            return allocateBoxUsingSelector(palletReservation, breakQuantity);
        } catch (Exception e) {
            log.error("分配料箱失败，将使用默认生成的料箱号: {}", e.getMessage());
            // 如果分配失败，回退到生成新的料箱号
            return generateNewBoxNo();
        }
    }

    /**
     * 使用ContainerSelector进行料箱分配
     * 
     * @param palletReservation 托盘预占用记录
     * @param breakQuantity     拆零数量
     * @return 分配的料箱号
     */
    private String allocateBoxUsingSelector(WmsAuxiliaryOutPreBox palletReservation, Integer breakQuantity) {
        // 构造类似辅料入库的DTO
        AuxiliaryPreboxDto preboxDto = new AuxiliaryPreboxDto();
        preboxDto.setContractNo(palletReservation.getContractNo());
        preboxDto.setStyleNo(palletReservation.getStyleNo());
        preboxDto.setMaterialCode(palletReservation.getMaterialCode());
        preboxDto.setMaterialName(palletReservation.getMaterialName());
        preboxDto.setInQuantity(breakQuantity);
        preboxDto.setMaterialModel(palletReservation.getMaterialSpec());
        preboxDto.setMaterialColor(palletReservation.getMaterialColor());
        preboxDto.setContainerType(1); // 目标是料箱
        preboxDto.setMaterialProperty(palletReservation.getMaterialProperty());

        // 查询物料信息
        WmsAuxiliaryInfo materialInfo = auxiliaryInfoService.getOne(
                new LambdaQueryWrapper<WmsAuxiliaryInfo>()
                        .eq(WmsAuxiliaryInfo::getMaterialCode, palletReservation.getMaterialCode())
                        .last("LIMIT 1"));

        if (materialInfo == null) {
            log.warn("未找到物料 {} 的基本信息，使用默认生成料箱号", palletReservation.getMaterialCode());
            return generateNewBoxNo();
        }

        // 使用ContainerSelector查找或创建合适的料箱
        try {
            Container targetContainer = containerSelector.findOrCreateContainerForItem(preboxDto, materialInfo);
            if (targetContainer != null && StringUtils.isNotBlank(targetContainer.getBoxNo())) {
                log.info("ContainerSelector成功分配料箱: {}", targetContainer.getBoxNo());
                return targetContainer.getBoxNo();
            } else {
                log.warn("ContainerSelector未返回有效料箱，使用默认生成");
                return generateNewBoxNo();
            }
        } catch (Exception e) {
            log.error("ContainerSelector分配料箱异常: {}", e.getMessage());
            return generateNewBoxNo();
        }
    }

    /**
     * 生成新的料箱编号
     */
    private String generateNewBoxNo() {
        // 简化实现，实际应该调用编号生成服务
        return "BOX" + System.currentTimeMillis();
    }

    /**
     * 生成物料唯一标识键（从预占用记录）
     * 格式: 物料编码_合约号_款号_型号_颜色
     */
    private String generateMaterialKeyFromReservation(WmsAuxiliaryOutPreBox reservation) {
        return String.format("%s_%s_%s_%s_%s",
                reservation.getMaterialCode(),
                reservation.getContractNo(),
                StringUtils.defaultString(reservation.getStyleNo(), ""),
                StringUtils.defaultString(reservation.getMaterialSpec(), ""),
                StringUtils.defaultString(reservation.getMaterialColor(), ""));
    }

    /**
     * 款式属性物料匹配策略
     * 对于款式属性的物料，需要严格匹配：同PO、同颜色、同款式
     *
     * @param queryWrapper 查询条件构建器
     * @param detail 出库明细
     */
    private void applyStyleAttributeMatching(LambdaQueryWrapper<BoxItem> queryWrapper, WmsAuxiliaryDetail detail) {
        log.debug("应用款式属性匹配策略，物料编码: {}", detail.getMaterialCode());

        // 款式属性物料必须严格匹配PO号
        if (StringUtils.isNotBlank(detail.getReqListId())) {
            // 假设ReqListId对应PO号，需要根据实际字段映射调整
            queryWrapper.eq(BoxItem::getContractNoPo, detail.getReqListId());
            log.debug("款式属性物料添加PO号匹配条件: {}", detail.getReqListId());
        } else {
            // 如果没有PO号，则查询PO号为空的记录
            queryWrapper.and(poCond -> poCond.isNull(BoxItem::getContractNoPo).or().eq(BoxItem::getContractNoPo, ""));
        }

        // 严格匹配物料颜色
        if (StringUtils.isNotBlank(detail.getMaterialColor())) {
            queryWrapper.eq(BoxItem::getMaterialColor, detail.getMaterialColor());
        } else {
            queryWrapper.and(colorCond -> colorCond.isNull(BoxItem::getMaterialColor).or().eq(BoxItem::getMaterialColor, ""));
        }

        // 严格匹配物料型号
        if (StringUtils.isNotBlank(detail.getMaterialModel())) {
            queryWrapper.eq(BoxItem::getMaterialModel, detail.getMaterialModel());
        } else {
            queryWrapper.and(modelCond -> modelCond.isNull(BoxItem::getMaterialModel).or().eq(BoxItem::getMaterialModel, ""));
        }
    }

    /**
     * 自身属性物料匹配策略
     * 对于自身属性的物料，PO、颜色、款式这3个条件如果有值则必须满足，没有可以忽略
     *
     * @param queryWrapper 查询条件构建器
     * @param detail 出库明细
     */
    private void applySelfAttributeMatching(LambdaQueryWrapper<BoxItem> queryWrapper, WmsAuxiliaryDetail detail) {
        log.debug("应用自身属性匹配策略，物料编码: {}", detail.getMaterialCode());

        // 自身属性物料：PO号有值则匹配，无值则忽略
        if (StringUtils.isNotBlank(detail.getReqListId())) {
            queryWrapper.eq(BoxItem::getContractNoPo, detail.getReqListId());
            log.debug("自身属性物料添加PO号匹配条件: {}", detail.getReqListId());
        }
        // 注意：如果PO号为空，不添加任何PO号相关的查询条件，允许匹配任何PO号的库存

        // 颜色有值则匹配，无值则忽略
        if (StringUtils.isNotBlank(detail.getMaterialColor())) {
            queryWrapper.eq(BoxItem::getMaterialColor, detail.getMaterialColor());
        }

        // 型号有值则匹配，无值则忽略
        if (StringUtils.isNotBlank(detail.getMaterialModel())) {
            queryWrapper.eq(BoxItem::getMaterialModel, detail.getMaterialModel());
        }
    }

    /**
     * 默认匹配策略（未指定物料类型时使用）
     * 保持原有的匹配逻辑
     *
     * @param queryWrapper 查询条件构建器
     * @param detail 出库明细
     */
    private void applyDefaultMatching(LambdaQueryWrapper<BoxItem> queryWrapper, WmsAuxiliaryDetail detail) {
        log.debug("应用默认匹配策略，物料编码: {}", detail.getMaterialCode());

        // 处理物料型号
        if (StringUtils.isNotBlank(detail.getMaterialModel())) {
            queryWrapper.eq(BoxItem::getMaterialModel, detail.getMaterialModel());
        } else {
            queryWrapper.and(modelCond -> modelCond.isNull(BoxItem::getMaterialModel).or()
                    .eq(BoxItem::getMaterialModel, ""));
        }

        // 处理物料颜色
        if (StringUtils.isNotBlank(detail.getMaterialColor())) {
            queryWrapper.eq(BoxItem::getMaterialColor, detail.getMaterialColor());
        } else {
            queryWrapper.and(colorCond -> colorCond.isNull(BoxItem::getMaterialColor).or()
                    .eq(BoxItem::getMaterialColor, ""));
        }
    }

    /**
     * 创建入库任务 - 区分料箱和托盘的不同入库方式
     * 
     * @param boxNo    料箱号/托盘号
     * @param boxType  容器类型（1-料箱 2-托盘）
     * @param taskType 任务类型（7-指定入库）
     */
    private void createInboundTask(String boxNo, Integer boxType, Integer taskType) {
        try {
            if (StringUtils.isBlank(boxNo)) {
                log.warn("容器号为空，无法创建入库任务");
                return;
            }

            // 创建入库任务
            WmsBoxTaskList task = new WmsBoxTaskList();
            task.setTaskStatus(0); // 待处理状态
            task.setBoxNo(boxNo);
            task.setBoxType(boxType != null ? boxType : 1); // 默认料箱
            task.setTaskType(taskType != null ? taskType : 7); // 默认指定入库

            // 使用WmsBoxTaskListService创建任务
            WmsBoxTaskList createdTask = wmsBoxTaskListService.createTask(task);
            if (createdTask != null) {
                log.info("成功创建入库任务: 容器={}, 容器类型={}, 任务类型={}, 任务单号={}",
                        boxNo, boxType == 1 ? "料箱" : "托盘", taskType, createdTask.getTaskOrder());

                // 根据容器类型采用不同的入库处理策略
                if (boxType != null && boxType == 2) {
                    // 托盘入库：实时下发处理
                    log.info("托盘入库任务已创建，将由AGV服务实时处理: {}", boxNo);
                } else {
                    // 料箱入库：批量预调度处理
                    log.info("料箱入库任务已创建，将由AGV服务按批次处理: {}", boxNo);
                }
            } else {
                log.error("创建入库任务失败: 容器={}, 任务类型={}", boxNo, taskType);
            }

        } catch (Exception e) {
            log.error("创建入库任务异常: 容器={}, 错误信息={}", boxNo, e.getMessage(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    @Override
    public QueryAuxiliaryOutTaskDto queryOutTask(QueryModel queryModel) {
        QueryAuxiliaryOutTaskDto resultDto = new QueryAuxiliaryOutTaskDto();

        try {
            log.info("开始查询辅料出库任务信息");
            
            // 设置查询参数（包括从Redis获取料箱信息）
            setupOutTaskQueryParameters(queryModel);
            
            // 添加详细的诊断日志
            if (queryModel != null && queryModel.getSearchParams() != null) {
                String containerNo = (String) queryModel.getSearchParams().get("containerNo");
                if (StringUtils.isNotEmpty(containerNo)) {
                    log.info("Redis指定的料箱编号: {}", containerNo);
                } else {
                    log.info("未从Redis获取到指定的料箱编号");
                }
            }

            // 查询所有预占用记录并构建任务状态统计
            List<WmsAuxiliaryOutPreBox> allPreBoxList = queryAllOutPreBoxes(queryModel);
            QueryAuxiliaryOutTaskDto.TaskStatusSummary summary = buildOutTaskStatusSummary(allPreBoxList);
            resultDto.setTaskStatusSummary(summary);

            // 查询输送线上的数据（通过任务状态过滤）
            queryModel.getSearchParams().put("filterByTaskStatus", "true");
            List<WmsAuxiliaryOutPreBox> filteredPreBoxList = queryConveyorOutPreBoxes(queryModel, allPreBoxList);

            // 设置状态信息
            setOutTaskStatusInfo(resultDto, summary);

            // 构建料箱属性数据
            if (!CollectionUtils.isEmpty(filteredPreBoxList)) {
                buildOutTaskBoxPropertyDataFromPreBoxes(resultDto, filteredPreBoxList);
            }

        } catch (Exception e) {
            log.error("查询出库任务信息失败: {}", e.getMessage(), e);
            resultDto.setStatusMessage("查询失败: " + e.getMessage());
            resultDto.setRefreshInterval(30);
        }

        return resultDto;
    }

    /**
     * 设置出库任务查询参数
     */
    private void setupOutTaskQueryParameters(QueryModel queryModel) {
        if (queryModel == null) {
            queryModel = new QueryModel();
        }
        if (queryModel.getSearchParams() == null) {
            queryModel.setSearchParams(new HashMap<>());
        }

        String REDIS_LIFT_BOX_CODE_KEY = "lift:box_code";
        String boxNo = stringRedisTemplate.opsForValue().get(REDIS_LIFT_BOX_CODE_KEY);
        if (StringUtils.isNotBlank(boxNo)) {
            queryModel.getSearchParams().put("containerNo", boxNo);
        }
    }

    /**
     * 查询所有预占用记录
     */
    private List<WmsAuxiliaryOutPreBox> queryAllOutPreBoxes(QueryModel queryModel) {
        LambdaQueryWrapper<WmsAuxiliaryOutPreBox> queryWrapper = new LambdaQueryWrapper<WmsAuxiliaryOutPreBox>()
                .eq(WmsAuxiliaryOutPreBox::getReservationStatus, WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED)
                .orderByDesc(WmsAuxiliaryOutPreBox::getCreateTime);

        // 如果指定了容器号，添加过滤条件
        String containerNo = (String) queryModel.getSearchParams().get("containerNo");
        if (StringUtils.isNotBlank(containerNo)) {
            queryWrapper.eq(WmsAuxiliaryOutPreBox::getBoxCode, containerNo);
            log.info("查询指定料箱 {} 的预占用记录", containerNo);
        } else {
            log.info("查询所有预占用记录（未指定料箱）");
        }

        // 如果指定了任务号，需要通过任务号查找对应的料箱号，然后查询预占用记录
        String taskOrder = (String) queryModel.getSearchParams().get("taskOrder");
        if (StringUtils.isNotBlank(taskOrder)) {
            // 通过任务号查找对应的料箱号
            List<WmsBoxTaskList> taskList = wmsBoxTaskListService.list(
                    new LambdaQueryWrapper<WmsBoxTaskList>()
                            .eq(WmsBoxTaskList::getTaskOrder, taskOrder)
                            .eq(WmsBoxTaskList::getDeleteFlag, 0));
            
            if (!taskList.isEmpty()) {
                Set<String> taskBoxNos = taskList.stream()
                        .map(WmsBoxTaskList::getBoxNo)
                        .collect(Collectors.toSet());
                queryWrapper.in(WmsAuxiliaryOutPreBox::getBoxCode, taskBoxNos);
                log.info("查询任务号 {} 对应的料箱数量: {}", taskOrder, taskBoxNos.size());
            } else {
                log.warn("未找到任务号 {} 对应的任务记录", taskOrder);
                return new ArrayList<>();
            }
        }

        List<WmsAuxiliaryOutPreBox> result = preBoxService.list(queryWrapper);
        log.info("查询到预占用记录数量: {}", result.size());
        
        if (!result.isEmpty()) {
            // 记录查找到的记录概要
            Map<String, Long> containerCounts = result.stream()
                    .collect(Collectors.groupingBy(WmsAuxiliaryOutPreBox::getBoxCode, Collectors.counting()));
            log.info("预占用记录按料箱分布: {}", containerCounts);
        }

        return result;
    }

    /**
     * 查询输送线上的预占用记录
     */
    private List<WmsAuxiliaryOutPreBox> queryConveyorOutPreBoxes(QueryModel queryModel, List<WmsAuxiliaryOutPreBox> allPreBoxList) {
        if (CollectionUtils.isEmpty(allPreBoxList)) {
            return new ArrayList<>();
        }

        // 查询任务状态信息
        Set<String> containerNos = allPreBoxList.stream()
                .map(WmsAuxiliaryOutPreBox::getBoxCode)
                .collect(Collectors.toSet());

        final Map<String, WmsBoxTaskList> taskMap;
        if (!containerNos.isEmpty()) {
            List<WmsBoxTaskList> taskList = wmsBoxTaskListService.list(
                    new LambdaQueryWrapper<WmsBoxTaskList>()
                            .in(WmsBoxTaskList::getBoxNo, containerNos)
                            .eq(WmsBoxTaskList::getDeleteFlag, 0)
                            .orderByDesc(WmsBoxTaskList::getCreateTime));

            taskMap = taskList.stream()
                    .collect(Collectors.toMap(
                            WmsBoxTaskList::getBoxNo,
                            task -> task,
                            (existing, replacement) -> existing));
        } else {
            taskMap = new HashMap<>();
        }

        // 过滤出库任务完成的数据（任务状态为4和5的料箱表示任务完成或其他状态）
        return allPreBoxList.stream()
                .filter(preBox -> {
                    WmsBoxTaskList task = taskMap.get(preBox.getBoxCode());
                    return task != null && (task.getTaskStatus() == 4 || task.getTaskStatus() == 5);
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建任务状态统计信息
     */
    private QueryAuxiliaryOutTaskDto.TaskStatusSummary buildOutTaskStatusSummary(List<WmsAuxiliaryOutPreBox> preBoxList) {
        QueryAuxiliaryOutTaskDto.TaskStatusSummary summary = new QueryAuxiliaryOutTaskDto.TaskStatusSummary();

        if (CollectionUtils.isEmpty(preBoxList)) {
            summary.setTotalBoxes(0);
            summary.setOnConveyorBoxes(0);
            summary.setWaitingBoxes(0);
            summary.setCompletedBoxes(0);
            summary.setTotalPendingQuantity(0);
            return summary;
        }

        try {
            // 查询任务状态信息
            Set<String> containerNos = preBoxList.stream()
                    .map(WmsAuxiliaryOutPreBox::getBoxCode)
                    .collect(Collectors.toSet());

            Map<String, Integer> containerTaskStatus = new HashMap<>();
            if (!containerNos.isEmpty()) {
                List<WmsBoxTaskList> taskList = wmsBoxTaskListService.list(
                        new LambdaQueryWrapper<WmsBoxTaskList>()
                                .in(WmsBoxTaskList::getBoxNo, containerNos)
                                .eq(WmsBoxTaskList::getDeleteFlag, 0)
                                .orderByDesc(WmsBoxTaskList::getCreateTime));

                containerTaskStatus = taskList.stream()
                        .collect(Collectors.toMap(
                                WmsBoxTaskList::getBoxNo,
                                task -> task.getTaskStatus() != null ? task.getTaskStatus() : -1,
                                (existing, replacement) -> existing));
            }

            // 按容器分组统计
            Map<String, List<WmsAuxiliaryOutPreBox>> containerGroups = preBoxList.stream()
                    .collect(Collectors.groupingBy(WmsAuxiliaryOutPreBox::getBoxCode));

            summary.setTotalBoxes(containerGroups.size());
            summary.setOnConveyorBoxes((int) containerTaskStatus.values().stream()
                    .filter(status -> status != null && (status == 4 || status == 5)).count());
            summary.setWaitingBoxes((int) containerTaskStatus.values().stream()
                    .filter(status -> status != null && status >= 0 && status < 4).count());
            summary.setCompletedBoxes((int) containerTaskStatus.values().stream()
                    .filter(status -> status != null && status == 6).count());

            // 计算总的待出库数量
            Integer totalPendingQuantity = calculateTotalPendingQuantityFromOutPreBoxes(preBoxList);
            summary.setTotalPendingQuantity(totalPendingQuantity);

        } catch (Exception e) {
            log.error("构建出库任务状态统计信息时发生错误: {}", e.getMessage(), e);
            summary.setTotalBoxes(0);
            summary.setOnConveyorBoxes(0);
            summary.setWaitingBoxes(0);
            summary.setCompletedBoxes(0);
            summary.setTotalPendingQuantity(0);
        }

        return summary;
    }

    /**
     * 从出库预占用记录计算待出库总数量
     */
    private Integer calculateTotalPendingQuantityFromOutPreBoxes(List<WmsAuxiliaryOutPreBox> preBoxList) {
        try {
            // 收集所有出库单号
            Set<String> outboundNumbers = preBoxList.stream()
                    .filter(preBox -> StringUtils.isNotEmpty(preBox.getOutboundNo()))
                    .map(WmsAuxiliaryOutPreBox::getOutboundNo)
                    .collect(Collectors.toSet());

            if (outboundNumbers.isEmpty()) {
                log.debug("未找到有效的出库单号，返回待出库数量为0");
                return 0;
            }

            // 查询出库单明细表，统计待出库总数量
            Integer totalQuantity = 0;
            for (String outboundNumber : outboundNumbers) {
                List<WmsAuxiliaryDetail> details = auxiliaryDetailService.getByRefNumber(outboundNumber);
                Integer outboundQuantity = details.stream()
                        .filter(detail -> detail.getOperationType() != null && detail.getOperationType() == 1) // 只统计出库类型
                        .filter(detail -> detail.getQuantity() != null)
                        .mapToInt(WmsAuxiliaryDetail::getQuantity)
                        .sum();
                totalQuantity += outboundQuantity;
            }

            log.debug("从出库单明细表统计待出库总数量: 出库单数量={}, 总数量={}", outboundNumbers.size(), totalQuantity);
            return totalQuantity;

        } catch (Exception e) {
            log.error("计算待出库总数量时发生错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 设置出库任务状态信息
     */
    private void setOutTaskStatusInfo(QueryAuxiliaryOutTaskDto resultDto, QueryAuxiliaryOutTaskDto.TaskStatusSummary summary) {
        // 空指针检查
        if (resultDto == null) {
            log.warn("setOutTaskStatusInfo: resultDto为null，跳过设置状态信息");
            return;
        }

        if (summary == null) {
            log.warn("setOutTaskStatusInfo: summary为null，使用默认状态信息");
            resultDto.setHasDataOnConveyor(false);
            resultDto.setStatusMessage("暂无料箱可供出库");
            resultDto.setRefreshInterval(30);
            return;
        }

        // 安全地获取统计数据，避免空指针
        Integer onConveyorBoxes = summary.getOnConveyorBoxes();
        Integer waitingBoxes = summary.getWaitingBoxes();

        // 如果属性为null，设置默认值
        if (onConveyorBoxes == null) {
            onConveyorBoxes = 0;
            log.warn("setOutTaskStatusInfo: onConveyorBoxes为null，设置为0");
        }
        if (waitingBoxes == null) {
            waitingBoxes = 0;
            log.warn("setOutTaskStatusInfo: waitingBoxes为null，设置为0");
        }

        resultDto.setHasDataOnConveyor(onConveyorBoxes > 0);

        if (onConveyorBoxes > 0) {
            resultDto.setStatusMessage("发现 " + onConveyorBoxes + " 个料箱已到达出库点");
            resultDto.setRefreshInterval(5);
        } else if (waitingBoxes > 0) {
            resultDto.setStatusMessage("有 " + waitingBoxes + " 个料箱正在准备中，请稍后刷新");
            resultDto.setRefreshInterval(10);
        } else {
            resultDto.setStatusMessage("暂无料箱可供出库");
            resultDto.setRefreshInterval(30);
        }
    }

    /**
     * 构建出库任务料箱属性数据（不依赖taskMap）
     */
    private void buildOutTaskBoxPropertyDataFromPreBoxes(QueryAuxiliaryOutTaskDto resultDto, List<WmsAuxiliaryOutPreBox> filteredPreBoxList) {
        if (CollectionUtils.isEmpty(filteredPreBoxList)) {
            resultDto.setBoxProperty(new ArrayList<>());
            return;
        }

        // 查询任务状态信息
        Set<String> containerNos = filteredPreBoxList.stream()
                .map(WmsAuxiliaryOutPreBox::getBoxCode)
                .collect(Collectors.toSet());

        final Map<String, WmsBoxTaskList> taskMap;
        if (!containerNos.isEmpty()) {
            List<WmsBoxTaskList> taskList = wmsBoxTaskListService.list(
                    new LambdaQueryWrapper<WmsBoxTaskList>()
                            .in(WmsBoxTaskList::getBoxNo, containerNos)
                            .eq(WmsBoxTaskList::getDeleteFlag, 0)
                            .orderByDesc(WmsBoxTaskList::getCreateTime));

            taskMap = taskList.stream()
                    .collect(Collectors.toMap(
                            WmsBoxTaskList::getBoxNo,
                            task -> task,
                            (existing, replacement) -> existing));
        } else {
            taskMap = new HashMap<>();
        }

        // 构建料箱属性数据
        buildOutTaskBoxPropertyData(resultDto, filteredPreBoxList, taskMap);
    }

    /**
     * 根据任务状态过滤预占用记录
     */
    private List<WmsAuxiliaryOutPreBox> filterByTaskStatus(List<WmsAuxiliaryOutPreBox> preBoxList, 
                                                          Map<String, WmsBoxTaskList> taskMap, 
                                                          QueryModel queryModel) {
        if (queryModel == null || queryModel.getSearchParams() == null) {
            return preBoxList;
        }

        String filterByTaskStatus = (String) queryModel.getSearchParams().get("filterByTaskStatus");
        String includeTaskStatus = (String) queryModel.getSearchParams().get("includeTaskStatus");
        String containerNo = (String) queryModel.getSearchParams().get("containerNo");

        List<WmsAuxiliaryOutPreBox> filteredList = preBoxList;

        // 按料箱号过滤
        if (StringUtils.isNotBlank(containerNo)) {
            filteredList = filteredList.stream()
                    .filter(prebox -> containerNo.equals(prebox.getBoxCode()))
                    .collect(Collectors.toList());
        }

        // 按任务状态过滤
        if ("true".equals(filterByTaskStatus)) {
            // 只返回任务状态为4,5（任务完成/其他状态）的料箱
            filteredList = filteredList.stream()
                    .filter(prebox -> {
                        WmsBoxTaskList task = taskMap.get(prebox.getBoxCode());
                        return task != null && (task.getTaskStatus() == 4 || task.getTaskStatus() == 5);
                    })
                    .collect(Collectors.toList());
        } else if (StringUtils.isNotBlank(includeTaskStatus)) {
            // 根据指定的任务状态进行过滤
            String[] statusArray = includeTaskStatus.split(",");
            Set<Integer> statusSet = Arrays.stream(statusArray)
                    .map(Integer::parseInt)
                    .collect(Collectors.toSet());
            
            filteredList = filteredList.stream()
                    .filter(prebox -> {
                        WmsBoxTaskList task = taskMap.get(prebox.getBoxCode());
                        return task != null && statusSet.contains(task.getTaskStatus());
                    })
                    .collect(Collectors.toList());
        }

        return filteredList;
    }

    /**
     * 构建出库任务状态统计信息
     */
    private void buildOutTaskStatusSummary(QueryAuxiliaryOutTaskDto resultDto, 
                                          List<WmsAuxiliaryOutPreBox> preBoxList, 
                                          Map<String, WmsBoxTaskList> taskMap) {
        QueryAuxiliaryOutTaskDto.TaskStatusSummary summary = new QueryAuxiliaryOutTaskDto.TaskStatusSummary();

        if (CollectionUtils.isEmpty(preBoxList)) {
            summary.setTotalBoxes(0);
            summary.setOnConveyorBoxes(0);
            summary.setWaitingBoxes(0);
            summary.setCompletedBoxes(0);
            summary.setTotalPendingQuantity(0);
        } else {
            // 按容器分组统计
            Map<String, List<WmsAuxiliaryOutPreBox>> containerGroups = preBoxList.stream()
                    .collect(Collectors.groupingBy(WmsAuxiliaryOutPreBox::getBoxCode));

            summary.setTotalBoxes(containerGroups.size());

            int onConveyorBoxes = 0;
            int waitingBoxes = 0; 
            int completedBoxes = 0;
            int totalPendingQuantity = 0;

            for (Map.Entry<String, List<WmsAuxiliaryOutPreBox>> entry : containerGroups.entrySet()) {
                String containerNo = entry.getKey();
                List<WmsAuxiliaryOutPreBox> containerPreBoxes = entry.getValue();

                WmsBoxTaskList task = taskMap.get(containerNo);
                if (task != null) {
                    if (task.getTaskStatus() == 4 || task.getTaskStatus() == 5) {
                        onConveyorBoxes++;
                    } else if (task.getTaskStatus() == 6) {
                        completedBoxes++;
                    } else {
                        waitingBoxes++;
                    }
                } else {
                    waitingBoxes++;
                }

                // 累计待出库数量
                totalPendingQuantity += containerPreBoxes.stream()
                        .mapToInt(prebox -> prebox.getReservedQuantity() != null ? 
                                prebox.getReservedQuantity().intValue() : 0)
                        .sum();
            }

            summary.setOnConveyorBoxes(onConveyorBoxes);
            summary.setWaitingBoxes(waitingBoxes);
            summary.setCompletedBoxes(completedBoxes);
            summary.setTotalPendingQuantity(totalPendingQuantity);
        }

        resultDto.setTaskStatusSummary(summary);
    }

    /**
     * 构建出库任务料箱属性数据
     */
    private void buildOutTaskBoxPropertyData(QueryAuxiliaryOutTaskDto resultDto, 
                                            List<WmsAuxiliaryOutPreBox> filteredPreBoxList, 
                                            Map<String, WmsBoxTaskList> taskMap) {
        List<BoxPropertyDto> boxPropertyDtosList = new ArrayList<>();

        Map<String, List<WmsAuxiliaryOutPreBox>> containerGroups = filteredPreBoxList.stream()
                .collect(Collectors.groupingBy(WmsAuxiliaryOutPreBox::getBoxCode));

        for (Map.Entry<String, List<WmsAuxiliaryOutPreBox>> entry : containerGroups.entrySet()) {
            String containerNo = entry.getKey();
            List<WmsAuxiliaryOutPreBox> containerPreBoxes = entry.getValue();

            BoxPropertyDto boxPropertyDto = buildOutTaskBoxPropertyDto(containerNo, containerPreBoxes, taskMap);
            boxPropertyDtosList.add(boxPropertyDto);
        }

        resultDto.setBoxProperty(boxPropertyDtosList);
        log.info("成功查询到 {} 个出库任务容器信息", boxPropertyDtosList.size());
    }

    /**
     * 构建单个出库任务料箱属性DTO
     */
    private BoxPropertyDto buildOutTaskBoxPropertyDto(String containerNo, 
                                                     List<WmsAuxiliaryOutPreBox> containerPreBoxes,
                                                     Map<String, WmsBoxTaskList> taskMap) {
        BoxPropertyDto boxPropertyDto = new BoxPropertyDto();
        boxPropertyDto.setBoxNo(containerNo);

        WmsAuxiliaryOutPreBox firstPreBox = containerPreBoxes.get(0);
        boxPropertyDto.setContainerType(firstPreBox.getContainerType());
        boxPropertyDto.setBoxType(firstPreBox.getContainerType());
          // 设置出库单ID
          boxPropertyDto.setBoundId(firstPreBox.getOutboundId());

        // 设置任务状态信息
        WmsBoxTaskList task = taskMap.get(containerNo);
        if (task != null) {
            boxPropertyDto.setTaskStatus(task.getTaskStatus());
            boxPropertyDto.setTaskOrder(task.getTaskOrder());
        } else {
            boxPropertyDto.setTaskStatus(0); // 未创建任务
            boxPropertyDto.setTaskOrder(""); 
        }

        List<BoxItemDto> boxItemDtoList = containerPreBoxes.stream()
                .map(this::convertOutTaskToBoxItem)
                .collect(Collectors.toList());
        boxPropertyDto.setBoxItemList(boxItemDtoList);
        
        // 为了兼容性，同时设置boxItem字段（与boxItemList相同）
        boxPropertyDto.setBoxItem(boxItemDtoList);

        return boxPropertyDto;
    }

    /**
     * 将出库预占用记录转换为BoxItemDto
     */
    private BoxItemDto convertOutTaskToBoxItem(WmsAuxiliaryOutPreBox preBox) {
        BoxItemDto boxItemDto = new BoxItemDto();
        
        // 设置主键ID - 对应BoxItem的主键，前端解绑时需要用到
        boxItemDto.setId(preBox.getBoxItemId());
        
        // 基本信息从预占用记录获取
        boxItemDto.setContractNo(preBox.getContractNo());
        boxItemDto.setItemNo(preBox.getStyleNo());
        boxItemDto.setMaterialCode(preBox.getMaterialCode());
        boxItemDto.setMaterialModel(preBox.getMaterialSpec());
        boxItemDto.setMaterialColor(preBox.getMaterialColor());
        boxItemDto.setContainerNo(preBox.getBoxCode());
        boxItemDto.setMaterialProperty(preBox.getMaterialProperty());
        
        // 对于出库任务，显示预占用数量作为待出库数量
        boxItemDto.setInQuantity(preBox.getReservedQuantity() != null ? 
                preBox.getReservedQuantity().intValue() : 0);
        boxItemDto.setActualInboundQty(0); // 出库任务不涉及实际入库数量
        
        // 设置当前物料的实际库存数据
        // 优先使用预占用记录中保存的原始库存数量，这是预占用时保存的准确原始库存
        if (preBox.getOriginalStockQuantity() != null) {
            // 首选：使用预占用时保存的原始库存数量（这是最准确的实际库存）
            int originalStockQuantity = preBox.getOriginalStockQuantity().intValue();
            boxItemDto.setStockQuantity(originalStockQuantity);

            log.debug("使用预占用记录中的原始库存数量: 物料编码={}, 原始库存={}",
                    preBox.getMaterialCode(), originalStockQuantity);
        } else {
            // 备选：如果预占用记录中没有原始库存数量，则查询当前BoxItem
            BoxItem currentBoxItem = boxItemService.getById(preBox.getBoxItemId());
            if (currentBoxItem != null) {
                // 当前库存数量（可能已经扣减了预占用数量）
                int currentStockQuantity = currentBoxItem.getMaterialQuantity() != null ?
                        currentBoxItem.getMaterialQuantity() : 0;
                int reservedQuantity = preBox.getReservedQuantity() != null ?
                        preBox.getReservedQuantity().intValue() : 0;

                // 恢复原始库存 = 当前库存 + 预占用数量（假设预占用时已扣减库存）
                int originalStockQuantity = currentStockQuantity + reservedQuantity;
                boxItemDto.setStockQuantity(originalStockQuantity);

                log.debug("通过当前库存计算原始库存: 物料编码={}, 当前库存={}, 预占用数量={}, 原始库存={}",
                        preBox.getMaterialCode(), currentStockQuantity, reservedQuantity, originalStockQuantity);
            } else {
                // 最后备选方案：使用预占用数量作为库存
                int reservedQuantity = preBox.getReservedQuantity() != null ?
                        preBox.getReservedQuantity().intValue() : 0;
                boxItemDto.setStockQuantity(reservedQuantity);

                log.warn("无法获取原始库存数量，使用预占用数量作为库存: 物料编码={}, 预占用数量={}",
                        preBox.getMaterialCode(), reservedQuantity);
            }
        }
        
        // 补充完善信息：从BoxItem表中查询补全格号、物料名称、PO号等信息
        try {
            // 查询对应的库存项获取完整信息
            BoxItem boxItem = boxItemService.getOne(new LambdaQueryWrapper<BoxItem>()
                    .eq(BoxItem::getBoxNo, preBox.getBoxCode())
                    .eq(BoxItem::getMaterialCode, preBox.getMaterialCode())
                    .eq(BoxItem::getContractNo, preBox.getContractNo())
                    .eq(BoxItem::getItemNo, preBox.getStyleNo())
                    .eq(StringUtils.isNotBlank(preBox.getMaterialColor()), BoxItem::getMaterialColor, preBox.getMaterialColor())
                    .eq(StringUtils.isNotBlank(preBox.getMaterialSpec()), BoxItem::getMaterialModel, preBox.getMaterialSpec())
                    .last("LIMIT 1"));
            
            if (boxItem != null) {
                // 设置格号
                if (boxItem.getGridId() != null) {
                    boxItemDto.setGridId(String.valueOf(boxItem.getGridId()));
                } else {
                    boxItemDto.setGridId(preBox.getGridCode()); // 回退到预占用记录中的格号
                }
                
                // 设置物料名称
                if (StringUtils.isNotBlank(boxItem.getMaterialName())) {
                    boxItemDto.setMaterialName(boxItem.getMaterialName());
                } else {
                    boxItemDto.setMaterialName(preBox.getMaterialName()); // 回退到预占用记录中的物料名称
                }
                
                // 设置PO号
                if (StringUtils.isNotBlank(boxItem.getContractNoPo())) {
                    boxItemDto.setPoNo(boxItem.getContractNoPo());
                } else {
                    boxItemDto.setPoNo(""); // 出库任务可能没有PO号
                }
                
                log.debug("成功补充出库任务物料信息: 物料编码={}, 格号={}, 物料名称={}, PO号={}",
                        preBox.getMaterialCode(), boxItemDto.getGridId(), 
                        boxItemDto.getMaterialName(), boxItemDto.getPoNo());
            } else {
                // 如果没有找到对应的库存项，使用预占用记录中的信息
                boxItemDto.setGridId(preBox.getGridCode());
                boxItemDto.setMaterialName(preBox.getMaterialName());
                boxItemDto.setPoNo("");
                
                log.warn("未找到出库任务对应的库存项，使用预占用记录信息: 容器={}, 物料编码={}", 
                        preBox.getBoxCode(), preBox.getMaterialCode());
            }
        } catch (Exception e) {
            // 异常情况下使用预占用记录中的信息
            boxItemDto.setGridId(preBox.getGridCode());
            boxItemDto.setMaterialName(preBox.getMaterialName());
            boxItemDto.setPoNo("");
            
            log.error("查询出库任务库存项信息时发生异常: 容器={}, 物料编码={}, 错误={}",
                    preBox.getBoxCode(), preBox.getMaterialCode(), e.getMessage());
        }

        return boxItemDto;
    }

    /**
     * 创建空的出库任务查询结果
     */
    private QueryAuxiliaryOutTaskDto createEmptyOutTaskResult(String message) {
        QueryAuxiliaryOutTaskDto resultDto = new QueryAuxiliaryOutTaskDto();
        resultDto.setBoxProperty(new ArrayList<>());
        resultDto.setStatusMessage(message);
        resultDto.setHasDataOnConveyor(false);
        resultDto.setRefreshInterval(10);
        
        QueryAuxiliaryOutTaskDto.TaskStatusSummary summary = new QueryAuxiliaryOutTaskDto.TaskStatusSummary();
        summary.setTotalBoxes(0);
        summary.setOnConveyorBoxes(0);
        summary.setWaitingBoxes(0);
        summary.setCompletedBoxes(0);
        summary.setTotalPendingQuantity(0);
        resultDto.setTaskStatusSummary(summary);
        
        return resultDto;
    }

    /**
     * 构建已存在的出库任务结果
     * 当检测到出库单已经创建了任务时，返回现有任务信息而不重复创建
     */
    private AuxiliaryOutDetailsDto buildExistingOutTaskResult(List<WmsAuxiliaryOutPreBox> existingReservations) {
        log.info("构建已存在的出库任务结果，预占用记录数量: {}", existingReservations.size());
        
        AuxiliaryOutDetailsDto resultDto = new AuxiliaryOutDetailsDto();
        List<BoxOutItemDto> boxOutItems = new ArrayList<>();
        List<BoxOutItemDto> rackOutItems = new ArrayList<>();
        
        // 获取所有相关的料箱编号，查询对应的任务信息
        Set<String> boxCodes = existingReservations.stream()
                .map(WmsAuxiliaryOutPreBox::getBoxCode)
                .collect(Collectors.toSet());
        
        Map<String, WmsBoxTaskList> taskMap = new HashMap<>();
        if (!boxCodes.isEmpty()) {
            List<WmsBoxTaskList> tasks = wmsBoxTaskListService.list(new LambdaQueryWrapper<WmsBoxTaskList>()
                    .in(WmsBoxTaskList::getBoxNo, boxCodes)
                    .orderByDesc(WmsBoxTaskList::getCreateTime));
            
            taskMap = tasks.stream()
                    .collect(Collectors.toMap(
                            WmsBoxTaskList::getBoxNo,
                            task -> task,
                            (existing, replacement) -> existing)); // 保留第一个（最新的）
        }
        
        // 按容器类型和容器号分组
        Map<String, List<WmsAuxiliaryOutPreBox>> containerGroups = existingReservations.stream()
                .collect(Collectors.groupingBy(
                        prebox -> prebox.getContainerType() + "_" + prebox.getBoxCode()));
        
        for (Map.Entry<String, List<WmsAuxiliaryOutPreBox>> entry : containerGroups.entrySet()) {
            String groupKey = entry.getKey();
            List<WmsAuxiliaryOutPreBox> containerReservations = entry.getValue();
            
            if (containerReservations.isEmpty()) continue;
            
            WmsAuxiliaryOutPreBox firstReservation = containerReservations.get(0);
            Integer containerType = firstReservation.getContainerType();
            String boxCode = firstReservation.getBoxCode();
            
            // 创建出库项DTO
            BoxOutItemDto outItemDto = new BoxOutItemDto();
            outItemDto.setBoxNo(boxCode);
            outItemDto.setBoxType(containerType);
            outItemDto.setTaskType(firstReservation.getOutboundType());
            
            // 设置任务号
            WmsBoxTaskList task = taskMap.get(boxCode);
            if (task != null && task.getTaskOrder() != null) {
                outItemDto.setTaskOrder(task.getTaskOrder());
                log.debug("为容器 {} 设置任务号: {}", boxCode, task.getTaskOrder());
            } else {
                log.warn("容器 {} 未找到对应的任务信息", boxCode);
            }
            
            // 转换为BoxItemDto列表
            List<BoxItemDto> boxItemDtoList = containerReservations.stream()
                    .map(this::convertOutTaskToBoxItem)
                    .collect(Collectors.toList());
            
            outItemDto.setBoxItemList(boxItemDtoList);
            
            // 计算总出库数量
            int totalOutQuantity = containerReservations.stream()
                    .mapToInt(prebox -> prebox.getReservedQuantity() != null ? 
                            prebox.getReservedQuantity().intValue() : 0)
                    .sum();
            outItemDto.setOutQuantityList(totalOutQuantity);
            
            // 根据容器类型分类
            if (containerType != null && containerType == 1) {
                boxOutItems.add(outItemDto); // 料箱
            } else if (containerType != null && containerType == 2) {
                rackOutItems.add(outItemDto); // 托盘
            }
        }
        
        resultDto.setBoxOutItemDtoList(boxOutItems);
        resultDto.setRackOutItemDtoList(rackOutItems);
        
        log.info("已存在出库任务构建完成，料箱任务: {}, 托盘任务: {}, 总任务数: {}", 
                boxOutItems.size(), rackOutItems.size(), 
                boxOutItems.size() + rackOutItems.size());
        return resultDto;
    }

}