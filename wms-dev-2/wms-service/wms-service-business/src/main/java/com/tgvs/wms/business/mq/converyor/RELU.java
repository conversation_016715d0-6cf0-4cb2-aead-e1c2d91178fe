package com.tgvs.wms.business.mq.converyor;

import com.tgvs.wms.business.mq.base.HEAD;
import com.tgvs.wms.business.mq.base.TelegramTools;

import lombok.Data;

@Data
public class RELU {
    private HEAD Header;

    private String Location;

    public void setHeader(HEAD Header) {
        this.Header = Header;
    }

    public void setLocation(String Location) {
        this.Location = Location;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof RELU))
            return false;
        RELU other = (RELU)o;
        if (!other.canEqual(this))
            return false;
        Object this$Header = getHeader(), other$Header = other.getHeader();
        if ((this$Header == null) ? (other$Header != null) : !this$Header.equals(other$Header))
            return false;
        Object this$Location = getLocation(), other$Location = other.getLocation();
        return !((this$Location == null) ? (other$Location != null) : !this$Location.equals(other$Location));
    }

    protected boolean canEqual(Object other) {
        return other instanceof RELU;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $Header = getHeader();
        result = result * 59 + (($Header == null) ? 43 : $Header.hashCode());
        Object $Location = getLocation();
        return result * 59 + (($Location == null) ? 43 : $Location.hashCode());
    }

    public RELU() {
        HEAD header = new HEAD("RELU");
        header.setMqindex(TelegramTools.getMqNumber());
        setHeader(header);
    }

    public HEAD getHeader() {
        return this.Header;
    }

    public String getLocation() {
        return this.Location;
    }

    public void setMQ(String str) throws Exception {
        if (null != str && str.length() > 0) {
            String[] mqdata = str.split(";", -1);
            if (null != mqdata && mqdata.length == 5) {
                this.Header.setMQ(str);
                setLocation(mqdata[4]);
            } else {
                throw new Exception("报文格式不正确1！");
            }
        } else {
            throw new Exception("报文格式不正确2！");
        }
    }

    public String toString() {
        String str = "";
        str = this.Header.toString();
        str = str + ";";
        str = str + getLocation();
        return str;
    }
}
