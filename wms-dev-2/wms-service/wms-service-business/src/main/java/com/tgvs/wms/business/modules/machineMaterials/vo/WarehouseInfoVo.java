package com.tgvs.wms.business.modules.machineMaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

//仓库信息同步Vo
@Data
@Schema(description = "仓库信息同步Vo")
public class WarehouseInfoVo{

    //仓库编码，唯一值
    @NotBlank(message = "仓库编码不能为空")
    @Schema(description = "仓库编码")
    private String wareHouseCode;
    //仓库名称
    @NotBlank(message = "仓库名称不能为空")
    @Schema(description = "仓库名称")
    private String wareHouseName;
    //1.新增；2.修改
    @Schema(description = "类型")
    private Integer type;
}
