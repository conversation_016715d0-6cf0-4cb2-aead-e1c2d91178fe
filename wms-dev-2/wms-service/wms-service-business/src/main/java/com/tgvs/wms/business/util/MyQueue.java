package com.tgvs.wms.business.util;


import java.util.LinkedList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class MyQueue {
    private LinkedList<Object> list = new LinkedList();

    private AtomicInteger count = new AtomicInteger();

    private final int minSize = 0;

    private final int maxSize;

    private Object lock;

    public MyQueue(int maxSize) {
        this.lock = new Object();
        this.maxSize = maxSize;
    }

    public void put(Object obj) {
        synchronized (this.lock) {
            while (this.count.get() == this.maxSize) {
                try {
                    this.lock.wait();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            this.list.add(obj);
            this.count.incrementAndGet();
            System.out.println("放入元素:" + obj);
            this.lock.notify();
        }
    }

    public Object take() {
        Object result = null;
        synchronized (this.lock) {
            getClass();
            while (this.count.get() == 0) {
                try {
                    this.lock.wait();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            result = this.list.removeFirst();
            this.count.decrementAndGet();
            System.out.println("拿走元素:" + result);
            this.lock.notify();
        }
        return result;
    }

    public int getSize() {
        return this.count.get();
    }

    public static void main(String[] args) {
        MyQueue queue = new MyQueue(5);
        queue.put("1");
        queue.put("2");
        queue.put("3");
        queue.put("4");
        queue.put("5");
        System.out.println("当前容器长度为:" + queue.getSize());
        Thread t1 = new Thread(() -> {
            queue.put("6");
            queue.put("7");
        });
        Thread t2 = new Thread(() -> {
            Object take1 = queue.take();
            Object take2 = queue.take();
        });
        t1.start();
        try {
            TimeUnit.SECONDS.sleep(2L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        t2.start();
    }
}
