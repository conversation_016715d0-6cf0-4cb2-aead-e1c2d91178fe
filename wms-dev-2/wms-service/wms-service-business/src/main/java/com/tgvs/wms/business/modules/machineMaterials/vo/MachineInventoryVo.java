package com.tgvs.wms.business.modules.machineMaterials.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 机物料盘点VO
 */
@Data
@Schema(description = "机物料盘点信息")
public class MachineInventoryVo implements Serializable {
//    private static final long serialVersionUID = 1L;
//
//    @Schema(description = "主键ID")
//    private String id;
    
    @NotBlank(message = "盘点单号不能为空")
    @Schema(description = "盘点单号", required = true)
    private String inStoreNumber;

    /**
     * 盘点类型：1.正常盘点；2.财务盘点；3：异常盘点
     */
//    @NotBlank(message = "盘点单类型不能为空")
    @Schema(description = "盘点单类型", required = true)
    private Integer inventoryType;

    @Schema(description = "盘点明细", required = true)
    List<MachineInventoryDetailsVo> machineInventoryDetailsList;

//    @Schema(description = "盘点数量")
//    private Integer inventoryQuantity;
//
//    @Schema(description = "盘点状态（0:未盘点,1:已盘点,2:盘点异常）", defaultValue = "0")
//    private Integer inventoryStatus = 0;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "盘点日期")
    private Date inventoryDate;
    
    @Schema(description = "盘点负责人")
    private String inventoryPerson;
    
    @Schema(description = "备注")
    private String remark;
} 