package com.tgvs.wms.business.mq.lift;

import java.util.Date;

import com.tgvs.wms.business.mq.base.HEAD;
import com.tgvs.wms.business.mq.base.TelegramState;
import com.tgvs.wms.business.mq.base.TelegramTools;

import lombok.Data;

@Data
public class LSCM {

    private String id;

    private TelegramState MQState;

    private HEAD Header;

    private Date creatTime;

    private String orderID;

    private String action;

    private String equId;

    private String lcid;

    private String from;

    private String to;


    public LSCM() {
        HEAD header = new HEAD("LSCM");
        header.setMqindex(TelegramTools.getMqNumber());
        setHeader(header);
        TelegramState mqs = new TelegramState();
        setMQState(mqs);
    }

    public LSCM(String Sender, String Recive) {
        HEAD header = new HEAD("LSCM");
        header.setSender(Sender);
        header.setReciver(Recive);
        header.setMqindex(TelegramTools.getMqNumber());
        setHeader(header);
        TelegramState mqs = new TelegramState();
        setMQState(mqs);
    }

}
