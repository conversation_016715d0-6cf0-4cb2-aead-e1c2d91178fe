package com.tgvs.wms.business.httpservice.baseBean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class BillBean {
    @J<PERSON>NField(name = "BillNo")
    private String BillNo;

    @JSONField(name = "SiteNo")
    private String SiteNo;

    @J<PERSON><PERSON>ield(name = "LatheNo")
    private String LatheNo;

    @J<PERSON>NField(name = "Completed")
    private Integer Completed;

    @JSONField(name = "Priority")
    private Integer Priority;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setBillNo(String BillNo) {
        this.BillNo = BillNo;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setLatheNo(String LatheNo) {
        this.LatheNo = LatheNo;
    }

    public void setCompleted(Integer Completed) {
        this.Completed = Completed;
    }

    public void setPriority(Integer Priority) {
        this.Priority = Priority;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BillBean))
            return false;
        BillBean other = (BillBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$BillNo = getBillNo(), other$BillNo = other.getBillNo();
        if ((this$BillNo == null) ? (other$BillNo != null) : !this$BillNo.equals(other$BillNo))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$LatheNo = getLatheNo(), other$LatheNo = other.getLatheNo();
        if ((this$LatheNo == null) ? (other$LatheNo != null) : !this$LatheNo.equals(other$LatheNo))
            return false;
        Object this$Completed = getCompleted(), other$Completed = other.getCompleted();
        if ((this$Completed == null) ? (other$Completed != null) : !this$Completed.equals(other$Completed))
            return false;
        Object this$Priority = getPriority(), other$Priority = other.getPriority();
        if ((this$Priority == null) ? (other$Priority != null) : !this$Priority.equals(other$Priority))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BillBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $BillNo = getBillNo();
        result = result * 59 + (($BillNo == null) ? 43 : $BillNo.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $LatheNo = getLatheNo();
        result = result * 59 + (($LatheNo == null) ? 43 : $LatheNo.hashCode());
        Object $Completed = getCompleted();
        result = result * 59 + (($Completed == null) ? 43 : $Completed.hashCode());
        Object $Priority = getPriority();
        result = result * 59 + (($Priority == null) ? 43 : $Priority.hashCode());
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "BillBean(BillNo=" + getBillNo() + ", SiteNo=" + getSiteNo() + ", LatheNo=" + getLatheNo() + ", Completed=" + getCompleted() + ", Priority=" + getPriority() + ", UserNo=" + getUserNo() + ")";
    }

    public String getBillNo() {
        return this.BillNo;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getLatheNo() {
        return this.LatheNo;
    }

    public Integer getCompleted() {
        return this.Completed;
    }

    public Integer getPriority() {
        return this.Priority;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}
