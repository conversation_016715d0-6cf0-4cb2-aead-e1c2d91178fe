package com.tgvs.wms.business.modules.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 手动上报查询请求DTO
 */
@Data
@ApiModel(value = "手动上报查询请求", description = "手动上报任务查询的请求参数")
public class ManualReportRequestDto {

    @ApiModelProperty(value = "当前页码，默认为1")
    private Long current = 1L;

    @ApiModelProperty(value = "每页大小，默认为10")
    private Long size = 10L;

    @ApiModelProperty(value = "上报状态：0待上报，1已上报，2上报失败，3已取消")
    private Integer reportStatus;

    @ApiModelProperty(value = "上报类型：INBOUND入库上报，OUTBOUND出库上报")
    private String reportType;

    @ApiModelProperty(value = "任务单号，支持模糊查询")
    private String taskOrder;

    @ApiModelProperty(value = "箱号/托盘号，支持模糊查询")
    private String boxNo;

    @ApiModelProperty(value = "任务类型：0采购入库，3领料出库等")
    private Integer taskType;

    @ApiModelProperty(value = "优先级：1最高，5普通，9最低")
    private Integer priority;

    @ApiModelProperty(value = "开始时间，格式：yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @ApiModelProperty(value = "结束时间，格式：yyyy-MM-dd HH:mm:ss")
    private String endTime;
} 