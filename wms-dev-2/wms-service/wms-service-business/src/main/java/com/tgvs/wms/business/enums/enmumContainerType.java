package com.tgvs.wms.business.enums;

import lombok.Getter;

/**
 * 容器类型枚举类
 */
@Getter
public enum enmumContainerType {
    //容器类型1-料箱 2-托盘
    BOX(1, "料箱"),
    PALLET(2, "托盘");

    private Integer code;
    private String value;

    enmumContainerType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据code值获取对应的枚举实例
     * @param code 容器类型代码
     * @return 枚举实例
     */
    public static enmumContainerType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (enmumContainerType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
