package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 出库属性DTO
 */
@Data
@Schema(description = "出库属性DTO")
public class BoxOutItemDto {

    /**
     * 料箱编号
     */
    @Schema(description = "料箱编号")
    private String boxNo;

    //容器类型
    private  Integer boxType;

    //出库任务类型
    private Integer taskType;

    /**
     * 任务编号
     */
    @Schema(description = "任务编号")
    private String taskOrder;

    /**
     * PO号
     */
    @Schema(description = "PO号")
    private List<String> poNoList;

    /**
     * 待出库数量
     */
    @Schema(description = "待出库数量（箱内物料总数量）")
    private Integer outQuantityList;

    /**
     * 箱内物料列表
     */
    @Schema(description = "箱内物料列表")
    private List<BoxItemDto> boxItemList;
} 