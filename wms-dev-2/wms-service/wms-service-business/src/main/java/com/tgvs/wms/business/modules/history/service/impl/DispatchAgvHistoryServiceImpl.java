package com.tgvs.wms.business.modules.history.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.history.entity.DispatchAgvHistory;
import com.tgvs.wms.business.modules.history.mapper.DispatchAgvHistoryMapper;
import com.tgvs.wms.business.modules.history.service.IDispatchAgvHistoryService;

@Service
public class DispatchAgvHistoryServiceImpl extends BaseServiceImpl<DispatchAgvHistoryMapper, DispatchAgvHistory> implements IDispatchAgvHistoryService {
}
