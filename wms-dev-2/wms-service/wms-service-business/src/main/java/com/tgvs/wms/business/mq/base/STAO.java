package com.tgvs.wms.business.mq.base;

import java.util.Date;

import lombok.Data;

@Data
public class STAO {

    private HEAD Header;

    private Date Time_State;

    private String OrderID;

    private String Order_Exec_Result;

    private String Order_ErrorCode;

    private Integer Occupy_Number;

    private String Order_Action_Type;


    public void setMQ(String str) throws Exception {
        if (null != str && str.length() > 0) {
            String[] mqdata = str.split(";", -1);
            if (null != mqdata && mqdata.length == 9) {
                this.Header.setMQ(str);
                setOrderID(mqdata[4]);
                setOrder_Exec_Result(mqdata[5]);
                setOrder_ErrorCode(mqdata[6]);
                try {
                    if (null != mqdata[7] && !mqdata[7].equals("")) {
                        Integer integer = Integer.valueOf(mqdata[7]);
                    } else {
                        setOccupy_Number(Integer.valueOf(0));
                    }
                } catch (Exception e) {
                    throw new Exception("报文格式不正确1！");
                }
                setOrder_Action_Type(mqdata[8]);
                setTime_State(new Date());
            } else {
                throw new Exception("报文格式不正确2！");
            }
        } else {
            throw new Exception("报文格式不正确3！");
        }
    }
}
