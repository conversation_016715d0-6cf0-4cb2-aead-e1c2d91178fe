package com.tgvs.wms.business.modules.machineAuxiliary.service;

import com.tgvs.wms.base.service.BaseService;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox;

import java.util.List;
import java.util.Map;

/**
 * 库存预占用表服务接口
 */
public interface IAuxiliaryOutPreBoxService extends BaseService<WmsAuxiliaryOutPreBox> {

    /**
     * 根据出库单ID查询预占用记录
     * 
     * @param outboundIds 出库单ID列表
     * @return 预占用记录列表
     */
    List<WmsAuxiliaryOutPreBox> getByOutboundIds(List<String> outboundIds);

    /**
     * 根据物料编码查询预占用数量（排除指定出库单）
     * 
     * @param materialCodes      物料编码列表
     * @param excludeOutboundIds 排除的出库单ID列表
     * @return 物料编码 -> 预占用数量的映射
     */
    Map<String, Integer> getReservedQuantityByMaterialCodes(List<String> materialCodes,
            List<String> excludeOutboundIds);

    /**
     * 根据库存项ID查询预占用数量
     * 
     * @param boxItemIds 库存项ID列表
     * @param status     预占用状态
     * @return 库存项ID -> 预占用数量的映射
     */
    Map<String, Integer> getReservedQuantityByBoxItemIds(List<String> boxItemIds, Integer status);

    /**
     * 批量更新预占用状态
     * 
     * @param outboundIds 出库单ID列表
     * @param oldStatus   原状态
     * @param newStatus   新状态
     * @return 是否成功
     */
    boolean updateStatusByOutboundIds(List<String> outboundIds, Integer oldStatus, Integer newStatus);

    /**
     * 创建预占用记录
     * 
     * @param outboundId       出库单ID
     * @param outboundNo       出库单号
     * @param boxItemId        库存项ID
     * @param reservedQuantity 预占用数量
     * @param materialCode     物料编码
     * @param contractNo       合约号
     * @param styleNo          款号
     * @param materialColor    物料颜色
     * @param materialSpec     物料规格
     * @param boxCode          箱号
     * @param containerType    容器类型
     * @param outboundType     出库类型
     * @return 创建的预占用记录
     */
    WmsAuxiliaryOutPreBox createReservation(String outboundId, String outboundNo, String boxItemId,
            Integer reservedQuantity, String materialCode, String contractNo,
            String styleNo, String materialColor, String materialSpec,
            String boxCode, Integer containerType, Integer outboundType,String gridCode);

    /**
     * 确认预占用记录（更新状态为已确认）
     * 
     * @param outboundIds 出库单ID列表
     * @return 是否成功
     */
    boolean confirmReservations(List<String> outboundIds);

    /**
     * 取消预占用记录（删除记录或更新状态为已取消）
     * 
     * @param outboundIds 出库单ID列表
     * @return 是否成功
     */
    boolean cancelReservations(List<String> outboundIds);
}