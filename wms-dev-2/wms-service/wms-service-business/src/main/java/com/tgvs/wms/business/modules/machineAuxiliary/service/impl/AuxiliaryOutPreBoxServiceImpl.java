package com.tgvs.wms.business.modules.machineAuxiliary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.business.modules.container.service.IBoxItemService;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox;
import com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryOutPreBoxMapper;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryOutPreBoxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import com.tgvs.wms.business.modules.container.entity.BoxItem;
import com.tgvs.wms.business.modules.storage.entity.Shelf;
import com.tgvs.wms.business.modules.storage.service.IShelfService;

/**
 * 库存预占用表服务实现类
 */
@Slf4j
@Service
public class AuxiliaryOutPreBoxServiceImpl extends
        BaseServiceImpl<WmsAuxiliaryOutPreBoxMapper, WmsAuxiliaryOutPreBox> implements IAuxiliaryOutPreBoxService {

    @Autowired
    private IBoxItemService boxItemService;
    
    @Autowired
    private IShelfService shelfService;

    @Override
    public List<WmsAuxiliaryOutPreBox> getByOutboundIds(List<String> outboundIds) {
        if (CollectionUtils.isEmpty(outboundIds)) {
            return Collections.emptyList();
        }

        return list(new LambdaQueryWrapper<WmsAuxiliaryOutPreBox>()
                .in(WmsAuxiliaryOutPreBox::getOutboundId, outboundIds)
                .eq(WmsAuxiliaryOutPreBox::getReservationStatus, 1));
    }

    @Override
    public Map<String, Integer> getReservedQuantityByMaterialCodes(List<String> materialCodes,
            List<String> excludeOutboundIds) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<WmsAuxiliaryOutPreBox> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WmsAuxiliaryOutPreBox::getMaterialCode, materialCodes)
                .eq(WmsAuxiliaryOutPreBox::getReservationStatus, WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED);

        if (CollectionUtils.isNotEmpty(excludeOutboundIds)) {
            queryWrapper.notIn(WmsAuxiliaryOutPreBox::getOutboundId, excludeOutboundIds);
        }

        List<WmsAuxiliaryOutPreBox> reservations = list(queryWrapper);

        return reservations.stream()
                .collect(Collectors.groupingBy(
                        WmsAuxiliaryOutPreBox::getMaterialCode,
                        Collectors.summingInt(r -> r.getReservedQuantity().intValue())));
    }

    @Override
    public Map<String, Integer> getReservedQuantityByBoxItemIds(List<String> boxItemIds, Integer status) {
        if (CollectionUtils.isEmpty(boxItemIds)) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<WmsAuxiliaryOutPreBox> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WmsAuxiliaryOutPreBox::getBoxItemId, boxItemIds);

        if (status != null) {
            queryWrapper.eq(WmsAuxiliaryOutPreBox::getReservationStatus, status);
        }

        List<WmsAuxiliaryOutPreBox> reservations = list(queryWrapper);

        return reservations.stream()
                .collect(Collectors.groupingBy(
                        WmsAuxiliaryOutPreBox::getBoxItemId,
                        Collectors.summingInt(r -> r.getReservedQuantity().intValue())));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatusByOutboundIds(List<String> outboundIds, Integer oldStatus, Integer newStatus) {
        if (CollectionUtils.isEmpty(outboundIds)) {
            return false;
        }

        LambdaUpdateWrapper<WmsAuxiliaryOutPreBox> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(WmsAuxiliaryOutPreBox::getReservationStatus, newStatus)
                .set(WmsAuxiliaryOutPreBox::getUpdateTime, LocalDateTime.now())
                .in(WmsAuxiliaryOutPreBox::getOutboundId, outboundIds);

        if (oldStatus != null) {
            updateWrapper.eq(WmsAuxiliaryOutPreBox::getReservationStatus, oldStatus);
        }

        // 根据新状态设置对应的时间字段
        if (newStatus.equals(WmsAuxiliaryOutPreBox.ReservationStatus.CONFIRMED)) {
            updateWrapper.set(WmsAuxiliaryOutPreBox::getConfirmTime, LocalDateTime.now());
        } else if (newStatus.equals(WmsAuxiliaryOutPreBox.ReservationStatus.CANCELLED)) {
            updateWrapper.set(WmsAuxiliaryOutPreBox::getCancelTime, LocalDateTime.now());
        }

        return update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WmsAuxiliaryOutPreBox createReservation(String outboundId, String outboundNo, String boxItemId,
            Integer reservedQuantity, String materialCode, String contractNo,
            String styleNo, String materialColor, String materialSpec,
            String boxCode, Integer containerType, Integer outboundType,String gridCode) {

        // 查询BoxItem获取当前库存信息，用于设置原始库存数量
        BoxItem boxItem = boxItemService.getById(boxItemId);
        if (boxItem == null) {
            log.error("创建预占用记录失败：库存项不存在，boxItemId={}", boxItemId);
            throw new RuntimeException("库存项不存在：" + boxItemId);
        }
        
        // 获取当前库存数量作为原始库存数量
        int currentStockQuantity = boxItem.getMaterialQuantity() != null ? boxItem.getMaterialQuantity() : 0;
        
        // 验证库存是否足够
        if (currentStockQuantity < reservedQuantity) {
            log.error("创建预占用记录失败：库存不足，boxItemId={}, 当前库存={}, 预占用数量={}", 
                    boxItemId, currentStockQuantity, reservedQuantity);
            throw new RuntimeException("库存不足，无法创建预占用记录");
        }

        WmsAuxiliaryOutPreBox reservation = new WmsAuxiliaryOutPreBox();
        reservation.setOutboundId(outboundId);
        reservation.setOutboundNo(outboundNo);
        reservation.setBoxItemId(boxItemId);
        reservation.setBoxCode(boxCode);
        reservation.setContainerType(containerType);
        reservation.setContractNo(contractNo);
        reservation.setStyleNo(styleNo);
        reservation.setMaterialCode(materialCode);
        reservation.setMaterialColor(materialColor);
        reservation.setMaterialSpec(materialSpec);
        reservation.setReservedQuantity(new BigDecimal(reservedQuantity));
        reservation.setOutboundType(outboundType);
        reservation.setReservationStatus(WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED);
        reservation.setReservationTime(LocalDateTime.now());
        reservation.setGridCode(gridCode);
        
        // 补充关键字段设置
        reservation.setOriginalStockQuantity(new BigDecimal(currentStockQuantity)); // 预占用时的原库存数量
        reservation.setOriginalQuantity(new BigDecimal(currentStockQuantity)); // 原库存数量（备份）
        reservation.setPendingQuantity(new BigDecimal(reservedQuantity)); // 待处理数量，初始等于预占用数量
        reservation.setActualQuantity(new BigDecimal(0)); // 实际出库数量，初始为0
        
        // 设置存储位置信息（如果有库位信息）
        try {
            // 查询BoxItem关联的库位信息
            if (StringUtils.isNotEmpty(boxCode)) {
                Shelf shelf = shelfService.selectByBoxNo(boxCode);
                if (shelf != null) {
                    reservation.setStorageLocation(shelf.getCode());
                    reservation.setShelfCode(shelf.getCode());
                }
            }
        } catch (Exception e) {
            log.warn("获取库位信息失败，继续创建预占用记录: {}", e.getMessage());
        }
        
        // 设置物料相关信息
        if (boxItem.getMaterialName() != null) {
            reservation.setMaterialName(boxItem.getMaterialName());
        }
        if (boxItem.getMaterialProperty() != null) {
            reservation.setMaterialProperty(boxItem.getMaterialProperty());
        }
        
        // 设置入库时间（从BoxItem获取）
        if (boxItem.getCreateTime() != null) {
            reservation.setInboundTime(boxItem.getCreateTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }

        save(reservation);

        log.info("创建预占用记录成功: outboundId={}, materialCode={}, reservedQuantity={}, originalStock={}", 
                outboundId, materialCode, reservedQuantity, currentStockQuantity);

        return reservation;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmReservations(List<String> outboundIds) {
        if (CollectionUtils.isEmpty(outboundIds)) {
            return false;
        }

        // 直接更新预占用记录状态为已确认（库存已在预出库时扣减）
        boolean statusUpdated = updateStatusByOutboundIds(outboundIds,
                WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED,
                WmsAuxiliaryOutPreBox.ReservationStatus.CONFIRMED);

        if (statusUpdated) {
            log.info("确认预占用记录成功，共处理 {} 个出库单", outboundIds.size());
        }

        return statusUpdated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelReservations(List<String> outboundIds) {
        if (CollectionUtils.isEmpty(outboundIds)) {
            return false;
        }

        // 方案1：直接删除预占用记录（推荐）
        LambdaQueryWrapper<WmsAuxiliaryOutPreBox> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WmsAuxiliaryOutPreBox::getOutboundId, outboundIds)
                .eq(WmsAuxiliaryOutPreBox::getReservationStatus, WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED);

        boolean removed = remove(queryWrapper);

        if (removed) {
            log.info("取消预占用记录成功，删除记录数: {}", outboundIds.size());
        }

        return removed;

        // 方案2：更新状态为已取消（备选）
        // return updateStatusByOutboundIds(outboundIds,
        // WmsAuxiliaryOutPreBox.ReservationStatus.RESERVED,
        // WmsAuxiliaryOutPreBox.ReservationStatus.CANCELLED);
    }
}