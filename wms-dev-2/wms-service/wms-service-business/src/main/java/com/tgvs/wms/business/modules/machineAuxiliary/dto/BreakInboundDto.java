package com.tgvs.wms.business.modules.machineAuxiliary.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 托盘拆零入库DTO
 */
@Data
@ApiModel(value = "托盘拆零入库", description = "将托盘中剩余物料转换为料箱入库的请求参数")
public class BreakInboundDto {

    @ApiModelProperty(value = "出库单ID列表", required = true)
    private List<String> outBoundId;

    @ApiModelProperty(value = "拆零物料明细", notes = "key: 托盘物料编码_合约号_款号, value: 拆零数量")
    private Map<String, Integer> breakQuantities;

    @ApiModelProperty(value = "目标库位编码", notes = "料箱存放的目标库位")
    private String targetShelfCode;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "操作人员")
    private String operator;

    /**
     * 拆零物料明细DTO
     */
    @Data
    @ApiModel(value = "拆零物料明细", description = "单个物料的拆零详情")
    public static class BreakMaterialDetail {

        @ApiModelProperty(value = "托盘物料ID")
        private String palletMaterialId;

        @ApiModelProperty(value = "物料编码")
        private String materialCode;

        @ApiModelProperty(value = "合约号")
        private String contractNo;

        @ApiModelProperty(value = "款号")
        private String itemNo;

        @ApiModelProperty(value = "拆零数量")
        private Integer breakQuantity;

        @ApiModelProperty(value = "目标料箱号")
        private String targetBoxNo;
    }
}