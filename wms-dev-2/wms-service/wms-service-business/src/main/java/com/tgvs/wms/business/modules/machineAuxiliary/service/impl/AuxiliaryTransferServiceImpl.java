package com.tgvs.wms.business.modules.machineAuxiliary.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryInboundService;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryOutboundService;
import com.tgvs.wms.business.modules.machineAuxiliary.service.IAuxiliaryTransferService;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.AuxiliaryMaterialVo;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutContractNoListVo;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutMaterialItemVo;
import com.tgvs.wms.business.modules.machineAuxiliary.vo.OutMaterialVo;
import com.tgvs.wms.business.modules.machineMaterials.vo.InContractNoListVo;
import com.tgvs.wms.business.modules.machineMaterials.vo.InMaterialVo;
import com.tgvs.wms.business.modules.machineMaterials.vo.TransferOrderVo;
import com.tgvs.wms.business.modules.warehouse.entity.WarehouseInfo;
import com.tgvs.wms.business.modules.warehouse.service.IWarehouseInfoService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 辅料调拨服务实现类
 */
@Slf4j
@Service
public class AuxiliaryTransferServiceImpl implements IAuxiliaryTransferService {

    @Autowired
    private IAuxiliaryOutboundService auxiliaryOutboundService;

    @Autowired
    private IAuxiliaryInboundService auxiliaryInboundService;

    @Autowired
    private IWarehouseInfoService warehouseInfoService;

    @Override
    public Boolean saveTransferOrder(TransferOrderVo transferOrderVo) {
        // 1. 验证参数
        if (transferOrderVo == null) {
            throw new IllegalArgumentException("调拨单信息不能为空");
        }

        if (transferOrderVo.getTransferNo() == null || transferOrderVo.getTransferNo().trim().isEmpty()) {
            throw new IllegalArgumentException("调拨单号不能为空");
        }

        if (transferOrderVo.getOutWarehouse() == null || transferOrderVo.getOutWarehouse().trim().isEmpty()) {
            throw new IllegalArgumentException("调出仓不能为空");
        }

        if (transferOrderVo.getInWarehouse() == null || transferOrderVo.getInWarehouse().trim().isEmpty()) {
            throw new IllegalArgumentException("调入仓不能为空");
        }

        if (transferOrderVo.getContractNoList() == null || transferOrderVo.getContractNoList().isEmpty()) {
            throw new IllegalArgumentException("合约号列表不能为空");
        }

        try {

            //判断调出仓和调入仓是否相同
            if (transferOrderVo.getOutWarehouse().equals(transferOrderVo.getInWarehouse())) {
                throw new IllegalArgumentException("调出仓和调入仓不能相同");
            }

            //查询当前仓库code,根据仓库编码查询是否存在判断是调入仓还是调出仓
            LambdaQueryWrapper<WarehouseInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(WarehouseInfo::getCode, transferOrderVo.getOutWarehouse(),transferOrderVo.getInWarehouse());
            List<WarehouseInfo> warehouseInfo = warehouseInfoService.getBaseMapper().selectList(queryWrapper);

            // 判断调出仓和调入仓是否存在
            boolean isOutWarehouse = false;
            boolean isInWarehouse = false;
            
            // 检查查询结果中是否包含调出仓和调入仓
            for (WarehouseInfo warehouse : warehouseInfo) {
                if (warehouse.getCode().equals(transferOrderVo.getOutWarehouse())) {
                    isOutWarehouse = true;
                }
                if (warehouse.getCode().equals(transferOrderVo.getInWarehouse())) {
                    isInWarehouse = true;
                }
            }
            
            // 验证调出仓和调入仓是否存在
            if (!isOutWarehouse) {
                throw new IllegalArgumentException("调出仓不存在: " + transferOrderVo.getOutWarehouse());
            }
            
            if (!isInWarehouse) {
                throw new IllegalArgumentException("调入仓不存在: " + transferOrderVo.getInWarehouse());
            }

            // 出库单号和入库单号都使用调拨单号
            String orderNumber = transferOrderVo.getTransferNo();
            
            // 根据当前仓库类型处理相应的单据
            if (isOutWarehouse) {
                // 当前仓库是调出仓，处理出库单
                processOutboundOrder(orderNumber, transferOrderVo);
            }
            
            if (isInWarehouse) {
                // 当前仓库是调入仓，处理入库单
                processInboundOrder(orderNumber, transferOrderVo);
            }

            return true;
        } catch (Exception e) {
            // 记录异常并抛出
            log.error("处理调拨单失败: {}", e.getMessage(), e);
            throw new RuntimeException("处理调拨单失败: " + e.getMessage(), e);
        }
    }

    
    /**
     * 处理出库单
     */
    private void processOutboundOrder(String outStoreNumber, TransferOrderVo transferOrderVo) {
        // 创建出库单VO
        OutMaterialVo outMaterialVo = new OutMaterialVo();
        outMaterialVo.setOutStoreNumber(outStoreNumber);
        outMaterialVo.setTaskType(1); // 调拨出库类型为1
        outMaterialVo.setPriority(1); // 默认优先级

        // 处理合约号列表
        List<OutContractNoListVo> outContractNoList = new ArrayList<>();
        for (InContractNoListVo inContractNoVo : transferOrderVo.getContractNoList()) {
            OutContractNoListVo outContractNoVo = new OutContractNoListVo();
            outContractNoVo.setContractNo(inContractNoVo.getContractNo());

            // 处理物料项
            List<OutMaterialItemVo> outMaterialItemList = new ArrayList<>();
            for (AuxiliaryMaterialVo inMaterialItem : inContractNoVo.getItemList()) {
                OutMaterialItemVo outMaterialItem = new OutMaterialItemVo();
                // 复制物料信息
                outMaterialItem.setItemNo(inMaterialItem.getItemNo());
                outMaterialItem.setMaterialCode(inMaterialItem.getMaterialCode());
                outMaterialItem.setMaterialName(inMaterialItem.getMaterialName());
                outMaterialItem.setMaterialModel(inMaterialItem.getMaterialModel());
                outMaterialItem.setMaterialColor(inMaterialItem.getMaterialColor());
                outMaterialItem.setMaterialColorCode(inMaterialItem.getMaterialColorCode());
                outMaterialItem.setMaterialUnit(inMaterialItem.getMaterialUnit());
                // 出库数量与入库数量相同
                outMaterialItem.setOutQuantity(inMaterialItem.getInQuantity());

                outMaterialItemList.add(outMaterialItem);
            }

            outContractNoVo.setOutMaterialItemList(outMaterialItemList);
            outContractNoList.add(outContractNoVo);
        }

        outMaterialVo.setContractNoList(outContractNoList);
        
        // 处理出库单
        boolean outResult = auxiliaryOutboundService.processOutMaterial(outMaterialVo);
        if (!outResult) {
            throw new RuntimeException("创建调拨出库单失败");
        }
    }
    
    /**
     * 处理入库单
     */
    private void processInboundOrder(String inStoreNumber, TransferOrderVo transferOrderVo) {
        // 创建入库单VO
        InMaterialVo inMaterialVo = new InMaterialVo();
        inMaterialVo.setInStoreNumber(inStoreNumber);
        inMaterialVo.setTaskType(0); // 调拨入库类型为1
        inMaterialVo.setPriority(1); // 默认优先级
        inMaterialVo.setContractNoList(transferOrderVo.getContractNoList());
        
        // 处理入库单
        boolean inResult = auxiliaryInboundService.processInMaterial(inMaterialVo);
        if (!inResult) {
            throw new RuntimeException("创建调拨入库单失败");
        }
    }
}
