package com.tgvs.wms.business.modules.machineMaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 辅料入库物料信息请求DTO
 */
@Data
@Schema(description = "辅料入库物料信息请求")
public class InMaterialVo {

    @NotBlank(message = "入库单号不能为空")
    @Schema(description = "入库单号", required = true)
    private String inStoreNumber;

    @Schema(description = "入库类型（1.采购入库，2.一般入库，3.产线退料回库;", required = true)
    private Integer taskType;

    @Schema(description = "入库优先级")
    private Integer priority;


    @NotEmpty(message = "合约号不能为空")
    @Schema(description = "合约号", required = true)
    @Valid
    private List<InContractNoListVo> contractNoList;

}