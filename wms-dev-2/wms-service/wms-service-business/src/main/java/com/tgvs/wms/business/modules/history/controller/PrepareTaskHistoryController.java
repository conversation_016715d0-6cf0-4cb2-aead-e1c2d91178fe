package com.tgvs.wms.business.modules.history.controller;


import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tgvs.wms.base.controller.BaseController;
import com.tgvs.wms.business.modules.history.entity.PrepareTaskHistory;
import com.tgvs.wms.business.modules.history.service.IPrepareTaskHistoryService;
import com.tgvs.wms.common.annotation.AutoLog;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.query.QueryGenerator;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = {"备货任务"})
@RestController
@RequestMapping({"/history/PrepareTask"})
public class PrepareTaskHistoryController extends BaseController<PrepareTaskHistory, IPrepareTaskHistoryService> {

    @Autowired
    private IPrepareTaskHistoryService PrepareTaskHistoryService;

    @ApiOperation(value = "备货任务-分页列表查询", notes = "备货任务-分页列表查询")
    @PostMapping({"/list"})
    public Result<?> queryPageList(PrepareTaskHistory prepareTaskHistory, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<PrepareTaskHistory> queryWrapper = QueryGenerator.initQueryWrapper(prepareTaskHistory, req.getParameterMap());
        Page<PrepareTaskHistory> page = new Page(pageNo.intValue(), pageSize.intValue());
        IPage<PrepareTaskHistory> pageList = this.PrepareTaskHistoryService.page((IPage)page, (Wrapper)queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog("备货任务-添加")
    @ApiOperation(value = "备货任务-添加", notes = "备货任务-添加")
    @RequiresPermissions({"PrepareTaskHistory:add"})
    @PostMapping({"/add"})
    public Result<?> add(@RequestBody PrepareTaskHistory prepareTaskHistory) {
        this.PrepareTaskHistoryService.save(prepareTaskHistory);
        return Result.OK("添加成功！");
    }

    @AutoLog("备货任务-编辑")
    @ApiOperation(value = "备货任务-编辑", notes = "备货任务-编辑")
    @RequiresPermissions({"PrepareTaskHistory:edit"})
    @PostMapping({"/edit"})
    public Result<?> edit(@RequestBody PrepareTaskHistory prepareTaskHistory) {
        this.PrepareTaskHistoryService.updateById(prepareTaskHistory);
        return Result.OK("编辑成功!");
    }

    @AutoLog("备货任务-通过id删除")
    @ApiOperation(value = "备货任务-通过id删除", notes = "备货任务-通过id删除")
    @RequiresPermissions({"PrepareTaskHistory:delete"})
    @PostMapping({"/delete"})
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        this.PrepareTaskHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog("备货任务-批量删除")
    @ApiOperation(value = "备货任务-批量删除", notes = "备货任务-批量删除")
    @RequiresPermissions({"PrepareTaskHistory:deleteBatch"})
    @PostMapping({"/deleteBatch"})
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.PrepareTaskHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog("备货任务-通过id查询")
    @ApiOperation(value = "备货任务-通过id查询", notes = "备货任务-通过id查询")
    @PostMapping({"/queryById"})
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        PrepareTaskHistory PrepareTaskHistory = (PrepareTaskHistory)this.PrepareTaskHistoryService.getById(id);
        if (PrepareTaskHistory == null)
            return Result.error("未找到对应数据");
        return Result.OK(PrepareTaskHistory);
    }

    @RequestMapping({"/exportXls"})
    public ModelAndView exportXls(HttpServletRequest request, PrepareTaskHistory prepareTaskHistory) {
        return exportXls(request, prepareTaskHistory, PrepareTaskHistory.class, "备货任务");
    }

    @RequiresPermissions({"PrepareTaskHistory:importExcel"})
    @RequestMapping(value = {"/importExcel"}, method = {RequestMethod.POST})
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return importExcel(request, response, PrepareTaskHistory.class);
    }

    @AutoLog("备货任务历史-还原历史备货")
    @ApiOperation(value = "备货任务历史-还原历史备货", notes = "备货任务历史-还原历史备货")
    @RequiresPermissions({"PrepareTaskHistory:redo"})
    @PostMapping({"/redo"})
    public Result<?> redo(@RequestParam(name = "ids", required = true) String ids) {
        this.PrepareTaskHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }
}