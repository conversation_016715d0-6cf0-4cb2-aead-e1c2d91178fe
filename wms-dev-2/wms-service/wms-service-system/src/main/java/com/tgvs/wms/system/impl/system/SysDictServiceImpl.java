package com.tgvs.wms.system.impl.system;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.common.util.SqlInjectionUtil;
import com.tgvs.wms.system.entity.SysDict;
import com.tgvs.wms.system.mapper.SysDictMapper;
import com.tgvs.wms.system.service.system.SysDictService;
import com.tgvs.wms.system.vo.DictModel;
import com.tgvs.wms.system.vo.DictModelMany;

@Service
public class SysDictServiceImpl extends BaseServiceImpl<SysDictMapper, SysDict> implements SysDictService {

    @Autowired
    private SysDictMapper sysDictMapper;

    @Override
    public List<DictModel> getDictItems(String dictCode) {
        List<DictModel> ls;
        if (dictCode.contains(",")) {
            //关联表字典（举例：sys_user,realname,id）
            String[] params = dictCode.split(",");
            if (params.length < 3) {
                // 字典Code格式不正确
                return null;
            }
            //SQL注入校验（只限制非法串改数据库）
            final String[] sqlInjCheck = {params[0], params[1], params[2]};
            SqlInjectionUtil.filterContent(sqlInjCheck);
            if (params.length == 4) {
                // SQL注入校验（查询条件SQL 特殊check，此方法仅供此处使用）
                SqlInjectionUtil.specialFilterContent(params[3]);
                ls = this.queryTableDictItemsByCodeAndFilter(params[0], params[1], params[2], params[3]);
            } else if (params.length == 3) {
                ls = this.queryTableDictItemsByCode(params[0], params[1], params[2]);
            } else {
                // 字典Code格式不正确
                return null;
            }
        } else {
            //字典表
            ls = this.queryDictItemsByCode(dictCode);
        }
        return ls;
    }

    /**
     * 通过查询指定code 获取字典
     * @param code
     * @return
     */
    @Override
//    @Cacheable(value = CacheConstant.SYS_DICT_CACHE,key = "#code", unless = "#result == null ")
    public List<DictModel> queryDictItemsByCode(String code) {
        log.debug("无缓存dictCache的时候调用这里！");
        return sysDictMapper.queryDictItemsByCode(code);
    }

    @Override
    //@Cacheable(value = CacheConstant.SYS_DICT_TABLE_CACHE)
    public List<DictModel> queryTableDictItemsByCode(String table, String text, String code) {
        log.debug("无缓存dictTableList的时候调用这里！");
        return sysDictMapper.queryTableDictItemsByCode(table,text,code);
    }

    @Override
    public List<DictModel> queryTableDictItemsByCodeAndFilter(String table, String text, String code, String filterSql) {
        log.debug("无缓存dictTableList的时候调用这里！");
        return sysDictMapper.queryTableDictItemsByCodeAndFilter(table,text,code,filterSql);
    }

    @Override
    public String translateDictFromTable(String table, String text, String code, String key) {
        return sysDictMapper.queryTableDictTextByKey(table, text, code, key);
    }

    @Override
    public String translateDict(String code, String key) {
        return sysDictMapper.queryDictTextByKey(code, key);
    }

    @Override
    public List<DictModel> queryTableDictTextByKeys(String table, String text, String code, List<String> keys) {
        //update-begin-author:taoyan date:20220113 for: @dict注解支持 dicttable 设置where条件
        String filterSql = null;
        if(table.toLowerCase().indexOf("where")>0){
            String[] arr = table.split(" (?i)where ");
            table = arr[0];
            filterSql = arr[1];
        }
        return sysDictMapper.queryTableDictByKeysAndFilterSql(table, text, code, filterSql, keys);
        //update-end-author:taoyan date:20220113 for: @dict注解支持 dicttable 设置where条件
    }

    @Override
    public Map<String, List<DictModel>> translateManyDict(String dictCodes, String keys) {
        List<String> dictCodeList = Arrays.asList(dictCodes.split(","));
        List<String> values = Arrays.asList(keys.split(","));
        return queryManyDictByKeys(dictCodeList, values);
    }

    public Map<String, List<DictModel>> queryManyDictByKeys(List<String> dictCodeList, List<String> keys) {
        List<DictModelMany> list = sysDictMapper.queryManyDictByKeys(dictCodeList, keys);
        Map<String, List<DictModel>> dictMap = new HashMap<>();
        for (DictModelMany dict : list) {
            List<DictModel> dictItemList = dictMap.computeIfAbsent(dict.getDictCode(), i -> new ArrayList<>());
            dictItemList.add(new DictModel(dict.getValue(), dict.getText()));
        }
        return dictMap;
    }
}
