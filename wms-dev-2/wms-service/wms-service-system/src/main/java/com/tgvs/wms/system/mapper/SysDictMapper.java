package com.tgvs.wms.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.system.entity.SysDict;
import com.tgvs.wms.system.vo.DictModel;
import com.tgvs.wms.system.vo.DictModelMany;

/**
 * <p>
 * 字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
public interface SysDictMapper extends BaseMapper<SysDict> {

    List<DictModel> queryDictItemsByCode(@Param("code") String code);
    List<DictModel> queryTableDictItemsByCode(@Param("table") String table,@Param("text") String text,@Param("code") String code);
    List<DictModel> queryTableDictItemsByCodeAndFilter(@Param("table") String table,@Param("text") String text,@Param("code") String code,@Param("filterSql") String filterSql);

    String queryDictTextByKey(@Param("code") String code,@Param("key") String key);

    String queryTableDictTextByKey(String table, String text, String code, String key);
    /**
     * 查询字典表的数据
     * @param table 表名
     * @param text   显示字段名
     * @param code   存储字段名
     * @param filterSql 条件sql
     * @param codeValues 存储字段值 作为查询条件in
     * @return
     */
    List<DictModel> queryTableDictByKeysAndFilterSql(@Param("table") String table, @Param("text") String text, @Param("code") String code, @Param("filterSql") String filterSql,  @Param("codeValues") List<String> codeValues);

    /**
     * 可通过多个字典code查询翻译文本
     * @param dictCodeList 多个字典code
     * @param keys 数据列表
     * @return
     */
    List<DictModelMany> queryManyDictByKeys(@Param("dictCodeList") List<String> dictCodeList, @Param("keys") List<String> keys);


}
