package com.tgvs.wms.system.impl.system;

import com.tgvs.wms.base.service.impl.BaseServiceImpl;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.system.entity.SysUserRole;
import com.tgvs.wms.system.mapper.SysUserRoleMapper;
import com.tgvs.wms.system.service.system.SysUserRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 用户角色关联服务实现类
 */
@Service
public class SysUserRoleServiceImpl extends BaseServiceImpl<SysUserRoleMapper, SysUserRole>
        implements SysUserRoleService {

    @Override
    public List<String> getRoleIdsByUserId(String userId) {
        // 根据用户ID查询角色ID列表
        return this.baseMapper.selectRoleIdsByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> insertUserRoles(String userId, List<String> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Result.ok("角色列表为空，无需添加");
        }

        // 构建用户角色对象列表
        List<SysUserRole> userRoles = new ArrayList<>();
        for (String roleId : roleIds) {
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRoles.add(userRole);
        }

        // 批量插入用户角色关联
        this.saveBatch(userRoles);
        return Result.ok("用户角色绑定成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateUserRoles(String userId, List<String> roleIds) {
        // 先删除用户原有角色关联
        deleteUserRolesByUserId(userId);

        // 添加新的角色关联
        if (roleIds != null && !roleIds.isEmpty()) {
            return insertUserRoles(userId, roleIds);
        }

        return Result.ok("用户角色更新成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteUserRolesByUserId(String userId) {
        // 删除指定用户的所有角色关联
        this.baseMapper.deleteUserRolesByUserId(userId);
        return Result.ok("用户角色关联删除成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteUserRoles(String[] userIds) {
        // 批量删除多个用户的角色关联
        this.baseMapper.deleteUserRolesByUserIds(Arrays.asList(userIds));
        return Result.ok("批量删除用户角色关联成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteUserRole(String userId, String roleId) {
        // 删除特定用户的特定角色
        this.baseMapper.deleteUserRole(userId, roleId);
        return Result.ok("用户角色关联删除成功");
    }

    @Override
    public boolean hasRole(String userId, String roleId) {
        // 检查用户是否拥有指定角色
        return this.baseMapper.checkUserRole(userId, roleId) > 0;
    }
}