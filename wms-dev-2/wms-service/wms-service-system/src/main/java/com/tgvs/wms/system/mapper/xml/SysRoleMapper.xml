<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.system.mapper.SysRoleMapper">

    <select id="getRolesByUserId" resultType="com.tgvs.wms.system.entity.SysRole">
        SELECT r.*
        FROM sys_role r
        JOIN sys_user_role ur ON r.id = ur.role_id
        JOIN sys_user u ON ur.user_id = u.id
        WHERE u.id = #{userId}
    </select>
</mapper>
