<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.1</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.tvgs.wms</groupId>
    <artifactId>wms</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <java.version>8</java.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <shiro.version>1.8.0</shiro.version>
        <bitwalker.version>1.21</bitwalker.version>
        <commons.io.version>2.11.0</commons.io.version>
        <fastjson.version>1.2.80</fastjson.version>
        <mybatis-plus.version>3.4.3.1</mybatis-plus.version>
        <druid.version>1.2.8</druid.version>
    </properties>

    <modules>
        <module>wms-web</module>
        <module>wms-common</module>
        <module>wms-framework</module>
        <module>wms-transaction</module>
        <module>wms-service</module>
        <module>wms-persistence</module>
        <module>wms-socket</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tvgs.wms</groupId>
                <artifactId>wms-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tvgs.wms</groupId>
                <artifactId>wms-framework</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tvgs.wms</groupId>
                <artifactId>wms-persistence</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.tvgs.wms</groupId>
                <artifactId>wms-service-base</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.tvgs.wms</groupId>
                <artifactId>wms-service-system</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.tvgs.wms</groupId>
                <artifactId>wms-service-business</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tvgs.wms</groupId>
                <artifactId>wms-transaction-base</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>4.2.1</version>
            </dependency>

            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- Shiro核心框架 -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-core</artifactId>
                <version>${shiro.version}</version>
            </dependency>

            <!-- Shiro使用Spring框架 -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring</artifactId>
                <version>${shiro.version}</version>
            </dependency>

            <!-- Shiro使用EhCache缓存框架 -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-ehcache</artifactId>
                <version>${shiro.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- mybatis-plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>