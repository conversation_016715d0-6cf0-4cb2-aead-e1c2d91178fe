package com.tgvs.wms.framework.aspect;

import com.alibaba.fastjson.JSON;
import com.tgvs.wms.common.annotation.Log;
import com.tgvs.wms.common.util.IpUtils;
import com.tgvs.wms.common.util.SqliteLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.UUID;

/**
 * 全局日志切面 - 使用现有@Log注解
 * 同时记录到文件和SQLite数据库
 */
@Aspect
@Component
@Slf4j
public class LogAspect {

    @Autowired
    private SqliteLogUtil sqliteLogUtil;

    /**
     * 定义切点：拦截所有标注了@Log注解的方法
     */
    @Pointcut("@annotation(com.tgvs.wms.common.annotation.Log)")
    public void logPointcut() {
    }

    /**
     * 环绕通知：记录方法执行前后的日志
     */
    @Around("logPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String requestId = UUID.randomUUID().toString().replace("-", "");
        
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取@Log注解
        Log logAnnotation = method.getAnnotation(Log.class);
        
        // 收集日志信息
        String module = logAnnotation.title();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = method.getName();
        
        // 获取请求信息
        HttpServletRequest request = null;
        String requestUrl = "";
        String requestMethod = "";
        String clientIp = "";
        String userAgent = "";
        
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                request = attributes.getRequest();
                requestUrl = request.getRequestURL().toString();
                requestMethod = request.getMethod();
                clientIp = IpUtils.getIpAddr(request);
                userAgent = request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败", e);
        }
        
        // 记录入参
        String requestParams = "";
        if (logAnnotation.isSaveRequestData()) {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                try {
                    // 过滤敏感参数
                    Object[] filteredArgs = filterSensitiveData(args);
                    requestParams = JSON.toJSONString(filteredArgs);
                } catch (Exception e) {
                    requestParams = "参数序列化失败: " + e.getMessage();
                }
            } else {
                requestParams = "无";
            }
        }
        
        // 构建控制台日志信息
        StringBuilder logInfo = new StringBuilder();
        logInfo.append("\n==================== 接口调用开始 ====================");
        logInfo.append("\n请求ID: ").append(requestId);
        logInfo.append("\n模块: ").append(module);
        logInfo.append("\n类名: ").append(className);
        logInfo.append("\n方法: ").append(methodName);
        logInfo.append("\n请求URL: ").append(requestUrl);
        logInfo.append("\n请求方式: ").append(requestMethod);
        logInfo.append("\n客户端IP: ").append(clientIp);
        logInfo.append("\n请求参数: ").append(requestParams);
        
        log.info(logInfo.toString());
        
        Object result = null;
        Exception exception = null;
        String status = "SUCCESS";
        String errorMessage = "";
        String responseData = "";
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();
        } catch (Exception e) {
            exception = e;
            status = "FAILED";
            errorMessage = e.getMessage();
            throw e;
        } finally {
            // 记录执行结果
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 记录出参
            if (logAnnotation.isSaveResponseData() && result != null && exception == null) {
                try {
                    responseData = JSON.toJSONString(result);
                } catch (Exception e) {
                    responseData = "返回结果序列化失败: " + e.getMessage();
                }
            }
            
            // 构建结果日志
            StringBuilder resultLog = new StringBuilder();
            resultLog.append("\n==================== 接口调用结束 ====================");
            resultLog.append("\n请求ID: ").append(requestId);
            resultLog.append("\n执行时间: ").append(executionTime).append("ms");
            resultLog.append("\n执行状态: ").append(status);
            
            if (exception != null) {
                resultLog.append("\n异常信息: ").append(errorMessage);
                log.error(resultLog.toString(), exception);
            } else {
                if (!responseData.isEmpty()) {
                    resultLog.append("\n返回结果: ").append(responseData);
                }
                log.info(resultLog.toString());
            }
            
            resultLog.append("\n====================================================");
            
            // 异步保存到SQLite数据库
            sqliteLogUtil.saveLogAsync(requestId, module, className, methodName,
                    requestUrl, requestMethod, clientIp, userAgent,
                    requestParams, responseData, executionTime,
                    status, errorMessage);
        }
        
        return result;
    }
    
    /**
     * 过滤敏感数据
     */
    private Object[] filterSensitiveData(Object[] args) {
        if (args == null) {
            return null;
        }
        
        Object[] filteredArgs = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg != null) {
                String argStr = arg.toString().toLowerCase();
                // 检查是否包含敏感信息
                if (argStr.contains("password") || argStr.contains("pwd") || 
                    argStr.contains("token") || argStr.contains("secret")) {
                    filteredArgs[i] = "***敏感信息已隐藏***";
                } else {
                    filteredArgs[i] = arg;
                }
            } else {
                filteredArgs[i] = null;
            }
        }
        return filteredArgs;
    }
} 