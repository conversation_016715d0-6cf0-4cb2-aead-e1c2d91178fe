package com.tgvs.wms.framework.aspect;

import java.util.List;

import com.tgvs.wms.common.util.StringUtils;
import com.tgvs.wms.common.util.cache.CacheUtils;
import com.tgvs.wms.system.entity.SysDict;

/**
 * 字典工具类
 * 
 * <AUTHOR>
 */
public class DictCacheUtil
{
    /**
     * 分隔符
     */
    public static final String SEPARATOR = ",";
    /**
     * 字典管理 cache name
     */
    public static final String SYS_DICT_CACHE = "sys-dict";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";


    public static boolean hasKey(String key){
        return CacheUtils.hasKey(getCacheName(), key);
    }

    /**
     * 设置字典缓存
     * 
     * @param key 参数键
     * @param dictDatas 字典数据列表
     */
    public static void setDictCache(String key, String dictData)
    {
        CacheUtils.put(getCacheName(), getCacheKey(key), dictData);
    }

    /**
     * 获取字典缓存
     * 
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static String getDictCache(String key)
    {
        Object cacheObj = CacheUtils.get(getCacheName(), getCacheKey(key));
        if (StringUtils.isNotNull(cacheObj))
        {
            return StringUtils.cast(cacheObj);
        }
        return null;
    }

    /**
     * 删除指定字典缓存
     * 
     * @param key 字典键
     */
    public static void removeDictCache(String key)
    {
        CacheUtils.remove(getCacheName(), getCacheKey(key));
    }

    /**
     * 清空字典缓存
     */
    public static void clearDictCache()
    {
        CacheUtils.removeAll(getCacheName());
    }

    /**
     * 获取cache name
     * 
     * @return 缓存名
     */
    public static String getCacheName()
    {
        return SYS_DICT_CACHE;
    }

    /**
     * 设置cache key
     * 
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey)
    {
        return SYS_DICT_KEY + configKey;
    }
}
