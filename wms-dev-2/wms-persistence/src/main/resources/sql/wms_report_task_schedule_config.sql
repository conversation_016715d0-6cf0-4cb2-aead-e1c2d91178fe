-- 出入库上报任务配置表
-- 极简版配置，操作人员易于理解和设置
CREATE TABLE `wms_report_task_schedule_config` (
    `id` varchar(32) NOT NULL COMMENT '主键ID',
    `config_name` varchar(100) NOT NULL COMMENT '配置名称',
    `task_type` varchar(50) NOT NULL COMMENT '任务类型：入库上报、出库上报',
    `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0禁用，1启用',
    `interval_minutes` int(11) NOT NULL DEFAULT '5' COMMENT '执行间隔（分钟）：5、10、15、30、60',
    `batch_size` int(11) NOT NULL DEFAULT '50' COMMENT '每次处理数量：20、50、100',
    `retry_count` int(11) NOT NULL DEFAULT '3' COMMENT '重试次数：1、2、3',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `last_execute_time` bigint(20) DEFAULT NULL COMMENT '最后执行时间（时间戳）',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `version` int(11) NOT NULL DEFAULT '1' COMMENT '版本号，用于乐观锁控制并发更新',
    `delete_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0正常，1已删除',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
    PRIMARY KEY (`id`),
    KEY `idx_task_type` (`task_type`),
    KEY `idx_enabled` (`is_enabled`),
    KEY `idx_interval` (`interval_minutes`),
    KEY `idx_delete_flag` (`delete_flag`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_last_execute` (`last_execute_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出入库上报任务配置表';

-- 插入默认配置数据
INSERT INTO `wms_report_task_schedule_config` 
(`id`, `config_name`, `task_type`, `is_enabled`, `interval_minutes`, `batch_size`, `retry_count`, `create_by`, `remark`) 
VALUES 
('inbound-default-001', '默认入库上报配置', '入库上报', 1, 5, 50, 3, 'system', '系统默认的入库任务上报配置'),
('outbound-default-001', '默认出库上报配置', '出库上报', 1, 5, 50, 3, 'system', '系统默认的出库任务上报配置'); 