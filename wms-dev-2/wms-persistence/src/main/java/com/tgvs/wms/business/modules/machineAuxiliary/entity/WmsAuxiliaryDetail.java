package com.tgvs.wms.business.modules.machineAuxiliary.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;



/**
 * 辅料出入库明细实体类
 */
@Data
@TableName("wms_auxiliary_detail")
@ApiModel(value = "WmsAuxiliaryDetail", description = "辅料出入库明细")
public class WmsAuxiliaryDetail extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;



    /**
     * 关联单号(入库单号或出库单号)
     */
    @ApiModelProperty("关联单号(入库单号或出库单号)")
    private String refNumber;

    /**
     * 操作类型(0入库,1出库)
     */
    @ApiModelProperty("操作类型(0入库,1出库)")
    private Integer operationType;

    /**
     * 合约号
     */
    @ApiModelProperty("合约号")
    private String contractNo;

    /**
     * 款号
     */
    @ApiModelProperty("款号")
    private String itemNo;

    /**
     * 物料唯一值
     */
    @ApiModelProperty("物料唯一值")
    private String reqListId;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty("物料名称")
    private String materialName;

    /**
     * 物料颜色
     */
    @ApiModelProperty("物料颜色")
    private String materialColor;

    /**
     * 物料颜色编码
     */
    @ApiModelProperty("物料颜色编码")
    private String materialColorCode;

    /**
     * 物料规格/型号
     */
    @ApiModelProperty("物料规格/型号")
    private String materialModel;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Integer quantity;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String materialUnit;

    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    private Integer priority;



    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    @TableField("update_by")
    private String updateBy;

	/**
	 * 删除标志
	 */
	@TableField(value = "delete_flag")
	private Integer deleteFlag;

    /**
     * 预装状态(0-未预装,1-已预装)
     */
    @ApiModelProperty("预装状态(0-未预装,1-已预装)")
    @TableField("prebox_status")
    private Integer preboxStatus;

    /**
     * 预装批次号
     */
    @ApiModelProperty("预装批次号")
    @TableField("prebox_batch_no")
    private String preboxBatchNo;

    /**
     * 所属预装箱ID
     */
    @TableField("prebox_id")
    private String preboxId;
    
    
} 