package com.tgvs.wms.business.modules.history.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_robot_dispatch_history")
@ApiModel(value = "wms_robot_dispatch_history对象", description = "机械手调度历史记录")
@Data
public class RobotDispatchHistory extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "主键", width = 22.0D)
    @ApiModelProperty("原主键")
    private String sourceId;

    @Excel(name = "任务号", width = 15.0D)
    @ApiModelProperty("任务号")
    private Integer taskId;

    @Excel(name = "父任务号", width = 15.0D)
    @ApiModelProperty("父任务号")
    private Integer taskPid;

    @Excel(name = "任务类型", width = 15.0D, dictTable = "wms_task_robot_type", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_task_robot_type", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务类型")
    private Integer type;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "调度执行对象", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("调度执行对象")
    private String dispatcher;

    @Excel(name = "布匹号", width = 15.0D)
    @ApiModelProperty("布匹号")
    private String clothNo;

    @Excel(name = "源设备类型", width = 15.0D, dictTable = "wms_carry_site_type", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_carry_site_type", dicText = "text", dicCode = "code")
    @ApiModelProperty("源设备类型")
    private String fromtype;

    @Excel(name = "目的设备类型", width = 15.0D, dictTable = "wms_carry_site_type", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_carry_site_type", dicText = "text", dicCode = "code")
    @ApiModelProperty("目的设备类型")
    private String totype;

    @Excel(name = "源位置", width = 15.0D, dictTable = "wms_point", dicText = "point_no", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_no", dicCode = "point_no")
    @ApiModelProperty("源位置")
    private String fromSite;

    @Excel(name = "目的位置", width = 15.0D, dictTable = "wms_point", dicText = "point_no", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_no", dicCode = "point_no")
    @ApiModelProperty("目的位置")
    private String toSite;

    @Excel(name = "源总层数", width = 15.0D)
    @ApiModelProperty("源总层数")
    private Integer fromlayercount;

    @Excel(name = "目的总层数", width = 15.0D)
    @ApiModelProperty("目的总层数")
    private Integer tolayercount;

    @Excel(name = "源层", width = 15.0D)
    @ApiModelProperty("源层")
    private Integer fromlayer;

    @Excel(name = "目的层", width = 15.0D)
    @ApiModelProperty("目的层")
    private Integer tolayer;

    @Excel(name = "源列", width = 15.0D)
    @ApiModelProperty("源列")
    private Integer fromcolumn;

    @Excel(name = "目的列", width = 15.0D)
    @ApiModelProperty("目的列")
    private Integer tocolumn;

    @Excel(name = "源区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("源区域")
    private String fromArea;

    @Excel(name = "目的区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("目的区域")
    private String toArea;

    @Excel(name = "任务状态", width = 15.0D, dictTable = "wms_dispatch_state", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_dispatch_state", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务状态")
    private Integer state;

    @Excel(name = "货位", width = 15.0D)
    @ApiModelProperty("货位")
    private Integer location;

    @Excel(name = "拉布单号", width = 22.0D)
    @ApiModelProperty("拉布单号")
    private String billno;

    @Excel(name = "铺床号", width = 15.0D)
    @ApiModelProperty("铺床号")
    private String latheno;

    @Excel(name = "缸号", width = 15.0D)
    @ApiModelProperty("缸号")
    private String vatno;

    @Excel(name = "计划日期", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划日期")
    private Date planDate;

    @Excel(name = "松布架号", width = 15.0D)
    @ApiModelProperty("松布架号")
    private String dollyNo;

    @Excel(name = "托板类型", width = 15.0D, dicCode = "pallet_type")
    @Dict(dicCode = "pallet_type")
    @ApiModelProperty("托板类型")
    private Integer pelletType;

    @Excel(name = "板高度", width = 15.0D, dicCode = "pallet_height")
    @Dict(dicCode = "pallet_height")
    @ApiModelProperty("板高度")
    private Integer palletHeight;

    @Excel(name = "坯布重量", width = 15.0D)
    @ApiModelProperty("坯布重量")
    private Integer clothWeight;

    @Excel(name = "货位异常", width = 15.0D, dicCode = "error_status")
    @Dict(dicCode = "error_status")
    @ApiModelProperty("货位异常")
    private Integer error;

    @Excel(name = "异常信息", width = 15.0D)
    @ApiModelProperty("异常信息")
    private String errorCode;

    @ApiModelProperty("订单编号")
    private String bookNo;

    @ApiModelProperty("创建人")
    private String createBy;

    @Excel(name = "创建时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "完成时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("完成时间")
    private Date endTime;

    @ApiModelProperty("删除状态")
    private Integer delFlag;

    @ApiModelProperty("父级节点")
    private String pid;

    @ApiModelProperty("是否有子节点")
    private String hasChild;
}
