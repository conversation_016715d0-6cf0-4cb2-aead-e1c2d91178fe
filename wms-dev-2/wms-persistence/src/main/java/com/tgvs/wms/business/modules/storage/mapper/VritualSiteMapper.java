package com.tgvs.wms.business.modules.storage.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.storage.entity.Lathe;
import com.tgvs.wms.business.modules.storage.entity.VritualSite;

public interface VritualSiteMapper extends BaseMapper<VritualSite> {
    VritualSite selectByCode(@Param("siteCode") String paramString);

    List<Lathe> selectLathe(@Param("area") String paramString);
}
