package com.tgvs.wms.business.modules.machineAuxiliary.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/*
 * 库存预占用表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_auxiliary_outprebox")
@ApiModel(value = "WmsAuxiliaryOutPreBox对象", description = "库存预占用表")
public class WmsAuxiliaryOutPreBox extends BaseEntity {

    @ApiModelProperty(value = "出库单ID")
    private String outboundId;

    @ApiModelProperty(value = "出库单号")
    private String outboundNo;

    @ApiModelProperty(value = "库存项ID")
    private String boxItemId;

    @ApiModelProperty(value = "箱号/托盘号")
    private String boxCode;

    @ApiModelProperty(value = "容器类型(1-料箱 2-托盘)")
    private Integer containerType;

    @ApiModelProperty(value = "货架编码")
    private String shelfCode;

    @ApiModelProperty(value = "格口编码")
    private String gridCode;

    @ApiModelProperty(value = "合约号")
    private String contractNo;

    @ApiModelProperty(value = "款号")
    private String styleNo;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料规格/型号")
    private String materialSpec;

    @ApiModelProperty(value = "物料颜色")
    private String materialColor;

    @ApiModelProperty(value = "物料颜色编码")
    private String materialColorCode;

    @ApiModelProperty(value = "物料属性(1-辅料 2-机物料)")
    private Integer materialProperty;

    @ApiModelProperty(value = "物料单位")
    private String materialUnit;

    @ApiModelProperty(value = "预占用数量")
    private BigDecimal reservedQuantity;

    @ApiModelProperty(value = "预占用时的原库存数量")
    private BigDecimal originalStockQuantity;

    @ApiModelProperty(value = "原库存数量")
    private BigDecimal originalQuantity;

    @ApiModelProperty(value = "待处理数量")
    private BigDecimal pendingQuantity;

    @ApiModelProperty(value = "实际出库数量")
    private BigDecimal actualQuantity;

    @ApiModelProperty(value = "存储位置")
    private String storageLocation;

    @ApiModelProperty(value = "入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inboundTime;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "出库类型(3-领料出库 5-采购退货出库)")
    private Integer outboundType;

    @ApiModelProperty(value = "预占用状态(1-已预占用 2-已确认出库 3-已取消)")
    private Integer reservationStatus;

    @ApiModelProperty(value = "预占用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reservationTime;

    @ApiModelProperty(value = "确认出库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmTime;

    @ApiModelProperty(value = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelTime;

    @ApiModelProperty(value = "操作人员")
    private String operator;

    @ApiModelProperty(value = "备注")
    private String remark;

    // 预占用状态常量
    public static class ReservationStatus {
        public static final int RESERVED = 1; // 已预占用
        public static final int CONFIRMED = 2; // 已确认出库
        public static final int CANCELLED = 3; // 已取消
    }
}
