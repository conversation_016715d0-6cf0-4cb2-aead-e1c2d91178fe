package com.tgvs.wms.business.modules.machineMaterials.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName( "wms_machine_out_detail_record" )
@Data
public class WmsMachineOutDetailRecord extends BaseEntity {
    private static final long serialVersionUID =  1125167523697984290L;

    /**
     * 出库单号
     */
    @TableField(value = "out_store_number")
    private String outStoreNumber;

    /**
     * 物料编码
     */
    @TableField(value = "material_code")
    private String materialCode;

    /**
     * 物料名称
     */
//    @TableField(value = "material_name")
//    private String materialName;

    /**
     * 出库数量
     */
    @TableField(value = "out_quantity")
    private Integer outQuantity;

    /**
     * 容器号
     */
    @TableField(value = "box_no")
    private String boxNo;

    /**
     * 状态：1.待拣货；2.已完成拣货
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 格号
     */
    @TableField(value = "grid_no")
    private Integer gridNo;

    /**
     * 唯一标识符
     */
    @TableField(value = "object_id")
    @ApiModelProperty("唯一标识符")
    private String objectId;

    /**
     * 是否删除：0.正常；1.删除
     */
    @TableField(value = "delete_flag")
    @ApiModelProperty("是否删除")
    private Integer deleteFlag;

    /**
     * 任务状态：0：未生成任务；1.已生成任务
     */
    @TableField(value = "task_status")
    @ApiModelProperty("任务状态：0：未生成任务；1.已生成任务")
    private Integer taskStatus;

    /**
     * 推送状态：1.未推送，2.已推送
     */
    @TableField(value = "push_status")
    @ApiModelProperty("推送状态：1.未推送，2.已推送")
    private Integer pushStatus;

    /**
     * 容器类型：1.料箱；2.料架
     */
    @TableField(value = "container_type")
    @ApiModelProperty("容器类型：1.料箱；2.料架")
    private Integer containerType;
}
