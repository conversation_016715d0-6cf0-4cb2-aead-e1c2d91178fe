package com.tgvs.wms.business.modules.machineMaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机物料盘点查询VO
 */
@Data
@Schema(description = "机物料盘点查询参数")
public class MachineInventoryQueryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private String id;
    
    @Schema(description = "盘点单号")
    private String inStoreNumber;
    
    @Schema(description = "物料编码")
    private String materialCode;
    
    @Schema(description = "物料名称")
    private String materialName;
    
    @Schema(description = "盘点状态")
    private Integer inventoryStatus;
    
    @Schema(description = "盘点负责人")
    private String inventoryPerson;
    
    @Schema(description = "开始盘点日期")
    private Date beginInventoryDate;
    
    @Schema(description = "结束盘点日期")
    private Date endInventoryDate;
    
    @Schema(description = "开始创建时间")
    private Date beginCreateTime;
    
    @Schema(description = "结束创建时间")
    private Date endCreateTime;
} 