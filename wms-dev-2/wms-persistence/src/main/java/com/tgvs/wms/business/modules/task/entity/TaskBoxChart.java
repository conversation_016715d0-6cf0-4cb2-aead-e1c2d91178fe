package com.tgvs.wms.business.modules.task.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.common.annotation.Excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("wms_task_box")
@ApiModel(value = "wms_task_box对象", description = "作业任务")
public class TaskBoxChart implements Serializable {
    @Excel(name = "", width = 15.0D)
    @ApiModelProperty("任务类型")
    private String name;

    @Excel(name = "", width = 15.0D)
    @ApiModelProperty("数量")
    private Integer total;
}
