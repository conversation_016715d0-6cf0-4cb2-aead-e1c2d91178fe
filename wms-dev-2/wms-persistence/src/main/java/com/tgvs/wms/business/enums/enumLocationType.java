package com.tgvs.wms.business.enums;

import com.tgvs.wms.common.util.oConvertUtils;

public enum enumLocationType {
    location(Integer.valueOf(0), "location", "货位"),
    inlevelline(Integer.valueOf(1), "inlevelline", "为层间线缓存位入库口"),
    outlevelline(Integer.valueOf(2), "outlevelline", "为层间线缓存位出库口");

    private Integer value;

    private String code;

    private String text;

    enumLocationType(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumLocationType getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumLocationType val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumLocationType toEnum(Integer Value) {
        for (enumLocationType e : values()) {
            if (e.getValue().equals(Value))
                return e;
        }
        return null;
    }
}
