package com.tgvs.wms.business.modules.bd.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.boot.convert.DataSizeUnit;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_point_router")
@ApiModel(value = "wms_point_router对象", description = "路由配置管理")
@Data
public class PointRouter extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "源节点", width = 15.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("源节点")
    private String fromPoint;

    @Excel(name = "目的节点", width = 15.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("目的节点")
    private String toPoint;

    @Excel(name = "源楼层", width = 15.0D)
    @ApiModelProperty("源楼层")
    private Integer fromFloor;

    @Excel(name = "目的楼层", width = 15.0D)
    @ApiModelProperty("目的楼层")
    private Integer toFloor;

    @Excel(name = "源区域", width = 15.0D)
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("源区域")
    private String fromArea;

    @Excel(name = "目的区域", width = 15.0D)
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("目的区域")
    private String toArea;

    @Excel(name = "路由类型", width = 15.0D, dictTable = "wms_router_type", dicText = "text", dicCode = "value")
    @Dict(enumType = "Router", dicText = "text", dicCode = "value")
    @ApiModelProperty("路由类型")
    private Integer type;

    @Excel(name = "路由设备", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("路由设备")
    private String deviceno;

    @Excel(name = "路由节点", width = 15.0D, dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @Dict(dictTable = "wms_point", dicText = "point_name", dicCode = "point_no")
    @ApiModelProperty("路由节点")
    private String routerPoint;

    @Excel(name = "描述", width = 15.0D)
    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;

}
