package com.tgvs.wms.business.modules.songbu.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("mes_pubu_plan_sub")
@ApiModel(value = "mes_pubu_plan_sub对象", description = "铺床计划子表")
@Data
public class MesPubuPlanSubLocal implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "拉布单号", width = 15.0D)
    @ApiModelProperty("拉布单号")
    private String billNo;

    @Excel(name = "布匹编号", width = 15.0D)
    @ApiModelProperty("布匹编号")
    private String clothNo;

    @Excel(name = "合约号", width = 15.0D)
    @ApiModelProperty("合约号")
    private String contractNo;

    @Excel(name = "缸号", width = 15.0D)
    @ApiModelProperty("缸号")
    private String vatNo;

    @Excel(name = "对应铺床", width = 15.0D)
    @ApiModelProperty("对应铺床")
    private String latheNo;

    @Excel(name = "铺布计划", width = 15.0D)
    @ApiModelProperty("铺布计划")
    private String pubuPlan;

    @Excel(name = "计划时间", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划时间")
    private Date planTime;

    @Excel(name = "发布更新时间", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("发布更新时间")
    private Date publishTime;

    @Excel(name = "完成标志", width = 15.0D)
    @ApiModelProperty("完成标志")
    private Integer completed;

    @Excel(name = "计划执行顺序号", width = 15.0D)
    @ApiModelProperty("计划执行顺序号")
    private Integer planSort;

    @Excel(name = "布匹高度", width = 15.0D)
    @ApiModelProperty("布匹高度")
    private Integer height;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "松布完成", width = 15.0D)
    @ApiModelProperty("松布完成")
    private Integer isSongbu;
}
