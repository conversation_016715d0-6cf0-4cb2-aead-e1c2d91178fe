package com.tgvs.wms.business.modules.storage.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.storage.entity.CacheLocationRowBase;

public interface CacheLocationRowBaseMapper extends BaseMapper<CacheLocationRowBase> {
    boolean updatePlanDateById(@Param("et") CacheLocationRowBase paramCacheLocationRowBase);
}
