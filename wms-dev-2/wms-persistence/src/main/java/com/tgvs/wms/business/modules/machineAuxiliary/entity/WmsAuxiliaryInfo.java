package com.tgvs.wms.business.modules.machineAuxiliary.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 辅料基础信息表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wms_auxiliary_info")
public class WmsAuxiliaryInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 分类
     */
    private String category;

    /**
     * 单位
     */
    private String unit;

    /**
     * 容积（箱容量）
     */
    private Integer capacity;

    /**
     * 优先料箱类型
     */
    private Integer priorityContainerType;

    /**
     * 物料属性（0自身属性，1款式属性）
     */
    private Integer materialType;

    /**
     * 物料类型
     */
    private  Integer materialCategory;

    /**
     * 是否入智能仓
     */
    private Integer isStore;

    /**
     * 备注
     */
    private String remark;
}
