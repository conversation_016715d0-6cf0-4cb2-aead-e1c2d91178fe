package com.tgvs.wms.business.modules.middleware.entity;


import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("mes_songbu_plan")
@ApiModel(value = "mes_songbu_plan对象", description = "铺布计划")
//@DS("sqlserver")
@Data
public class MesSongbuPlan implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "布匹编号", width = 15.0D)
    @ApiModelProperty("布匹编号")
    private String clothno;

    @Excel(name = "铺布计划编号", width = 15.0D)
    @ApiModelProperty("铺布计划编号")
    private String pubuplan;

    @Excel(name = "合约号", width = 15.0D)
    @ApiModelProperty("合约号")
    private String contractno;

    @Excel(name = "缸号", width = 15.0D)
    @ApiModelProperty("缸号")
    private String vatno;

    @Excel(name = "对应铺床号", width = 15.0D)
    @ApiModelProperty("对应铺床号")
    private String latheno;

    @Excel(name = "松布计划日期", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("松布计划日期")
    private Date plandate;

    @Excel(name = "发布更新日期", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("发布更新日期")
    private Date updatedate;

    @Excel(name = "完成标志", width = 15.0D)
    @ApiModelProperty("完成标志")
    private Integer completed;

    @Excel(name = "计划执行顺序号", width = 15.0D)
    @ApiModelProperty("计划执行顺序号")
    private Integer plansort;

    @Excel(name = "拉布单号", width = 15.0D)
    @ApiModelProperty("拉布单号")
    private Integer billno;

    @Excel(name = "布匹高度", width = 15.0D)
    @ApiModelProperty("布匹高度")
    private Integer hight;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;
}
