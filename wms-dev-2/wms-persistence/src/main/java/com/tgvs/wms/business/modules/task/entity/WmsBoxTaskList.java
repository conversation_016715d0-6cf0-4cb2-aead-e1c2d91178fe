package com.tgvs.wms.business.modules.task.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@TableName ( "wms_box_task_list" )
@ApiModel(value = "wms_box_task_list对象", description = "任务表")
@Data
public class WmsBoxTaskList  extends BaseEntity {

	private static final long serialVersionUID =  5549248367282662182L;

	/**
	 * 任务号
	 */
	@TableField(value = "task_order")
	private  String taskOrder;

	/**
	 * 起始位置
	 */
   	@TableField(value = "from_site")
	private String fromSite;

	/**
	 * 目的位置
	 */
   	@TableField(value = "to_site")
	private String toSite;

	/**
	 * 容器类型：1.料箱；2.料架
	 */
   	@TableField(value = "box_type")
	private Integer boxType;

	/**
	 * 容器号
	 */
   	@TableField(value = "box_no")
	private String boxNo;

	/**
	 * 任务类型：0采购入库，1一般入库，2生产退料回库，3领料出库，4调拨出库，5采购退货出库，6指定出库，7.指定入库，8.紧急出库。9调拨入库 10.盘点出库
	 */
   	@TableField(value = "task_type")
	@Dict(dicCode = "taskType")
	private Integer taskType;

	/**
	 * 任务状态：0-创建，1.任务已下发，2.执行搬运，3.输送线运输， 4.完成
	 */
   	@TableField(value = "task_status")
	private Integer taskStatus;

	/**
	 * 优先级：数值越大，优先级越高
	 */
   	@TableField(value = "priority")
	private Integer priority;

	/**
	 * 推送状态：0.未推送，1.已推送，2.不用推送
	 */
	@TableField(value = "push_status")
	private Integer pushStatus;
	/**
	 * 设备编号
	 */
	@TableField(value = "device_no")
	private String deviceNo;

	/**
	 * 设备类型：1.CTU；2.AGV
	 */
	@TableField(value = "device_type")
	@Dict(dicCode = "taskType")
	private Integer deviceType;

	/**
	 * 是否删除：0.正常；1.删除
	 */
	@TableField(value = "delete_flag")
	@Dict(dicCode = "deleteFlag")
	private Integer deleteFlag;

	/**
	 * 物料类型：1.机物料；2.辅料
	 */
	@TableField(value = "material_type")
	@Dict(dicCode = "materialType")
	private Integer materialType;
}
