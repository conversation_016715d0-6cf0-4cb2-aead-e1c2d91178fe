package com.tgvs.wms.business.modules.dispatch.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_dispatch_agv")
@ApiModel(value = "wms_dispatch_agv对象", description = "AGV调度管理")
@Data
public class DispatchAgv extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "任务号", width = 15.0D)
    @ApiModelProperty("任务号")
    private Integer taskId;

    @Excel(name = "任务类型", width = 15.0D, dictTable = "wms_task_agv_type", dicText = "text", dicCode = "value")
    @Dict(enumType = "DispatchType", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务类型")
    private Integer type;

    @Excel(name = "源位置", width = 15.0D, dictTable = "wms_vritual_site", dicText = "site_code", dicCode = "site_code")
    @Dict(dictTable = "wms_vritual_site", dicText = "site_code", dicCode = "site_code")
    @ApiModelProperty("源位置")
    private String fromSite;

    @Excel(name = "目的位置", width = 15.0D, dictTable = "wms_vritual_site", dicText = "site_code", dicCode = "site_code")
    @Dict(dictTable = "wms_vritual_site", dicText = "site_code", dicCode = "site_code")
    @ApiModelProperty("目的位置")
    private String toSite;

    @Excel(name = "源区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("源区域")
    private String fromArea;

    @Excel(name = "目的区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("目的区域")
    private String toArea;

    @Excel(name = "任务状态", width = 15.0D, dictTable = "wms_dispatch_state", dicText = "text", dicCode = "value")
    @Dict(dictTable = "wms_dispatch_state", dicText = "text", dicCode = "value")
    @ApiModelProperty("任务状态")
    private Integer state;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "松布架号", width = 15.0D)
    @ApiModelProperty("松布架号")
    private String dollyNo;

    @Excel(name = "松布架层数", width = 15.0D, dicCode = "dolly_layers_type")
    @Dict(dicCode = "dolly_layers_type")
    @ApiModelProperty("松布架层数")
    private Integer dollyLayers;

    @Excel(name = "余料标记", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("余料标记")
    private Integer scrapFlag;

    @Excel(name = "空车标记", width = 15.0D, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty("空车标记")
    private Integer dollyflag;

    @Excel(name = "异常描述", width = 15.0D)
    @ApiModelProperty("异常描述")
    private String errmsg;

    @Excel(name = "调度明细", width = 15.0D)
    @ApiModelProperty("调度明细")
    private String content;

    @Excel(name = "铺布计划", width = 15.0D)
    @ApiModelProperty("铺布计划")
    private String pubuplan;

    @Excel(name = "计划时间", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划时间")
    private Date plandate;

    @Excel(name = "拉布单号", width = 15.0D)
    @ApiModelProperty("拉布单号")
    private String billno;

    @Excel(name = "计划序号", width = 15.0D)
    @ApiModelProperty("计划序号")
    private Integer plansort;

    @Excel(name = "铺床号", width = 15.0D)
    @ApiModelProperty("铺床号")
    private String latheno;

    @Excel(name = "缸号", width = 15.0D)
    @ApiModelProperty("缸号")
    private String vatno;

    @Excel(name = "布匹编码", width = 15.0D)
    @ApiModelProperty("布匹编码")
    private String clothNo;

    @Excel(name = "布匹高度", width = 15.0D, dicCode = "pallet_height")
    @Dict(dicCode = "pallet_height")
    @ApiModelProperty("布匹高度")
    private Integer height;

    @Excel(name = "MES发布更新日期", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("MES发布更新日期")
    private Date updatedate;

    @Excel(name = "调度执行对象", width = 15.0D)
    @ApiModelProperty("调度执行对象")
    private String dispatcher;

    @Excel(name = "合约号", width = 15.0D)
    @ApiModelProperty("合约号")
    private String contractNo;

    @Excel(name = "订单编号", width = 15.0D)
    @ApiModelProperty("订单编号")
    private String bookNo;

    @Excel(name = "异常代码", width = 15.0D)
    @ApiModelProperty("异常代码")
    private String batch;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "更新时间", width = 20.0D, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date endTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;

    @Excel(name = "用时", width = 15.0D)
    @ApiModelProperty("用时")
    @TableField(exist = false)
    private Integer useTime;
}
