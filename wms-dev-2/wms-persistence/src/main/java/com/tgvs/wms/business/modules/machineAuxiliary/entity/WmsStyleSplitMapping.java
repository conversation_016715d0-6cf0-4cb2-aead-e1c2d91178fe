package com.tgvs.wms.business.modules.machineAuxiliary.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tgvs.wms.base.entity.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 款式拆分映射表实体
 * 记录预装表数据拆分为款式属性的映射关系
 */
@Data
@TableName("wms_style_split_mapping")
public class WmsStyleSplitMapping extends BaseEntity {
    
    /**
     * 源预装记录ID
     * 关联预装表的具体记录
     */
    @TableField("source_prebox_item_id")
    private String sourcePreboxItemId;
    
    /**
     * 源物料编码
     * 来源物料的唯一编码标识
     */
    @TableField("source_material_code")
    private String sourceMaterialCode;
    
    /**
     * 源合约号
     * 原始合约的编号
     */
    @TableField("source_contract_no")
    private String sourceContractNo;
    
    /**
     * 源款号
     * 原始款式的编号
     */
    @TableField("source_item_no")
    private String sourceItemNo;
    
    /**
     * 源原始数量
     * 拆分前的原始数量
     */
    @TableField("source_original_qty")
    private Integer sourceOriginalQty;
    
    /**
     * 拆分PO号
     * 拆分后生成的采购订单号
     */
    @TableField("split_po_no")
    private String splitPoNo;
    
    /**
     * 拆分款式颜色
     * 拆分后的款式颜色属性
     */
    @TableField("split_style_color")
    private String splitStyleColor;
    
    /**
     * 拆分物料规格
     * 拆分后的物料规格型号
     */
    @TableField("split_material_model")
    private String splitMaterialModel;
    
    /**
     * 拆分物料颜色
     * 拆分后的物料颜色属性
     */
    @TableField("split_material_color")
    private String splitMaterialColor;
    
    /**
     * 拆分入库数量
     * 拆分后实际入库的数量
     */
    @TableField("split_inbound_qty")
    private Integer splitInboundQty;
    
    /**
     * 容器类型
     * 1-料箱 2-托盘
     */
    @TableField("container_type")
    private Integer containerType;
    
    /**
     * 容器编号
     * 料箱或托盘的唯一编号
     */
    @TableField("box_no")
    private String boxNo;
    
    /**
     * 关联的入库单ID
     * 对应的入库单据标识
     */
    @TableField("inbound_id")
    private String inboundId;
    
    /**
     * 拆分时间
     * 执行拆分操作的时间
     */
    @TableField("split_time")
    private Date splitTime;
    
    /**
     * 操作员ID
     * 执行拆分操作的用户ID
     */
    @TableField("operator_id")
    private String operatorId;
    
    /**
     * 状态
     * 1-有效 0-无效
     */
    @TableField("status")
    private Integer status;

    /**
     * 删除标志
     * 0-存在 1-删除
     */
    @TableField("delete_flag")
    private Integer deleteFlag;
} 