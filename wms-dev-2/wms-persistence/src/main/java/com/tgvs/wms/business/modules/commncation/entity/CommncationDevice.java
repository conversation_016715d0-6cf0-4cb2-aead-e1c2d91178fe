package com.tgvs.wms.business.modules.commncation.entity;

import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_commncation_device")
@ApiModel(value = "wms_commncation_device对象", description = "通讯设备配置")
@Data
public class CommncationDevice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "设备编号", width = 15.0D)
    @ApiModelProperty("设备编号")
    private String deviceno;

    @Excel(name = "设备名称", width = 15.0D)
    @ApiModelProperty("设备名称")
    private String devicename;

    @Excel(name = "所属系统", width = 15.0D, dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @Dict(dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @ApiModelProperty("所属系统")
    private String sysid;

    @Excel(name = "IP地址", width = 15.0D)
    @ApiModelProperty("IP地址")
    private String ipaddress;

    @Excel(name = "端口", width = 15.0D)
    @ApiModelProperty("端口")
    private Integer port;

    @Excel(name = "序列号", width = 15.0D)
    @ApiModelProperty("序列号")
    private String license;

    @Excel(name = "是否监控", width = 15.0D)
    @ApiModelProperty("是否监控")
    private Integer monitorcontroller;

    @Excel(name = "监控间隔", width = 15.0D)
    @ApiModelProperty("监控间隔")
    private Integer monitorinterval;

    @Excel(name = "重连次数", width = 15.0D)
    @ApiModelProperty("重连次数")
    private Integer monitormaxretrycount;

    @Excel(name = "超时时间", width = 15.0D)
    @ApiModelProperty("超时时间")
    private Integer timeout;

    @Excel(name = "设备类型", width = 15.0D, dicCode = "device_type")
    @Dict(dicCode = "device_type")
    @ApiModelProperty("设备类型")
    private Integer devicetype;

    @Excel(name = "描述", width = 15.0D)
    @ApiModelProperty("描述")
    private String description;

    @Excel(name = "消息类别", width = 15.0D, dicCode = "msg_format")
    @Dict(dicCode = "msg_format")
    @ApiModelProperty("消息类别")
    private Integer msgtype;

    @Excel(name = "返回的消息", width = 15.0D)
    @ApiModelProperty("返回的消息")
    private String msg;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;

    @Excel(name = "状态", width = 15.0D, dicCode = "connect_status")
    @Dict(enumType = "ConnectStatus", dicText = "text", dicCode = "value")
    @ApiModelProperty("状态")
    private Integer state;

    @Excel(name = "异常代码", width = 15.0D)
    @ApiModelProperty("异常代码")
    private String errorCode;
}
