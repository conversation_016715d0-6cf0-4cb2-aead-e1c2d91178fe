<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.task.mapper.TaskBoxMapper">

	<update id="updateTreeNodeStatus" parameterType="java.lang.String">
		update wms_task_box set has_child = #{status} where id = #{id}
	</update>

	<select id="selectListCount" resultType="com.tgvs.wms.business.modules.task.entity.TaskBoxChart">
		<!--select type as name,count(id) as total from wms_task_box GROUP BY type ORDER BY total desc-->
		SELECT count(*) as total,11 as name from wms_task_box_history where create_time>CURRENT_DATE() and type=1 and box_empty=1/*空箱入库*/
		union
		SELECT count(*) as total,12 as name from wms_task_box_history where create_time>CURRENT_DATE() and type=1 and box_empty=2/*实箱入库*/
		union
		SELECT count(*) as total,13 as name from wms_task_box_history where create_time>CURRENT_DATE() and type=5 /*料箱输送*/
		union
		SELECT count(*) as total,21 as name from wms_task_box_history where create_time>CURRENT_DATE() and type!=1 and type!=5 and box_empty=1 and priority = 60 /*空箱出库*/
		union
		SELECT count(*) as total,22 as name from wms_task_box_history where create_time>CURRENT_DATE() and type!=1 and type!=5 and box_empty=2/*实箱出库*/
		UNION
		SELECT count(*) as total,23 as name from wms_task_box_history where create_time>CURRENT_DATE() and type!=1 and type!=5 and box_empty=1 and priority = 10 /*空箱补给*/
		union
		SELECT count(*) as total,31 as name from wms_robot_dispatch_history where create_time>CURRENT_DATE() and type=2001 and pellet_type=4 and  to_site like '%00B%'/*松布匹数*/
		UNION
		SELECT count(*) as total,32 as name from wms_robot_dispatch_history where create_time>CURRENT_DATE() and type=3002 and pellet_type=4 and to_site like '%00C%' /*铺布匹数*/
		UNION
		SELECT count(*) as total,33 as name from wms_robot_dispatch_history where create_time>CURRENT_DATE() and type=1002 and pellet_type=3  /*余布收集*/
		UNION
		SELECT count(*) as total,34 as name from wms_robot_dispatch_history where create_time>CURRENT_DATE() and type=2002 and pellet_type=3 /*余布处理*/
		UNION
		SELECT count(*) as total,41 as name from wms_robot_dispatch_history where create_time>CURRENT_DATE() /*搬运托板*/
		UNION
		SELECT count(*) as total,51 as name from wms_agv_dolly_history where create_time>CURRENT_DATE() /*调度松布架*/
		UNION
		SELECT count(*) as total,98 as name from wms_stock where box_empty=1/*空箱库存*/
		/*UNION
		SELECT count(*) as total,99 as name from wms_stock where box_empty=2实箱库存*/
	</select>

	<select id="selectListCountByDate" resultType="com.tgvs.wms.business.modules.task.entity.TaskBoxChart">
		<!--select type as name,count(id) as total from wms_task_box GROUP BY type ORDER BY total desc-->
		SELECT count(*) as total,11 as name from wms_task_box_history where  create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type=1 and box_empty=1/*空箱入库*/
		union
		SELECT count(*) as total,12 as name from wms_task_box_history where  create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type=1 and box_empty=2/*实箱入库*/
		union
		SELECT count(*) as total,13 as name from wms_task_box_history where create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type=5 /*料箱输送*/
		union
		SELECT count(*) as total,21 as name from wms_task_box_history where  create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type!=1 and type!=5 and box_empty=1 and priority = 60/*空箱出库*/
		union
		SELECT count(*) as total,22 as name from wms_task_box_history where create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type!=1 and type!=5 and box_empty=2/*实箱出库*/
		UNION
		SELECT count(*) as total,23 as name from wms_task_box_history wherecreate_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type!=1 and type!=5 and box_empty=1 and priority = 10 /*空箱补给*/
		union
		SELECT count(*) as total,31 as name from wms_robot_dispatch_history where  create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type=2001 and pellet_type=4 and  to_site like '%00B%'/*松布匹数*/
		UNION
		SELECT count(*) as total,32 as name from wms_robot_dispatch_history where  create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type=3002 and pellet_type=4 and to_site like '%00C%' /*铺布匹数*/
		UNION
		SELECT count(*) as total,33 as name from wms_robot_dispatch_history where create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type=1002 and pellet_type=3  /*余布收集*/
		UNION
		SELECT count(*) as total,34 as name from wms_robot_dispatch_history where create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} and type=2002 and pellet_type=3 /*余布处理*/
		UNION
		SELECT count(*) as total,41 as name from wms_robot_dispatch_history where create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} /*搬运托板*/
		UNION
		SELECT count(*) as total,51 as name from wms_agv_dolly_history where create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd} /*调度松布架*/

	</select>

</mapper>