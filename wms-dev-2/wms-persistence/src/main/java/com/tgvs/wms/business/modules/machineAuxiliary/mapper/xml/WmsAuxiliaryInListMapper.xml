<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.machineAuxiliary.mapper.WmsAuxiliaryInListMapper">

    <!-- 为DTO添加明确的ResultMap -->
    <resultMap id="AuxiliaryInBoundDtoMap" type="com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryInBoundDto">
        <result column="inStoreNumber" property="inStoreNumber"/>
        <result column="taskType" property="taskType"/>
        <result column="status" property="status"/>
        <result column="remarks" property="remarks"/>
        <result column="createBy" property="createBy"/>
        <result column="createTime" property="createTime"/>
        <result column="updateBy" property="updateBy"/>
        <result column="updateTime" property="updateTime"/>
        <result column="deleteFlag" property="deleteFlag"/>
        <result column="id" property="id"/>
        <result column="operationType" property="operationType"/>
        <result column="contractNo" property="contractNo"/>
        <result column="itemNo" property="itemNo"/>
        <result column="reqListId" property="reqListId"/>
        <result column="materialCode" property="materialCode"/>
        <result column="materialName" property="materialName"/>
        <result column="materialColor" property="materialColor"/>
        <result column="materialColorCode" property="materialColorCode"/>
        <result column="materialModel" property="materialModel"/>
        <result column="quantity" property="quantity"/>
        <result column="materialUnit" property="materialUnit"/>
        <result column="priority" property="priority"/>
        <result column="materialType" property="materialType"/>
        <result column="isStore" property="isStore"/>
    </resultMap>

    <!-- Custom select for combined DTO with pagination -->
    <select id="selectDtoPage" resultMap="AuxiliaryInBoundDtoMap">
        SELECT
            h.in_store_number       AS inStoreNumber,
            h.task_type             AS taskType,
            h.status                AS status,
            h.remarks               AS remarks,
            h.create_by             AS createBy,
            h.create_time           AS createTime,
            h.update_by             AS updateBy,
            h.update_time           AS updateTime,
            h.delete_flag           AS deleteFlag,
            d.id                    AS id,
            d.operation_type        AS operationType,
            d.contract_no           AS contractNo,
            d.item_no               AS itemNo,
            d.req_list_id           AS reqListId,
            d.material_code         AS materialCode,
            d.material_name         AS materialName,
            d.material_color        AS materialColor,
            d.material_color_code   AS materialColorCode,
            d.material_model        AS materialModel,
            d.quantity              AS quantity,
            d.material_unit         AS materialUnit,
            d.priority              AS priority,
            i.material_type         AS materialType,
            i.is_store            AS isStore
        FROM
            wms_auxiliary_inbound h
                INNER JOIN
            wms_auxiliary_detail d ON h.in_store_number = d.ref_number
                LEFT JOIN
            wms_auxiliary_info i ON d.material_code = i.material_code
        ${ew.customSqlSegment}
    </select>
    
    <!-- 自定义条件查询辅料入库列表 -->
    <select id="selectWmsAuxiliaryInListCustom" resultType="com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInBound">
        select id, contract_no, item_no, material_code, material_name, material_colour, material_model,
               in_quantity, priority, create_by, create_time, update_by, update_time, delete_flag
        from wms_auxiliary_inbound
        <where>
            <if test="contractNo != null and contractNo != ''">
                AND contract_no = #{contractNo}
            </if>
            <if test="itemNo != null and itemNo != ''">
                AND item_no = #{itemNo}
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND material_code = #{materialCode}
            </if>
            <if test="materialName != null and materialName != ''">
                AND material_name like concat('%', #{materialName}, '%')
            </if>
            <if test="materialColour != null and materialColour != ''">
                AND material_colour = #{materialColour}
            </if>
            <if test="materialModel != null and materialModel != ''">
                AND material_model = #{materialModel}
            </if>
            <if test="inQuantity != null">
                AND in_quantity = #{inQuantity}
            </if>
            <if test="priority != null">
                AND priority = #{priority}
            </if>
            AND delete_flag = 0
        </where>
        order by create_time desc
    </select>
    
    <!-- 批量逻辑删除辅料入库信息 -->
    <update id="deleteWmsAuxiliaryInListByIds">
        update wms_auxiliary_inbound set delete_flag = 1 
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    
</mapper>
