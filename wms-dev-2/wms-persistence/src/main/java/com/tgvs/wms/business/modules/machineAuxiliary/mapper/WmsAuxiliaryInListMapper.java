package com.tgvs.wms.business.modules.machineAuxiliary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tgvs.wms.business.modules.machineAuxiliary.dto.AuxiliaryInBoundDto;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryInBound;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 辅料入库列表Mapper接口
 * 对于基本的CRUD操作，直接使用MyBatis-Plus的BaseMapper提供的方法：
 * - 插入: insert
 * - 更新: updateById
 * - 删除: deleteById
 * - ID查询: selectById
 * - 列表查询: selectList
 * - 批量ID查询: selectBatchIds
 */
@Mapper
public interface WmsAuxiliaryInListMapper extends BaseMapper<WmsAuxiliaryInBound> {
    
    /**
     * 自定义条件查询辅料入库列表
     * 
     * @param wmsAuxiliaryInBound 查询条件
     * @return 辅料入库列表
     */
    List<WmsAuxiliaryInBound> selectWmsAuxiliaryInListCustom(WmsAuxiliaryInBound wmsAuxiliaryInBound);
    
    /**
     * 批量逻辑删除辅料入库信息
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteWmsAuxiliaryInListByIds(String[] ids);

    /**
     * 分页查询辅料入库头、明细及物料信息
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件包装器 (MyBatis-Plus风格)
     * @return 分页结果，包含组合后的 DTO
     */
    IPage<AuxiliaryInBoundDto> selectDtoPage(IPage<AuxiliaryInBoundDto> page, @Param("ew") QueryWrapper<AuxiliaryInBoundDto> queryWrapper);
}
