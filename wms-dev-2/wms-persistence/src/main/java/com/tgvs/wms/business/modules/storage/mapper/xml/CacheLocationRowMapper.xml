<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tgvs.wms.business.modules.storage.mapper.CacheLocationRowMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  wms_cache_location_row 
		WHERE
			 mainid = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.tgvs.wms.business.modules.storage.entity.CacheLocationRow">
		SELECT * 
		FROM  wms_cache_location_row
		WHERE
			 mainid = #{mainId} 	</select>
</mapper>
