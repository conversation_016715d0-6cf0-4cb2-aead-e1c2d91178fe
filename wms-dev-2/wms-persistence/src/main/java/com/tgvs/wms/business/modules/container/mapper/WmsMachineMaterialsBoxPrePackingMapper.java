package com.tgvs.wms.business.modules.container.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.container.entity.WmsMachineMaterialsBoxPrePacking;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmsMachineMaterialsBoxPrePackingMapper extends BaseMapper<WmsMachineMaterialsBoxPrePacking> {

    /**
     * 根据容器类型查询物料信息
     * @param containerType 查询条件
     * @return 返回物料列表，永不为null
     */
    List<WmsMachineMaterialsBoxPrePacking> selectMaterialCode(@Param("containerType") Integer containerType);

}
