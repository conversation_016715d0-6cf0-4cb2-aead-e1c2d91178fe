package com.tgvs.wms.business.modules.storage.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tgvs.wms.base.entity.BaseEntity;
import com.tgvs.wms.common.annotation.Dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName("wms_vritual_site")
@ApiModel(value = "wms_vritual_site对象", description = "缓存站点")
@Data
public class VritualSite extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键")
    private String id;

    @Excel(name = "站点编码", width = 15.0D)
    @ApiModelProperty("站点编码")
    private String siteCode;

    @Excel(name = "站点类型", width = 15.0D, dictTable = "wms_site_type", dicText = "text", dicCode = "value")
    @Dict(enumType = "VritualSiteType", dicText = "text", dicCode = "value")
    @ApiModelProperty("站点类型")
    private Integer type;

    @Excel(name = "所属系统", width = 15.0D, dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @Dict(dictTable = "wms_commncation_config", dicText = "sysname", dicCode = "sysid")
    @ApiModelProperty("所属系统")
    private String sysid;

    @Excel(name = "区域", width = 15.0D, dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @Dict(dictTable = "wms_base_area", dicText = "text", dicCode = "code")
    @ApiModelProperty("区域")
    private String area;

    @Excel(name = "智能设备", width = 15.0D, dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @Dict(dictTable = "wms_commncation_device", dicText = "deviceName", dicCode = "deviceNo")
    @ApiModelProperty("智能设备")
    private String robot;

    @Excel(name = "货位", width = 15.0D)
    @ApiModelProperty("货位")
    private Integer location;

    @Excel(name = "楼层", width = 15.0D)
    @ApiModelProperty("楼层")
    private Integer floor;

    @Excel(name = "优先级", width = 15.0D)
    @ApiModelProperty("优先级")
    private Integer priority;

    @Excel(name = "锁定", width = 15.0D, dicCode = "locked_status")
    @Dict(enumType = "LockedStatus", dicText = "text", dicCode = "value")
    @ApiModelProperty("锁定")
    private Integer locked;

    @Excel(name = "调度状态", width = 15.0D, dicCode = "dispatch_status")
    @Dict(dicCode = "dispatch_status")
    @ApiModelProperty("调度状态")
    private Integer dispatchState;

    @Excel(name = "站点状态", width = 15.0D, dicCode = "site_status")
    @Dict(enumType = "VritualSiteStatus", dicText = "text", dicCode = "value")
    @ApiModelProperty("站点状态")
    private Integer state;

    @Excel(name = "站点异常", width = 15.0D, dicCode = "error_status")
    @Dict(dicCode = "error_status")
    @ApiModelProperty("站点异常")
    private Integer error;

    @Excel(name = "异常信息", width = 15.0D)
    @ApiModelProperty("异常信息")
    private String errorCode;

    @Excel(name = "松布架号", width = 15.0D)
    @ApiModelProperty("松布架号")
    private String dollyNo;

    @Excel(name = "松布架层数", width = 15.0D, dicCode = "dolly_layers_type")
    @Dict(dicCode = "dolly_layers_type")
    @ApiModelProperty("松布架层数")
    private Integer dollyLayers;

    @Excel(name = "松布架类型", width = 15.0D, dicCode = "dolly_type")
    @Dict(dicCode = "dolly_type")
    @ApiModelProperty("松布架类型")
    private Integer dollyType;

    @Excel(name = "后续层数", width = 15.0D, dicCode = "dolly_layers_type")
    @Dict(dicCode = "dolly_layers_type")
    @ApiModelProperty("后续层数")
    private Integer singleqty;

    @Excel(name = "拉布单号", width = 15.0D)
    @ApiModelProperty("拉布单号")
    private String billno;

    @Excel(name = "铺布计划", width = 15.0D)
    @ApiModelProperty("铺布计划")
    private String pubuplan;

    @Excel(name = "铺布计划时间", width = 15.0D, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("铺布计划时间")
    private Date planDate;

    @Excel(name = "物料编码", width = 15.0D)
    @ApiModelProperty("物料编码")
    private String clothNo;

    @Excel(name = "订单编号", width = 15.0D)
    @ApiModelProperty("订单编号")
    private String orderCode;

    @Excel(name = "对应松布架操作类型", width = 15.0D)
    @ApiModelProperty("对应松布架操作类型")
    private Integer workType;

    @ApiModelProperty("创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @Excel(name = "删除状态", width = 15.0D)
    @ApiModelProperty("删除状态")
    private Integer delFlag;
}
