package com.tgvs.wms.business.modules.machineAuxiliary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.machineAuxiliary.entity.WmsAuxiliaryOutPreBox;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 库存预占用表 Mapper 接口
 */
@Mapper
public interface WmsAuxiliaryOutPreBoxMapper extends BaseMapper<WmsAuxiliaryOutPreBox> {

        /**
         * 根据出库单ID查询预占用记录
         * 
         * @param outboundIds 出库单ID列表
         * @return 预占用记录列表
         */
        List<WmsAuxiliaryOutPreBox> selectByOutboundIds(@Param("outboundIds") List<String> outboundIds);

        /**
         * 根据物料编码查询预占用数量
         * 
         * @param materialCodes      物料编码列表
         * @param excludeOutboundIds 排除的出库单ID列表
         * @return 预占用记录列表
         */
        List<WmsAuxiliaryOutPreBox> selectReservedByMaterialCodes(
                        @Param("materialCodes") List<String> materialCodes,
                        @Param("excludeOutboundIds") List<String> excludeOutboundIds);

        /**
         * 根据库存项ID查询预占用数量
         * 
         * @param boxItemIds 库存项ID列表
         * @param status     预占用状态
         * @return 预占用记录列表
         */
        List<WmsAuxiliaryOutPreBox> selectReservedByBoxItemIds(
                        @Param("boxItemIds") List<String> boxItemIds,
                        @Param("status") Integer status);

        /**
         * 批量更新预占用状态
         * 
         * @param outboundIds 出库单ID列表
         * @param oldStatus   原状态
         * @param newStatus   新状态
         * @return 更新记录数
         */
        int updateStatusByOutboundIds(
                        @Param("outboundIds") List<String> outboundIds,
                        @Param("oldStatus") Integer oldStatus,
                        @Param("newStatus") Integer newStatus);

        /**
         * 根据物料编码和合约信息查询预占用汇总
         * 
         * @param materialCode  物料编码
         * @param contractNo    合约号
         * @param styleNo       款号
         * @param materialColor 物料颜色
         * @param materialSpec  物料规格
         * @return 预占用汇总信息
         */
        @MapKey("materialCode")
        Map<String, Object> selectReservedSummaryByMaterial(
                        @Param("materialCode") String materialCode,
                        @Param("contractNo") String contractNo,
                        @Param("styleNo") String styleNo,
                        @Param("materialColor") String materialColor,
                        @Param("materialSpec") String materialSpec);

        /**
         * 查询即将超时的预占用记录
         * 
         * @param timeoutMinutes 超时分钟数
         * @param limit          限制查询数量
         * @return 即将超时的预占用记录列表
         */
        List<WmsAuxiliaryOutPreBox> selectExpiredReservations(
                        @Param("timeoutMinutes") Integer timeoutMinutes,
                        @Param("limit") Integer limit);

        /**
         * 根据出库单号查询预占用记录
         * 
         * @param outboundNo 出库单号
         * @return 预占用记录列表
         */
        List<WmsAuxiliaryOutPreBox> selectByOutboundNo(@Param("outboundNo") String outboundNo);

        /**
         * 查询库存项的预占用统计
         * 
         * @param boxItemIds 库存项ID列表
         * @return 预占用统计信息列表
         */
        @MapKey("boxItemId")
        List<Map<String, Object>> selectBoxItemReservationStat(@Param("boxItemIds") List<String> boxItemIds);
}