package com.tgvs.wms.business.modules.commncation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tgvs.wms.business.modules.commncation.entity.ReportTaskScheduleConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 出入库上报任务配置表 Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface ReportTaskScheduleConfigMapper extends BaseMapper<ReportTaskScheduleConfig> {

    /**
     * 查询所有启用的配置
     * @return 启用的配置列表
     */
    List<ReportTaskScheduleConfig> selectEnabledConfigs();

    /**
     * 根据任务类型查询配置
     * @param taskType 任务类型
     * @return 配置对象
     */
    ReportTaskScheduleConfig selectByTaskType(@Param("taskType") String taskType);

    /**
     * 根据任务类型查询启用的配置
     * @param taskType 任务类型
     * @return 配置对象
     */
    ReportTaskScheduleConfig selectEnabledByTaskType(@Param("taskType") String taskType);

    /**
     * 批量更新启用状态
     * @param ids 配置ID列表
     * @param isEnabled 启用状态
     * @return 更新的记录数
     */
    int batchUpdateEnabledStatus(@Param("ids") List<String> ids, @Param("isEnabled") Integer isEnabled);

    /**
     * 根据执行间隔查询配置
     * @param intervalMinutes 执行间隔（分钟）
     * @return 配置列表
     */
    List<ReportTaskScheduleConfig> selectByInterval(@Param("intervalMinutes") Integer intervalMinutes);

    /**
     * 查询需要执行的配置（根据时间间隔）
     * @param currentTime 当前时间戳
     * @return 需要执行的配置列表
     */
    List<ReportTaskScheduleConfig> selectConfigsToExecute(@Param("currentTime") Long currentTime);

    /**
     * 更新配置的最后执行时间
     * @param id 配置ID
     * @param lastExecuteTime 最后执行时间
     * @return 更新的记录数
     */
    int updateLastExecuteTime(@Param("id") String id, @Param("lastExecuteTime") Long lastExecuteTime);

    /**
     * 统计各任务类型的配置数量
     * @return 统计结果
     */
    List<java.util.Map<String, Object>> countByTaskType();

    /**
     * 查询配置详情（包含统计信息）
     * @param id 配置ID
     * @return 配置详情
     */
    java.util.Map<String, Object> selectConfigDetail(@Param("id") String id);

    /**
     * 软删除配置
     * @param ids 配置ID列表
     * @return 删除的记录数
     */
    int softDeleteByIds(@Param("ids") List<String> ids);

    /**
     * 恢复软删除的配置
     * @param ids 配置ID列表
     * @return 恢复的记录数
     */
    int restoreByIds(@Param("ids") List<String> ids);
} 