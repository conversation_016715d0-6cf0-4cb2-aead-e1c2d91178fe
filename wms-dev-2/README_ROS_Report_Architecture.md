# WMS出入库任务上报ROS系统 - 简化架构

## 概述

本文档描述了WMS系统向ROS系统上报出入库任务完成信息的简化架构。该架构移除了复杂的Redis事件机制，采用更直接和可控的方式处理任务上报。

## 系统架构

```
AGV任务完成 → 记录上报队列(基础信息) → 前端管理页面(查询+勾选) → 数据整合服务(构建ROS入参) → ROS接口调用
                                    ↑
                            定时调度器(根据配置自动处理)
```

## 核心组件

### 1. 任务完成记录（AgvHkService）

**位置**: `wms-service-business/src/main/java/com/tgvs/wms/business/wmsservice/server/AgvHkService.java`

**功能**: 
- AGV任务完成时自动调用`recordTaskToReportQueue()`方法
- 只记录需要上报的任务类型（0.采购入库，3.领料出库）
- 将基础任务信息存储到`TaskReportQueue`表

**关键代码**:
```java
// 在taskExecutionNotice方法的end分支中
recordTaskToReportQueue(wmsBoxTaskListList);
```

### 2. 上报队列实体（TaskReportQueue）

**位置**: `wms-persistence/src/main/java/com/tgvs/wms/business/modules/task/entity/TaskReportQueue.java`

**功能**:
- 存储待上报的任务基础信息
- 支持重试机制和状态管理
- 记录上报历史和错误信息

**核心字段**:
- `taskOrder`: 任务单号
- `taskType`: 任务类型（0,3）
- `reportType`: 上报类型（INBOUND/OUTBOUND）
- `reportStatus`: 上报状态（0待上报,1已上报,2失败,3取消）
- `retryCount`: 重试次数
- `errorMessage`: 错误信息

### 3. 数据整合服务（RosDataIntegrationService）

**位置**: `wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosDataIntegrationService.java`

**功能**:
- 根据上报队列记录，整合完整的ROS接口入参
- 分别处理入库和出库数据整合
- 验证数据完整性

**核心方法**:
- `buildRosReportData()`: 构建ROS接口入参
- `buildInboundData()`: 整合入库数据
- `buildOutboundData()`: 整合出库数据
- `validateReportData()`: 验证数据完整性

### 4. ROS上报服务（RosReportService）

**位置**: `wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosReportServiceImpl.java`

**功能**:
- 调用第三方ROS接口进行实际上报
- 支持单个和批量上报
- 处理重试和错误机制

**核心方法**:
- `reportSingleTask()`: 单个任务上报
- `reportBatchTasks()`: 批量任务上报
- `reportByType()`: 按类型批量上报
- `retryFailedTask()`: 重试失败任务

### 5. 手动上报控制器（ManualReportController）

**位置**: `wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/controller/ManualReportController.java`

**功能**:
- 提供前端操作界面的API接口
- 支持任务查询、筛选、批量操作
- 提供统计信息和连接检查

**核心接口**:
- `POST /manual-report/list`: 查询待上报任务列表
- `POST /manual-report/execute`: 手动上报选中任务
- `POST /manual-report/retry`: 重试失败任务
- `POST /manual-report/cancel`: 取消任务上报
- `GET /manual-report/statistics`: 获取统计信息

### 6. 定时调度器（ReportTaskScheduler）

**位置**: `wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/scheduler/ReportTaskScheduler.java`

**功能**:
- 根据配置表自动执行定时上报
- 支持不同间隔时间的批量处理
- 使用分布式锁防止重复执行

**配置表**: `ReportTaskScheduleConfig`
- 支持入库上报和出库上报配置
- 可设置执行间隔（5、10、15、30、60分钟）
- 可设置批处理数量（20、50、100）

### 7. 前端管理页面

**位置**: `wms-web/src/main/webapp/pages/system/manualReport/index.html`

**功能**:
- 可视化的任务上报队列管理
- 支持多条件查询和筛选
- 批量操作（上报、重试、取消）
- 实时统计信息展示
- ROS连接状态检查

## 业务流程

### 1. 任务完成记录流程

```
1. AGV任务完成 → AgvHkService.taskExecutionNotice() 
2. 判断任务类型 → 只处理taskType=0,3的任务
3. 创建上报队列记录 → TaskReportQueue.reportStatus=0
4. 记录基础信息 → taskOrder, taskType, boxNo, reportType等
```

### 2. 手动上报流程

```
1. 前端查询待上报任务 → ManualReportController.queryPendingTasks()
2. 用户勾选任务 → 获取taskReportIds列表
3. 提交上报请求 → ManualReportController.executeManualReport()
4. 数据整合 → RosDataIntegrationService.buildRosReportData()
5. 调用ROS接口 → RosReportService.reportBatchTasks()
6. 更新任务状态 → TaskReportQueue.reportStatus=1
```

### 3. 自动上报流程

```
1. 定时器触发 → ReportTaskScheduler.executeScheduledReports()
2. 检查配置 → ReportTaskScheduleConfig.isEnabled=1
3. 按类型上报 → RosReportService.reportByType()
4. 批量处理 → 根据batchSize分批执行
5. 更新执行时间 → ReportTaskScheduleConfig.lastExecuteTime
```

## 配置说明

### 1. ROS接口配置

在`RosReportServiceImpl`中配置ROS接口地址：
```java
// ROS接口配置（后续应从配置表获取）
private static final String ROS_INBOUND_URL = "http://ros-api.example.com/api/inbound/report";
private static final String ROS_OUTBOUND_URL = "http://ros-api.example.com/api/outbound/report";
```

### 2. 自动上报配置

在数据库表`wms_report_task_schedule_config`中配置：
- `task_type`: "入库上报" 或 "出库上报"
- `is_enabled`: 1启用，0禁用
- `interval_minutes`: 执行间隔（5、10、15、30、60分钟）
- `batch_size`: 每次处理数量（20、50、100）

### 3. 重试配置

在`TaskReportQueue`实体中：
- `max_retry_count`: 最大重试次数（默认3次）
- `next_retry_time`: 下次重试时间（指数退避策略）

## 监控和维护

### 1. 统计监控

通过`/manual-report/statistics`接口获取：
- 待上报任务数量
- 已上报任务数量
- 失败任务数量
- 成功率统计

### 2. 连接检查

通过`/manual-report/connection-status`接口：
- 检查ROS系统连接状态
- 测试入库和出库接口可用性

### 3. 错误处理

- 所有错误信息记录在`TaskReportQueue.error_message`字段
- 支持按错误类型统计和分析
- 失败任务支持手动重试

## 数据库结构

### TaskReportQueue表结构

```sql
CREATE TABLE `wms_task_report_queue` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `task_order` varchar(100) NOT NULL COMMENT '任务单号',
  `task_type` int(11) NOT NULL COMMENT '任务类型：0采购入库,3领料出库',
  `box_no` varchar(100) NOT NULL COMMENT '箱号/托盘号',
  `box_type` int(11) NOT NULL COMMENT '箱型：1料箱,2托盘',
  `material_type` int(11) DEFAULT NULL COMMENT '物料类型：1生产物料,2辅料',
  `report_type` varchar(20) NOT NULL COMMENT '上报类型：INBOUND/OUTBOUND',
  `report_status` int(11) NOT NULL DEFAULT '0' COMMENT '上报状态：0待上报,1已上报,2失败,3取消',
  `priority` int(11) NOT NULL DEFAULT '5' COMMENT '优先级：1最高,5普通,9最低',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int(11) NOT NULL DEFAULT '3' COMMENT '最大重试次数',
  `error_code` varchar(50) DEFAULT NULL COMMENT '错误代码',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `first_report_time` datetime DEFAULT NULL COMMENT '首次上报时间',
  `last_report_time` datetime DEFAULT NULL COMMENT '最后上报时间',
  `success_report_time` datetime DEFAULT NULL COMMENT '成功上报时间',
  `next_retry_time` datetime DEFAULT NULL COMMENT '下次重试时间',
  `delete_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标志：0正常,1删除',
  `version` int(11) NOT NULL DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_task_order` (`task_order`),
  KEY `idx_report_status` (`report_status`),
  KEY `idx_report_type` (`report_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WMS任务上报ROS队列表';
```

## 部署注意事项

1. **ROS接口地址配置**: 根据实际环境修改ROS接口URL
2. **定时任务配置**: 根据业务需求配置合适的执行间隔
3. **重试策略**: 根据网络环境调整重试次数和延迟时间
4. **监控告警**: 建议对失败率较高的情况设置告警
5. **数据备份**: 定期清理历史上报记录，避免数据量过大

## 优势

1. **架构简化**: 移除复杂的Redis事件机制，降低系统复杂度
2. **用户友好**: 提供可视化管理界面，操作人员可直接控制
3. **可靠性强**: 保持定时批量处理能力，确保任务不遗漏
4. **数据完整**: 强化数据整合环节，确保上报数据准确性
5. **易于维护**: 简化的架构更容易理解和维护

## 与之前版本的差异

1. **移除Redis Stream**: 不再使用Redis事件流机制
2. **移除混合服务**: 简化HybridReportService为单纯的ROS上报服务
3. **新增前端界面**: 提供可视化的任务管理界面
4. **强化数据整合**: 独立的数据整合服务确保数据质量
5. **简化配置**: 配置更加直观，易于操作人员理解 