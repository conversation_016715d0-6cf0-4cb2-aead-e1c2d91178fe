package com.tgvs.wms.socket.abb;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.Executor;

@Component
@Slf4j
public class AbbServer {

    @Autowired
    private AbbConfig abbConfig;

    @Autowired
    private Executor executor;

    public static Socket socketClient;

    public void startAbbSocket() throws IOException{
        log.info("启动abb服务器");
        try (ServerSocket server = new ServerSocket(abbConfig.getPort())) {
            while (true) {
                socketClient = server.accept();
                executor.execute(new AbbSocketThread(socketClient));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
