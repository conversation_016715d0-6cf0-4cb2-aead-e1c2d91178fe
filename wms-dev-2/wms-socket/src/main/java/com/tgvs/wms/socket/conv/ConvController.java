package com.tgvs.wms.socket.conv;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tgvs.wms.socket.util.SocketUtil;

import lombok.extern.slf4j.Slf4j;

@RequestMapping("conv")
@RestController
@Slf4j
public class ConvController {

    @RequestMapping("msg")
    public String sendConvMsg(@RequestBody String convMsg){

        log.info("收到信息:{}", convMsg);
        SocketUtil.sendMsg(ConvServer.clientSocket, convMsg);

        return "发送成功";
    }
}
