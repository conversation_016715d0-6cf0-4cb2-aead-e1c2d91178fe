package com.tgvs.wms.test;

import com.tgvs.wms.socket.common.BaseSocketThread;

import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;
import java.util.Scanner;

public class ClientSocketTest {
    //客户端发送信息
    public static void main(String[] args) {
        OutputStream out = null;
        Socket socket = null;
        try {
            //绑定到本地端口
            socket = new Socket("127.0.0.1", 9002);

            new Thread(new BaseSocketThread(socket)).start();

            //发送消息
            while (true) {
                out = socket.getOutputStream();
                //输入文字，从控制台输入
                Scanner san = new Scanner(System.in);
                String str = san.next();
                out.write(str.getBytes());
                out.flush();

//                //接收信息
//                InputStream in = socket.getInputStream();
//
//                byte bytes[] = new byte[2048];
//                int read = in.read(bytes);
//                String data = null;
//                if(read >0 ) {
//                    //客户端发来了数据
//                    byte[] dataByte = MfcServer.handlerByte(bytes, read);
//                    data = new String(dataByte);
//                }
//
//                System.out.println("来自服务器的数据:" + data);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (socket != null) {
                    socket.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
