<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wms</artifactId>
        <groupId>com.tvgs.wms</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wms-transaction</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>wms-transaction-mes</module>
        <module>wms-transaction-abb</module>
        <module>wms-transaction-mfc</module>
        <module>wms-transaction-lift</module>
        <module>wms-transaction-base</module>
        <module>wms-transaction-agv</module>
        <module>wms-transaction-conv</module>
    </modules>


    <dependencyManagement>
        <dependencies>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tvgs.wms</groupId>
            <artifactId>wms-persistence</artifactId>
        </dependency>
    </dependencies>

</project>