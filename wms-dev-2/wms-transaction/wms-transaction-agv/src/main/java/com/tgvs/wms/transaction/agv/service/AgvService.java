package com.tgvs.wms.transaction.agv.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.business.modules.mqlog.entity.MqLog;
import com.tgvs.wms.business.modules.storage.entity.Shelf;
import com.tgvs.wms.business.modules.task.entity.WmsBoxTaskList;
import com.tgvs.wms.business.modules.task.mapper.WmsBoxTaskListMapper;
import com.tgvs.wms.common.constant.Result;
import com.tgvs.wms.common.util.DateUtils;
import com.tgvs.wms.common.util.ShiroUtils;
import com.tgvs.wms.common.util.http.HttpUtils;
import com.tgvs.wms.transaction.service.MainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * AGV调度服务
 */
@Slf4j
@Component
public class AgvService {

    // 类实例引用
    public static AgvService agvService;

    // 系统标识常量
    public static final String sysname = "HikRoboot";



    // 线程相关
    protected static Thread threadRcsTask;
    protected static boolean agvWorking = true;
    static ConcurrentLinkedQueue<String> queue = new ConcurrentLinkedQueue<>();
    public static Lock agvlock = new ReentrantLock();

    // AGV系统配置参数
    private String url = "http://localhost:8080"; // 默认RCS系统URL，应从配置中加载
    private String token = ""; // 默认为空，应从配置中加载
    private boolean rcsWorking = true; // RCS工作状态

    // PLC连接参数
    private String plcIp = "***********"; // 默认PLC IP，应从配置中加载
    private int plcPort = 102; // 默认PLC端口，应从配置中加载
    private String plcName = "S7-1200"; // 默认PLC名称，应从配置中加载


    /** CTU放箱位状态，表示是否允许AGV在此处放箱 */
    public static final String REDIS_CTU_PLACE_BOX_STATUS_KEY = "ctu:place_box:status";

    /** 料箱释放状态，控制输送线上料箱的释放 */
    public static final String REDIS_BOX_RELEASE_STATUS_KEY = "box:release:status";

    /** 拣货工位料箱条码，存储当前拣货工位上料箱的编码 */
    public static final String REDIS_LIFT_BOX_CODE_KEY = "lift:box_code";


    /** 拣货工位释放状态，控制输送线上料箱的释放 */
    public static final String REDIS_LIFT_BOX_RELEASE_STATUS_KEY = "lift:box:release:status";

    /** 拣货工位请求释放信号，表示是否需要释放拣货工位 */
    public static final String REDIS_WMS_REQUEST_LIFT_RELEASE_KEY = "wms:request:lift:release";

    /** 预调度成功的料箱列表，用于与ConvService协同 */
    public static final String REDIS_PRETASK_SUCCESS_BOX_KEY = "pretask:success:box:";

    /** 预调度成功的料箱有效期（秒） */
    private static final int PRETASK_SUCCESS_BOX_EXPIRE = 300; // 1小时

    @Autowired
    private WmsBoxTaskListMapper wmsBoxTaskListMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 安全获取Redis值的辅助方法，避免空指针异常
     * @param key Redis键
     * @return 值或空字符串（如果为null）
     */
    private String safeGetRedisValue(String key) {
        try {
            if (stringRedisTemplate != null && key != null) {
                String value = stringRedisTemplate.opsForValue().get(key);
                return value != null ? value : "";
            }
            return "";
        } catch (Exception e) {
            log.error("获取Redis值异常: {}", key, e);
            return "";
        }
    }

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        log.info("AGV服务初始化开始...");
        agvService = this;

        // 验证关键组件是否可用
        if (stringRedisTemplate == null) {
            log.warn("Redis模板未注入，部分功能可能不可用");
        }

    }

    /**
     * 启动服务
     * 简化版本，确保服务状态正确设置，RCS任务处理线程正常启动
     */
    public void Start() {
        // 防止重复启动
        if (agvWorking && threadRcsTask != null && threadRcsTask.isAlive()) {
            log.info("{} 服务已在运行中，无需重复启动", sysname);
            return;
        }

        log.info("{} 服务开始启动...", sysname);

        // 设置运行标志
        agvWorking = true;
        rcsWorking = true;

        // 更新服务状态
        try {
            updateServiceStatus(1);
        } catch (Exception e) {
            log.error("更新服务状态失败", e);
        }

        // 启动RCS任务处理线程
        if (threadRcsTask == null || !threadRcsTask.isAlive()) {
            threadRcsTask = new Thread(this::processRcsTasks);
            threadRcsTask.setName("RCS-Task-Thread");
            threadRcsTask.setDaemon(true);
            threadRcsTask.start();
            log.info("RCS任务处理线程已启动");
        }

        log.info("{} 服务启动完成", sysname);
    }

    /**
     * 停止服务
     * 简化版本，确保资源正确释放，服务状态正确设置
     */
    public void Stop() {
        // 防止重复停止
        if (!agvWorking) {
            log.info("{} 服务已经停止，无需重复操作", sysname);
            return;
        }

        log.info("{} 服务正在停止...", sysname);

        // 设置停止标志
        rcsWorking = false;
        agvWorking = false;

        // 更新服务状态
        try {
            updateServiceStatus(0);
        } catch (Exception e) {
            log.error("更新服务停止状态失败", e);
        }

        // 等待线程自然结束（因为while循环会检查agvWorking标志）
        if (threadRcsTask != null && threadRcsTask.isAlive()) {
            try {
                // 给线程最多3秒时间自行结束
                threadRcsTask.join(3000);
                if (threadRcsTask.isAlive()) {
                    log.warn("RCS任务线程未能在预定时间内停止");
                }
            } catch (InterruptedException e) {
                log.error("等待RCS任务线程结束时被中断", e);
                Thread.currentThread().interrupt();
            }
        }

        log.info("{} 服务已停止", sysname);
    }

    /**
     * 安全更新服务状态的辅助方法
     * @param status 状态值：0-停止，1-运行
     */
    private void updateServiceStatus(int status) {
        try {
            // 直接使用server_status方法，它内部有异常处理
            MainService.server_status(sysname, status);
        } catch (Exception e) {
            log.error("调用服务状态更新方法异常", e);
        }
    }

    /**
     * RCS任务处理线程
     */
    void processRcsTasks() {
        while (agvWorking) {
            agvlock.lock();
            try {
//                OutBoundTask();
                // 处理定时任务逻辑
                executeAgvTask();
                taskCompletionReport();
            } catch (Exception e) {
                log.error("处理RCS任务异常", e);
            } finally {
                agvlock.unlock();
            }

            try {
                Thread.sleep(10000L); // 每10秒执行一次
            } catch (InterruptedException e) {
                log.error("RCS任务线程被中断", e);
            }
        }
    }

    /**
     * 把装箱入库结果推送到OA
     */
    private void taskCompletionReport() {

        try {
            String url="http://localhost:8081/api/task/taskBox/requestOA";
            LambdaQueryWrapper<WmsBoxTaskList> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WmsBoxTaskList::getTaskStatus, 5);
            queryWrapper.ge(WmsBoxTaskList::getPushStatus, 0);
            queryWrapper.eq(WmsBoxTaskList::getDeleteFlag, 0);
            queryWrapper.eq(WmsBoxTaskList::getMaterialType,1);
            queryWrapper.orderByAsc(WmsBoxTaskList::getCreateTime);
            List<WmsBoxTaskList> taskList = MainService.mainServiceutil.wmsBoxTaskListMapper.selectList(queryWrapper);

            if(taskList != null && !taskList.isEmpty()){
                for (WmsBoxTaskList item : taskList) {
                    try {
                        if (stringRedisTemplate != null && item.getTaskOrder() != null) {
                            String taskCode = item.getTaskOrder();
                            boolean hasKey = stringRedisTemplate.hasKey(taskCode);
                            if (hasKey) {
                                String responseJson = safeGetRedisValue(item.getTaskOrder());
                                log.info("任务 {} 的Redis返回值: {}", item.getTaskOrder(), responseJson);
                                if (responseJson != null && !responseJson.isEmpty()) {
                                    String requestJson = HttpUtils.sendPost( url,responseJson);
                                    if(!requestJson.isEmpty()) {
                                        boolean statuas = Boolean.parseBoolean(requestJson);
                                        if (statuas) {
                                            stringRedisTemplate.delete(taskCode);
                                        } else {
                                            log.error("请求OA失败");
                                        }
                                    }else{
                                        log.error("API接口空响应："+url+"；请求参数："+responseJson);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理任务{}时出现异常", item.getTaskOrder(), e);
                    }
                }
            } else {
                log.info("没有待上报的完成任务");
            }
        } catch (Exception e) {
            log.error("任务完成报告处理异常", e);
        }
    }


    // 执行辅料出入库AGV调度任务
    private void executeAgvTask() {
        log.info("开始执行辅料出入库AGV调度任务");
        try {
            // 处理出库任务
            processOutboundTasks();

            // 处理入库任务
            processInboundTasks();
        } catch (Exception e) {
            log.error("执行辅料出入库AGV调度任务异常", e);
        }
    }


//    private void OutBoundTask()
//    {
//        LambdaQueryWrapper<Shelf> outboundQueryWrapper = new LambdaQueryWrapper<>();
////        outboundQueryWrapper.lt(Shelf::getUpdateTime,"2025-05-12 11:55:18");
//        outboundQueryWrapper.eq(Shelf::getLocked,0);
//        outboundQueryWrapper.eq(Shelf::getDeleteFlag,5);
//        List<Shelf> shelfList=MainService.mainServiceutil.shelfMapper.selectList(outboundQueryWrapper);
//        for(Shelf item:shelfList)
//        {
//            WmsBoxTaskList wmsBoxTaskList = new WmsBoxTaskList();
//            String orderType = item.getBoxNo()+"-"+"2025-06-09";
//            wmsBoxTaskList.setTaskOrder(orderType);
//            wmsBoxTaskList.setBoxNo(item.getBoxNo());
//            wmsBoxTaskList.setBoxType(1);
//            wmsBoxTaskList.setTaskStatus(0);
//            wmsBoxTaskList.setTaskType(6);
//            wmsBoxTaskList.setToSite("RGZQ3A01011");//料箱出库口
//            wmsBoxTaskList.setFromSite(item.getCode());
//            wmsBoxTaskList.setPriority(11);
//            wmsBoxTaskList.setPushStatus(2);
//            wmsBoxTaskList.setCreateTime(DateUtils.getDate());
//            wmsBoxTaskList.setCreateBy("admin");
//            wmsBoxTaskListMapper.insert(wmsBoxTaskList);
//            item.setLocked(1);
//            MainService.mainServiceutil.shelfMapper.updateById(item);
//        }
//    }
    /**
     * 处理出库任务
     */
    private void processOutboundTasks() {
        // 查询待出库任务
        LambdaQueryWrapper<WmsBoxTaskList> outboundQueryWrapper = new LambdaQueryWrapper<>();
        outboundQueryWrapper.eq(WmsBoxTaskList::getTaskStatus, 0); // 待处理状态
        outboundQueryWrapper.in(WmsBoxTaskList::getTaskType, 3,4,5,6,8,10); // 出库任务
        outboundQueryWrapper.eq(WmsBoxTaskList::getDeleteFlag, 0);
        outboundQueryWrapper.orderByDesc(WmsBoxTaskList::getPriority).orderByDesc(WmsBoxTaskList::getCreateTime);
        List<WmsBoxTaskList> outboundTasks = MainService.mainServiceutil.wmsBoxTaskListMapper.selectList(outboundQueryWrapper);

        if (outboundTasks == null || outboundTasks.isEmpty()) {
            log.info("未发现待处理的出库任务");
            return;
        }
        log.info("发现{}个待处理的出库任务", outboundTasks.size());
        // 根据容器类型分组处理
        Map<Integer, List<WmsBoxTaskList>> outboundTaskMap = outboundTasks.stream()
                .collect(Collectors.groupingBy(WmsBoxTaskList::getBoxType));

        // 处理托盘类型的出库任务（实时下发）
        List<WmsBoxTaskList> palletTasks = outboundTaskMap.getOrDefault(2, Collections.emptyList());
        if (!palletTasks.isEmpty()) {
            log.info("发现{}个托盘类型的出库任务，准备下发", palletTasks.size());
            boolean success = submitTasks(palletTasks,2);
            if (success) {
                log.info("托盘出库任务下发成功");
                //设置潜伏车位置站点锁定状态
              

            }
        }

        // 处理料箱类型的出库任务
        List<WmsBoxTaskList> boxTasks = outboundTaskMap.getOrDefault(1, Collections.emptyList());
        if (!boxTasks.isEmpty()) {
            log.info("发现{}个料箱类型的出库任务", boxTasks.size());
            // 获取配置的任务阈值数量，默认为7
            int taskThreshold = 7;
            try {
                String configValue = safeGetRedisValue("config:box:outbound:threshold");
                if (!configValue.isEmpty()) {
                    taskThreshold = Integer.parseInt(configValue);
                }
            } catch (Exception e) {
                log.warn("获取料箱出库任务阈值配置失败，使用默认值7", e);
            }

            // 判断任务数量是否满足阈值
            if (boxTasks.size() >= taskThreshold)
            {
                List<WmsBoxTaskList> taskList=boxTasks.subList(0, taskThreshold);
                boolean status=groupTask(taskList);
                if(status)
                {
                    log.info("料箱出库任务数量{}已达到阈值{}，准备下发", boxTasks.size(), taskThreshold);
                    boolean success = submitTasks(taskList, 2);
                    if (success) {
                        //更新任务状态为已下发
                        for (WmsBoxTaskList task : boxTasks.subList(0, taskThreshold)) {
                            task.setTaskStatus(1); // 假设1表示已下发状态
                            wmsBoxTaskListMapper.updateById(task);
                        }
                        log.info("料箱出库任务下发成功");
                    }
                }else{
                    log.error("组任务下发失败："+JSON.toJSONString(taskList));
                }
            }
            else
            {
                // 判断最早任务的等待时间是否超过阈值
                WmsBoxTaskList earliestTask = boxTasks.get(0);
                long waitTimeMinutes = (new Date().getTime() - earliestTask.getCreateTime().getTime()) / (60 * 1000);

                // 获取配置的最大等待时间，默认为30分钟
                int maxWaitTime = 1;
                try {
                    String configValue = safeGetRedisValue("config:box:outbound:maxwaittime");
                    if (!configValue.isEmpty()) {
                        maxWaitTime = Integer.parseInt(configValue);
                    }
                } catch (Exception e) {
                    log.warn("获取料箱出库最大等待时间配置失败，使用默认值30分钟", e);
                }

                if (waitTimeMinutes >= maxWaitTime) {
                    log.info("最早的料箱出库任务等待时间{}分钟已超过阈值{}分钟，准备下发", waitTimeMinutes, maxWaitTime);
                    boolean status=groupTask(boxTasks.subList(0, Math.min(taskThreshold, boxTasks.size())));
                    if(status) {
                        boolean success = submitTasks(boxTasks.subList(0, Math.min(taskThreshold, boxTasks.size())), 2);
                        if (success) {
                            //将下发任务写入redis
                            log.info("料箱出库任务下发成功");
                            //更新任务状态为已下发
                            for (WmsBoxTaskList task : boxTasks.subList(0, Math.min(taskThreshold, boxTasks.size()))) {
                                task.setTaskStatus(1); // 假设1表示已下发状态
                                wmsBoxTaskListMapper.updateById(task);
                            }
                        }
                    }else{
                        log.error("组任务下发失败："+JSON.toJSONString(boxTasks.subList(0, Math.min(taskThreshold, boxTasks.size()))));
                    }
                } else {
                    log.info("料箱出库任务数量{}未达到阈值{}，且最早任务等待时间{}分钟未超过{}分钟，暂不下发",
                            boxTasks.size(), taskThreshold, waitTimeMinutes, maxWaitTime);
                }
            }
        }
    }

    /**
     * 处理入库任务
     */
    private void processInboundTasks() {
        // 查询待入库任务
        LambdaQueryWrapper<WmsBoxTaskList> inboundQueryWrapper = new LambdaQueryWrapper<>();
        inboundQueryWrapper.eq(WmsBoxTaskList::getTaskStatus, 0); // 待处理状态
        inboundQueryWrapper.in(WmsBoxTaskList::getTaskType, 0,1,2,7); // 入库任务类型
        inboundQueryWrapper.eq(WmsBoxTaskList::getDeleteFlag, 0);
        inboundQueryWrapper.orderByAsc(WmsBoxTaskList::getCreateTime);
        List<WmsBoxTaskList> inboundTasks = MainService.mainServiceutil.wmsBoxTaskListMapper.selectList(inboundQueryWrapper);

        if (inboundTasks == null || inboundTasks.isEmpty()) {
            log.info("未发现待处理的入库任务");
            return;
        }

        // 根据物料类型分组处理
        Map<Integer, List<WmsBoxTaskList>> inboundTaskMap = inboundTasks.stream()
                .collect(Collectors.groupingBy(WmsBoxTaskList::getBoxType));

        // 处理托盘类型的入库任务（实时下发）
        List<WmsBoxTaskList> palletTasks = inboundTaskMap.getOrDefault(2, Collections.emptyList());
        if (!palletTasks.isEmpty()) {
            log.info("发现{}个托盘类型的入库任务，准备下发", palletTasks.size());
            if(submitTasks(palletTasks,1)){
                log.info("托盘入库任务下发成功");
                
            }else{
                log.error("托盘入库任务下发失败");
            }
        }

        // 处理料箱类型的入库任务
        List<WmsBoxTaskList> boxTasks = inboundTaskMap.getOrDefault(1, Collections.emptyList());
        if (!boxTasks.isEmpty()) {
            log.info("发现{}个料箱类型的入库任务", boxTasks.size());

            // 首要条件：按照配置的任务数量下发
            int taskThreshold = 5;
            try {
                String configValue = safeGetRedisValue("config:box:inbound:threshold");
                if (!configValue.isEmpty()) {
                    taskThreshold = Integer.parseInt(configValue);
                }
            } catch (Exception e) {
                log.warn("获取料箱入库任务阈值配置失败，使用默认值5", e);
            }

            // 按照配置的任务数量下发
            int batchSize = Math.min(taskThreshold, boxTasks.size());
            if (batchSize > 0) {
                log.info("按照配置下发{}个料箱入库任务", batchSize);
                List<WmsBoxTaskList> batchTasks = boxTasks.subList(0, batchSize);

                // 为每个料箱执行预调度并记录结果
                for (WmsBoxTaskList task : batchTasks) {
                    String boxCode = task.getBoxNo();
                    if (boxCode != null && !boxCode.isEmpty()) {
                        //调用预调度接口之前，判断redis中当前拣货工位PLC传感器读取的料箱号是否和当前任务的料箱号一致
                        String liftBoxCode = safeGetRedisValue(REDIS_LIFT_BOX_CODE_KEY);
                        if (!boxCode.equals(liftBoxCode)) {
                            log.info("当前拣货工位PLC传感器读取的料箱号 {} 和任务的料箱号 {} 不一致，跳过预调度", liftBoxCode, boxCode);
                            continue;
                        }
                        boolean preTaskSuccess = preTask(boxCode);
                        if (preTaskSuccess) {
                            log.info("料箱 {} 预调度成功，记录到Redis", boxCode);
                            //更新任务状态为已下发
                            task.setTaskStatus(1); // 假设1表示已下发状态
                            wmsBoxTaskListMapper.updateById(task);
                            // 将预调度成功的料箱信息写入Redis
                            String taskInfo = JSON.toJSONString(task);
                            stringRedisTemplate.opsForValue().set(
                                    REDIS_PRETASK_SUCCESS_BOX_KEY + boxCode,
                                    taskInfo,
                                    PRETASK_SUCCESS_BOX_EXPIRE,
                                    TimeUnit.SECONDS
                            );

                            // 记录通信日志
                            log.debug("PreTaskSuccess,料箱预调度成功并写入Redis:{}", boxCode);
                        } else {
                            log.error("料箱 {} 预调度失败", boxCode);
                        }
                    }
                }

            }

            // 获取拣货工位当前状态（作为次要处理）
            String liftBoxCode = "";
            String liftBoxReleaseStatus = "0";

            try {
                // 从Redis获取拣货工位信息
                liftBoxCode = safeGetRedisValue(REDIS_LIFT_BOX_CODE_KEY);
                liftBoxReleaseStatus = safeGetRedisValue(REDIS_LIFT_BOX_RELEASE_STATUS_KEY);

                log.info("当前拣货工位状态: 料箱编号={}, 释放状态={}",
                        liftBoxCode.isEmpty() ? "无" : liftBoxCode,
                        "1".equals(liftBoxReleaseStatus) ? "允许释放" : "不允许释放");

                // 如果拣货工位有料箱且未释放，检查是否有匹配的任务
                if (!liftBoxCode.isEmpty() && !"1".equals(liftBoxReleaseStatus)) {
                    // 首先检查此料箱是否已经预调度成功并记录在Redis中
                    String cachedTaskInfo = safeGetRedisValue(REDIS_PRETASK_SUCCESS_BOX_KEY + liftBoxCode);

                    if (cachedTaskInfo != null && !cachedTaskInfo.isEmpty()) {
                        log.info("拣货工位料箱 {} 已预调度成功，将设置允许释放", liftBoxCode);

                        // 设置允许释放信号
                        stringRedisTemplate.opsForValue().set(REDIS_WMS_REQUEST_LIFT_RELEASE_KEY, "1");
                        log.info("已设置拣货工位料箱 {} 允许释放", liftBoxCode);

                        // 记录通信日志
                        insertMQ(sysname, "CONV", liftBoxCode, "LiftBoxRelease",
                                "设置拣货工位料箱允许释放: " + liftBoxCode);

                        return;
                    }

                    // 如果Redis中没有预调度记录，检查是否有匹配的任务并执行预调度
                    boolean hasMatchingTask = false;

                    for (WmsBoxTaskList task : boxTasks) {
                        if (liftBoxCode.equals(task.getBoxNo())) {
                            log.info("发现拣货工位料箱 {} 匹配入库任务 {}", liftBoxCode, task.getTaskOrder());
                            hasMatchingTask = true;

                            // 调用预调度接口
                            boolean preTaskSuccess = preTask(liftBoxCode);
                            if (preTaskSuccess) {
                                log.info("拣货工位料箱 {} 预调度成功，将设置允许释放", liftBoxCode);
                                // 更新任务状态为已下发
                                task.setTaskStatus(1); // 假设1表示已下发状态
                                wmsBoxTaskListMapper.updateById(task);
                                // 将预调度成功的料箱信息写入Redis
                                String taskInfo = JSON.toJSONString(task);
                                stringRedisTemplate.opsForValue().set(
                                        REDIS_PRETASK_SUCCESS_BOX_KEY + liftBoxCode,
                                        taskInfo,
                                        PRETASK_SUCCESS_BOX_EXPIRE,
                                        TimeUnit.SECONDS
                                );

                                // 设置允许释放信号
                                stringRedisTemplate.opsForValue().set(REDIS_WMS_REQUEST_LIFT_RELEASE_KEY, "1");
                                log.info("已设置拣货工位料箱 {} 允许释放", liftBoxCode);

                                // 记录通信日志
                                insertMQ(sysname, "CONV", liftBoxCode, "LiftBoxRelease",
                                        "设置拣货工位料箱允许释放: " + liftBoxCode);
                            } else {
                                log.warn("拣货工位料箱 {} 预调度失败", liftBoxCode);
                            }

                            break;
                        }
                    }

                    if (!hasMatchingTask) {
                        log.info("拣货工位料箱 {} 没有匹配的入库任务", liftBoxCode);
                    }
                }
            } catch (Exception e) {
                log.error("处理拣货工位状态异常: {}", e.getMessage());
            }
        }
    }

    /**
     * 预调度任务
     * @param boxCode 料箱编号
     * @return 是否成功
     */
    private boolean preTask(String boxCode) {
        if (boxCode == null || boxCode.isEmpty()) {
            return false;
        }

        String url = "http://localhost:8087/task/taskBox/preTask";

        try {
            // 构建预调度请求
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("siteCode", "0296450AA0300137");

            // 调用预调度接口
            String responseJson = HttpUtils.sendPost(url, JSON.toJSONString(requestMap));
            log.debug("料箱 {} 预调度响应: {}", boxCode, responseJson);

            if (responseJson == null || responseJson.isEmpty()) {
                log.error("预调度API返回空响应");
                return false;
            }

            Result parsedResult = JSON.parseObject(responseJson, Result.class);
            return parsedResult != null && (parsedResult.getCode() == 200 ||
                    "200".equals(String.valueOf(parsedResult.getCode())) ||
                    "SUCCESS".equals(String.valueOf(parsedResult.getCode())));

        } catch (Exception e) {
            log.error("预调度异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 提交任务到系统
     * @param tasks 需要提交的任务列表
     * @return 是否成功提交
     */
    private boolean submitTasks(List<WmsBoxTaskList> tasks,int taskType) {
        if (tasks == null || tasks.isEmpty()) {
            return false;
        }
        String url="http://localhost:8087/task/taskBox/outboundTask";//默认出库
        if (taskType == 1) {//入库
            url = "http://localhost:8087/task/taskBox/addemptyinbound";
        }


        try {

            String responseJson = HttpUtils.sendPost(url, JSON.toJSONString(tasks));
            // 解析响应
            Result parsedResult = JSON.parseObject(responseJson, Result.class);
            if (parsedResult == null) {
                return false;
            }
            //如果不等于200，Success，则返回false
            if(parsedResult.getCode().equals("500")){
                log.error("任务下发API返回失败状态. 响应: {}", responseJson);
                return false;
            }
            return true;
            
        } catch (Exception e) {
            log.error("任务下发异常: {}", e.getMessage());
            return false;
        }
    }


    private boolean groupTask(List<WmsBoxTaskList> tasks){
        boolean status=false;
            try{
                String url="http://localhost:8087/task/taskBox/outboundGroupTask";//默认出库
                String responseJson = HttpUtils.sendPost(url, JSON.toJSONString(tasks));
                Result parsedResult = JSON.parseObject(responseJson, Result.class);
                if (parsedResult == null)
                {
                    log.error("组任务下发API返回空响应");
                    return false;
                }else{
                   if(parsedResult.getCode().equals(200))
                   {
                       status=true;
                   }else{
                       log.error("输出组任务下发失败原因："+JSON.toJSONString(parsedResult));
                   }
                }
            }catch (Exception ex){
                log.error("组任务下发异常："+ex.toString());
            }
        return status;
    }

    //3,出库完成结束任务并更新库位信息和任务历史记录

    //出库拣货，拣货完成确认解绑，更新容器库存数据，生成入库任务，
    // 并把拣货明细上报或者写入redis（辅料需要存入上报记录表，待人工手动上报或者定时任务轮询上报；机物料可以直接上报）

    //机物料入库上报
    //机物料在生成出入库任务时把任务号作为key，上报内容作为value存入redis
    //定时任务轮询任务表，查出任务结束但并未上报的任务，用该任务号去redis查出需要上报的内容
    // 调用上报接口，调用成功后更新任务表的是否上报状态并清除redis缓存


    //辅料入库上报
    //辅料装箱入库绑定时把装箱明细存入上报记录表，人工手动上报或者定时任务轮询上报


    /**
     * 记录通信日志
     */
    public void insertMQ(String sender, String reciver, String taskid, String faunctionName, String text) {
        try {
            if (faunctionName != null) {
                Date nowtime = new Date();
                MqLog mqLog = new MqLog();
                mqLog.setSender(sender);
                mqLog.setReceive(reciver);
                mqLog.setType(faunctionName);
                mqLog.setMqNumber((taskid != null && !taskid.isEmpty()) ? taskid : UUID.randomUUID().toString());
                mqLog.setSysid(sysname);
                mqLog.setMsg(text);
                mqLog.setMqTime(nowtime);
                mqLog.setCreateTime(nowtime);
                mqLog.setDevice(sysname);

                MainService.mainServiceutil.mqLogMapper.insert(mqLog);
            }
        } catch (Exception e) {
            log.error("插入MQ日志异常 - 发送方: {}, 接收方: {}, 任务ID: {}, 功能: {}", sender, reciver, taskid, faunctionName, e);
        }
    }
}
