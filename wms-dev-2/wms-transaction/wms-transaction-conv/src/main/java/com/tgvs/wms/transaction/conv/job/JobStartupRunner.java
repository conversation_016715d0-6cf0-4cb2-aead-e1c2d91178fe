package com.tgvs.wms.transaction.conv.job;

import com.tgvs.wms.transaction.conv.config.SchedulerConfig;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalTime;

@Slf4j
@Component
public class JobStartupRunner implements CommandLineRunner {
    @Autowired
    SchedulerConfig schedulerConfig;
    
    private static String JOB_NAME = "job_name_conv";
    private static String JOB_GROUP_NAME = "job_group_name_conv";
    private static String TRIGGER_NAME = "trigger_name_conv";
    private static String TRIGGER_GROUP_NAME = "trigger_group_name_conv";

    // 优化：根据业务场景调整定时任务频率
    // 早上8:00-8:30期间每10秒执行一次，其他时间每59秒执行一次
    private static String JOB_CRON_CONV = "0/50 * * * * ?";

    // 新增：业务时间配置
    private static final LocalTime WORK_START_TIME = LocalTime.of(8, 0);  // 早上8点
    private static final LocalTime WORK_END_TIME = LocalTime.of(8, 30);   // 早上8点30分

    @Override
    public void run(String... args) {
        Scheduler scheduler;
        try {
            scheduler = schedulerConfig.scheduler();
            TriggerKey triggerKey = TriggerKey.triggerKey(TRIGGER_NAME, TRIGGER_GROUP_NAME);
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            if (null == trigger) {
                Class clazz = ConvQuartzJob.class;
                JobDetail jobDetail = JobBuilder.newJob(clazz).withIdentity(JOB_NAME, JOB_GROUP_NAME).build();
                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(JOB_CRON_CONV);
                trigger = TriggerBuilder.newTrigger().withIdentity(TRIGGER_NAME, TRIGGER_GROUP_NAME)
                        .withSchedule(scheduleBuilder.withMisfireHandlingInstructionDoNothing())
                        .build();
                scheduler.scheduleJob(jobDetail, trigger);
                
                log.info("输送线定时任务已创建 - 支持每日断电重启场景");
                log.info("工作时间: {} - {} (重连频率增强)", WORK_START_TIME, WORK_END_TIME);
            }

            scheduler.start();
        } catch (Exception e) {
            log.error("创建定时任务异常", e);
        }
    }
    
    /**
     * 新增：判断当前是否在工作启动时间段
     * @return true 如果在早上8:00-8:30之间
     */
    public static boolean isWorkStartupTime() {
        LocalTime now = LocalTime.now();
        return now.isAfter(WORK_START_TIME) && now.isBefore(WORK_END_TIME);
    }
    
    /**
     * 新增：判断当前是否在工作时间
     * @return true 如果在工作时间段
     */
    public static boolean isWorkingHours() {
        LocalTime now = LocalTime.now();
        return now.isAfter(LocalTime.of(8, 0)) && now.isBefore(LocalTime.of(18, 0));
    }
    
    /**
     * 新增：判断是否应该重置重连计数器
     * 每天早上8点重置，确保能重新尝试连接
     * @return true 如果应该重置重连计数器
     */
    public static boolean shouldResetReconnectCounter() {
        LocalTime now = LocalTime.now();
        // 在早上8:00-8:05之间重置重连计数器
        return now.isAfter(WORK_START_TIME) && now.isBefore(WORK_START_TIME.plusMinutes(5));
    }
    
    /**
     * 新增：获取当前时间段的重连间隔
     * @return 重连间隔（毫秒）
     */
    public static long getReconnectInterval() {
        if (isWorkStartupTime()) {
            return 3000; // 工作启动时间：3秒间隔，快速重连
        } else if (isWorkingHours()) {
            return 10000; // 工作时间：10秒间隔
        } else {
            return 30000; // 非工作时间：30秒间隔，减少资源消耗
        }
    }
    
    /**
     * 新增：获取当前时间段的最大重连次数
     * @return 最大重连次数
     */
    public static int getMaxReconnectAttempts() {
        if (isWorkStartupTime()) {
            return 100; // 工作启动时间：允许更多重连次数
        } else if (isWorkingHours()) {
            return 20;  // 工作时间：适中的重连次数
        } else {
            return 5;   // 非工作时间：保持原有限制
        }
    }
}