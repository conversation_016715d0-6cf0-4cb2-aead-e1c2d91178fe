# �Ƿ�ʹ��properties��Ϊ���ݴ洢
org.quartz.jobStore.useProperties=false
# ���ݿ��б������ǰ׺
org.quartz.jobStore.tablePrefix=QRTZ_
# �Ƿ���һ����Ⱥ���ǲ��Ƿֲ�ʽ������
org.quartz.jobStore.isClustered=false
# ��Ⱥ������ڣ���λΪ���룬�����Զ�������ʱ�䡣��ĳһ���ڵ�崻���ʱ�������ڵ�ȴ���ú�ʼִ������
org.quartz.jobStore.clusterCheckinInterval=5000
# ��λΪ���룬��Ⱥ�еĽڵ��˳����ٴμ������ʱ����
org.quartz.jobStore.misfireThreshold=1000
# ������뼶��
org.quartz.jobStore.txIsolationLevelReadCommitted=true
# �洢�������������
org.quartz.jobStore.class=org.springframework.scheduling.quartz.LocalDataSourceJobStore
# ʹ�õ�Delegate����
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
# ��Ⱥ��������һ����ȺҪ����ͬ������
org.quartz.scheduler.instanceName=ClusterQuartz
# �ڵ�������������Զ��塣AUTO�����Զ�����
org.quartz.scheduler.instanceId=AUTO
# rmiԶ��Э���Ƿ񷢲�
org.quartz.scheduler.rmi.export=false
# rmiԶ��Э������Ƿ񴴽�
org.quartz.scheduler.rmi.proxy=false
# �Ƿ�ʹ���û����Ƶ����񻷾�����ִ������
org.quartz.scheduler.wrapJobExecutionInUserTransaction=false

