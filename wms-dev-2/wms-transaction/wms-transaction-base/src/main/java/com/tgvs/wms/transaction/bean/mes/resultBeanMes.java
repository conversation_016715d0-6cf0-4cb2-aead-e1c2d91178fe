package com.tgvs.wms.transaction.bean.mes;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class resultBeanMes {
    @J<PERSON><PERSON>ield(name = "TaskID")
    private String TaskID;

    private String status;

    private String errorcode;

    private String errorinfo;

    private BoxSite data;

    public void setTaskID(String TaskID) {
        this.TaskID = TaskID;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setErrorcode(String errorcode) {
        this.errorcode = errorcode;
    }

    public void setErrorinfo(String errorinfo) {
        this.errorinfo = errorinfo;
    }

    public void setData(BoxSite data) {
        this.data = data;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof resultBeanMes))
            return false;
        resultBeanMes other = (resultBeanMes)o;
        if (!other.canEqual(this))
            return false;
        Object this$TaskID = getTaskID(), other$TaskID = other.getTaskID();
        if ((this$TaskID == null) ? (other$TaskID != null) : !this$TaskID.equals(other$TaskID))
            return false;
        Object this$status = getStatus(), other$status = other.getStatus();
        if ((this$status == null) ? (other$status != null) : !this$status.equals(other$status))
            return false;
        Object this$errorcode = getErrorcode(), other$errorcode = other.getErrorcode();
        if ((this$errorcode == null) ? (other$errorcode != null) : !this$errorcode.equals(other$errorcode))
            return false;
        Object this$errorinfo = getErrorinfo(), other$errorinfo = other.getErrorinfo();
        if ((this$errorinfo == null) ? (other$errorinfo != null) : !this$errorinfo.equals(other$errorinfo))
            return false;
        Object this$data = getData(), other$data = other.getData();
        return !((this$data == null) ? (other$data != null) : !this$data.equals(other$data));
    }

    protected boolean canEqual(Object other) {
        return other instanceof resultBeanMes;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $TaskID = getTaskID();
        result = result * 59 + (($TaskID == null) ? 43 : $TaskID.hashCode());
        Object $status = getStatus();
        result = result * 59 + (($status == null) ? 43 : $status.hashCode());
        Object $errorcode = getErrorcode();
        result = result * 59 + (($errorcode == null) ? 43 : $errorcode.hashCode());
        Object $errorinfo = getErrorinfo();
        result = result * 59 + (($errorinfo == null) ? 43 : $errorinfo.hashCode());
        Object $data = getData();
        return result * 59 + (($data == null) ? 43 : $data.hashCode());
    }

    public String toString() {
        return "resultBeanMes(TaskID=" + getTaskID() + ", status=" + getStatus() + ", errorcode=" + getErrorcode() + ", errorinfo=" + getErrorinfo() + ", data=" + getData() + ")";
    }

    public String getTaskID() {
        return this.TaskID;
    }

    public String getStatus() {
        return this.status;
    }

    public String getErrorcode() {
        return this.errorcode;
    }

    public String getErrorinfo() {
        return this.errorinfo;
    }

    public BoxSite getData() {
        return this.data;
    }

    public resultBeanMes() {}

    public resultBeanMes(String ok) {
        setStatus(ok);
    }

    public void setSuccess() {
        setStatus("0");
    }

    public void setFault() {
        setStatus("1");
    }
}
