package com.tgvs.wms.transaction.util;

import com.tgvs.wms.business.enums.enumMsgState;
import com.tgvs.wms.business.enums.enumMsgType;
import com.tgvs.wms.business.modules.msg.entity.MsgAbb;
import com.tgvs.wms.transaction.service.MainService;

public class MsgUtil {
    public static void insertMsgAbb(String msgContent){
        MsgAbb msgAbb = new MsgAbb();

        msgAbb.setMsgType(enumMsgType.msg_type_mes2abb.getCode());
        msgAbb.setMsgContent(msgContent);
        msgAbb.setState(enumMsgState.init.getValue());

        MainService.mainServiceutil.msgAbbMaper.insert(msgAbb);
    }
}
