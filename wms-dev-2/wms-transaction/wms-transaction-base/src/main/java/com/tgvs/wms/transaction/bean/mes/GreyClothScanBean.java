package com.tgvs.wms.transaction.bean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class GreyClothScanBean {
    @JSONField(name = "SiteNo")
    private String SiteNo;

    @J<PERSON>NField(name = "ClothNo")
    private String ClothNo;

    @J<PERSON><PERSON>ield(name = "ContractNo")
    private String ContractNo;

    @JSONField(name = "BillNo")
    private String BillNo;

    @JSONField(name = "VatNo")
    private String VatNo;

    @JSONField(name = "LatheNo")
    private String LatheNo;

    @JSONField(name = "Height")
    private String Height;

    @JSONField(name = "Weight")
    private Integer Weight;

    @J<PERSON>NField(name = "UserNo")
    private String UserNo;

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setClothNo(String ClothNo) {
        this.ClothNo = ClothNo;
    }

    public void setContractNo(String ContractNo) {
        this.ContractNo = ContractNo;
    }

    public void setBillNo(String BillNo) {
        this.BillNo = BillNo;
    }

    public void setVatNo(String VatNo) {
        this.VatNo = VatNo;
    }

    public void setLatheNo(String LatheNo) {
        this.LatheNo = LatheNo;
    }

    public void setHeight(String Height) {
        this.Height = Height;
    }

    public void setWeight(Integer Weight) {
        this.Weight = Weight;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof GreyClothScanBean))
            return false;
        GreyClothScanBean other = (GreyClothScanBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$ClothNo = getClothNo(), other$ClothNo = other.getClothNo();
        if ((this$ClothNo == null) ? (other$ClothNo != null) : !this$ClothNo.equals(other$ClothNo))
            return false;
        Object this$ContractNo = getContractNo(), other$ContractNo = other.getContractNo();
        if ((this$ContractNo == null) ? (other$ContractNo != null) : !this$ContractNo.equals(other$ContractNo))
            return false;
        Object this$BillNo = getBillNo(), other$BillNo = other.getBillNo();
        if ((this$BillNo == null) ? (other$BillNo != null) : !this$BillNo.equals(other$BillNo))
            return false;
        Object this$VatNo = getVatNo(), other$VatNo = other.getVatNo();
        if ((this$VatNo == null) ? (other$VatNo != null) : !this$VatNo.equals(other$VatNo))
            return false;
        Object this$LatheNo = getLatheNo(), other$LatheNo = other.getLatheNo();
        if ((this$LatheNo == null) ? (other$LatheNo != null) : !this$LatheNo.equals(other$LatheNo))
            return false;
        Object this$Height = getHeight(), other$Height = other.getHeight();
        if ((this$Height == null) ? (other$Height != null) : !this$Height.equals(other$Height))
            return false;
        Object this$Weight = getWeight(), other$Weight = other.getWeight();
        if ((this$Weight == null) ? (other$Weight != null) : !this$Weight.equals(other$Weight))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof GreyClothScanBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $ClothNo = getClothNo();
        result = result * 59 + (($ClothNo == null) ? 43 : $ClothNo.hashCode());
        Object $ContractNo = getContractNo();
        result = result * 59 + (($ContractNo == null) ? 43 : $ContractNo.hashCode());
        Object $BillNo = getBillNo();
        result = result * 59 + (($BillNo == null) ? 43 : $BillNo.hashCode());
        Object $VatNo = getVatNo();
        result = result * 59 + (($VatNo == null) ? 43 : $VatNo.hashCode());
        Object $LatheNo = getLatheNo();
        result = result * 59 + (($LatheNo == null) ? 43 : $LatheNo.hashCode());
        Object $Height = getHeight();
        result = result * 59 + (($Height == null) ? 43 : $Height.hashCode());
        Object $Weight = getWeight();
        result = result * 59 + (($Weight == null) ? 43 : $Weight.hashCode());
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "GreyClothScanBean(SiteNo=" + getSiteNo() + ", ClothNo=" + getClothNo() + ", ContractNo=" + getContractNo() + ", BillNo=" + getBillNo() + ", VatNo=" + getVatNo() + ", LatheNo=" + getLatheNo() + ", Height=" + getHeight() + ", Weight=" + getWeight() + ", UserNo=" + getUserNo() + ")";
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getClothNo() {
        return this.ClothNo;
    }

    public String getContractNo() {
        return this.ContractNo;
    }

    public String getBillNo() {
        return this.BillNo;
    }

    public String getVatNo() {
        return this.VatNo;
    }

    public String getLatheNo() {
        return this.LatheNo;
    }

    public String getHeight() {
        return this.Height;
    }

    public Integer getWeight() {
        return this.Weight;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}
