package com.tgvs.wms.transaction.bean.mq.base;

public enum enumRunState {
    unknown("未知", "UNKN"),
    normal("正常", "NOAL"),
    error("故障", "ERRO");

    private String text;

    private String value;

    enumRunState(String text, String value) {
        this.text = text;
        this.value = value;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static enumRunState toEnum(String Value) {
        for (enumRunState e : values()) {
            if (e.getValue() == Value)
                return e;
        }
        return unknown;
    }
}
