package com.tgvs.wms.transaction.bean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class ForceAwayDollyBean {
    @JSONField(name = "SiteNo")
    private String SiteNo;

    @J<PERSON>NField(name = "LayerQty")
    private Integer LayerQty;

    @J<PERSON><PERSON>ield(name = "SingleQty")
    private Integer SingleQty;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setLayerQty(Integer LayerQty) {
        this.LayerQty = LayerQty;
    }

    public void setSingleQty(Integer SingleQty) {
        this.SingleQty = SingleQty;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof ForceAwayDollyBean))
            return false;
        ForceAwayDollyBean other = (ForceAwayDollyBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$LayerQty = getLayerQty(), other$LayerQty = other.getLayerQty();
        if ((this$LayerQty == null) ? (other$LayerQty != null) : !this$LayerQty.equals(other$LayerQty))
            return false;
        Object this$SingleQty = getSingleQty(), other$SingleQty = other.getSingleQty();
        if ((this$SingleQty == null) ? (other$SingleQty != null) : !this$SingleQty.equals(other$SingleQty))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ForceAwayDollyBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $LayerQty = getLayerQty();
        result = result * 59 + (($LayerQty == null) ? 43 : $LayerQty.hashCode());
        Object $SingleQty = getSingleQty();
        result = result * 59 + (($SingleQty == null) ? 43 : $SingleQty.hashCode());
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "ForceAwayDollyBean(SiteNo=" + getSiteNo() + ", LayerQty=" + getLayerQty() + ", SingleQty=" + getSingleQty() + ", UserNo=" + getUserNo() + ")";
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public Integer getLayerQty() {
        return this.LayerQty;
    }

    public Integer getSingleQty() {
        return this.SingleQty;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}
