package com.tgvs.wms.transaction.bean.mq.base;

public class MQBase {

    public static final String comma = ",";

    public static final String colon = ":";

    public static final String semicolon = ";";

    public static final String open_brace = "{";

    public static final String colse_brace = "}";

    public static final String open_bracket = "[";

    public static final String colse_bracket = "]";

    public static final String open_paren = "(";

    public static final String colse_paren = ")";

    public static final byte STX = 60;

    public static final byte EXT = 62;

    public static final String ping = "PING";

    public static final String pong = "PONG";

    public static final String ackn = "ACKN";

    public static final String terr = "TERR";

    public static final String reqm = "REQM";

    public static final String stam = "STAM";

    public static final String reqo = "REQO";

    public static final String cano = "CANO";

    public static final String relu = "RELU";

    public static final String fnpa = "FNPA";

    public static final String inlc = "INLC";

    public static final String lcin = "LCIN";

    public static final String fill = "FILL";

    public static final String inri = "INRI";

    public static final String mhar = "MHAR";

    public static final String avst = "AVST";

    public static final String high = "HIGH";

    public static final String scan = "SCAN";

    public static final String lscm = "LSCM";

    public static final String lrcm = "LRCM";

    public static final String gscm = "GSCM";

    public static final String grcm = "GRCM";

    public static final String doly = "DOLY";
}
