package com.tgvs.wms.transaction.bean.mes;


import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

public class BoxRequestSite {
    @J<PERSON><PERSON>ield(name = "BoxNo")
    private String BoxNo;

    @JSONField(name = "ReqSiteNo")
    private String ReqSiteNo;

    @J<PERSON>NField(name = "PreSiteNo")
    private String PreSiteNo;

    public void setBoxNo(String BoxNo) {
        this.BoxNo = BoxNo;
    }

    public void setReqSiteNo(String ReqSiteNo) {
        this.ReqSiteNo = ReqSiteNo;
    }

    public void setPreSiteNo(String PreSiteNo) {
        this.PreSiteNo = PreSiteNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BoxRequestSite))
            return false;
        BoxRequestSite other = (BoxRequestSite)o;
        if (!other.canEqual(this))
            return false;
        Object this$BoxNo = getBoxNo(), other$BoxNo = other.getBoxNo();
        if ((this$BoxNo == null) ? (other$BoxNo != null) : !this$BoxNo.equals(other$BoxNo))
            return false;
        Object this$ReqSiteNo = getReqSiteNo(), other$ReqSiteNo = other.getReqSiteNo();
        if ((this$ReqSiteNo == null) ? (other$ReqSiteNo != null) : !this$ReqSiteNo.equals(other$ReqSiteNo))
            return false;
        Object this$PreSiteNo = getPreSiteNo(), other$PreSiteNo = other.getPreSiteNo();
        return !((this$PreSiteNo == null) ? (other$PreSiteNo != null) : !this$PreSiteNo.equals(other$PreSiteNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BoxRequestSite;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $BoxNo = getBoxNo();
        result = result * 59 + (($BoxNo == null) ? 43 : $BoxNo.hashCode());
        Object $ReqSiteNo = getReqSiteNo();
        result = result * 59 + (($ReqSiteNo == null) ? 43 : $ReqSiteNo.hashCode());
        Object $PreSiteNo = getPreSiteNo();
        return result * 59 + (($PreSiteNo == null) ? 43 : $PreSiteNo.hashCode());
    }

    public String toString() {
        return "BoxRequestSite(BoxNo=" + getBoxNo() + ", ReqSiteNo=" + getReqSiteNo() + ", PreSiteNo=" + getPreSiteNo() + ")";
    }

    public String getBoxNo() {
        return this.BoxNo;
    }

    public String getReqSiteNo() {
        return this.ReqSiteNo;
    }

    public String getPreSiteNo() {
        return this.PreSiteNo;
    }
}
