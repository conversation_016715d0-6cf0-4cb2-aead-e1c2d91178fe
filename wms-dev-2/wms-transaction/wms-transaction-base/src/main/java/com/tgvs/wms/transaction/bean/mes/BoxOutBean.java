package com.tgvs.wms.transaction.bean.mes;


import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

public class BoxOutBean {
    @JSONField(name = "OrderNo")
    private Integer OrderNo;

    @J<PERSON>NField(name = "BookNo")
    private String BookNo;

    @J<PERSON>NField(name = "SiteNo")
    private String SiteNo;

    @JSONField(name = "Group")
    private String Group;

    @JSONField(name = "Priority")
    private Integer Priority;

    @JSONField(name = "UserNo")
    private String UserNo;

    @JSONField(name = "SubBoxList")
    private List<OutBoxInfo> SubBoxList;

    public void setOrderNo(Integer OrderNo) {
        this.OrderNo = OrderNo;
    }

    public void setBookNo(String BookNo) {
        this.BookNo = BookNo;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setGroup(String Group) {
        this.Group = Group;
    }

    public void setPriority(Integer Priority) {
        this.Priority = Priority;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public void setSubBoxList(List<OutBoxInfo> SubBoxList) {
        this.SubBoxList = SubBoxList;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BoxOutBean))
            return false;
        BoxOutBean other = (BoxOutBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$OrderNo = getOrderNo(), other$OrderNo = other.getOrderNo();
        if ((this$OrderNo == null) ? (other$OrderNo != null) : !this$OrderNo.equals(other$OrderNo))
            return false;
        Object this$BookNo = getBookNo(), other$BookNo = other.getBookNo();
        if ((this$BookNo == null) ? (other$BookNo != null) : !this$BookNo.equals(other$BookNo))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$Group = getGroup(), other$Group = other.getGroup();
        if ((this$Group == null) ? (other$Group != null) : !this$Group.equals(other$Group))
            return false;
        Object this$Priority = getPriority(), other$Priority = other.getPriority();
        if ((this$Priority == null) ? (other$Priority != null) : !this$Priority.equals(other$Priority))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        if ((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo))
            return false;
        List<OutBoxInfo> this$SubBoxList = (List<OutBoxInfo>)getSubBoxList(), other$SubBoxList = (List<OutBoxInfo>)other.getSubBoxList();
        return !((this$SubBoxList == null) ? (other$SubBoxList != null) : !this$SubBoxList.equals(other$SubBoxList));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BoxOutBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $OrderNo = getOrderNo();
        result = result * 59 + (($OrderNo == null) ? 43 : $OrderNo.hashCode());
        Object $BookNo = getBookNo();
        result = result * 59 + (($BookNo == null) ? 43 : $BookNo.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $Group = getGroup();
        result = result * 59 + (($Group == null) ? 43 : $Group.hashCode());
        Object $Priority = getPriority();
        result = result * 59 + (($Priority == null) ? 43 : $Priority.hashCode());
        Object $UserNo = getUserNo();
        result = result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
        List<OutBoxInfo> $SubBoxList = (List<OutBoxInfo>)getSubBoxList();
        return result * 59 + (($SubBoxList == null) ? 43 : $SubBoxList.hashCode());
    }

    public String toString() {
        return "BoxOutBean(OrderNo=" + getOrderNo() + ", BookNo=" + getBookNo() + ", SiteNo=" + getSiteNo() + ", Group=" + getGroup() + ", Priority=" + getPriority() + ", UserNo=" + getUserNo() + ", SubBoxList=" + getSubBoxList() + ")";
    }

    public Integer getOrderNo() {
        return this.OrderNo;
    }

    public String getBookNo() {
        return this.BookNo;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getGroup() {
        return this.Group;
    }

    public Integer getPriority() {
        return this.Priority;
    }

    public String getUserNo() {
        return this.UserNo;
    }

    public List<OutBoxInfo> getSubBoxList() {
        return this.SubBoxList;
    }
}
