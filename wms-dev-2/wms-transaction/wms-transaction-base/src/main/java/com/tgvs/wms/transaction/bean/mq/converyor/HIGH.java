package com.tgvs.wms.transaction.bean.mq.converyor;


import com.tgvs.wms.transaction.bean.mq.base.HEAD;

import lombok.Data;

@Data
public class HIGH {

    private HEAD Header;

    private String Location;

    private String lcid;

    private String readState;

    private Integer High;

    public void setHeader(HEAD Header) {
        this.Header = Header;
    }

    public void setLocation(String Location) {
        this.Location = Location;
    }

    public void setLcid(String lcid) {
        this.lcid = lcid;
    }

    public void setReadState(String readState) {
        this.readState = readState;
    }

    public void setHigh(Integer High) {
        this.High = High;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof HIGH))
            return false;
        HIGH other = (HIGH)o;
        if (!other.canEqual(this))
            return false;
        Object this$Header = getHeader(), other$Header = other.getHeader();
        if ((this$Header == null) ? (other$Header != null) : !this$Header.equals(other$Header))
            return false;
        Object this$Location = getLocation(), other$Location = other.getLocation();
        if ((this$Location == null) ? (other$Location != null) : !this$Location.equals(other$Location))
            return false;
        Object this$lcid = getLcid(), other$lcid = other.getLcid();
        if ((this$lcid == null) ? (other$lcid != null) : !this$lcid.equals(other$lcid))
            return false;
        Object this$readState = getReadState(), other$readState = other.getReadState();
        if ((this$readState == null) ? (other$readState != null) : !this$readState.equals(other$readState))
            return false;
        Object this$High = getHigh(), other$High = other.getHigh();
        return !((this$High == null) ? (other$High != null) : !this$High.equals(other$High));
    }

    protected boolean canEqual(Object other) {
        return other instanceof HIGH;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $Header = getHeader();
        result = result * 59 + (($Header == null) ? 43 : $Header.hashCode());
        Object $Location = getLocation();
        result = result * 59 + (($Location == null) ? 43 : $Location.hashCode());
        Object $lcid = getLcid();
        result = result * 59 + (($lcid == null) ? 43 : $lcid.hashCode());
        Object $readState = getReadState();
        result = result * 59 + (($readState == null) ? 43 : $readState.hashCode());
        Object $High = getHigh();
        return result * 59 + (($High == null) ? 43 : $High.hashCode());
    }

    public HIGH() {
        HEAD header = new HEAD("MHAR");
        setHeader(header);
    }

    public HEAD getHeader() {
        return this.Header;
    }

    public String getLocation() {
        return this.Location;
    }

    public String getLcid() {
        return this.lcid;
    }

    public String getReadState() {
        return this.readState;
    }

    public Integer getHigh() {
        return this.High;
    }

    public void setMQ(String str) throws Exception {
        if (null != str && str.length() > 0) {
            String[] mqdata = str.split(";", -1);
            if (null != mqdata && mqdata.length == 8) {
                this.Header.setMQ(str);
                setLocation(mqdata[4]);
                setReadState(mqdata[5]);
                setLcid(mqdata[6]);
                try {
                    Integer integer = new Integer(mqdata[7]);
                    setHigh(integer);
                } catch (Exception e) {
                    throw new Exception("报文格式不正确！0");
                }
            } else {
                throw new Exception("报文格式不正确！1");
            }
        } else {
            throw new Exception("报文格式不正确！2");
        }
    }

    public String toString() {
        String str = "";
        str = this.Header.toString();
        str = str + ";";
        str = str + getLocation() + ";";
        str = str + getReadState() + ";";
        str = str + getLcid() + ";";
        str = str + getHigh().toString();
        return str;
    }
}
