package com.tgvs.wms.transaction.bean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class BoxTaskErrorBean {
    @JSONField(name = "OrderNo")
    private Integer OrderNo;

    @J<PERSON><PERSON>ield(name = "BoxNo")
    private String BoxNo;

    @J<PERSON><PERSON>ield(name = "ErrSiteNo")
    private String ErrSiteNo;

    @JSONField(name = "SiteNo")
    private String SiteNo;

    @JSONField(name = "Type")
    private Integer Type;

    @JSONField(name = "ErrorCode")
    private Integer ErrorCode;

    @JSONField(name = "ErrorMsg")
    private String ErrorMsg;

    public void setOrderNo(Integer OrderNo) {
        this.OrderNo = OrderNo;
    }

    public void setBoxNo(String BoxNo) {
        this.BoxNo = BoxNo;
    }

    public void setErrSiteNo(String ErrSiteNo) {
        this.ErrSiteNo = ErrSiteNo;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setType(Integer Type) {
        this.Type = Type;
    }

    public void setErrorCode(Integer ErrorCode) {
        this.ErrorCode = ErrorCode;
    }

    public void setErrorMsg(String ErrorMsg) {
        this.ErrorMsg = ErrorMsg;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof BoxTaskErrorBean))
            return false;
        BoxTaskErrorBean other = (BoxTaskErrorBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$OrderNo = getOrderNo(), other$OrderNo = other.getOrderNo();
        if ((this$OrderNo == null) ? (other$OrderNo != null) : !this$OrderNo.equals(other$OrderNo))
            return false;
        Object this$BoxNo = getBoxNo(), other$BoxNo = other.getBoxNo();
        if ((this$BoxNo == null) ? (other$BoxNo != null) : !this$BoxNo.equals(other$BoxNo))
            return false;
        Object this$ErrSiteNo = getErrSiteNo(), other$ErrSiteNo = other.getErrSiteNo();
        if ((this$ErrSiteNo == null) ? (other$ErrSiteNo != null) : !this$ErrSiteNo.equals(other$ErrSiteNo))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$Type = getType(), other$Type = other.getType();
        if ((this$Type == null) ? (other$Type != null) : !this$Type.equals(other$Type))
            return false;
        Object this$ErrorCode = getErrorCode(), other$ErrorCode = other.getErrorCode();
        if ((this$ErrorCode == null) ? (other$ErrorCode != null) : !this$ErrorCode.equals(other$ErrorCode))
            return false;
        Object this$ErrorMsg = getErrorMsg(), other$ErrorMsg = other.getErrorMsg();
        return !((this$ErrorMsg == null) ? (other$ErrorMsg != null) : !this$ErrorMsg.equals(other$ErrorMsg));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BoxTaskErrorBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $OrderNo = getOrderNo();
        result = result * 59 + (($OrderNo == null) ? 43 : $OrderNo.hashCode());
        Object $BoxNo = getBoxNo();
        result = result * 59 + (($BoxNo == null) ? 43 : $BoxNo.hashCode());
        Object $ErrSiteNo = getErrSiteNo();
        result = result * 59 + (($ErrSiteNo == null) ? 43 : $ErrSiteNo.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $Type = getType();
        result = result * 59 + (($Type == null) ? 43 : $Type.hashCode());
        Object $ErrorCode = getErrorCode();
        result = result * 59 + (($ErrorCode == null) ? 43 : $ErrorCode.hashCode());
        Object $ErrorMsg = getErrorMsg();
        return result * 59 + (($ErrorMsg == null) ? 43 : $ErrorMsg.hashCode());
    }

    public String toString() {
        return "BoxTaskErrorBean(OrderNo=" + getOrderNo() + ", BoxNo=" + getBoxNo() + ", ErrSiteNo=" + getErrSiteNo() + ", SiteNo=" + getSiteNo() + ", Type=" + getType() + ", ErrorCode=" + getErrorCode() + ", ErrorMsg=" + getErrorMsg() + ")";
    }

    public Integer getOrderNo() {
        return this.OrderNo;
    }

    public String getBoxNo() {
        return this.BoxNo;
    }

    public String getErrSiteNo() {
        return this.ErrSiteNo;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public Integer getType() {
        return this.Type;
    }

    public Integer getErrorCode() {
        return this.ErrorCode;
    }

    public String getErrorMsg() {
        return this.ErrorMsg;
    }
}
