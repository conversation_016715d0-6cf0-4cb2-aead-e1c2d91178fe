package com.tgvs.wms.transaction.bean.mq.base;

public class TelegramTools {
    public static int mqnumber = 1;

    public static synchronized String getMqNumber() {
        if (mqnumber < 9999) {
            mqnumber++;
//            System.out.println(mqnumber);
            return String.format("%04d", new Object[] { Integer.valueOf(mqnumber) });
        }
        mqnumber = 1;
//        System.out.println(mqnumber);
        return String.format("%04d", new Object[] { Integer.valueOf(mqnumber) });
    }
}
