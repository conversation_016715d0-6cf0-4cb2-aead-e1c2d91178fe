package com.tgvs.wms.transaction.bean.mq.base;

public enum enumOperatState {
    unknown("未知", "UNKN"),
    offLine("离线状态", "DLIN"),
    onLine("在线状态", "OLIN");

    private String text;

    private String value;

    enumOperatState(String text, String value) {
        this.text = text;
        this.value = value;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static enumOperatState toEnum(String Value) {
        for (enumOperatState e : values()) {
            if (e.getValue() == Value)
                return e;
        }
        return unknown;
    }
}