package com.tgvs.wms.transaction.delegate;

public abstract class Notifier {
    private EventHandler eventHandler = new EventHandler();

    public EventHandler getEventHandler() {
        return this.eventHandler;
    }

    public void setEventHandler(EventHandler eventHandler) {
        this.eventHandler = eventHandler;
    }

    public abstract void addListener(Object paramObject, String paramString, Object... paramVarArgs);

    public abstract void notifyX();
}
