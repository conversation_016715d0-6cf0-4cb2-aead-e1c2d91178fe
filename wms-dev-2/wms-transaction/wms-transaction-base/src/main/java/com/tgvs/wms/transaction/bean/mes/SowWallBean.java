package com.tgvs.wms.transaction.bean.mes;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;

public class SowWallBean {
    @JSONField(name = "SiteNo")
    private String SiteNo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JSONField(name = "PieceNo")
    private String PieceNo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JSONField(name = "CrossNo")
    private String CrossNo;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setPieceNo(String PieceNo) {
        this.PieceNo = PieceNo;
    }

    public void setCrossNo(String CrossNo) {
        this.CrossNo = CrossNo;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof SowWallBean))
            return false;
        SowWallBean other = (SowWallBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$PieceNo = getPieceNo(), other$PieceNo = other.getPieceNo();
        if ((this$PieceNo == null) ? (other$PieceNo != null) : !this$PieceNo.equals(other$PieceNo))
            return false;
        Object this$CrossNo = getCrossNo(), other$CrossNo = other.getCrossNo();
        if ((this$CrossNo == null) ? (other$CrossNo != null) : !this$CrossNo.equals(other$CrossNo))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof SowWallBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $PieceNo = getPieceNo();
        result = result * 59 + (($PieceNo == null) ? 43 : $PieceNo.hashCode());
        Object $CrossNo = getCrossNo();
        result = result * 59 + (($CrossNo == null) ? 43 : $CrossNo.hashCode());
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "SowWallBean(SiteNo=" + getSiteNo() + ", PieceNo=" + getPieceNo() + ", CrossNo=" + getCrossNo() + ", UserNo=" + getUserNo() + ")";
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getPieceNo() {
        return this.PieceNo;
    }

    public String getCrossNo() {
        return this.CrossNo;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}
