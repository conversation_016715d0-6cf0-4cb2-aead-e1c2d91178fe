package com.tgvs.wms.transaction.bean.mes;


import com.alibaba.fastjson.annotation.JSONField;

public class EmputyShelfBean {
    @JSONField(name = "ShelfType")
    private String ShelfType;

    @JSONField(name = "ShelfCount")
    private Integer ShelfCount;

    public void setShelfType(String ShelfType) {
        this.ShelfType = ShelfType;
    }

    public void setShelfCount(Integer ShelfCount) {
        this.ShelfCount = ShelfCount;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof EmputyShelfBean))
            return false;
        EmputyShelfBean other = (EmputyShelfBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$ShelfType = getShelfType(), other$ShelfType = other.getShelfType();
        if ((this$ShelfType == null) ? (other$ShelfType != null) : !this$ShelfType.equals(other$ShelfType))
            return false;
        Object this$ShelfCount = getShelfCount(), other$ShelfCount = other.getShelfCount();
        return !((this$ShelfCount == null) ? (other$ShelfCount != null) : !this$ShelfCount.equals(other$ShelfCount));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EmputyShelfBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $ShelfType = getShelfType();
        result = result * 59 + (($ShelfType == null) ? 43 : $ShelfType.hashCode());
        Object $ShelfCount = getShelfCount();
        return result * 59 + (($ShelfCount == null) ? 43 : $ShelfCount.hashCode());
    }

    public String toString() {
        return "EmputyShelfBean(ShelfType=" + getShelfType() + ", ShelfCount=" + getShelfCount() + ")";
    }

    public String getShelfType() {
        return this.ShelfType;
    }

    public Integer getShelfCount() {
        return this.ShelfCount;
    }
}
