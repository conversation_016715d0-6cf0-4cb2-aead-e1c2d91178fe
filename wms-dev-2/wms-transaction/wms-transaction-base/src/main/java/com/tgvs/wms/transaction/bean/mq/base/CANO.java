package com.tgvs.wms.transaction.bean.mq.base;

import lombok.Data;

@Data
public class CANO {
    private HEAD Header;

    private String OrderID;

    public CANO() {
        HEAD header = new HEAD();
        header.setMqtype("CANO");
        header.setMqindex(TelegramTools.getMqNumber());
        setHeader(header);
    }

    public CANO(String orderID) {
        HEAD header = new HEAD();
        header.setMqtype("CANO");
        header.setMqindex(TelegramTools.getMqNumber());
        setHeader(header);
        this.OrderID = orderID;
    }

    public HEAD getHeader() {
        return this.Header;
    }

    public void setHeader(HEAD header) {
        this.Header = header;
    }

    public String getOrderID() {
        return this.OrderID;
    }

    public void setOrderID(String orderID) {
        this.OrderID = orderID;
    }

    public String toString() {
        String str = "";
        str = this.Header.toString();
        str = str + ";";
        str = str + getOrderID();
        return str;
    }
}
