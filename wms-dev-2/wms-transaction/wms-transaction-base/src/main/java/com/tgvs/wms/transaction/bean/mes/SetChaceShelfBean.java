package com.tgvs.wms.transaction.bean.mes;

import com.alibaba.fastjson.annotation.JSONField;

public class SetChaceShelfBean {
    @JSONField(name = "site_code")
    private String site_code;

    @<PERSON><PERSON><PERSON>ield(name = "code")
    private String code;

    @<PERSON><PERSON><PERSON>ield(name = "column_number")
    private int column_number;

    @JSO<PERSON>ield(name = "levelsum")
    private int levelsum;

    @JSONField(name = "level")
    private int level;

    @JSONField(name = "UserNo")
    private String UserNo;

    public void setSite_code(String site_code) {
        this.site_code = site_code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setColumn_number(int column_number) {
        this.column_number = column_number;
    }

    public void setLevelsum(int levelsum) {
        this.levelsum = levelsum;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof SetChaceShelfBean))
            return false;
        SetChaceShelfBean other = (SetChaceShelfBean)o;
        if (!other.canEqual(this))
            return false;
        Object this$site_code = getSite_code(), other$site_code = other.getSite_code();
        if ((this$site_code == null) ? (other$site_code != null) : !this$site_code.equals(other$site_code))
            return false;
        Object this$code = getCode(), other$code = other.getCode();
        if ((this$code == null) ? (other$code != null) : !this$code.equals(other$code))
            return false;
        if (getColumn_number() != other.getColumn_number())
            return false;
        if (getLevelsum() != other.getLevelsum())
            return false;
        if (getLevel() != other.getLevel())
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        return !((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo));
    }

    protected boolean canEqual(Object other) {
        return other instanceof SetChaceShelfBean;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $site_code = getSite_code();
        result = result * 59 + (($site_code == null) ? 43 : $site_code.hashCode());
        Object $code = getCode();
        result = result * 59 + (($code == null) ? 43 : $code.hashCode());
        result = result * 59 + getColumn_number();
        result = result * 59 + getLevelsum();
        result = result * 59 + getLevel();
        Object $UserNo = getUserNo();
        return result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
    }

    public String toString() {
        return "SetChaceShelfBean(site_code=" + getSite_code() + ", code=" + getCode() + ", column_number=" + getColumn_number() + ", levelsum=" + getLevelsum() + ", level=" + getLevel() + ", UserNo=" + getUserNo() + ")";
    }

    public String getSite_code() {
        return this.site_code;
    }

    public String getCode() {
        return this.code;
    }

    public int getColumn_number() {
        return this.column_number;
    }

    public int getLevelsum() {
        return this.levelsum;
    }

    public int getLevel() {
        return this.level;
    }

    public String getUserNo() {
        return this.UserNo;
    }
}
