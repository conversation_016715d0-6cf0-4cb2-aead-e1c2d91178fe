package com.tgvs.wms.transaction.bean.agv;


import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

public class songbuAwayFullDolly {
    @J<PERSON><PERSON><PERSON>(name = "TaskID")
    private String TaskID;

    @J<PERSON><PERSON>ield(name = "SiteNo")
    private String SiteNo;

    @<PERSON><PERSON><PERSON><PERSON>(name = "<PERSON>No")
    private String DollyNo;

    @J<PERSON>NField(name = "Content")
    private String Content;

    @J<PERSON><PERSON>ield(name = "UserNo")
    private String UserNo;

    @J<PERSON><PERSON>ield(name = "SysCode")
    private String SysCode;

    public void setTaskID(String TaskID) {
        this.TaskID = TaskID;
    }

    public void setSiteNo(String SiteNo) {
        this.SiteNo = SiteNo;
    }

    public void setDollyNo(String DollyNo) {
        this.DollyNo = DollyNo;
    }

    public void setContent(String Content) {
        this.Content = Content;
    }

    public void setUserNo(String UserNo) {
        this.UserNo = UserNo;
    }

    public void setSysCode(String SysCode) {
        this.SysCode = SysCode;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof songbuAwayFullDolly))
            return false;
        songbuAwayFullDolly other = (songbuAwayFullDolly)o;
        if (!other.canEqual(this))
            return false;
        Object this$TaskID = getTaskID(), other$TaskID = other.getTaskID();
        if ((this$TaskID == null) ? (other$TaskID != null) : !this$TaskID.equals(other$TaskID))
            return false;
        Object this$SiteNo = getSiteNo(), other$SiteNo = other.getSiteNo();
        if ((this$SiteNo == null) ? (other$SiteNo != null) : !this$SiteNo.equals(other$SiteNo))
            return false;
        Object this$DollyNo = getDollyNo(), other$DollyNo = other.getDollyNo();
        if ((this$DollyNo == null) ? (other$DollyNo != null) : !this$DollyNo.equals(other$DollyNo))
            return false;
        Object this$Content = getContent(), other$Content = other.getContent();
        if ((this$Content == null) ? (other$Content != null) : !this$Content.equals(other$Content))
            return false;
        Object this$UserNo = getUserNo(), other$UserNo = other.getUserNo();
        if ((this$UserNo == null) ? (other$UserNo != null) : !this$UserNo.equals(other$UserNo))
            return false;
        Object this$SysCode = getSysCode(), other$SysCode = other.getSysCode();
        return !((this$SysCode == null) ? (other$SysCode != null) : !this$SysCode.equals(other$SysCode));
    }

    protected boolean canEqual(Object other) {
        return other instanceof songbuAwayFullDolly;
    }

    public int hashCode() {
        int PRIME = 59, result = 1;
        Object $TaskID = getTaskID();
        result = result * 59 + (($TaskID == null) ? 43 : $TaskID.hashCode());
        Object $SiteNo = getSiteNo();
        result = result * 59 + (($SiteNo == null) ? 43 : $SiteNo.hashCode());
        Object $DollyNo = getDollyNo();
        result = result * 59 + (($DollyNo == null) ? 43 : $DollyNo.hashCode());
        Object $Content = getContent();
        result = result * 59 + (($Content == null) ? 43 : $Content.hashCode());
        Object $UserNo = getUserNo();
        result = result * 59 + (($UserNo == null) ? 43 : $UserNo.hashCode());
        Object $SysCode = getSysCode();
        return result * 59 + (($SysCode == null) ? 43 : $SysCode.hashCode());
    }

    public String toString() {
        return "songbuAwayFullDolly(TaskID=" + getTaskID() + ", SiteNo=" + getSiteNo() + ", DollyNo=" + getDollyNo() + ", Content=" + getContent() + ", UserNo=" + getUserNo() + ", SysCode=" + getSysCode() + ")";
    }

    public String getTaskID() {
        return this.TaskID;
    }

    public String getSiteNo() {
        return this.SiteNo;
    }

    public String getDollyNo() {
        return this.DollyNo;
    }

    public String getContent() {
        return this.Content;
    }

    public String getUserNo() {
        return this.UserNo;
    }

    public String getSysCode() {
        return this.SysCode;
    }
}
