package com.tgvs.wms.transaction.bean.mq.converyor;

import com.tgvs.wms.transaction.bean.mq.base.HEAD;

import lombok.Data;

@Data
public class MHAR {
    private HEAD Header;

    private String Location;

    private String lcid;

    public void setHeader(HEAD Header) {
        this.Header = Header;
    }

    public void setLocation(String Location) {
        this.Location = Location;
    }

    public void setLcid(String lcid) {
        this.lcid = lcid;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof MHAR))
            return false;
        MHAR other = (MHAR)o;
        if (!other.canEqual(this))
            return false;
        Object this$Header = getHeader(), other$Header = other.getHeader();
        if ((this$Header == null) ? (other$Header != null) : !this$Header.equals(other$Header))
            return false;
        Object this$Location = getLocation(), other$Location = other.getLocation();
        if ((this$Location == null) ? (other$Location != null) : !this$Location.equals(other$Location))
            return false;
        Object this$lcid = getLcid(), other$lcid = other.getLcid();
        return !((this$lcid == null) ? (other$lcid != null) : !this$lcid.equals(other$lcid));
    }

    protected boolean canEqual(Object other) {
        return other instanceof MHAR;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $Header = getHeader();
        result = result * 59 + (($Header == null) ? 43 : $Header.hashCode());
        Object $Location = getLocation();
        result = result * 59 + (($Location == null) ? 43 : $Location.hashCode());
        Object $lcid = getLcid();
        return result * 59 + (($lcid == null) ? 43 : $lcid.hashCode());
    }

    public MHAR() {
        HEAD header = new HEAD("MHAR");
        setHeader(header);
    }

    public HEAD getHeader() {
        return this.Header;
    }

    public String getLocation() {
        return this.Location;
    }

    public String getLcid() {
        return this.lcid;
    }

    public void setMQ(String str) throws Exception {
        if (null != str && str.length() > 0) {
            String[] mqdata = str.split(";", -1);
            if (null != mqdata && mqdata.length == 6) {
                this.Header.setMQ(str);
                setLocation(mqdata[4]);
                setLcid(mqdata[5]);
            } else {
                throw new Exception("报文格式不正确！1");
            }
        } else {
            throw new Exception("报文格式不正确！2");
        }
    }

    public String toString() {
        String str = "";
        str = this.Header.toString();
        str = str + ";";
        str = str + getLocation() + ";";
        str = str + getLcid();
        return str;
    }
}
