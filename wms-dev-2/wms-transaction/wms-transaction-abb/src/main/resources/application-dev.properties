spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.druid.master.url=************************************************************************************************************************************************
spring.datasource.druid.master.username=leon
spring.datasource.druid.master.password=123456
spring.datasource.druid.slave.enabled=false
spring.datasource.druid.slave.url=
spring.datasource.druid.slave.username=
spring.datasource.druid.slave.password=
spring.datasource.druid.initialSize=5
spring.datasource.druid.minIdle=10
spring.datasource.druid.maxActive=20
spring.datasource.druid.maxWait=60000
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.maxEvictableIdleTimeMillis=900000
spring.datasource.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.webStatFilter.enabled=true
spring.datasource.druid.statViewServlet.enabled=true
spring.datasource.druid.statViewServlet.allow=
spring.datasource.druid.statViewServlet.url-pattern=/druid/*
spring.datasource.druid.statViewServlet.login-username=ruoyi
spring.datasource.druid.statViewServlet.login-password=123456
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=1000
spring.datasource.druid.filter.stat.merge-sql=true
spring.datasource.druid.filter.wall.config.multi-statement-allow=true


mybatis-plus.type-aliases-package=com.tgvs.wms.**.entity
mybatis-plus.mapper-locations=classpath*:com/tgvs/wms/**/xml/*Mapper.xml
mybatis-plus.configuration.map-underscore-to-camel-case=true