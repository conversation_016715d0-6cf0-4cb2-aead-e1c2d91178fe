package com.tgvs.wms.transaction.mfc.bean;


public class InLocation {
    private int s_level;

    private int s_location;

    private int e_level;

    private int e_location;

    private int r_level;

    private int r_location;

    public void setS_level(int s_level) {
        this.s_level = s_level;
    }

    public void setS_location(int s_location) {
        this.s_location = s_location;
    }

    public void setE_level(int e_level) {
        this.e_level = e_level;
    }

    public void setE_location(int e_location) {
        this.e_location = e_location;
    }

    public void setR_level(int r_level) {
        this.r_level = r_level;
    }

    public void setR_location(int r_location) {
        this.r_location = r_location;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof InLocation))
            return false;
        InLocation other = (InLocation)o;
        return !other.canEqual(this) ? false : ((getS_level() != other.getS_level()) ? false : ((getS_location() != other.getS_location()) ? false : ((getE_level() != other.getE_level()) ? false : ((getE_location() != other.getE_location()) ? false : ((getR_level() != other.getR_level()) ? false : (!(getR_location() != other.getR_location())))))));
    }

    protected boolean canEqual(Object other) {
        return other instanceof InLocation;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        result = result * 59 + getS_level();
        result = result * 59 + getS_location();
        result = result * 59 + getE_level();
        result = result * 59 + getE_location();
        result = result * 59 + getR_level();
        return result * 59 + getR_location();
    }

    public String toString() {
        return "InLocation(s_level=" + getS_level() + ", s_location=" + getS_location() + ", e_level=" + getE_level() + ", e_location=" + getE_location() + ", r_level=" + getR_level() + ", r_location=" + getR_location() + ")";
    }

    public int getS_level() {
        return this.s_level;
    }

    public int getS_location() {
        return this.s_location;
    }

    public int getE_level() {
        return this.e_level;
    }

    public int getE_location() {
        return this.e_location;
    }

    public int getR_level() {
        return this.r_level;
    }

    public int getR_location() {
        return this.r_location;
    }
}
