package com.tgvs.wms.transaction.mfc.bean;


import com.fasterxml.jackson.annotation.JsonProperty;

public class OutOrder {
    private Integer id;

    public void setId(Integer id) {
        this.id = id;
    }

    public void setMessageName(String messageName) {
        this.messageName = messageName;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public void setLocation(Integer location) {
        this.location = location;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setWmsId(String wmsId) {
        this.wmsId = wmsId;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public void setOutbound(Integer outbound) {
        this.outbound = outbound;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof OutOrder))
            return false;
        OutOrder other = (OutOrder)o;
        if (!other.canEqual(this))
            return false;
        Object this$id = getId(), other$id = other.getId();
        if ((this$id == null) ? (other$id != null) : !this$id.equals(other$id))
            return false;
        Object this$messageName = getMessageName(), other$messageName = other.getMessageName();
        if ((this$messageName == null) ? (other$messageName != null) : !this$messageName.equals(other$messageName))
            return false;
        Object this$boxId = getBoxId(), other$boxId = other.getBoxId();
        if ((this$boxId == null) ? (other$boxId != null) : !this$boxId.equals(other$boxId))
            return false;
        Object this$level = getLevel(), other$level = other.getLevel();
        if ((this$level == null) ? (other$level != null) : !this$level.equals(other$level))
            return false;
        Object this$location = getLocation(), other$location = other.getLocation();
        if ((this$location == null) ? (other$location != null) : !this$location.equals(other$location))
            return false;
        Object this$type = getType(), other$type = other.getType();
        if ((this$type == null) ? (other$type != null) : !this$type.equals(other$type))
            return false;
        Object this$wmsId = getWmsId(), other$wmsId = other.getWmsId();
        if ((this$wmsId == null) ? (other$wmsId != null) : !this$wmsId.equals(other$wmsId))
            return false;
        Object this$priority = getPriority(), other$priority = other.getPriority();
        if ((this$priority == null) ? (other$priority != null) : !this$priority.equals(other$priority))
            return false;
        Object this$outbound = getOutbound(), other$outbound = other.getOutbound();
        return !((this$outbound == null) ? (other$outbound != null) : !this$outbound.equals(other$outbound));
    }

    protected boolean canEqual(Object other) {
        return other instanceof OutOrder;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        Object $id = getId();
        result = result * 59 + (($id == null) ? 43 : $id.hashCode());
        Object $messageName = getMessageName();
        result = result * 59 + (($messageName == null) ? 43 : $messageName.hashCode());
        Object $boxId = getBoxId();
        result = result * 59 + (($boxId == null) ? 43 : $boxId.hashCode());
        Object $level = getLevel();
        result = result * 59 + (($level == null) ? 43 : $level.hashCode());
        Object $location = getLocation();
        result = result * 59 + (($location == null) ? 43 : $location.hashCode());
        Object $type = getType();
        result = result * 59 + (($type == null) ? 43 : $type.hashCode());
        Object $wmsId = getWmsId();
        result = result * 59 + (($wmsId == null) ? 43 : $wmsId.hashCode());
        Object $priority = getPriority();
        result = result * 59 + (($priority == null) ? 43 : $priority.hashCode());
        Object $outbound = getOutbound();
        return result * 59 + (($outbound == null) ? 43 : $outbound.hashCode());
    }

    public String toString() {
        return "OutOrder(id=" + getId() + ", messageName=" + getMessageName() + ", boxId=" + getBoxId() + ", level=" + getLevel() + ", location=" + getLocation() + ", type=" + getType() + ", wmsId=" + getWmsId() + ", priority=" + getPriority() + ", outbound=" + getOutbound() + ")";
    }

    public Integer getId() {
        return this.id;
    }

    private String messageName = "appointStockOut";

    @JsonProperty("boxId")
    private String boxId;

    private Integer level;

    private Integer location;

    private Integer type;

    @JsonProperty("wmsId")
    private String wmsId;

    private Integer priority;

    private Integer outbound;

    public String getMessageName() {
        return this.messageName;
    }

    public String getBoxId() {
        return this.boxId;
    }

    public Integer getLevel() {
        return this.level;
    }

    public Integer getLocation() {
        return this.location;
    }

    public Integer getType() {
        return this.type;
    }

    public String getWmsId() {
        return this.wmsId;
    }

    public Integer getPriority() {
        return this.priority;
    }

    public Integer getOutbound() {
        return this.outbound;
    }
}
