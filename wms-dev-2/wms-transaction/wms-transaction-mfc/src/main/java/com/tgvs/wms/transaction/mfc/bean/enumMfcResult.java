package com.tgvs.wms.transaction.mfc.bean;


import com.tgvs.wms.common.util.oConvertUtils;

public enum enumMfcResult {
    ok(Integer.valueOf(0), "ok", "正常"),
    run(Integer.valueOf(2), "run", "执行中"),
    error(Integer.valueOf(1), "error", "异常");

    private Integer value;

    private String code;

    private String text;

    enumMfcResult(Integer value, String code, String text) {
        this.value = value;
        this.code = code;
        this.text = text;
    }

    public static enumMfcResult getByValue(int value) {
        if (oConvertUtils.isEmpty(Integer.valueOf(value)))
            return null;
        for (enumMfcResult val : values()) {
            if (val.getValue().equals(Integer.valueOf(value)))
                return val;
        }
        return null;
    }

    public Integer getValue() {
        return this.value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static enumMfcResult toEnum(Integer Value) {
        for (enumMfcResult e : values()) {
            if (e.getValue() == Value)
                return e;
        }
        return null;
    }
}
