package com.tgvs.wms.transaction.mfc;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tgvs.wms.business.enums.enumFactory;
import com.tgvs.wms.business.enums.enumPointType;
import com.tgvs.wms.business.modules.bd.entity.Point;
import com.tgvs.wms.business.modules.bd.mapper.PointMapper;
import com.tgvs.wms.business.modules.commncation.entity.CommncationDevice;
import com.tgvs.wms.business.modules.commncation.mapper.CommncationDeviceMapper;
import com.tgvs.wms.business.modules.mqlog.entity.MqLog;
import com.tgvs.wms.business.modules.mqlog.mapper.MqLogMapper;
import com.tgvs.wms.common.util.CommonUtil;
import com.tgvs.wms.common.util.StringUtils;
import com.tgvs.wms.transaction.mfc.bean.MFCSystemStatus;
import com.tgvs.wms.transaction.mfc.bean.MFCstationPD;
import com.tgvs.wms.transaction.mfc.bean.ResultResponse;
import com.tgvs.wms.transaction.mfc.bean.messageType;
import com.tgvs.wms.transaction.mfc.config.MfcConfig;
import com.tgvs.wms.transaction.service.MainService;
import com.tgvs.wms.transaction.socket.SocketClient;
import com.tgvs.wms.transaction.util.Logger;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class MfcInitService {

    @Autowired
    private MainService mainService;
    @Autowired
    private MfcConfig mfcConfig;

    protected static SocketClient mfcClient1;
    protected static Thread threadMain;
    protected static Thread threadmainreflush;
    protected static Thread threadallot;
    protected static Thread threadComplete;
    protected static Thread threadInallot;
    protected static Thread threadstam;
    protected static Thread threadEmpty;

    public static List<Byte> listbyteclient1 = new ArrayList<>();
    public static MfcInitService mfcInitService = new MfcInitService();

    @PostConstruct
    public void mfcInitialService(){
        mainService.init();
        init();
        start();
    }
    public void init(){

        //初始化MFC1设备
        LambdaQueryWrapper<CommncationDevice> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CommncationDevice::getSysid, "MFC1");
        List<CommncationDevice> list = MainService.mainServiceutil.commncationDeviceMapper.selectList(queryWrapper);
        if (StringUtils.isNotEmpty(list)) {
            for (CommncationDevice device : list) {
                switch (device.getDeviceno()) {
                    case "MFC1":
                        mfcConfig.setMfcaddress1(device.getIpaddress());
                        mfcConfig.setMfcport1(device.getPort().intValue());
                }
            }
        }

        //设置出库站台
        LambdaQueryWrapper<Point> queryWrapperPoint = new LambdaQueryWrapper();
        queryWrapperPoint.eq(Point::getPointType, enumPointType.outbound.getValue());
        List<Point> listPoint = MainService.mainServiceutil.pointMapper.selectList(queryWrapperPoint);
        if (StringUtils.isNotEmpty(listPoint)){
            mfcConfig.setOutSite(listPoint.get(0));
        }

        //设置空箱补给线
        LambdaQueryWrapper<Point> queryWrapperPointEmpty = new LambdaQueryWrapper();
        queryWrapperPointEmpty.eq(Point::getPointType, enumPointType.emptyline.getValue());
        List<Point> listPointEmpty = MainService.mainServiceutil.pointMapper.selectList(queryWrapperPointEmpty);
        if (StringUtils.isNotEmpty(listPointEmpty)){
            mfcConfig.setEmptyPointSite(listPointEmpty.get(0));
        }
        Logger.logFile("MFC1初始化配置信息！");


        //设置入库节点
        LambdaQueryWrapper<Point> queryWrapperInPoint = new LambdaQueryWrapper();
        queryWrapperInPoint.eq(Point::getPointType, enumPointType.inbound.getValue());

        if(mfcConfig.getPDstation() ==null){
            mfcConfig.setPDstation(new ArrayList<>());
        }else{
            mfcConfig.getPDstation().clear();
        }
        List<Point> listInPoint = MainService.mainServiceutil.pointMapper.selectList((Wrapper)queryWrapperInPoint);
        if (StringUtils.isNotEmpty(listInPoint)) {
            for (Point point : listInPoint) {
//                for (int i = 1; i <= iLevel; i++) {
//                    MFCstationPD pd = new MFCstationPD();
//                    pfd.setStatus(Integer.valueOf(0));
//                    pd.setId(Integer.valueOf(i));
//                    pd.setLevel(Integer.valueOf(i));
//                    pd.setPos(point.getPointDirection());
//                    PDstation.add(pd);
//                }
            }
        }
    }

    public void start() {
        mfcClient1 = new SocketClient(mfcConfig.getMfcaddress1(), mfcConfig.getMfcport1(), "MFC1", "RecMsgByte", this);
        mfcClient1.setMethod_error("connect_error");
        mfcClient1.setObject_error(this);
        mfcClient1.setMethod_ping("ping");
        mfcClient1.setObject_ping(this);
        mfcClient1.connect();

//        threadMain = new Thread(new Runnable() {
//            public void run() {
//                MfcService.mainTaskThread(MfcFourBase.mfcClient1);
//            }
//        });
//        threadMain.start();
//        threadmainreflush = new Thread(new Runnable() {
//            public void run() {
//                MfcService.mainTaskThreadFlush(MfcFourBase.mfcClient1);
//            }
//        });
//        threadmainreflush.start();
//        threadallot = new Thread(new Runnable() {
//            public void run() {
//                if ("B".equals(enumFactory.four.getCode())) {
//                    MfcService.loopTaskThreadFour();
//                } else if ("B".equals(enumFactory.two.getCode())) {
//                    MfcService.loopTaskThreadTwo();
//                }
//            }
//        });
//        threadallot.start();
//        threadComplete = new Thread(new Runnable() {
//            public void run() {
//                if (!"B".equals(enumFactory.four.getCode()))
//                    if ("B".equals(enumFactory.two.getCode()))
//                        MfcService.loopTaskThreadComplete();
//            }
//        });
//        threadComplete.start();
//        threadInallot = new Thread(new Runnable() {
//            public void run() {
//                MfcService.loopTaskThreadInBound();
//            }
//        });
//        threadInallot.start();
//        threadstam = new Thread(new Runnable() {
//            public void run() {
//                MfcService.getStam();
//            }
//        });
//        threadstam.start();
//        threadEmpty = new Thread(new Runnable() {
//            public void run() {
//                MfcService.EmptyTaskThread();
//            }
//        });
//        threadEmpty.start();
//        MainService.updatecommncationServer("MFC1", 1);
    }

    public static void connect_error(SocketClient sc, Integer status) {
        if (null == sc)
            return;
        try {
            LambdaQueryWrapper<CommncationDevice> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CommncationDevice::getDeviceno, sc.getName());
            List<CommncationDevice> list = MainService.mainServiceutil.commncationDeviceMapper.selectList((Wrapper)queryWrapper);
            if (null != list && list.size() > 0) {
                ((CommncationDevice)list.get(0)).setState(status);
                MainService.mainServiceutil.commncationDeviceMapper.updateById(list.get(0));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void RecMsgByte(SocketClient sc, byte[] recBytes) {
        try {
            switch (sc.getName()) {
                case "MFC1":
                    for (byte b : recBytes)
                        listbyteclient1.add(Byte.valueOf(b));
                    RecClienthandler(sc, listbyteclient1);
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {}
    }



    public static void RecClienthandler(SocketClient sc, List<Byte> recBytes) {
        try {
            byte[] srtMsg = listTobyte(recBytes);
            String str = new String(srtMsg);
            if (!str.equals("")) {
                String[] listString = str.split("\r\n");
                if (listString.length > 0) {
                    if (!listString[listString.length - 1].equals("")) {
                        byte[] endbyte = listString[listString.length - 1].getBytes();
                        int i = 0;
                        for (byte b : endbyte) {
                            if (b == 123) {
                                i++;
                            } else if (b == 125) {
                                i--;
                            }
                        }
                        if (i != 0) {
                            listbyteclient1.clear();
                            for (byte b : endbyte)
                                listbyteclient1.add(Byte.valueOf(b));
                        } else {
                            listbyteclient1.clear();
                        }
                    }
                    if (listbyteclient1.size() == 0) {
                        for (String msg : listString)
                            RecMsghander(sc, msg);
                    } else {
                        for (int i = 0; i < listString.length - 1; i++)
                            RecMsghander(sc, listString[i]);
                    }
                }
            }
        } catch (Exception exception) {
            log.error("客户端接收消息异常", exception);
        } finally {}
    }

    public static void RecMsghander(SocketClient sc, String recData) {
        Logger.logFile("<<<<<" + recData);
        if (null != recData && !recData.equals(""))
            if (null != recData && !recData.equals("")) {
                ResultResponse resultResponse = null;
                try {
                    MFCSystemStatus mfcSystemStatus;
                    JSONObject jSONObject = JSON.parseObject(recData);
                    messageType type = JSON.toJavaObject(jSONObject, messageType.class);
                    if (null == type)
                        return;
                    switch (type.getMessageName()) {
                        case "appointBoxAnnounce":
                            resultResponse = JSON.toJavaObject(jSONObject, ResultResponse.class);
                            MainService.delegateEvent(mfcInitService, "insertMQ", new Object[] { "MFC1", "WMS1", resultResponse.getId().toString(), "appointBoxAnnounce", recData });
//                            UpdateInBoundByResponse(resultResponse);
                            break;
//                        case "appointStockOut":
//                            resultResponse = (ResultResponse)JSON.toJavaObject((JSON)jSONObject, ResultResponse.class);
//                            MainService.delegateEvent(mfcService, "insertMQ", new Object[] { "MFC1", "WMS1", resultResponse.getId().toString(), "appointStockOut", recData });
//                            UpdateOutBoundByResponse(resultResponse);
//                        case "moveLibrary":
//                            resultResponse = (ResultResponse)JSON.toJavaObject((JSON)jSONObject, ResultResponse.class);
//                            MainService.delegateEvent(mfcService, "insertMQ", new Object[] { "MFC1", "WMS1", resultResponse.getId().toString(), "moveLibrary", recData });
//                            UpdateOutBoundByResponse(resultResponse);
//                        case "getAllStatus":
//                            mfcSystemStatus = new MFCSystemStatus();
//                            mfcSystemStatus = (MFCSystemStatus)JSON.toJavaObject((JSON)jSONObject, MFCSystemStatus.class);
//                            MainService.delegateEvent(mfcService, "insertMQ", new Object[] { "MFC1", "WMS1", mfcSystemStatus.getId().toString(), "getAllStatus", recData });
//                            UpdateState(mfcSystemStatus);
//                        case "pong":
//                            return;
                            default:

                                Logger.logFile("-----未定义的报文格式");
                    }
                } catch (Exception e) {
                    Logger.logFile("消息处理异常：" + e.getMessage());
                }
            }
    }

    private static byte[] listTobyte(List<Byte> list) {
        if (list == null || list.size() <= 0)
            return null;
        byte[] bytes = new byte[list.size()];
        int i = 0;
        Iterator<Byte> iterator = list.iterator();
        while (iterator.hasNext()) {
            bytes[i] = ((Byte)iterator.next()).byteValue();
            i++;
        }
        return bytes;
    }


    public static void insertMQ(String sender, String reciver, String taskid, String faunctionName, String text) {
        if (null != faunctionName)
            try {
                Date nowtime = new Date();
                MqLog log = new MqLog();
                log.setSender(sender);
                log.setReceive(reciver);
                log.setType(faunctionName);
                log.setMqNumber(taskid);
                log.setSysid("MFC1");
                log.setMsg(text);
                log.setMqTime(nowtime);
                log.setCreateTime(nowtime);
                log.setDevice("MFC1");
                MainService.mainServiceutil.mqLogMapper.insert(log);
            } catch (Exception exception) {
                log.error("保存mq日志异常", exception);
            }
    }

}
