package com.tgvs.wms.transaction.mfc.bean;


public class AdjustLocation {
    private int level;

    private int location;

    private String boxId;

    private int outbound;

    public void setLevel(int level) {
        this.level = level;
    }

    public void setLocation(int location) {
        this.location = location;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public void setOutbound(int outbound) {
        this.outbound = outbound;
    }

    public boolean equals(Object o) {
        if (o == this)
            return true;
        if (!(o instanceof AdjustLocation))
            return false;
        AdjustLocation other = (AdjustLocation)o;
        if (!other.canEqual(this))
            return false;
        if (getLevel() != other.getLevel())
            return false;
        if (getLocation() != other.getLocation())
            return false;
        Object this$boxId = getBoxId(), other$boxId = other.getBoxId();
        return ((this$boxId == null) ? (other$boxId != null) : !this$boxId.equals(other$boxId)) ? false : (!(getOutbound() != other.getOutbound()));
    }

    protected boolean canEqual(Object other) {
        return other instanceof AdjustLocation;
    }

    public int hashCode() {
        int PRIME = 59,
        result = 1;
        result = result * 59 + getLevel();
        result = result * 59 + getLocation();
        Object $boxId = getBoxId();
        result = result * 59 + (($boxId == null) ? 43 : $boxId.hashCode());
        return result * 59 + getOutbound();
    }

    public String toString() {
        return "AdjustLocation(level=" + getLevel() + ", location=" + getLocation() + ", boxId=" + getBoxId() + ", outbound=" + getOutbound() + ")";
    }

    public int getLevel() {
        return this.level;
    }

    public int getLocation() {
        return this.location;
    }

    public String getBoxId() {
        return this.boxId;
    }

    public int getOutbound() {
        return this.outbound;
    }
}
